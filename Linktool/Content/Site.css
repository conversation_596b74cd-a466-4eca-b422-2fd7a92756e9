body {
    padding-bottom: 20px;
    /*background-color:rgba(182, 215, 168, 0.4);*/
    font-family: 'Segoe UI', 'Helvetica Neue', 'Droid Sans', Arial, Tahoma, Geneva, sans-serif;
}

/* Set padding to keep content from hitting the edges */
.body-content {
    padding-left: 15px;
    padding-right: 15px;
}

/* toggle class for menu*/

.page-wrapper-toggle {
    margin-left: 0 !important;
}

.slide-handle {
    cursor: pointer;
    position: relative;
    left: -1.6%;
}

/* visited link color */
.dxgvControl_ModernoNeosys a:visited {
    color: #969696 !important;
    text-decoration: none;
}

h1 {
    font-size: 28px;
}



.navbar-default {
    /*background-color: rgba(182, 215, 168, 0);*/
}

li.active a {
    font-weight: bold;
}

ul.nav li a {
    color: #141414;
}

/*login checkbox fix*/
.radio input[type="radio"], .radio-inline input[type="radio"], .checkbox input[type="checkbox"], .checkbox-inline input[type="checkbox"] {
    margin-left: 0;
}

/*right login link fix*/
.navbar-nav.navbar-right:last-child {
    margin-right: 0px;
}

.navbar-default {
    border: none;
}

/*leftbar size*/
.sidebar {
    margin-top: 100px;
}

@media(min-width:768px) {
    .sidebar {
        width: 200px;
    }
}

@media(min-width:768px) {
    #page-wrapper {
        margin-left: 200px;
    }
}

/*add button*/
 .dxgvHeader_ModernoNeosys a.dxbButton_ModernoNeosys.dxbButtonSys img  {
    width: 20px;
    opacity: 0.4;
    filter: alpha(opacity=40); /* For IE8 and earlier */
}

 .dxgvHeader_ModernoNeosys a.dxbButton_ModernoNeosys.dxbButtonSys img:hover {
        width: 20px;
        opacity: 1.0;
        filter: alpha(opacity=100); /* For IE8 and earlier */
    }

/*edit & delete button etc*/

.dxeHyperlink_ModernoNeosys img,
.dxtlCommandCell_ModernoNeosys img,
.dxgvCommandColumn_ModernoNeosys img,
a.dxbButton_ModernoNeosys.dxbButtonSys img {
    opacity: 0.4;
    filter: alpha(opacity=40); /* For IE8 and earlier */
    width: 15px;
    margin-left: 5px;
}

/* copy button*/
.dxgvCommandColumnItem_ModernoNeosys img {
    opacity: 0.9;
    filter: alpha(opacity=90); /* For IE8 and earlier */
    width: 15px;
    margin-left: 5px;
}

.dxeHyperlink_ModernoNeosys img:hover,
.dxtlCommandCell_ModernoNeosys img:hover,
.dxgvCommandColumn_ModernoNeosys img:hover,
a.dxbButton_ModernoNeosys.dxbButtonSys img:hover {
    opacity: 1.0;
    filter: alpha(opacity=100); /* For IE8 and earlier */
    width: 15px;
    margin-left: 5px;
}

/*save button*/
.dxgvStatusBar_ModernoNeosys a.dxbButton_ModernoNeosys.dxbButtonSys img {
    width: 26px;
    margin-right: 15px;
}

.dxgvStatusBar_ModernoNeosys a.dxbButton_ModernoNeosys.dxbButtonSys img:hover {
        width: 26px;
        margin-right: 15px;
    }

.dxeHyperlink_ModernoNeosys a:visited {
    color: #969696 !important;
    text-decoration: none;
}

#wrapper .dxgvGroupPanel_ModernoNeosys {
    /*background-color: #FFF8DC;*/
    margin: 20px 0 20px 0;
    border-style: dashed;
    border-width: 1px;
    border-color: #d1d1d1;
}

#wrapper .dxgvHeader_Moderno, #wrapper .dxtlHeader_Moderno {
    border: 1px Solid #d1d1d1;
    background: #E1E1E1;
}

#wrapper .dxgvDataRowAlt_Moderno, #wrapper .dxtlAltNode_Moderno {
    background-color: #eaeaea;
}

#wrapper .dxgvFilterRow_Moderno {
    background-color: #E1E1E1;
}

#wrapper .dxgvSelectedRow_Moderno {
    background-color: rgb(225, 239, 220);
}

#wrapper .divToolbar {
    width: 100%;
    overflow: hidden;
    margin-bottom: 5px;
}

#wrapper .divButtonsLeft {
    float: right;
}

/*Breadcrumbx*/
.breadcrumb-noesys {
    font-size: 12px;
    padding: 10px 0 0 0;
    vertical-align: middle;
}

.breadcrumb-noesys-navigation a {
    vertical-align: middle;
}

.breadcrumb-noesys-navigation span {
    vertical-align: middle;
}

/* Tree list*/
.btn-new-category {
    margin-bottom: 5px;
}

/* Import */
.popupImport .dxucButton_ModernoNeosys a {
    color: #2B2B2B;
    display: inline-block;
    border: 1px solid #c3c3c3;
    padding: 4px 22px;
    background: #eaeaea;
    background: -ms-linear-gradient(top, rgba(234,234,234,1) 0%, rgba(223,223,223,1) 100%);
    background: -moz-linear-gradient(top, rgba(234,234,234,1) 0%, rgba(223,223,223,1) 100%);
    background: -o-linear-gradient(top, rgba(234,234,234,1) 0%, rgba(223,223,223,1) 100%);
    background: -webkit-linear-gradient(top, rgba(234,234,234,1) 0%, rgba(223,223,223,1) 100%);
    background: linear-gradient(to bottom, rgba(234,234,234,1) 0%, rgba(223,223,223,1) 100%);
    border-radius: 4px;
    -webkit-border-radius: 4px;
    box-shadow: inset 0px 1px 0px 0px rgba(255,255,255,0.35), 0px 1px 3px 0px rgba(0,0,0,0.1);
    -webkit-box-shadow: inset 0px 1px 0px 0px rgba(255,255,255,0.35), 0px 1px 3px 0px rgba(0,0,0,0.1);
}

    .popupImport .dxucButton_ModernoNeosys a:hover {
        text-decoration: none;
    }

.popupImport .dxucButton_ModernoNeosys:hover a {
    border: 1px Solid #9FBD92;
    color: white;
    background: #1d85cd;
    background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pg0KPHN2ZyB4bWxucz0ia…hlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4NCjwvc3ZnPg0K);
    background: -ms-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
    background: -moz-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
    background: -o-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
    background: -webkit-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
    background: linear-gradient(to bottom, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
    box-shadow: inset 0px 1px 0px 0px rgba(255,255,255,0.15), 0px 1px 3px 0px rgba(0,0,0,0.1);
    -webkit-box-shadow: inset 0px 1px 0px 0px rgba(255,255,255,0.15), 0px 1px 3px 0px rgba(0,0,0,0.1);
}

.popupImport .dxucButton_ModernoNeosys a img {
    margin-right: 5px;
}

.popupImport .dxucButtonDisabled_ModernoNeosys a {
    display: inline-block;
    border: 1px solid #c3c3c3;
    padding: 4px 22px;
    background: #eaeaea;
    background: -ms-linear-gradient(top, rgba(234,234,234,1) 0%, rgba(223,223,223,1) 100%);
    background: -moz-linear-gradient(top, rgba(234,234,234,1) 0%, rgba(223,223,223,1) 100%);
    background: -o-linear-gradient(top, rgba(234,234,234,1) 0%, rgba(223,223,223,1) 100%);
    background: -webkit-linear-gradient(top, rgba(234,234,234,1) 0%, rgba(223,223,223,1) 100%);
    background: linear-gradient(to bottom, rgba(234,234,234,1) 0%, rgba(223,223,223,1) 100%);
    border-radius: 4px;
    -webkit-border-radius: 4px;
    box-shadow: inset 0px 1px 0px 0px rgba(255,255,255,0.35), 0px 1px 3px 0px rgba(0,0,0,0.1);
    -webkit-box-shadow: inset 0px 1px 0px 0px rgba(255,255,255,0.35), 0px 1px 3px 0px rgba(0,0,0,0.1);
}

    .popupImport .dxucButtonDisabled_ModernoNeosys a span {
        color: #9E9E9E;
    }

.popupImport .dxucButtonDisabled_ModernoNeosys:hover a {
    display: inline-block;
    border: 1px solid #c3c3c3;
    padding: 4px 22px;
    background: #eaeaea;
    background: -ms-linear-gradient(top, rgba(234,234,234,1) 0%, rgba(223,223,223,1) 100%);
    background: -moz-linear-gradient(top, rgba(234,234,234,1) 0%, rgba(223,223,223,1) 100%);
    background: -o-linear-gradient(top, rgba(234,234,234,1) 0%, rgba(223,223,223,1) 100%);
    background: -webkit-linear-gradient(top, rgba(234,234,234,1) 0%, rgba(223,223,223,1) 100%);
    background: linear-gradient(to bottom, rgba(234,234,234,1) 0%, rgba(223,223,223,1) 100%);
    border-radius: 4px;
    -webkit-border-radius: 4px;
    box-shadow: inset 0px 1px 0px 0px rgba(255,255,255,0.35), 0px 1px 3px 0px rgba(0,0,0,0.1);
    -webkit-box-shadow: inset 0px 1px 0px 0px rgba(255,255,255,0.35), 0px 1px 3px 0px rgba(0,0,0,0.1);
}

.popupImport .dxucButtonDisabled_ModernoNeosys a img {
    opacity: 0.4;
}

/* labels kundendokumentitem status*/
.label-New {
    background-color: #5cb85c;
}

.label-NewVersion {
    background-color: #5cb85c;
}

.label-OldVersion {
    background-color: #f0ad4e;
}

.label-Replaced {
    background-color: #ff0000;
}

.label-Removed {
    background-color: #ff0000;
}

.label-Existing {
    background-color: #AAAAAA;
}

.label-Unchanged {
    background-color: #AAAAAA;
}

.label-ToExamine {
    background-color: #5cb85c;
}

.changed {
    background-color: #ffff00;
}

.forderungs-old-version-beschreibung {
    height: 400px;
    padding-top: 35px;
}

.editor-field {
    padding-top: 7px;
}

#wrapper .dxgvControl_ModernoNeosys .dxgvTable_ModernoNeosys td > a {
    text-decoration: underline;
}

#wrapper .dxeListBoxItem_ModernoNeosys {
    white-space: normal;
}

#wrapper .dxeListBox_ModernoNeosys .dxeHD {
    background-color: rgb(227, 227, 227);
}

#wrapper .dxeListBoxItemSelected_ModernoNeosys {
    background-color: #009C49;
}

.divPwdChange {
    margin-bottom: 20px;
}

.popupButtons {
    float: right;
    margin-top: 10px;
}

    .popupButtons .button {
        margin-right: 10px;
    }

#popupContent {
    margin-right: 10px;
    height: 400px;
    padding: 5px;
    border: solid 1px rgb(209, 209, 209);
    overflow-y: scroll;
}

.iconlink i {
    opacity: 0.5;
    filter: alpha(opacity=50);
}

    .iconlink i:hover {
        opacity: 1;
        filter: alpha(opacity=100);
    }

#wrapper .dxgv {
    vertical-align: top;
}

    #wrapper .dxgv span.dxichCellSys.dxeTAR {
        padding: 8px 3px 4px;
    }

#wrapper .dxtc-content {
    overflow: visible;
}

.table_positionfixed {
    position: fixed;
    top: 0;
}

#gvRowSelection_DXPagerBottom {
    min-width: 450px !important;
}