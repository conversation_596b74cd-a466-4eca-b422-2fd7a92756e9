@using NeoSysLCS.Linktool.Helpers
@using NeoSysLCS.Linktool.Models
@using NeoSysLCS.Linktool.Repositories
@using NeoSysLCS.Site.Helpers

@model IQueryable<NeoSysLCS.Linktool.Models.SuvalinkViewModel>

@{
    var grid = Html.DevExpress().GridView(settings =>
    {
        var context = new NeoSysLCS_Dev();
        var _unitOfWork = new UnitOfWork(context);

        settings.Name = "SuvalinkGridView";
        settings.KeyFieldName = "SuvalinkID";

        GridViewHelper.ApplyDefaultSettings(settings);

        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;

        settings.CallbackRouteValues = new {Controller = "Suvalink", Action = "SuvalinkGridView"};
        settings.SettingsEditing.AddNewRowRouteValues = new { Controller = "Suvalink", Action = "SuvalinkViewPartialAddNew" };
        settings.SettingsEditing.UpdateRowRouteValues = new { Controller = "Suvalink", Action = "SuvalinkViewPartialUpdate" };
        settings.SettingsEditing.DeleteRowRouteValues = new { Controller = "Suvalink", Action = "SuvalinkViewPartialDelete" };        

        settings.SettingsBehavior.ConfirmDelete = true;
        settings.Styles.AlternatingRow.Enabled = DefaultBoolean.True;
        settings.SettingsBehavior.EnableRowHotTrack = true;

        settings.SettingsPopup.EditForm.Modal = true;
        settings.SettingsPopup.EditForm.VerticalAlign = PopupVerticalAlign.WindowCenter;
        settings.SettingsPopup.EditForm.HorizontalAlign = PopupHorizontalAlign.WindowCenter;

        settings.SettingsPopup.EditForm.ShowHeader = true;

        settings.CommandColumn.Visible = true;
        settings.CommandColumn.ShowNewButtonInHeader = true;
        settings.CommandColumn.ShowDeleteButton = true;
        settings.CommandColumn.ShowEditButton = true;

        settings.Settings.ShowGroupPanel = false;

        settings.BeforeGetCallbackResult = (sender, e) =>
        {
            var gridView = sender as MVCxGridView;

            gridView.SettingsText.PopupEditFormCaption = gridView.IsNewRowEditing ? "Neuen Link hinzuf�gen" : "Link bearbeiten";
        };

        settings.Columns.Add(column =>
        {
            column.Caption = "Link-ID";
            column.FieldName = "InternerLink";
            column.EditFormSettings.Visible = DefaultBoolean.True;
            column.EditFormSettings.VisibleIndex = 1;
            column.EditFormSettings.ColumnSpan = 2;
            column.Settings.AutoFilterCondition = AutoFilterCondition.Contains;
        });
        settings.Columns.Add(column =>
        {
            column.Caption = "Ziel-URL";
            column.FieldName = "TargetURL";
            column.EditFormSettings.Visible = DefaultBoolean.True;
            column.EditFormSettings.VisibleIndex = 3;
            column.EditFormSettings.ColumnSpan = 2;
            column.ReadOnly = true;
            column.SetDataItemTemplateContent(container =>
            {
                var url = DataBinder.Eval(container.DataItem, "TargetURL");
                string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url);
                ViewContext.Writer.Write(htmlLink);
            });
            column.Settings.AutoFilterCondition = AutoFilterCondition.Contains;
        });
        settings.Columns.Add(column =>
        {
            column.FieldName = "ErlassID";
            column.Caption = "Erlass";
            column.EditFormSettings.Visible = DefaultBoolean.True;
            column.EditFormSettings.VisibleIndex = 2;
            column.EditFormSettings.ColumnSpan = 2;
            column.ColumnType = MVCxGridViewColumnType.ComboBox;
            column.Settings.FilterMode = ColumnFilterMode.DisplayText;
            column.EditorProperties().ComboBox(c =>
            {
                c.CallbackRouteValues = new { Controller = "Suvalink", Action = "SuvalinkComboBox" };
                c.ValueField = "ErlassID";
                c.TextField = "Titel";
                c.Columns.Add("SrNummer", "SrNummer");
                c.Columns.Add("Titel", "Titel");
                c.CallbackPageSize = 1000;
                c.DropDownStyle = DropDownStyle.DropDownList;
                c.TextFormatString = "{1}";
                c.BindList(_unitOfWork.ErlassRepositoryRepository.GetErlassComboBox().OrderBy(x => x.Titel));
                c.IncrementalFilteringMode = IncrementalFilteringMode.Contains;
            });
        });
        settings.Columns.Add(column =>
        {
            column.FieldName = "SprachID";
            column.Caption = "Sprache";
            column.EditFormSettings.Visible = DefaultBoolean.True;
            column.EditFormSettings.VisibleIndex = 3;
            column.EditFormSettings.ColumnSpan = 2;
            //column..CssClass = "ComboBoxSize";
            column.ColumnType = MVCxGridViewColumnType.ComboBox;
            column.Settings.FilterMode = ColumnFilterMode.DisplayText;
            var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;
            comboBoxProperties.DataSource = (from x in context.Sprachen select new { x.SpracheID, x.Name }).ToList();
            comboBoxProperties.TextField = "Name";
            comboBoxProperties.ValueField = "SpracheID";
            comboBoxProperties.ValueType = typeof(int);
        });

    });

    if (ViewData["EditError"] != null)
    {
        grid.SetEditErrorText((string)ViewData["EditError"]);
    }
}

@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model; ;
    e.KeyExpression = "SuvalinkID";
}).GetHtml()
