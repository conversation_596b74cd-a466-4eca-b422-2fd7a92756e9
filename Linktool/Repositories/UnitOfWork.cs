using System;
using NeoSysLCS.Linktool.Models;

namespace NeoSysLCS.Linktool.Repositories
{
    public class UnitOfWork : IUnitOfWork
    {
        private readonly NeoSysLCS_Dev _context;
        private bool _disposed = false;
        private ErlassRepository _erlassRepository;
        private GenericRepository<Sprache> _spracheRepository;
        private SuvalinkRepository _suvalinkRepository;

        public UnitOfWork()
        {
            _context = new NeoSysLCS_Dev();
        }

        public UnitOfWork(NeoSysLCS_Dev contextCandidate)
        {
            _context = contextCandidate;
        }

        public NeoSysLCS_Dev Context
        {
            get { return _context; }
        }

        public SuvalinkRepository SuvalinkRepository
        {
            get
            {
                if (_suvalinkRepository == null)
                {
                    _suvalinkRepository = new SuvalinkRepository(_context);
                }
                return _suvalinkRepository;
            }
        }

        public GenericRepository<Sprache> SpracheRepository
        {
            get
            {

                if (_spracheRepository == null)
                {
                    _spracheRepository = new GenericRepository<Sprache>(_context);
                }
                return _spracheRepository;
            }
        }

        // Erlass Repository
        public ErlassRepository ErlassRepository
        {
            get
            {

                if (_erlassRepository == null)
                {
                    _erlassRepository = new ErlassRepository(_context);
                }
                return _erlassRepository;
            }
        }

        public void Save()
        {
            _context.SaveChanges();
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _context.Dispose();
                }
            }
            _disposed = true;
        }

       
    }

  
}