/* -- CheckBox -- */
.dxICBFocused_ModernoNeosys 
{
    outline: 1px dotted #d1d1d1;

	*border: 1px dotted #d1d1d1;
	*margin: 0;
}
.dxICheckBox_ModernoNeosys 
{
}
/* -- ASPxCallbackPanel -- */
.dxcpDisabled_ModernoNeosys
{
	color: #A6A6A6;
	cursor: default;
}

/* -- ASPxCloudControl -- */
.dxccControl_ModernoNeosys
{
	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
	color: #2B2B2B;
	text-decoration: none;
	background-color: #FFFFFF;
}
.dxccControl_ModernoNeosys a
{
	text-decoration: none;
	color: #009C49;
}
.dxccControl_ModernoNeosys a:hover
{
    text-decoration: underline;
}
.dxccControl_ModernoNeosys .dxccValue
{
    color: #B9B9B9;
}
/* Disabled */
.dxccDisabled_ModernoNeosys,
.dxccDisabled_ModernoNeosys span.dxccValue,
.dxccDisabled_ModernoNeosys span.dxccBEText
{
	color: #A6A6A6;
	cursor: default;
}

/* -- ASPxDataView -- */
.dxdvControl_ModernoNeosys
{
	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
	color: #2B2B2B;
}
.dxdvControl_ModernoNeosys td.dxdvCtrl
{
	padding: 0px;
}
.dxdvContent_ModernoNeosys
{
	padding: 20px;
	border-bottom: 1px Solid #d1d1d1;
	border-top: 1px Solid #d1d1d1;
}
.dxdvItem_ModernoNeosys,
.dxdvFlowItem_ModernoNeosys
{
	background-color: #FFFFFF;
	padding: 20px;
	height: 180px;
	height: expression("154px");
}
.dxdvFlowItem_ModernoNeosys
{
	float: left;
	overflow: hidden;
}
.dxdvFlowItemsContainer_ModernoNeosys
{
}
.dxdvEmptyItem_ModernoNeosys
{
	text-align: left;
	vertical-align: top;
	padding: 20px;
	height: 180px;
	height: expression("154px");
}
.dxdvPagerPanel_ModernoNeosys
{
	padding-top: 8px;
	padding-bottom: 8px;
}
.dxdvEmptyData_ModernoNeosys
{
	color: #4F4F4F;
	padding: 12px 40px;
}
.dxdvEPContainer_ModernoNeosys
{
    min-height: 58px;
	text-align: center;
}
.dxdvEPContainer_ModernoNeosys  div
{
	padding-top: 20px;
}
.dxdvEPContainer_ModernoNeosys a
{
	color: #009C49;
}
.dxdvEPContainer_ModernoNeosys  a:hover
{
	color: #2B2B2B;
}
/* Disabled */
.dxdvDisabled_ModernoNeosys
{
	color: #A6A6A6;
	cursor: default;
}

/* -- ASPxHeadline -- */
.dxhlControl_ModernoNeosys 
{
	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
	color: #2B2B2B;
}
.dxhlControl_ModernoNeosys a
{
	color: #009C49;
}
.dxhlControl_ModernoNeosys a:hover
{
	color: #2B2B2B;
}
.dxhlContent_ModernoNeosys
{
	line-height: 120%!important;
	padding: 4px 0;
} 
.dxhlDate_ModernoNeosys
{
	color: #808080;
	white-space: nowrap;
	font-size: 0.86em;
}
.dxhlHeader_ModernoNeosys
{
	color: black;
	font-weight: normal;
	line-height: 141%;
	font-size: 1.5em;
}
.dxhlDateHeader_ModernoNeosys
{
	font-size: 0.57em;
	color: #808080;
	white-space: nowrap;
	font-weight: normal;
	padding: 0 5px;
}
.dxhlLeftPanel_ModernoNeosys
{
	line-height: 121%;
	text-align: right;
}
.dxhlRightPanel_ModernoNeosys
{
	line-height: 121%;
}
.dxhlLeftPanel_ModernoNeosys img,
.dxhlRightPanel_ModernoNeosys img 
{
	margin-top: 5px;
}
.dxhlDateLeftPanel_ModernoNeosys,
.dxhlDateRightPanel_ModernoNeosys
{
	color: #808080;
	white-space: nowrap;
	padding: 10px 0 0;
	font-size: 0.86em;
}
.dxhlTailDiv_ModernoNeosys
{
	color: #009C49;
}
/* Disabled */
.dxhlDisabled_ModernoNeosys
{
	color: #A6A6A6;
	cursor: default;
}

/* -- ASPxLoadingPanel -- */
.dxlpLoadingPanel_ModernoNeosys,
.dxlpLoadingPanelWithContent_ModernoNeosys
{
	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
	color: #2B2B2B;
	background-color: White;
	border: 1px solid #cfcfcf;
	box-shadow: 0px 1px 3px 0px rgba(0,0,0,0.1);
	-webkit-box-shadow: 0px 1px 3px 0px rgba(0,0,0,0.1);
}
.dxlpLoadingPanel_ModernoNeosys td.dx,
.dxlpLoadingPanelWithContent_ModernoNeosys td.dx {
    white-space: nowrap;
    text-align: center;
    padding: 15px 34px 15px 20px;
}
/* DocumentViewer Ribbon */
.dxrControl_ModernoNeosys .dxr-tmplItem .dxxrdvrPageNumberComboBox {
    width: 130px;
}
.dxrControl_ModernoNeosys .dxr-tmplItem .dxxrdvrCurrentPageLabel {
    padding: 0 0 4px 0;
    display: block;
}
.dxrControl_ModernoNeosys .dxr-tmplItem .dxxrdvrPageCountLabel {
    padding: 4px 0 0 0;
    display: block;
}
}
.dxlpLoadingPanel_ModernoNeosys .dxlp-loadingImage,
.dxlpLoadingPanelWithContent_ModernoNeosys .dxlp-loadingImage {
	background-image: url('Loading.gif');
	height: 40px;
	width: 40px;
}
.dxlpControl_ModernoNeosys.dxlpLoadingDiv_ModernoNeosys 
{
	background-color: white;
	opacity: 0.7;
	filter: progid:DXImageTransform.Microsoft.Alpha(Style=0, Opacity=70);
}

/* -- ASPxMenu Lite -- */
.dxmLite_ModernoNeosys
{
	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
	color: #2B2B2B;
}
.dxmLite_ModernoNeosys .dxm-main
{
	color: #2B2B2B;
}

.dxmLite_ModernoNeosys .dxm-horizontal
{
	padding: 0px;
}
.dxmLite_ModernoNeosys .dxm-vertical
{
	min-width: 100px;
	padding: 0;
}

.dxmLite_ModernoNeosys .dxm-popup
{
	border: 1px solid #d8d8d8;
	background-color: white;
	padding: 1px;

	-webkit-box-shadow:  0px 2px 4px 2px rgba(0, 0, 0, 0.15)!important;
    box-shadow:  0px 1px 6px 0px rgba(0, 0, 0, 0.15)!important;
}

.dxmBrdCor_ModernoNeosys
{
	background-color: #F9F9F9;
}

.dxmLite_ModernoNeosys .dxm-item
{
	cursor: default;
    text-align: left;
}
.dxmLite_ModernoNeosys.dxm-rtl .dxm-item
{
	text-align: right;
}
.dxmLite_ModernoNeosys .dxm-image-t .dxm-item,
.dxmLite_ModernoNeosys .dxm-image-b .dxm-item
{
	text-align: center;
}

.dxmLite_ModernoNeosys .dxm-horizontal .dxm-item,
.dxmLite_ModernoNeosys .dxm-vertical .dxm-item
{
    border: 1px solid transparent;
}

.dxmLite_ModernoNeosys .dxm-content
{
    display: block;
	white-space: nowrap;
}

.dxmLite_ModernoNeosys,
.dxmLite_ModernoNeosys .dxm-item a.dx
{
	color: #2B2B2B;
}
.dxmLite_ModernoNeosys .dxm-hovered,
.dxmLite_ModernoNeosys .dxm-hovered a.dx
{
	color: white;
}
.dxmLite_ModernoNeosys .dxm-disabled,
.dxmLite_ModernoNeosys .dxm-disabled a.dx
{
	color: #A6A6A6;
}
.dxmLite_ModernoNeosys .dxm-item a.dx
{
	text-decoration: none;
}

/* Checked, Selected, Hovered */
.dxmLite_ModernoNeosys.dxm-ltr .dxm-dropDownMode .dxm-popOut
{
	border: Solid 1px transparent;
}
.dxmLite_ModernoNeosys .dxm-dropDownMode.dxm-hovered .dxm-popOut,
.dxmLite_ModernoNeosys .dxm-dropDownMode.dxm-selected .dxm-popOut,
.dxmLite_ModernoNeosys .dxm-dropDownMode.dxm-checked .dxm-popOut
{
	border-left-color: white;
}
.dxmLite_ModernoNeosys.dxm-ltr .dxm-image-r .dxm-dropDownMode.dxm-hovered .dxm-popOut,
.dxmLite_ModernoNeosys.dxm-ltr .dxm-image-r .dxm-dropDownMode.dxm-selected .dxm-popOut,
.dxmLite_ModernoNeosys.dxm-ltr .dxm-image-r .dxm-dropDownMode.dxm-checked .dxm-popOut,
.dxmLite_ModernoNeosys.dxm-rtl .dxm-dropDownMode.dxm-hovered .dxm-popOut,
.dxmLite_ModernoNeosys.dxm-rtl .dxm-dropDownMode.dxm-selected .dxm-popOut,
.dxmLite_ModernoNeosys.dxm-rtl .dxm-dropDownMode.dxm-checked .dxm-popOut
{
	border-right-color: white;
}
.dxmLite_ModernoNeosys .dxm-horizontal .dxm-checked,
.dxmLite_ModernoNeosys .dxm-horizontal .dxm-selected
{
	background: #dcdcdc;
	border-color: #c2c2c2;
}
.dxmLite_ModernoNeosys .dxm-horizontal .dxm-hovered
{
	background: #009C49;
	border-color: transparent;
}
.dxmLite_ModernoNeosys .dxm-vertical .dxm-checked,
.dxmLite_ModernoNeosys .dxm-vertical .dxm-selected
{
	background: #dcdcdc;
	border-color: #c2c2c2;
}
.dxmLite_ModernoNeosys .dxm-vertical .dxm-hovered
{
	background: #009C49;
	border-color: transparent;
}
.dxmLite_ModernoNeosys .dxm-popup .dxm-selected
{
	background-color: #dcdcdc;
}
.dxmLite_ModernoNeosys .dxm-popup .dxm-hovered
{
	background-color: #009C49;
}

/* Content */
.dxmLite_ModernoNeosys.dxm-ltr .dxm-horizontal .dxm-image-l .dxm-content,
.dxmLite_ModernoNeosys.dxm-ltr .dxm-horizontal .dxm-image-r .dxm-content
{
	padding: 5px 28px 6px 26px;
}
.dxmLite_ModernoNeosys.dxm-rtl .dxm-horizontal .dxm-image-l .dxm-content,
.dxmLite_ModernoNeosys.dxm-rtl .dxm-horizontal .dxm-image-r .dxm-content
{
	padding: 4px 26px 5px 28px;
}
.dxmLite_ModernoNeosys.dxm-ltr .dxm-horizontal .dxm-image-t .dxm-content,
.dxmLite_ModernoNeosys.dxm-ltr .dxm-horizontal .dxm-image-b .dxm-content
{
	padding: 5px 12px 4px 11px;
}
.dxmLite_ModernoNeosys.dxm-rtl .dxm-horizontal .dxm-image-t .dxm-content,
.dxmLite_ModernoNeosys.dxm-rtl .dxm-horizontal .dxm-image-b .dxm-content
{
	padding: 5px 11px 4px 12px;
}
.dxmLite_ModernoNeosys.dxm-ltr .dxm-horizontal .dxm-image-t.dxm-noImages .dxm-item .dxm-content,
.dxmLite_ModernoNeosys.dxm-ltr .dxm-horizontal .dxm-image-b.dxm-noImages .dxm-item .dxm-content,
.dxmLite_ModernoNeosys.dxm-ltr .dxm-horizontal .dxm-image-t .dxm-noImage .dxm-content,
.dxmLite_ModernoNeosys.dxm-ltr .dxm-horizontal .dxm-image-b .dxm-noImage .dxm-content
{
	padding: 5px 12px 4px 11px;
}
.dxmLite_ModernoNeosys.dxm-rtl .dxm-horizontal .dxm-image-t.dxm-noImages .dxm-item .dxm-content,
.dxmLite_ModernoNeosys.dxm-rtl .dxm-horizontal .dxm-image-b.dxm-noImages .dxm-item .dxm-content,
.dxmLite_ModernoNeosys.dxm-rtl .dxm-horizontal .dxm-image-t .dxm-noImage .dxm-content,
.dxmLite_ModernoNeosys.dxm-rtl .dxm-horizontal .dxm-image-b .dxm-noImage .dxm-content
{
	padding: 5px 11px 4px 12px;
}
.dxmLite_ModernoNeosys.dxm-ltr .dxm-horizontal .dxm-image-l .dxm-subMenu .dxm-content,
.dxmLite_ModernoNeosys.dxm-ltr .dxm-horizontal .dxm-image-t .dxm-subMenu .dxm-content,
.dxmLite_ModernoNeosys.dxm-ltr .dxm-horizontal .dxm-image-b .dxm-subMenu .dxm-content
{
	padding-right: 18px;
}
.dxmLite_ModernoNeosys.dxm-rtl .dxm-horizontal .dxm-image-r .dxm-subMenu .dxm-content,
.dxmLite_ModernoNeosys.dxm-rtl .dxm-horizontal .dxm-image-t .dxm-subMenu .dxm-content,
.dxmLite_ModernoNeosys.dxm-rtl .dxm-horizontal .dxm-image-b .dxm-subMenu .dxm-content
{
	padding-left: 18px;
}
.dxmLite_ModernoNeosys.dxm-ltr .dxm-horizontal .dxm-image-l .dxm-dropDownMode .dxm-content,
.dxmLite_ModernoNeosys.dxm-ltr .dxm-horizontal .dxm-image-t .dxm-dropDownMode .dxm-content,
.dxmLite_ModernoNeosys.dxm-ltr .dxm-horizontal .dxm-image-b .dxm-dropDownMode .dxm-content
{
	padding-right: 12px;
}
.dxmLite_ModernoNeosys.dxm-rtl .dxm-horizontal .dxm-image-l .dxm-dropDownMode .dxm-content,
.dxmLite_ModernoNeosys.dxm-rtl .dxm-horizontal .dxm-image-t .dxm-dropDownMode .dxm-content,
.dxmLite_ModernoNeosys.dxm-rtl .dxm-horizontal .dxm-image-b .dxm-dropDownMode .dxm-content
{
	padding-left: 12px;
}
.dxmLite_ModernoNeosys.dxm-ltr .dxm-horizontal .dxm-image-r .dxm-subMenu .dxm-content
{
	padding-left: 18px;
}
.dxmLite_ModernoNeosys.dxm-rtl .dxm-horizontal .dxm-image-r .dxm-subMenu .dxm-content
{
	padding-right: 18px;
}
.dxmLite_ModernoNeosys.dxm-ltr .dxm-horizontal .dxm-image-r .dxm-dropDownMode .dxm-content
{
	padding-left: 12px;
}
.dxmLite_ModernoNeosys.dxm-rtl .dxm-horizontal .dxm-image-r .dxm-dropDownMode .dxm-content
{
	padding-right: 12px;
}

.dxmLite_ModernoNeosys.dxm-ltr .dxm-vertical .dxm-image-l .dxm-content,
.dxmLite_ModernoNeosys.dxm-ltr .dxm-vertical .dxm-image-r .dxm-content
{
	padding: 6px 19px 7px 8px;
}
.dxmLite_ModernoNeosys.dxm-rtl .dxm-vertical .dxm-image-l .dxm-content,
.dxmLite_ModernoNeosys.dxm-rtl .dxm-vertical .dxm-image-r .dxm-content
{
	padding: 4px 8px 5px 19px;
}

.dxmLite_ModernoNeosys.dxm-ltr .dxm-vertical .dxm-image-r .dxm-noSubMenu .dxm-content,
.dxmLite_ModernoNeosys.dxm-ltr .dxm-vertical .dxm-image-r .dxm-subMenu .dxm-content,
.dxmLite_ModernoNeosys.dxm-ltr .dxm-vertical .dxm-image-r .dxm-dropDownMode .dxm-content
{
	padding-left: 18px;
	padding-right: 5px;
}
.dxmLite_ModernoNeosys.dxm-rtl .dxm-vertical .dxm-image-l .dxm-noSubMenu .dxm-content,
.dxmLite_ModernoNeosys.dxm-rtl .dxm-vertical .dxm-image-l .dxm-subMenu .dxm-content,
.dxmLite_ModernoNeosys.dxm-rtl .dxm-vertical .dxm-image-l .dxm-dropDownMode .dxm-content
{
	padding-right: 18px;
	padding-left: 5px;
}
.dxmLite_ModernoNeosys.dxm-ltr .dxm-vertical .dxm-image-t .dxm-content,
.dxmLite_ModernoNeosys.dxm-ltr .dxm-vertical .dxm-image-b .dxm-content
{
	padding: 5px 12px 5px 11px;
}
.dxmLite_ModernoNeosys.dxm-rtl .dxm-vertical .dxm-image-t .dxm-content,
.dxmLite_ModernoNeosys.dxm-rtl .dxm-vertical .dxm-image-b .dxm-content
{
	padding: 5px 11px 5px 12px;
}

.dxmLite_ModernoNeosys.dxm-ltr .dxm-popup .dxm-content
{
	padding: 5px 9px 6px 5px;
}
.dxmLite_ModernoNeosys.dxm-rtl .dxm-popup .dxm-content
{
	padding: 5px 5px 6px 9px;
}
.dxmLite_ModernoNeosys.dxm-ltr .dxm-popup .dxm-noSubMenu .dxm-content,
.dxmLite_ModernoNeosys.dxm-ltr .dxm-popup .dxm-subMenu .dxm-content,
.dxmLite_ModernoNeosys.dxm-ltr .dxm-popup .dxm-dropDownMode .dxm-content
{
	padding-right: 17px;
}
.dxmLite_ModernoNeosys.dxm-rtl .dxm-popup .dxm-noSubMenu .dxm-content,
.dxmLite_ModernoNeosys.dxm-rtl .dxm-popup .dxm-subMenu .dxm-content,
.dxmLite_ModernoNeosys.dxm-rtl .dxm-popup .dxm-dropDownMode .dxm-content
{
	padding-left: 17px;
}

/* Image */
.dxmLite_ModernoNeosys .dxm-horizontal .dxm-image-l .dxm-hasText .dxm-image
{
    margin: 1px 5px 1px 0;
}
.dxmLite_ModernoNeosys .dxm-horizontal .dxm-image-r .dxm-hasText .dxm-image
{
    margin: 1px 0 1px 5px;
}
.dxmLite_ModernoNeosys .dxm-horizontal .dxm-image-t .dxm-hasText .dxm-image
{
	margin-bottom: 5px;
}
.dxmLite_ModernoNeosys .dxm-horizontal .dxm-image-b .dxm-hasText .dxm-image
{
	margin-top: 5px;
}
.dxmLite_ModernoNeosys .dxm-vertical .dxm-image-l .dxm-image
{
	margin-right: 12px;
}
.dxmLite_ModernoNeosys .dxm-vertical .dxm-image-r .dxm-image
{
	margin-left: 12px;
}
.dxmLite_ModernoNeosys.dxm-ltr .dxm-popup .dxm-image
{
	margin-right: 16px;
}
.dxmLite_ModernoNeosys.dxm-rtl .dxm-popup .dxm-image
{
	margin-left: 16px;
}
.dxmLite_ModernoNeosys .dxm-popup .dxm-image
{
    margin-top: 1px;
    margin-bottom: 1px;
}

/* Image replacement */
.dxmLite_ModernoNeosys.dxm-ltr .dxm-horizontal .dxm-image-l.dxm-noImages .dxm-item,
.dxmLite_ModernoNeosys.dxm-ltr .dxm-horizontal .dxm-image-l .dxm-noImage,
.dxmLite_ModernoNeosys.dxm-ltr .dxm-horizontal .dxm-image-r.dxm-noImages .dxm-item,
.dxmLite_ModernoNeosys.dxm-ltr .dxm-horizontal .dxm-image-r .dxm-noImage
{
	padding-left: 4px;
}
.dxmLite_ModernoNeosys.dxm-rtl .dxm-horizontal .dxm-image-l.dxm-noImages .dxm-item,
.dxmLite_ModernoNeosys.dxm-rtl .dxm-horizontal .dxm-image-l .dxm-noImage,
.dxmLite_ModernoNeosys.dxm-rtl .dxm-horizontal .dxm-image-r.dxm-noImages .dxm-item,
.dxmLite_ModernoNeosys.dxm-rtl .dxm-horizontal .dxm-image-r .dxm-noImage
{
	padding-right: 4px;
}
.dxmLite_ModernoNeosys.dxm-ltr .dxm-horizontal .dxm-image-r .dxm-noImage.dxm-dropDownMode
{
	padding-left: 0;
}
.dxmLite_ModernoNeosys.dxm-rtl .dxm-horizontal .dxm-image-l .dxm-noImage.dxm-dropDownMode
{
	padding-right: 0;
}
.dxmLite_ModernoNeosys .dxm-vertical .dxm-image-l .dxm-noImage
{
	padding-left: 26px;
}
.dxmLite_ModernoNeosys .dxm-vertical .dxm-image-r .dxm-noImage
{
	padding-right: 26px;
}
.dxmLite_ModernoNeosys .dxm-vertical .dxm-image-t .dxm-noImage,
.dxmLite_ModernoNeosys .dxm-vertical .dxm-image-b .dxm-noImage
{
	padding-left: 3px;
}
.dxmLite_ModernoNeosys.dxm-ltr .dxm-popup .dxm-noImage
{
	padding-left: 30px;
}
.dxmLite_ModernoNeosys.dxm-rtl .dxm-popup .dxm-noImage
{
	padding-right: 30px;
}
.dxmLite_ModernoNeosys.dxm-ltr .dxm-popup .dxm-noImages .dxm-item
{
	padding-left: 6px;
}
.dxmLite_ModernoNeosys.dxm-rtl .dxm-popup .dxm-noImages .dxm-item
{
	padding-right: 6px;
}

/* PopOut */
.dxmLite_ModernoNeosys .dxm-horizontal .dxm-image-l .dxm-popOut,
.dxmLite_ModernoNeosys .dxm-horizontal .dxm-image-r .dxm-popOut,
.dxmLite_ModernoNeosys .dxm-horizontal .dxm-image-t.dxm-noImages .dxm-popOut,
.dxmLite_ModernoNeosys .dxm-horizontal .dxm-image-t .dxm-noImage .dxm-popOut,
.dxmLite_ModernoNeosys .dxm-horizontal .dxm-image-b.dxm-noImages .dxm-popOut,
.dxmLite_ModernoNeosys .dxm-horizontal .dxm-image-b .dxm-noImage .dxm-popOut
{
	padding-top: 12px;
	padding-bottom: 13px;
}
.dxmLite_ModernoNeosys .dxm-horizontal .dxm-image-l .dxm-dropDownMode .dxm-popOut,
.dxmLite_ModernoNeosys .dxm-horizontal .dxm-image-r .dxm-dropDownMode .dxm-popOut,
.dxmLite_ModernoNeosys .dxm-horizontal .dxm-image-t.dxm-noImages .dxm-dropDownMode .dxm-popOut,
.dxmLite_ModernoNeosys .dxm-horizontal .dxm-image-t .dxm-noImage .dxm-dropDownMode .dxm-popOut,
.dxmLite_ModernoNeosys .dxm-horizontal .dxm-image-b.dxm-noImages .dxm-dropDownMode .dxm-popOut,
.dxmLite_ModernoNeosys .dxm-horizontal .dxm-image-b .dxm-noImage .dxm-dropDownMode .dxm-popOut
{
	padding-top: 11px;
	padding-bottom: 12px;
}
.dxmLite_ModernoNeosys .dxm-horizontal .dxm-image-t .dxm-popOut,
.dxmLite_ModernoNeosys .dxm-horizontal .dxm-image-b .dxm-popOut
{
	padding-top: 26px;
	padding-bottom: 27px;
}
.dxmLite_ModernoNeosys.dxm-ltr .dxm-horizontal .dxm-image-l .dxm-popOut
{
	padding-right: 13px;
}
.dxmLite_ModernoNeosys.dxm-rtl .dxm-horizontal .dxm-image-r .dxm-popOut
{
	padding-left: 13px;
}
.dxmLite_ModernoNeosys.dxm-ltr .dxm-horizontal .dxm-image-r .dxm-popOut
{
	padding-left: 9px;
}
.dxmLite_ModernoNeosys.dxm-rtl .dxm-horizontal .dxm-image-l .dxm-popOut
{
	padding-right: 9px;
}
.dxmLite_ModernoNeosys.dxm-ltr .dxm-horizontal .dxm-image-t .dxm-popOut,
.dxmLite_ModernoNeosys.dxm-ltr .dxm-horizontal .dxm-image-b .dxm-popOut
{
	padding-right: 6px;
}
.dxmLite_ModernoNeosys.dxm-rtl .dxm-horizontal .dxm-image-t .dxm-popOut,
.dxmLite_ModernoNeosys.dxm-rtl .dxm-horizontal .dxm-image-b .dxm-popOut
{
	padding-left: 6px;
}
.dxmLite_ModernoNeosys .dxm-horizontal .dxm-image-l .dxm-dropDownMode .dxm-popOut,
.dxmLite_ModernoNeosys .dxm-horizontal .dxm-image-t .dxm-dropDownMode .dxm-popOut,
.dxmLite_ModernoNeosys .dxm-horizontal .dxm-image-b .dxm-dropDownMode .dxm-popOut
{
	padding-left: 12px;
	padding-right: 13px;
}
.dxmLite_ModernoNeosys .dxm-horizontal .dxm-image-r .dxm-dropDownMode .dxm-popOut
{
	padding-left: 13px;
	padding-right: 12px;
}

.dxmLite_ModernoNeosys .dxm-vertical .dxm-image-l .dxm-popOut,
.dxmLite_ModernoNeosys .dxm-vertical .dxm-image-r .dxm-popOut
{
	padding-top: 10px;
	padding-bottom: 10px;
}
.dxmLite_ModernoNeosys .dxm-vertical .dxm-image-t.dxm-noImages .dxm-popOut,
.dxmLite_ModernoNeosys .dxm-vertical .dxm-image-t .dxm-noImage .dxm-popOut,
.dxmLite_ModernoNeosys .dxm-vertical .dxm-image-b.dxm-noImages .dxm-popOut,
.dxmLite_ModernoNeosys .dxm-vertical .dxm-image-b .dxm-noImage .dxm-popOut
{
	padding-top: 7px;
	padding-bottom: 6px;
}
.dxmLite_ModernoNeosys .dxm-vertical .dxm-image-t .dxm-popOut,
.dxmLite_ModernoNeosys .dxm-vertical .dxm-image-b .dxm-popOut
{
	padding-top: 25px;
	padding-bottom: 25px;
}
.dxmLite_ModernoNeosys.dxm-ltr .dxm-vertical .dxm-popOut
{
	padding-right: 12px;
}
.dxmLite_ModernoNeosys.dxm-rtl .dxm-vertical .dxm-popOut
{
	padding-left: 6px;
}
.dxmLite_ModernoNeosys.dxm-ltr .dxm-vertical .dxm-dropDownMode .dxm-popOut
{
	padding-right: 8px;
	padding-left: 7px;
}
.dxmLite_ModernoNeosys.dxm-rtl .dxm-vertical .dxm-dropDownMode .dxm-popOut
{
	padding-left: 8px;
	padding-right: 7px;
}
.dxmLite_ModernoNeosys.dxm-ltr .dxm-vertical .dxm-image-r .dxm-popOut
{
	padding-left: 8px;
	padding-right: 4px;
}
.dxmLite_ModernoNeosys.dxm-rtl .dxm-vertical .dxm-image-l .dxm-popOut
{
	padding-right: 8px;
	padding-left: 4px;
}
.dxmLite_ModernoNeosys.dxm-ltr .dxm-vertical .dxm-image-r .dxm-dropDownMode .dxm-popOut
{
	padding-left: 6px;
	padding-right: 10px;
}
.dxmLite_ModernoNeosys.dxm-rtl .dxm-vertical .dxm-image-l .dxm-dropDownMode .dxm-popOut
{
	padding-left: 10px;
	padding-right: 6px;
}

.dxmLite_ModernoNeosys .dxm-popup .dxm-popOut
{
	padding-top: 9px;
	padding-bottom: 9px;
}
.dxmLite_ModernoNeosys.dxm-ltr .dxm-popup .dxm-popOut
{
	padding-right: 7px;
}
.dxmLite_ModernoNeosys.dxm-rtl .dxm-popup .dxm-popOut
{
	padding-left: 7px;
}
.dxmLite_ModernoNeosys.dxm-ltr .dxm-popup .dxm-dropDownMode .dxm-popOut
{
	padding-left: 6px;
	padding-right: 7px;
}
.dxmLite_ModernoNeosys.dxm-rtl .dxm-popup .dxm-dropDownMode .dxm-popOut
{
	padding-left: 7px;
	padding-right: 6px;
}

/* PopOut replacement */
.dxmLite_ModernoNeosys.dxm-ltr .dxm-popup .dxm-noSubMenu
{
	padding-right: 17px;
}
.dxmLite_ModernoNeosys.dxm-rtl .dxm-popup .dxm-noSubMenu
{
	padding-left: 17px;
}

/* Spacings, Separator */
.dxmLite_ModernoNeosys .dxm-horizontal .dxm-spacing
{
	width: 0px;
	height: 1px;
}
.dxmLite_ModernoNeosys .dxm-vertical .dxm-spacing,
.dxmLite_ModernoNeosys .dxm-popup .dxm-spacing
{
	height: 1px;
}
.dxmLite_ModernoNeosys.dxm-ie7 .dxm-vertical .dxm-spacing,
.dxmLite_ModernoNeosys.dxm-ie7 .dxm-popup .dxm-spacing
{
	margin-top: -3px;
}

.dxmLite_ModernoNeosys .dxm-horizontal .dxm-separator b
{
    height: 100%;
	width: 1px;
    *height: 32px;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
.dxmLite_ModernoNeosys .dxm-horizontal .dxm-image-t .dxm-separator,
.dxmLite_ModernoNeosys .dxm-horizontal .dxm-image-b .dxm-separator
{
	*height: 66px;
}
.dxmLite_ModernoNeosys .dxm-vertical .dxm-separator b,
.dxmLite_ModernoNeosys .dxm-popup .dxm-separator b
{
	height: 1px;
}
.dxmLite_ModernoNeosys .dxm-main .dxm-separator b
{
	background-color: #d1d1d1;
}
.dxmLite_ModernoNeosys .dxm-popup .dxm-separator b
{
	background-color: #cfcfcf;
}
.dxmLite_ModernoNeosys .dxm-horizontal .dxm-separator
{
	padding: 1px 2px;
}
.dxmLite_ModernoNeosys .dxm-vertical .dxm-separator
{
	padding: 2px 0;
}
.dxmLite_ModernoNeosys.dxm-ltr .dxm-popup .dxm-separator,
.dxmLite_ModernoNeosys.dxm-rtl .dxm-popup .dxm-separator
{
	padding-top: 1px;
	padding-bottom: 1px;
}
.dxmLite_ModernoNeosys.dxm-ltr .dxm-popup .dxm-separator
{
	padding-left: 36px;
}
.dxmLite_ModernoNeosys.dxm-rtl .dxm-popup .dxm-separator
{
	padding-right: 36px;
}
.dxmLite_ModernoNeosys.dxm-ie7 .dxm-vertical .dxm-separator
{
	padding-top: -3px;
}
.dxmLite_ModernoNeosys.dxm-ie7 .dxm-popup .dxm-separator
{
	padding-top: 0px;
}
.dxmLite_ModernoNeosys.dxm-ltr .dxm-popup .dxm-noImages .dxm-separator
{
	padding-left: 0;
}
.dxmLite_ModernoNeosys.dxm-rtl .dxm-popup .dxm-noImages .dxm-separator
{
	padding-right: 0;
}
/* Scroll elements */
.dxmLite_ModernoNeosys .dxm-scrollUpBtn, 
.dxmLite_ModernoNeosys .dxm-scrollDownBtn
{
	cursor: pointer;
	font-size: 0;
	padding: 1px;
	text-align: center;
}
.dxmLite_ModernoNeosys .dxm-scrollUpBtn
{
	margin-bottom: 1px;
	padding: 3px 0 4px;
}
.dxmLite_ModernoNeosys .dxm-scrollDownBtn
{
	margin-top: 1px;
	padding: 4px 0 3px;
}
.dxmLite_ModernoNeosys .dxm-scrollBtnHovered
{
	background: #009C49;
}
.dxmLite_ModernoNeosys .dxm-scrollBtnPressed
{
	background: #dcdcdc;
}
.dxmLite_ModernoNeosys .dxm-scrollBtnDisabled
{
	cursor: default;
}
.dxmLite_ModernoNeosys .dxm-scrollArea
{
	overflow: hidden;
	position: relative;
}

/* -- ASPxMenu Lite Toolbar mode -- */
.dxmLite_ModernoNeosys .dxm-main.dxmtb .dxm-item,
.dxmLite_ModernoNeosys .dxm-main.dxmtb .dxm-item a.dx
{
    font-size: 0.8em;
}
.dxmLite_ModernoNeosys .dxm-main.dxmtb .dxm-item.dxm-tmpl
{
    font-size: 1em;
}
.dxmLite_ModernoNeosys .dxm-main.dxmtb
{
	padding: 4px 2px;
}
.dxmLite_ModernoNeosys.dxm-ltr .dxm-horizontal .dxm-image-l.dxm-noImages .dxm-item, 
.dxmLite_ModernoNeosys.dxm-ltr .dxm-horizontal .dxm-image-l .dxm-noImage, 
.dxmLite_ModernoNeosys.dxm-ltr .dxm-horizontal .dxm-image-r.dxm-noImages .dxm-item, 
.dxmLite_ModernoNeosys.dxm-ltr .dxm-horizontal .dxm-image-r .dxm-noImage,
.dxmLite_ModernoNeosys.dxm-ltr .dxm-horizontal .dxm-image-t.dxm-noImages .dxm-item, 
.dxmLite_ModernoNeosys.dxm-ltr .dxm-horizontal .dxm-image-t .dxm-noImage, 
.dxmLite_ModernoNeosys.dxm-ltr .dxm-horizontal .dxm-image-b.dxm-noImages .dxm-item, 
.dxmLite_ModernoNeosys.dxm-ltr .dxm-horizontal .dxm-image-b .dxm-noImage
{
	padding-left: 0px;
}
/* has image */
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-l .dxm-content,
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-r .dxm-content,
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-l .dxm-subMenu .dxm-content,
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-r .dxm-subMenu .dxm-content,
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-t .dxm-content,
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-b .dxm-content,
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-t .dxm-subMenu .dxm-content,
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-b .dxm-subMenu .dxm-content
{
	padding: 4px;
}
/* no image */
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-l .dxm-subMenu.dxm-noImage .dxm-content,
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-r .dxm-subMenu.dxm-noImage .dxm-content,
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-l.dxm-noImages .dxm-item .dxm-content,
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-r.dxm-noImages .dxm-item .dxm-content,
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-l .dxm-noImage .dxm-content,
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-r .dxm-noImage .dxm-content,
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-t .dxm-subMenu.dxm-noImage .dxm-content,
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-b .dxm-subMenu.dxm-noImage .dxm-content,
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-t.dxm-noImages .dxm-item .dxm-content,
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-b.dxm-noImages .dxm-item .dxm-content,
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-t .dxm-noImage .dxm-content,
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-b .dxm-noImage .dxm-content
{
	padding: 4px;
}
/* dd has image */
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-l .dxm-dropDownMode .dxm-content,
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-r .dxm-dropDownMode .dxm-content,
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-t .dxm-dropDownMode .dxm-content,
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-b .dxm-dropDownMode .dxm-content
{
	padding: 4px;
}
/* dd no image */
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-l .dxm-dropDownMode.dxm-noImage .dxm-content,
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-r .dxm-dropDownMode.dxm-noImage .dxm-content,
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-t .dxm-dropDownMode.dxm-noImage .dxm-content,
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-b .dxm-dropDownMode.dxm-noImage .dxm-content
{
	padding: 4px;
}

.dxmLite_ModernoNeosys.dxm-ltr .dxmtb.dxm-popup .dxm-image
{
	margin-right: 10px;
}
.dxmLite_ModernoNeosys.dxm-rtl .dxmtb.dxm-popup .dxm-image
{
	margin-left: 10px;
}
.dxmLite_ModernoNeosys.dxm-ltr .dxmtb.dxm-popup .dxm-noImage
{
	padding-left: 26px;
}
.dxmLite_ModernoNeosys.dxm-rtl .dxmtb.dxm-popup .dxm-noImage
{
	padding-right: 26px;
}
.dxmLite_ModernoNeosys.dxm-ltr .dxmtb.dxm-horizontal .dxm-image-l .dxm-dropDownMode.dxm-noImage,
.dxmLite_ModernoNeosys.dxm-rtl .dxmtb.dxm-horizontal .dxm-image-r .dxm-dropDownMode.dxm-noImage,
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-t .dxm-dropDownMode.dxm-noImage,
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-b .dxm-dropDownMode.dxm-noImage
{
	padding-left: 0;
}
.dxmLite_ModernoNeosys.dxm-rtl .dxmtb.dxm-horizontal .dxm-image-l .dxm-dropDownMode.dxm-noImage
.dxmLite_ModernoNeosys.dxm-ltr .dxmtb.dxm-horizontal .dxm-image-r .dxm-dropDownMode.dxm-noImage
{
	padding-right: 0;
}
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-l .dxm-dropDownMode .dxm-popOut,
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-r .dxm-dropDownMode .dxm-popOut,
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-l .dxm-popOut,
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-r .dxm-popOut,
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-t .dxm-dropDownMode .dxm-popOut,
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-b .dxm-dropDownMode .dxm-popOut,
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-t .dxm-popOut,
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-b .dxm-popOut
{
	padding: 9px 4px;
}
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-item
{
    border-radius: 2px;
    border: 1px solid transparent;
}
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-hovered
{
	border: 1px solid #9FBD92;
	background: #1d85cd;
	background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pg0KPHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIHZpZXdCb3g9IjAgMCAxIDEiIHByZXNlcnZlQXNwZWN0UmF0aW89Im5vbmUiPg0KICA8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWQtdWNnZy1nZW5lcmF0ZWQiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIiB4MT0iMCUiIHkxPSIwJSIgeDI9IjAlIiB5Mj0iMTAwJSI+DQogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iIzFEODVDRCIgc3RvcC1vcGFjaXR5PSIxIi8+DQogICAgPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjMDg2Q0IzIiBzdG9wLW9wYWNpdHk9IjEiLz4NCiAgPC9saW5lYXJHcmFkaWVudD4NCiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4NCjwvc3ZnPg0K);
	background: -ms-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: -moz-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: -o-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: -webkit-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: linear-gradient(to bottom, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	color: white;

	box-shadow: inset 0px 1px 0px 0px rgba(255,255,255,0.35);
	-webkit-box-shadow: inset 0px 1px 0px 0px rgba(255,255,255,0.35);
}
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-checked,
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-selected
{
	border: 1px solid #cccccc;
	background: #dcdcdc;
	color: #a7a7a7;

	box-shadow: inset 0px 1px 1px 0px rgba(0,0,0,0.05);
	-webkit-box-shadow: inset 0px 1px 1px 0px rgba(0,0,0,0.05);
}

.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-dropDownMode.dxm-hovered .dxm-popOut
{
	border-left-color: #9FBD92;
}
.dxmLite_ModernoNeosys.dxm-rtl .dxmtb.dxm-horizontal .dxm-dropDownMode.dxm-hovered .dxm-popOut,
.dxmLite_ModernoNeosys.dxm-ltr .dxmtb.dxm-horizontal .dxm-image-r .dxm-dropDownMode.dxm-hovered .dxm-popOut
{
	border-right-color: #9FBD92;
}
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-dropDownMode.dxm-selected .dxm-popOut,
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-dropDownMode.dxm-checked .dxm-popOut
{
	border-left-color: #cccccc;
}
.dxmLite_ModernoNeosys.dxm-ltr .dxmtb.dxm-horizontal .dxm-image-r .dxm-dropDownMode.dxm-selected .dxm-popOut,
.dxmLite_ModernoNeosys.dxm-ltr .dxmtb.dxm-horizontal .dxm-image-r .dxm-dropDownMode.dxm-checked .dxm-popOut,
.dxmLite_ModernoNeosys.dxm-rtl .dxmtb.dxm-horizontal .dxm-dropDownMode.dxm-selected .dxm-popOut,
.dxmLite_ModernoNeosys.dxm-rtl .dxmtb.dxm-horizontal .dxm-dropDownMode.dxm-checked .dxm-popOut
{
	border-right-color: #cccccc;
}
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-separator
{
	padding: 1px 4px;
}
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-separator b
{
	*height: 24px;
}
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-t .dxm-separator b,
.dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-image-b .dxm-separator b
{
	*height: 40px;
}
.dxmLite_ModernoNeosys .dxm-horizontal.dxmtb .dxtb-comboBoxMenuItem 
{
    margin: -1px 0;
    padding: 0 2px;
}
.dxmLite_ModernoNeosys .dxm-horizontal.dxmtb .dxtb-labelMenuItem 
{
	padding: 7px 6px 0!important;
}
.dxmLite_ModernoNeosys .dxm-horizontal.dxmtb .dxtb-comboBoxMenuItem .dxeTextBox_ModernoNeosys td.dxic
{
	padding: 1px 2px 1px 7px!important;
}
.dxmLite_ModernoNeosys .dxm-horizontal.dxmtb .dxtb-comboBoxMenuItem .dxeButtonEdit_ModernoNeosys td.dxic 
{
	padding: 1px 2px 1px 7px!important;
}
.dxmLite_ModernoNeosys .dxm-horizontal.dxmtb .dxtb-comboBoxMenuItem .dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeTextEditCTypeSys
{
    padding-top: 5px;
}
@media screen and (-webkit-min-device-pixel-ratio:0) {
    .dxmLite_ModernoNeosys .dxm-horizontal.dxmtb .dxtb-comboBoxMenuItem .dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeTextEditCTypeSys
    {
        padding-top: 6px;
    }
}
.dxmLite_ModernoNeosys .dxm-horizontal.dxmtb .dxtb-comboBoxMenuItem .dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeTextEditCTypeSys.dxeCaptionCellSafariSys
{
    padding-top: 5px;
}
noindex:-o-prefocus, body:first-of-type .dxmLite_ModernoNeosys .dxm-horizontal.dxmtb .dxtb-comboBoxMenuItem .dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeTextEditCTypeSys
{
    padding-top: 6px;
}
.dxmLite_ModernoNeosys .dxm-horizontal.dxmtb .dxtb-comboBoxMenuItem .dxeButtonEditButton_ModernoNeosys 
{
    padding: 9px 6px 7px;
}

.dxmLite_ModernoNeosys .menuLinks_ModernoNeosys .dxm-item 
{
	color: #009C49;
}

.dxmLite_ModernoNeosys.dxm-ltr .menuLinks_ModernoNeosys.dxm-horizontal .dxm-image-l .dxm-content,
.dxmLite_ModernoNeosys.dxm-ltr .menuLinks_ModernoNeosys.dxm-horizontal .dxm-image-r .dxm-content
{
    padding-left: 2px;
    padding-right: 2px;
}

.dxmLite_ModernoNeosys .menuLinks_ModernoNeosys .dxm-hasText 
{
	text-decoration: underline;
}

.dxmLite_ModernoNeosys .menuLinks_ModernoNeosys .dxm-hovered,
.dxmLite_ModernoNeosys .menuLinks_ModernoNeosys .dxm-hovered a.dx,
.dxmLite_ModernoNeosys .dxm-horizontal.menuLinks_ModernoNeosys .dxm-hovered,
.dxmLite_ModernoNeosys .dxm-vertical.menuLinks_ModernoNeosys .dxm-hovered
{
    background: none;
	color: #F39128;
}
.dxmLite_ModernoNeosys .menuLinks_ModernoNeosys .dxm-disabled,
.dxmLite_ModernoNeosys .menuLinks_ModernoNeosys .dxm-disabled a.dx,
.dxmLite_ModernoNeosys .dxm-horizontal.menuLinks_ModernoNeosys .dxm-disabled,
.dxmLite_ModernoNeosys .dxm-vertical.menuLinks_ModernoNeosys .dxm-disabled
{
	color: #A6A6A6 !important;
	cursor: default;
}
/*--------------------------------------------------------------------------------------*/
.dxmLite_ModernoNeosys .menuButtons_ModernoNeosys .dxm-item
{
	color: #2B2B2B;
	vertical-align: middle;
	border: 1px solid #c3c3c3;
	border-radius: 4px;
	padding: 3px 0px 1px 0px;
	cursor: pointer;
	background: #eaeaea;
	background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pg0KPHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIHZpZXdCb3g9IjAgMCAxIDEiIHByZXNlcnZlQXNwZWN0UmF0aW89Im5vbmUiPg0KICA8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWQtdWNnZy1nZW5lcmF0ZWQiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIiB4MT0iMCUiIHkxPSIwJSIgeDI9IjAlIiB5Mj0iMTAwJSI+DQogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iI0VBRUFFQSIgc3RvcC1vcGFjaXR5PSIxIi8+DQogICAgPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjREZERkRGIiBzdG9wLW9wYWNpdHk9IjEiLz4NCiAgPC9saW5lYXJHcmFkaWVudD4NCiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4NCjwvc3ZnPg0K);
	background: -ms-linear-gradient(top, rgba(234,234,234,1) 0%, rgba(223,223,223,1) 100%);
	background: -moz-linear-gradient(top, rgba(234,234,234,1) 0%, rgba(223,223,223,1) 100%);
	background: -o-linear-gradient(top, rgba(234,234,234,1) 0%, rgba(223,223,223,1) 100%);
	background: -webkit-linear-gradient(top, rgba(234,234,234,1) 0%, rgba(223,223,223,1) 100%);
	background: linear-gradient(to bottom, rgba(234,234,234,1) 0%, rgba(223,223,223,1) 100%);
	-webkit-border-radius: 4px;
	box-shadow: inset 0px 1px 0px 0px rgba(255,255,255,0.35), 0px 1px 3px 0px rgba(0,0,0,0.1);
	-webkit-box-shadow: inset 0px 1px 0px 0px rgba(255,255,255,0.35), 0px 1px 3px 0px rgba(0,0,0,0.1);
}

.dxmLite_ModernoNeosys .menuButtons_ModernoNeosys .dxm-hovered,
.dxmLite_ModernoNeosys .menuButtons_ModernoNeosys .dxm-hovered a.dx,
.dxmLite_ModernoNeosys .dxm-horizontal.menuButtons_ModernoNeosys .dxm-hovered,
.dxmLite_ModernoNeosys .dxm-vertical.menuButtons_ModernoNeosys .dxm-hovered
{
	border: 1px Solid #9FBD92;
	color: white !important;
	background: #1d85cd;
	background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pg0KPHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIHZpZXdCb3g9IjAgMCAxIDEiIHByZXNlcnZlQXNwZWN0UmF0aW89Im5vbmUiPg0KICA8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWQtdWNnZy1nZW5lcmF0ZWQiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIiB4MT0iMCUiIHkxPSIwJSIgeDI9IjAlIiB5Mj0iMTAwJSI+DQogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iIzFEODVDRCIgc3RvcC1vcGFjaXR5PSIxIi8+DQogICAgPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjMDg2Q0IzIiBzdG9wLW9wYWNpdHk9IjEiLz4NCiAgPC9saW5lYXJHcmFkaWVudD4NCiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4NCjwvc3ZnPg0K);
	background: -ms-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: -moz-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: -o-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: -webkit-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: linear-gradient(to bottom, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
}

.dxmLite_ModernoNeosys .menuButtons_ModernoNeosys .dxm-disabled,
.dxmLite_ModernoNeosys .menuButtons_ModernoNeosys .dxm-disabled a.dx,
.dxmLite_ModernoNeosys .dxm-horizontal.menuButtons_ModernoNeosys .dxm-disabled,
.dxmLite_ModernoNeosys .dxm-vertical.menuButtons_ModernoNeosys .dxm-disabled
{
	background: #e7e7e7;
	color: #C3C3C3;
	border-color: #d3d3d3;
	cursor: default;
}

.dxmLite_ModernoNeosys.dxm-ltr .menuButtons_ModernoNeosys.dxm-horizontal .dxm-image-l .dxm-content,
.dxmLite_ModernoNeosys.dxm-ltr .menuButtons_ModernoNeosys.dxm-horizontal .dxm-image-r .dxm-content
{
	padding: 6px 30px 7px 30px;
}

/* -- ASPxNavBar Lite -- */
.dxnbLite_ModernoNeosys 
{
	background-color: white;
	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
	color: #2B2B2B;
	list-style: none none outside;
	margin: 0;
	padding: 0;
	float: left;
	width: 200px;
}
.dxnbLite_ModernoNeosys a
{
	color: #2b2b2b;
	text-decoration: none!important;
}
.dxnbLite_ModernoNeosys .dxnb-gr 
{
	margin-bottom: 1px;
}
.dxnbLite_ModernoNeosys .dxnb-header,
.dxnbLite_ModernoNeosys .dxnb-headerCollapsed
{
    border-top: 1px solid #d1d1d1;
	overflow: hidden;
	padding: 8px 4px;
	cursor: pointer;
	clear: both;
	background-color: #fafafa;
	color: #7e7e7e;
    white-space: nowrap;
}
.dxnbLite_ModernoNeosys .dxnb-last .dxnb-headerCollapsed
{
	border-bottom: 1px solid #d1d1d1;
}
.dxnbLite_ModernoNeosys .dxnb-header a,
.dxnbLite_ModernoNeosys .dxnb-headerCollapsed a 
{
	color: #009C49;
	text-decoration: underline!important;
}
.dxnbLite_ModernoNeosys .dxnb-header a:hover,
.dxnbLite_ModernoNeosys .dxnb-headerCollapsed a:hover 
{
	color: #2b2b2b;
}
.dxnbLite_ModernoNeosys .dxnb-content
{
	list-style: none none outside;
	margin: 0;
	padding: 0;
	overflow: hidden;

	border-top: 1px solid #d1d1d1;
}
.dxnbLite_ModernoNeosys .dxnb-last .dxnb-content
{
    border-bottom: 1px solid #d1d1d1;
}
.dxnbLite_ModernoNeosys .dxnb-item,
.dxnbLite_ModernoNeosys .dxnb-large,
.dxnbLite_ModernoNeosys .dxnb-bullet
{
	clear: both;
	overflow: hidden;
	cursor: default;
	background-color: White;
	border-bottom: 1px solid #e5e5e5;
}
.dxnbLite_ModernoNeosys .dxnb-item.dxnb-last,
.dxnbLite_ModernoNeosys .dxnb-large.dxnb-last,
.dxnbLite_ModernoNeosys .dxnb-bullet.dxnb-last
{
	border-bottom: 0;
}
.dxnbLite_ModernoNeosys .dxnb-item.dxnb-link,
.dxnbLite_ModernoNeosys .dxnb-item .dxnb-link,
.dxnbLite_ModernoNeosys .dxnb-large.dxnb-link,
.dxnbLite_ModernoNeosys .dxnb-large .dxnb-link
{
	padding: 13px 15px;
    white-space: nowrap;
}
.dxnbLite_ModernoNeosys .dxnb-bullet,
.dxnbLite_ModernoNeosys .dxnb-bulletHover,
.dxnbLite_ModernoNeosys .dxnb-bulletSelected 
{
	padding: 4px 8px;
	overflow: visible;
	margin-bottom: 1px;
}
.dxnbLite_ModernoNeosys .dxnb-itemSelected,
.dxnbLite_ModernoNeosys .dxnb-largeSelected
{
	background-color: #f3f3f3;
}
.dxnbLite_ModernoNeosys .dxnb-itemHover,
.dxnbLite_ModernoNeosys .dxnb-itemHover a,
.dxnbLite_ModernoNeosys .dxnb-largeHover,
.dxnbLite_ModernoNeosys .dxnb-largeHover a
{
	background-color: #009C49;
	color: white;
}
.dxnbLite_ModernoNeosys .dxnb-header,
.dxnbLite_ModernoNeosys .dxnb-headerCollapsed, 
.dxnbLite_ModernoNeosys .dxnb-item,
.dxnbLite_ModernoNeosys .dxnb-itemHover,
.dxnbLite_ModernoNeosys .dxnb-itemSelected,
.dxnbLite_ModernoNeosys .dxnb-bullet,
.dxnbLite_ModernoNeosys .dxnb-bulletHover,
.dxnbLite_ModernoNeosys .dxnb-bulletSelected
{
	text-align: left;
}
.dxnbLite_ModernoNeosys .dxnb-large,
.dxnbLite_ModernoNeosys .dxnb-largeHover,
.dxnbLite_ModernoNeosys .dxnb-largeSelected
{
	text-align: center;
}
.dxnbLite_ModernoNeosys .dxnb-headerHover
{
}
.dxnbLite_ModernoNeosys .dxnb-headerCollapsedHover
{
}
.dxnbLite_ModernoNeosys .dxnb-last
{
	margin-bottom: 1px;
}
.dxnbLite_ModernoNeosys .dxnb-btn,
.dxnbLite_ModernoNeosys .dxnb-btnLeft,
.dxnbLite_ModernoNeosys .dxnb-img
{
	border-width: 0;
}
.dxnbLite_ModernoNeosys .dxnb-btnLeft
{
	float: left;
	margin: 0 12px;
}
.dxnbLite_ModernoNeosys .dxnb-btn,
.dxnbLite_ModernoNeosys .dxnb-rtlHeader .dxnb-btn 
{
	float: right;
	margin: 0 12px;
}
.dxnbLite_ModernoNeosys .dxnb-img
{
	margin: 0 10px 0 0;
}
.dxnbLite_ModernoNeosys .dxnb-right .dxnb-item .dxnb-img,
.dxnbLite_ModernoNeosys .dxnb-rtlHeader .dxnb-img
{
	margin: 0 0 0 10px;
}
.dxnbLite_ModernoNeosys .dxnb-header.dxnb-header-left,
.dxnbLite_ModernoNeosys .dxnb-headerCollapsed.dxnb-header-left,
.dxnbLite_ModernoNeosys .dxnb-rtlHeader
{
	text-align: right;
}
.dxnbLite_ModernoNeosys .dxnb-top .dxnb-large .dxnb-img
{
	margin-bottom: 10px;
}
.dxnbLite_ModernoNeosys .dxnb-bottom .dxnb-large .dxnb-img
{
	margin-top: 10px;
}
.dxnbLite_ModernoNeosys .dxnb-large .dxnb-img
{
	display: block;
	margin-left: auto;
	margin-right: auto;
}
.dxnbLiteDisabled_ModernoNeosys,
.dxnbLite_ModernoNeosys .dxnbLiteDisabled_ModernoNeosys,
.dxnbLiteDisabled_ModernoNeosys a,
.dxnbLiteDisabled_ModernoNeosys .dxnb-item,
.dxnbLiteDisabled_ModernoNeosys .dxnb-large,
.dxnbLiteDisabled_ModernoNeosys .dxnb-bullet,
.dxnbLiteDisabled_ModernoNeosys .dxnb-header,
.dxnbLiteDisabled_ModernoNeosys .dxnb-headerCollapsed 
{
	color: #A6A6A6;
	cursor: default;
}

/* -- ASPxNewsControl -- */
.dxncControl_ModernoNeosys 
{
	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
	color: #2B2B2B;
	background-color: White;
}
.dxncControl_ModernoNeosys td.dxncCtrl
{
	padding: 18px;
}
.dxncControl_ModernoNeosys a
{
	color: #009C49;
}
.dxncControl_ModernoNeosys a:hover
{
	color: #2B2B2B;
}
.dxncContent_ModernoNeosys
{
	padding: 20px;
}
.dxncPagerPanel_ModernoNeosys
{
	padding: 9px 10px 7px;
}
.dxncItem_ModernoNeosys
{
	padding: 12px 8px;
}
.dxncEmptyItem_ModernoNeosys
{
	padding: 12px 12px 12px 14px;
}
.dxncEPContainer_ModernoNeosys
{
    height: 58px;
	text-align: center;
}
.dxncEPContainer_ModernoNeosys  div
{
	padding-top: 20px;
}
.dxncEPContainer_ModernoNeosys a
{
	color: #009C49;
}
.dxncEPContainer_ModernoNeosys  a:hover
{
	color: #2b2b2b;
}
.dxncEmptyData_ModernoNeosys
{
	color: #4F4F4F;
}
/* BackToTop */
.dxncBackToTop_ModernoNeosys
{
	font-size: 0.86em;
	text-decoration: none;
	padding: 4px 0px;
}
/* Disabled */
.dxncDisabled_ModernoNeosys,
.dxncDisabled_ModernoNeosys span.dxhl,
.dxncDisabled_ModernoNeosys a,
.dxncDisabled_ModernoNeosys a:hover
{
	color: #A6A6A6;
	cursor: default;
}

/* -- ASPxPager Lite -- */
.dxpLite_ModernoNeosys
{
	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
	color: #2B2B2B;
	padding: 6px 2px;
	float: left;

    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
}

.dxpLite_ModernoNeosys .dxp-summary,
.dxpLite_ModernoNeosys .dxp-sep,
.dxpLite_ModernoNeosys .dxp-button,
.dxpLite_ModernoNeosys .dxp-pageSizeItem,
.dxpLite_ModernoNeosys .dxp-num,
.dxpLite_ModernoNeosys .dxp-current,
.dxpLite_ModernoNeosys .dxp-ellip
{
	margin-left: 4px;
	font-weight: normal;
}
.dxpLite_ModernoNeosys .dxp-lead
{
	margin-left: 0!important;
}

.dxpLite_ModernoNeosys .dxp-button
{
	color: #009C49;
	cursor: pointer;
	padding: 8px 14px;
	text-align: center;
	text-decoration: none;
	white-space: nowrap;

	background: #E9E9E9;
	border: 1px solid LightGrey;
	border-radius: 2px;
	-webkit-border-radius: 2px;

	box-shadow: 0px 1px 3px 0px rgba(0,0,0,0.1), inset 0px 1px 0px 0px rgba(255, 255, 255, 0.35);
	-webkit-box-shadow: 0px 1px 3px 0px rgba(0,0,0,0.1), inset 0px 1px 0px 0px rgba(255, 255, 255, 0.35);
}
.dxpLite_ModernoNeosys .dxp-button img
{
	border: none;
	text-decoration: none;
}
.dxpLite_ModernoNeosys .dxp-button span
{
    margin-top: -1px;
}
@media screen and (-webkit-min-device-pixel-ratio:0) {
    .dxpLite_ModernoNeosys .dxp-button span
    {
        margin-top: 0;
    }
}
doesnotexist:-o-prefocus, .dxpLite_ModernoNeosys .dxp-button span
{
    margin-top: 0;
}
.dxpLite_ModernoNeosys .dxp-button.dxp-bti 
{
	padding: 6px 14px;
}
.dxpLite_ModernoNeosys .dxp-disabledButton
{
	text-decoration: none;
	color: #acacac;
	cursor: default;
}

.dxpLite_ModernoNeosys .dxp-pageSizeItem
{
    padding: 1px 4px 0px 32px;
    white-space: nowrap;
    color: #979797;
}
@media screen and (-webkit-min-device-pixel-ratio:0) {
    .dxpLite_ModernoNeosys .dxp-pageSizeItem
    {
        padding: 0 4px 0px 32px;
    }
    ::i-block-chrome,
    .dxpLite_ModernoNeosys .dxp-pageSizeItem
    {
        padding: 1px 4px 0px 32px;
    }
}

.dxpLite_ModernoNeosys .dxp-pageSizeItem .dx
{
    display: block;
    float: left;
    margin-top: 6px;
    white-space: nowrap;
}
.dxpLite_ModernoNeosys .dxp-comboBox
{
    background-color: White;
    display: block;
    float: left;
    border: 1px solid #d1d1d1;
    margin: -1px 0px -2px;
	height: 30px;
}
.dxpLite_ModernoNeosys .dxp-comboBox input
{
	color: #2B2B2B;
    margin: 6px 4px 0px;
    width: 32px;
}
.dxpLite_ModernoNeosys .dxp-hoverComboBox
{
}
.dxpLite_ModernoNeosys .dxp-pressedComboBox
{
}
.dxpLite_ModernoNeosys .dxp-dropDownButton
{
    font-size: 0;
    display: block;
    float: left;
    cursor: pointer;
    border: 1px Solid Transparent;
    padding: 12px 10px 11px;
}
.dxpLite_ModernoNeosys .dxp-hoverDropDownButton
{
	border: 1px Solid #9FBD92;
	background: #1d85cd;
	background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pg0KPHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIHZpZXdCb3g9IjAgMCAxIDEiIHByZXNlcnZlQXNwZWN0UmF0aW89Im5vbmUiPg0KICA8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWQtdWNnZy1nZW5lcmF0ZWQiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIiB4MT0iMCUiIHkxPSIwJSIgeDI9IjAlIiB5Mj0iMTAwJSI+DQogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iIzFEODVDRCIgc3RvcC1vcGFjaXR5PSIxIi8+DQogICAgPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjMDg2Q0IzIiBzdG9wLW9wYWNpdHk9IjEiLz4NCiAgPC9saW5lYXJHcmFkaWVudD4NCiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4NCjwvc3ZnPg0K);
	background: -ms-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: -moz-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: -o-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: -webkit-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: linear-gradient(to bottom, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);

	box-shadow: inset 0px 1px 0px 0px rgba(255,255,255,0.35);
	-webkit-box-shadow: inset 0px 1px 0px 0px rgba(255,255,255,0.35);
}
.dxpLite_ModernoNeosys .dxp-pressedDropDownButton
{
	background: #dcdcdc;
	border: 1px Solid #d1d1d1;

	box-shadow: inset 0px 1px 1px 0px rgba(0,0,0,0.05);
	-webkit-box-shadow: inset 0px 1px 1px 0px rgba(0,0,0,0.05);
}
.dxpLite_ModernoNeosys .dxp-dropDownButton img
{
    border: none;
	text-decoration: none;
	vertical-align: middle;
}

.dxpLite_ModernoNeosys .dxp-num
{
	color: #009C49;
	text-decoration: underline;
	padding: 6px 12px 5px;
    margin-top: 3px;
	cursor: pointer;
}

.dxpLite_ModernoNeosys .dxp-current
{
	color: #979797;
	background-color: #dcdcdc;
	text-decoration: none;
	cursor: text;
}

.dxpLite_ModernoNeosys .dxp-summary,
.dxpLite_ModernoNeosys .dxp-ellip
{
	color: #979797;
	white-space: nowrap;
	padding: 9px 4px 8px;
}
.dxpLite_ModernoNeosys .dxp-ellip
{
	color: #009C49;
}

.dxpLite_ModernoNeosys .dxp-summary
{
	margin: 0 32px;
}

.dxpLite_ModernoNeosys .dxp-sep
{
	background-color: #d1d1d1;
	width: 1px;
	height: 30px;
}

.dxpLiteDisabled_ModernoNeosys,
.dxpLiteDisabled_ModernoNeosys a,
.dxpLiteDisabled_ModernoNeosys .dxp-summary,
.dxpLiteDisabled_ModernoNeosys .dxp-sep,
.dxpLiteDisabled_ModernoNeosys .dxp-button,
.dxpLiteDisabled_ModernoNeosys .dxp-num,
.dxpLiteDisabled_ModernoNeosys .dxp-current,
.dxpLiteDisabled_ModernoNeosys .dxp-ellip
{
	color: #A6A6A6;
	cursor: default;
}
.dxpLite_ModernoNeosys .dxp-disabledComboBox
{
}
.dxpLite_ModernoNeosys .dxp-disabledComboBox input
{
    color: #acacac;
}
.dxpLite_ModernoNeosys .dxp-disabledDropDownButton
{
    cursor: default;
}

/* -- ASPxPopupControl Lite -- */
.dxpcLite_ModernoNeosys,
.dxdpLite_ModernoNeosys 
{
	width: 200px;
	left: 0;
	top: 0;
	display: none;
	position: absolute;
	visibility: hidden;
	border-spacing: 0;

	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
	color: #2B2B2B;
}

.dxpcLite_ModernoNeosys .dxpc-shadow,
.dxpcLite_ModernoNeosys.dxpc-shadow,
.dxdpLite_ModernoNeosys .dxpc-shadow,
.dxdpLite_ModernoNeosys.dxpc-shadow
{
	-moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.dxpcLite_ModernoNeosys .dxpc-mainDiv,
.dxpcLite_ModernoNeosys.dxpc-mainDiv,
.dxdpLite_ModernoNeosys .dxpc-mainDiv,
.dxdpLite_ModernoNeosys.dxpc-mainDiv 
{
	top: 0;
	left: 0;
	background-color: white;
	border: 1px solid #cfcfcf;
}

.dxpcLite_ModernoNeosys a.dxpc-link,
.dxdpLite_ModernoNeosys a.dxpc-link 
{
	color: #009C49;
}
.dxpcLite_ModernoNeosys a.dxpc-link:hover,
.dxdpLite_ModernoNeosys a.dxpc-link:hover 
{
	color: #2b2b2b;
}
.dxpcLite_ModernoNeosys a.dxpc-link *,
.dxdpLite_ModernoNeosys a.dxpc-link * 
{
	text-decoration: underline;
	cursor: pointer;
}

.dxpcLite_ModernoNeosys .dxpc-closeBtn,
.dxdpLite_ModernoNeosys .dxpc-closeBtn,
.dxpcLite_ModernoNeosys .dxpc-pinBtn,
.dxdpLite_ModernoNeosys .dxpc-pinBtn,
.dxpcLite_ModernoNeosys .dxpc-refreshBtn,
.dxdpLite_ModernoNeosys .dxpc-refreshBtn,
.dxpcLite_ModernoNeosys .dxpc-collapseBtn,
.dxdpLite_ModernoNeosys .dxpc-collapseBtn,
.dxpcLite_ModernoNeosys .dxpc-maximizeBtn,
.dxdpLite_ModernoNeosys .dxpc-maximizeBtn
{
	border: 1px solid Transparent;
	padding: 2px;
	float: right;
}

.dxpcLite_ModernoNeosys.dxRtl  .dxpc-closeBtn,
.dxdpLite_ModernoNeosys.dxRtl  .dxpc-closeBtn,
.dxpcLite_ModernoNeosys.dxRtl  .dxpc-pinBtn,
.dxdpLite_ModernoNeosys.dxRtl  .dxpc-pinBtn,
.dxpcLite_ModernoNeosys.dxRtl  .dxpc-refreshBtn,
.dxdpLite_ModernoNeosys.dxRtl  .dxpc-refreshBtn,
.dxpcLite_ModernoNeosys.dxRtl  .dxpc-collapseBtn,
.dxdpLite_ModernoNeosys.dxRtl  .dxpc-collapseBtn,
.dxpcLite_ModernoNeosys.dxRtl  .dxpc-maximizeBtn,
.dxdpLite_ModernoNeosys.dxRtl  .dxpc-maximizeBtn
{
	float: left;
}

.dxpcLite_ModernoNeosys .dxpc-closeBtnHover,
.dxdpLite_ModernoNeosys .dxpc-closeBtnHover,
.dxpcLite_ModernoNeosys .dxpc-pinBtnHover,
.dxdpLite_ModernoNeosys .dxpc-pinBtnHover,
.dxpcLite_ModernoNeosys .dxpc-refreshBtnHover,
.dxdpLite_ModernoNeosys .dxpc-refreshBtnHover,
.dxpcLite_ModernoNeosys .dxpc-collapseBtnHover,
.dxdpLite_ModernoNeosys .dxpc-collapseBtnHover,
.dxpcLite_ModernoNeosys .dxpc-maximizeBtnHover,
.dxdpLite_ModernoNeosys .dxpc-maximizeBtnHover
{
	border: 1px solid #9FBD92;
	background: #1d85cd;
	background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pg0KPHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIHZpZXdCb3g9IjAgMCAxIDEiIHByZXNlcnZlQXNwZWN0UmF0aW89Im5vbmUiPg0KICA8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWQtdWNnZy1nZW5lcmF0ZWQiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIiB4MT0iMCUiIHkxPSIwJSIgeDI9IjAlIiB5Mj0iMTAwJSI+DQogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iIzFEODVDRCIgc3RvcC1vcGFjaXR5PSIxIi8+DQogICAgPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjMDg2Q0IzIiBzdG9wLW9wYWNpdHk9IjEiLz4NCiAgPC9saW5lYXJHcmFkaWVudD4NCiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4NCjwvc3ZnPg0K);
	background: -ms-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: -moz-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: -o-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: -webkit-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: linear-gradient(to bottom, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	color: white;

	border-radius: 2px;
	-webkit-border-radius: 2px;

	box-shadow: inset 0px 1px 0px 0px rgba(255,255,255,0.35), 0px 1px 3px 0px rgba(0,0,0,0.1);
	-webkit-box-shadow: inset 0px 1px 0px 0px rgba(255,255,255,0.35), 0px 1px 3px 0px rgba(0,0,0,0.1);
}

.dxpcLite_ModernoNeosys .dxpc-header,
.dxdpLite_ModernoNeosys .dxpc-header
{
	border-bottom: 1px solid #e6e6e6;
	padding: 7px 6px 7px 12px;
	background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pg0KPHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIHZpZXdCb3g9IjAgMCAxIDEiIHByZXNlcnZlQXNwZWN0UmF0aW89Im5vbmUiPg0KICA8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWQtdWNnZy1nZW5lcmF0ZWQiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIiB4MT0iMCUiIHkxPSIwJSIgeDI9IjAlIiB5Mj0iMTAwJSI+DQogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iI0ZBRkFGQSIgc3RvcC1vcGFjaXR5PSIxIi8+DQogICAgPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjRURFREVEIiBzdG9wLW9wYWNpdHk9IjEiLz4NCiAgPC9saW5lYXJHcmFkaWVudD4NCiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4NCjwvc3ZnPg0K);
	background: -ms-linear-gradient(top, rgba(250,250,250,1) 0%, rgba(237,237,237,1) 100%);
	background: -moz-linear-gradient(top, rgba(250,250,250,1) 0%, rgba(237,237,237,1) 100%);
	background: -o-linear-gradient(top, rgba(250,250,250,1) 0%, rgba(237,237,237,1) 100%);
	background: -webkit-linear-gradient(top, rgba(250,250,250,1) 0%, rgba(237,237,237,1) 100%);
	background: linear-gradient(to bottom, rgba(250,250,250,1) 0%, rgba(237,237,237,1) 100%);
}

.dxpcLite_ModernoNeosys.dxRtl .dxpc-header,
.dxdpLite_ModernoNeosys.dxRtl .dxpc-header 
{
	padding-right: 12px;
	padding-left: 2px;
}

.dxpcLite_ModernoNeosys .dxpc-headerText,
.dxdpLite_ModernoNeosys .dxpc-headerText
{
	white-space: nowrap;
}

.dxpcLite_ModernoNeosys .dxpc-headerImg,
.dxdpLite_ModernoNeosys .dxpc-headerImg
{
	margin: 0 4px 0 0;
}

.dxpcLite_ModernoNeosys.dxRtl .dxpc-headerImg,
.dxdpLite_ModernoNeosys.dxRtl .dxpc-headerImg
{
	margin: 0 0 0 4px;
}

.dxpcLite_ModernoNeosys .dxpc-contentWrapper,
.dxdpLite_ModernoNeosys .dxpc-contentWrapper 
{
	background-color: #FFFFFF;
}

.dxpcLite_ModernoNeosys .dxpc-content,
.dxdpLite_ModernoNeosys .dxpc-content
{
	white-space: normal;
	line-height: 128%;
	padding: 14px 20px 15px;
	background-color: white;
}

.dxpcLite_ModernoNeosys .dxpc-footer,
.dxdpLite_ModernoNeosys .dxpc-footer
{
	background: #fafafa;
	border-top: 1px solid #ececec;
}

.dxpcLite_ModernoNeosys .dxpc-footerContent,
.dxdpLite_ModernoNeosys .dxpc-footerContent
{
	padding: 7px 24px 7px 12px;
	min-height: 12px;
}

.dxpcLite_ModernoNeosys.dxRtl .dxpc-footerContent,
.dxdpLite_ModernoNeosys.dxRtl .dxpc-footerContent
{
	padding: 7px 12px 7px 24px;
}

.dxpcLite_ModernoNeosys .dxpc-footerText,
.dxdpLite_ModernoNeosys .dxpc-footerText
{
	white-space: nowrap;
}

.dxpcLite_ModernoNeosys .dxpc-footerImg,
.dxdpLite_ModernoNeosys .dxpc-footerImg
{
	margin: 0 4px 0 0;
}

.dxpcLite_ModernoNeosys.dxRtl .dxpc-footerImg,
.dxdpLite_ModernoNeosys.dxRtl .dxpc-footerImg
{
	margin: 0 0 0 4px;
}

.dxpcLite_ModernoNeosys .dxpc-sizeGrip,
.dxdpLite_ModernoNeosys .dxpc-sizeGrip
{
	float: right;
}

.dxpcLite_ModernoNeosys.dxRtl .dxpc-sizeGrip,
.dxdpLite_ModernoNeosys.dxRtl .dxpc-sizeGrip
{
	float: left;
}

.dxpcModalBackLite_ModernoNeosys,
.dxdpModalBackLite_ModernoNeosys
{
	background-color: #777777;
	opacity: 0.7;
	filter: progid:DXImageTransform.Microsoft.Alpha(Style=0, Opacity=70);
	position: fixed;
	left: 0;
	top: 0;
	visibility: hidden;
}
.dxpcLiteDisabled_ModernoNeosys,
.dxdpLiteDisabled_ModernoNeosys
{
	color: #A6A6A6;
	cursor: default;
}

/* -- ASPxRoundPanel -- */
.dxrpControl_ModernoNeosys,
.dxrpControlGB_ModernoNeosys
{
	border: 1px solid #d9d9d9;
}
.dxrpControl_ModernoNeosys td.dxrp,
.dxrpControl_ModernoNeosys td.dxrpHeader_ModernoNeosys,
.dxrpControlGB_ModernoNeosys td.dxrp
{
	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
	color: #2b2b2b;
}
.dxrpControl_ModernoNeosys .dxrpCI
{
	display:block;
}
/*Corner Radius*/
.dxrpControlGB_ModernoNeosys:not(.dxrp-hasDefaultImages),
.dxrpControlGB_ModernoNeosys:not(.dxrp-hasDefaultImages) > tbody > tr.dxrpCR > .dxrpcontent,
.dxrpControl_ModernoNeosys:not(.dxrp-hasDefaultImages)
{
	border-radius: 4px;
}
.dxrpControl_ModernoNeosys:not(.dxrp-hasDefaultImages) .dxrpHeader_ModernoNeosys
{
	border-radius:4px 4px 0px 0px;
}
.dxrpControl_ModernoNeosys:not(.dxrp-hasDefaultImages) > tbody > tr.dxrpCR > .dxrpcontent
{
	border-radius:0px 0px 4px 4px;
}
.dxrpWithoutHeader_ModernoNeosys:not(.dxrp-hasDefaultImages) > tbody > tr.dxrpCR > .dxrpcontent
{
	border-radius:4px;
}
/* Header */
.dxrpHeader_ModernoNeosys,
.dxrpControl_ModernoNeosys td.dxrpHeader_ModernoNeosys,
.dxrpControlGB_ModernoNeosys span.dxrpHeader_ModernoNeosys
{
	color: #949494;
}
.dxrpControlGB_ModernoNeosys span.dxrpHeader_ModernoNeosys
{
	background-color: white;
}
.dxrpHeader_ModernoNeosys
{
    background-color: #fafafa;
}
.dxrpControl_ModernoNeosys  > tbody > tr > .dxrpHeader_ModernoNeosys > a,
.dxrpControl_ModernoNeosys  > tbody > tr > .dxrpHeader_ModernoNeosys > a > span.dxrpHT,
.dxrpControlGB_ModernoNeosys span.dxrpHeader_ModernoNeosys a
{
    color: #009C49;
}
.dxrpControl_ModernoNeosys td.dxrpHeader_ModernoNeosys
{
	vertical-align: top;
	white-space: nowrap;
}
.dxrpControl_ModernoNeosys td.dxrpHeader_ModernoNeosys
{
	border-bottom: 1px solid #d9d9d9;
}
/* Header image */
.dxrpControl_ModernoNeosys .dxrpHI
{
	margin-right: 4px;
}
.dxrpControl_ModernoNeosys .dxrpHIR
{
	margin-left: 4px;
}
/* Content */
.dxrpControl_ModernoNeosys > tbody > tr.dxrpCR > .dxrpcontent,
.dxrpControlGB_ModernoNeosys > tbody > tr.dxrpCR > .dxrpcontent
{
    background-image: none;
	vertical-align: top;
}
/* Paddings */
.dxrpControl_ModernoNeosys td.dxrpHeader_ModernoNeosys 
{
	padding: 5px 11px 6px 9px;
}
.dxrpControlGB_ModernoNeosys span.dxrpHeader_ModernoNeosys 
{
	padding: 0px 6px 0px 6px;
}
.dxrpControl_ModernoNeosys.dxrp-noCollapsing > tbody > tr.dxrpCR > .dxrpcontent,
.dxrpControl_ModernoNeosys > tbody > tr.dxrpCR > .dxrpcontent > .dxrpAW > .dxrpCW, .dxrpControl_ModernoNeosys > tbody > tr.dxrpCR > .dxrpcontent >  .dxrpCW,
.dxrpControlGB_ModernoNeosys > tbody > tr.dxrpCR > .dxrpcontent > .dxrpCW
{
	padding: 10px 11px 10px 9px;
}
/* GroupboxCaptionOffset */
.dxrpControlGB_ModernoNeosys > tbody > tr.dxrpCR > .dxrpcontent > div:first-child
{
	top: -10px;
	left: 6px;
	margin-bottom:-10px;
}
.dxrpControlGB_ModernoNeosys {
	margin-top: 9px;
}
/* Disabled */
.dxrpDisabled_ModernoNeosys,
.dxrpDisabled_ModernoNeosys td.dxrp
{
	color: #A6A6A6;
	cursor: default;
}
/* -- ASPxSiteMapControl -- */
.dxsmControl_ModernoNeosys 
{
	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
	color: #2B2B2B;
	background-color: #FFFFFF;
	border-width: 0px;
}
.dxsmControl_ModernoNeosys a
{
	color: #009C49;
	text-decoration: none;
}
.dxsmControl_ModernoNeosys a:hover
{
	text-decoration: underline;
}
/* - Category Level - */
.dxsmCategoryLevel_ModernoNeosys,
.dxsmCategoryLevel_ModernoNeosys a
{
	color: #7e7e7e;
	text-decoration: none;
}
.dxsmCategoryLevel_ModernoNeosys
{
	font-size: 1.14em;
	border-bottom: 1px solid #d1d1d1;
	white-space: nowrap;
	padding: 0px 0px 6px!important;
}
 /*flow layout*/
.dxsmLevelCategoryFlow_ModernoNeosys,
.dxsmLevelCategoryFlow_ModernoNeosys a
{
	color: #5689C5;
	font-weight: bold;
	text-decoration: underline;
}
.dxsmLevelCategoryFlow_ModernoNeosys
{
	font-size: 1.29em;
}
/* - Level 0 - */
.dxsmLevel0_ModernoNeosys,
.dxsmLevel0_ModernoNeosys a,
.dxsmLevel0Categorized_ModernoNeosys a,
.dxsmLevel0Categorized_ModernoNeosys
{
	color: #7e7e7e;
	font-weight: normal;
}
.dxsmLevel0_ModernoNeosys,
.dxsmLevel0Categorized_ModernoNeosys
{
	white-space: nowrap;
	padding: 0px 0px 1px;
}
.dxsmLevel0_ModernoNeosys
{
	padding: 0px 0px 1px;
}

 /*flow layout*/
.dxsmLevel0Flow_ModernoNeosys,
.dxsmLevel0Flow_ModernoNeosys a,
.dxsmLevel0CategorizedFlow_ModernoNeosys a,
.dxsmLevel0CategorizedFlow_ModernoNeosys
{
	color: #009C49;
}
.dxsmLevel0Flow_ModernoNeosys
{
	font-size: 0.93em;
	padding: 0px;
	text-decoration: none;
}

/* - Level 1 - */
.dxsmLevel1_ModernoNeosys,
.dxsmLevel1_ModernoNeosys a,
.dxsmLevel1Categorized_ModernoNeosys a,
.dxsmLevel1Categorized_ModernoNeosys
{
	color: #009C49;
}
.dxsmLevel1_ModernoNeosys,
.dxsmLevel1Categorized_ModernoNeosys
{
	font-size: 0.86em;
	white-space: nowrap;
	padding: 0px;
}


/*flow layout*/
.dxsmLevel1Flow_ModernoNeosys,
.dxsmLevel1Flow_ModernoNeosys a,
.dxsmLevel1CategorizedFlow_ModernoNeosys,
.dxsmLevel1CategorizedFlow_ModernoNeosys a
{
	color: #009C49;
}
.dxsmLevel1Flow_ModernoNeosys
{
	font-size: 0.86em;
	text-decoration: none;
	padding: 0px;
}

/* - Level 2 - */
.dxsmLevel2_ModernoNeosys,
.dxsmLevel2_ModernoNeosys a,
.dxsmLevel2Categorized_ModernoNeosys a,
.dxsmLevel2Categorized_ModernoNeosys
{
	color: #009C49;
}
.dxsmLevel2_ModernoNeosys,
.dxsmLevel2Categorized_ModernoNeosys
{
    font-size: 0.79em;
	white-space: nowrap;
	padding: 0px;
	text-decoration: none;
}
/*flow layout*/
.dxsmLevel2Flow_ModernoNeosys,
.dxsmLevel2Flow_ModernoNeosys a
{
	color: #5689C5;
	text-decoration: underline;
}
.dxsmLevel2Flow_ModernoNeosys
{
    font-size: 0.86em;
	padding: 0px;
	text-decoration: none;
}
/* - Level 3 - */
.dxsmLevel3_ModernoNeosys,
.dxsmLevel3_ModernoNeosys a
{
	color: #009C49;
}
.dxsmLevel3_ModernoNeosys
{
    font-size: 0.79em;
	white-space: nowrap;
	padding: 0px;
	text-decoration: none;
}
/*flow layout*/
.dxsmLevel3Flow_ModernoNeosys,
.dxsmLevel3Flow_ModernoNeosys a
{
	color: #5689C5;
	text-decoration: underline;
}
.dxsmLevel3Flow_ModernoNeosys
{
	font-size: 0.86em;
}
/* - Level 4 - */
.dxsmLevel4_ModernoNeosys,
.dxsmLevel4_ModernoNeosys a
{
	color: #009C49;
}
.dxsmLevel4_ModernoNeosys
{
    font-size: 0.79em;
	white-space: nowrap;
	padding: 0px;
	text-decoration: none;
}
/*flow layout*/
.dxsmLevel4Flow_ModernoNeosys,
.dxsmLevel4Flow_ModernoNeosys a
{
	color: #5689C5;
	text-decoration: underline;
}
.dxsmLevel4Flow_ModernoNeosys
{
    font-size: 0.86em;
	padding: 0px;
	text-decoration: none;
}
/* - Other Levels - */
.dxsmLevelOther_ModernoNeosys
{
	color: #009C49;
}
.dxsmLevelOther_ModernoNeosys
{
    font-size: 0.79em;
	white-space: nowrap;
	padding: 0px;
}
/*flow layout*/
.dxsmLevelOtherFlow_ModernoNeosys,
.dxsmLevelOtherFlow_ModernoNeosys a
{
	color: #5689C5;
	text-decoration: underline;
}
.dxsmLevelOtherFlow_ModernoNeosys
{
	font-size: 0.86em;
}
/* Disabled */
.dxsmDisabled_ModernoNeosys
{
	color: #A6A6A6;
	cursor: default;
}

/* -- ASPxTabControl Lite -- */
.dxtcLite_ModernoNeosys
{
	overflow: hidden;
    color: #2B2B2B;
	float: left;
	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
}
.dxtcLite_ModernoNeosys > .dxtc-stripContainer,
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-strip
{
	list-style: none outside none;
	float: left;
	padding: 0;
	margin: 0;
	_overflow: hidden;
}
.dxtcLite_ModernoNeosys.dxtc-top > .dxtc-stripContainer
{
	padding-top: 3px;
}
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-tab,
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-activeTab,
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-leftIndent,
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-spacer,
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-rightIndent,
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-sbWrapper,
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-sbIndent,
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-sbSpacer
{
	display: block;
	margin: 0;
}
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-tab,
.dxtcLite_ModernoNeosys.dxtc-noSpacing > .dxtc-stripContainer .dxtc-tab.dxtc-lead,
.dxtcLite_ModernoNeosys.dxtc-noSpacing > .dxtc-stripContainer .dxtc-tab
{
}
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-activeTab,
.dxtcLite_ModernoNeosys.dxtc-noSpacing > .dxtc-stripContainer .dxtc-activeTab.dxtc-lead,
.dxtcLite_ModernoNeosys.dxtc-noSpacing > .dxtc-stripContainer .dxtc-activeTab
{
	background-color: #F0F0F0!important;
}
.dxtcLite_ModernoNeosys.dxtc-noSpacing > .dxtc-stripContainer .dxtc-tab,
.dxtcLite_ModernoNeosys.dxtc-noSpacing > .dxtc-stripContainer .dxtc-activeTab
{
	border-left-style: none;
}
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-tab,
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-activeTab
{
	background: white;
	border-bottom: 4px solid #f0f0f0!important;
	float: left;
	overflow: hidden;
	text-align: center;
	white-space: nowrap;
}
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-activeTab
{
	border-bottom: 1px solid white;
	background: #FFFFFF;
}
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-tabHover,
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-tabHover .dxtc-link
{
	background: #009C49;
	color: white;
}
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-leftIndent,
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-spacer,
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-rightIndent,
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-sbWrapper,
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-sbIndent,
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-sbSpacer
{
	float: left;
	border-right-width: 0;
	border-left-width: 0;
	border-top: 1px solid transparent;
	border-bottom: 4px solid #f0f0f0!important;
	overflow: hidden;
	_border-top-color: #000001;
	_zoom: 1;
	_filter: progid:DXImageTransform.Microsoft.Chroma(color=#000001);
}
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-lineBreak
{
	float: none;
	display: block;
	clear: both;
	height: 0;
	width: 0;
	font-size: 0;
	line-height: 0;
	overflow: hidden;
	visibility: hidden;
}
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-spacer
{
	width: 1px;
}
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-leftIndent,
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-rightIndent
{
	width: 5px;
}
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-link
{
	padding: 5px 15px 7px 16px;
	display: block;
	height: 100%;
	_float: left;
    color: #2B2B2B;
}
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-link,
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-leftIndent,
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-rightIndent
{
	text-decoration: none;
	white-space: nowrap;
}
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-activeTab .dxtc-link
{
	color: #5c5c5c;
}
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-img
{
	border-style: none;
	margin: 0 5px 0 0;
}
.dxtcLite_ModernoNeosys.dxtc-rtl > .dxtc-stripContainer .dxtc-img
{
	margin: 0 0 0 5px;
}
.dxtcLite_ModernoNeosys > .dxtc-content 
{
	background-color: #FFFFFF;
	float: left;
	clear: left;
	border: 1px solid #d1d1d1;
	overflow: hidden;
	padding: 11px;
}
/* Rtl */
.dxtcLite_ModernoNeosys.dxtc-rtl,
.dxtcLite_ModernoNeosys.dxtc-rtl > .dxtc-content,
.dxtcLite_ModernoNeosys.dxtc-rtl > .dxtc-stripContainer,
.dxtcLite_ModernoNeosys.dxtc-rtl > .dxtc-stripContainer .dxtc-strip,
.dxtcLite_ModernoNeosys.dxtc-rtl > .dxtc-stripContainer .dxtc-leftIndent,
.dxtcLite_ModernoNeosys.dxtc-rtl > .dxtc-stripContainer .dxtc-spacer,
.dxtcLite_ModernoNeosys.dxtc-rtl > .dxtc-stripContainer .dxtc-rightIndent,
.dxtcLite_ModernoNeosys.dxtc-rtl > .dxtc-stripContainer .dxtc-sbWrapper,
.dxtcLite_ModernoNeosys.dxtc-rtl > .dxtc-stripContainer .dxtc-sbIndent,
.dxtcLite_ModernoNeosys.dxtc-rtl > .dxtc-stripContainer .dxtc-sbSpacer,
.dxtcLite_ModernoNeosys.dxtc-rtl > .dxtc-stripContainer .dxtc-tab,
.dxtcLite_ModernoNeosys.dxtc-rtl > .dxtc-stripContainer .dxtc-activeTab
{
	float: right;
}
.dxtcLite_ModernoNeosys.dxtc-top.dxtc-rtl > .dxtc-content,
.dxtcLite_ModernoNeosys.dxtc-bottom.dxtc-rtl > .dxtc-stripContainer,
.dxtcLite_ModernoNeosys.dxtc-bottom.dxtc-rtl > .dxtc-stripContainer .dxtc-strip
{
	clear: right!important;
}
.dxtcLite_ModernoNeosys.dxtc-left.dxtc-rtl > .dxtc-strip,
.dxtcLite_ModernoNeosys.dxtc-left.dxtc-rtl > .dxtc-stripContainer .dxtc-strip 
{
	float: left;
}
.dxtcLite_ModernoNeosys.dxtc-rtl > .dxtc-content,
.dxtcLite_ModernoNeosys.dxtc-rtl > .dxtc-stripContainer,
.dxtcLite_ModernoNeosys.dxtc-rtl > .dxtc-stripContainer .dxtc-strip
{
	*float: left;
}
.dxtcLite_ModernoNeosys.dxtc-rtl > .dxtc-content
{
	*clear: left!important;
}
/* Scrolling */
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-sb
{
	border: 1px solid Transparent;
	margin-top: 2px;
	padding: 1px;
}
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-sbHover
{
	border: 1px solid #9FBD92;
	background: #1d85cd;
	background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pg0KPHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIHZpZXdCb3g9IjAgMCAxIDEiIHByZXNlcnZlQXNwZWN0UmF0aW89Im5vbmUiPg0KICA8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWQtdWNnZy1nZW5lcmF0ZWQiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIiB4MT0iMCUiIHkxPSIwJSIgeDI9IjAlIiB5Mj0iMTAwJSI+DQogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iIzFEODVDRCIgc3RvcC1vcGFjaXR5PSIxIi8+DQogICAgPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjMDg2Q0IzIiBzdG9wLW9wYWNpdHk9IjEiLz4NCiAgPC9saW5lYXJHcmFkaWVudD4NCiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4NCjwvc3ZnPg0K);
	background: -ms-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: -moz-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: -o-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: -webkit-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: linear-gradient(to bottom, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	color: white;
	border-radius: 2px;
	-webkit-border-radius: 2px;

	box-shadow: inset 0px 1px 0px 0px rgba(255,255,255,0.35), 0px 1px 3px 0px rgba(0,0,0,0.1);
	-webkit-box-shadow: inset 0px 1px 0px 0px rgba(255,255,255,0.35), 0px 1px 3px 0px rgba(0,0,0,0.1);
}
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-sbPressed
{
	border: 1px solid #cccccc;
	background: #dcdcdc;
	border-radius: 2px;
	-webkit-border-radius: 2px;

	box-shadow: inset 0px 1px 0px 0px rgba(255,255,255,0.35), 0px 1px 3px 0px rgba(0,0,0,0.1);
	-webkit-box-shadow: inset 0px 1px 0px 0px rgba(255,255,255,0.35), 0px 1px 3px 0px rgba(0,0,0,0.1);
}
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-sb img 
{
	cursor: pointer;
	border: none;
}
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-sbIndent
{
	width: 5px;
}
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-sbSpacer
{
	width: 1px;
}
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-sbDisabled img
{
	cursor: default;
}
/* Multi-row */
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-n
{
	_display: inline;
}
.dxtcLiteDisabled_ModernoNeosys,
.dxtcLiteDisabled_ModernoNeosys > .dxtc-stripContainer .dxtc-link,
.dxtcLiteDisabled_ModernoNeosys > .dxtc-stripContainer .dxtc-activeTab .dxtc-link,
.dxtcLiteDisabled_ModernoNeosys > .dxtc-content,
.dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtcLiteDisabled_ModernoNeosys .dxtc-link
{
	color: #A6A6A6;
	cursor: default;
}
/* bottom  */
.dxtcLite_ModernoNeosys.dxtc-bottom > .dxtc-stripContainer
{
	padding-bottom: 3px;
}
.dxtcLite_ModernoNeosys.dxtc-bottom > .dxtc-stripContainer,
.dxtcLite_ModernoNeosys.dxtc-bottom > .dxtc-stripContainer .dxtc-strip
{
	clear: left;
	*float: none;
}
.dxtcLite_ModernoNeosys.dxtc-bottom > .dxtc-stripContainer .dxtc-tab,
.dxtcLite_ModernoNeosys.dxtc-bottom > .dxtc-stripContainer .dxtc-activeTab,
.dxtcLite_ModernoNeosys.dxtc-bottom.dxtc-noSpacing > .dxtc-stripContainer .dxtc-activeTab.dxtc-lead,
.dxtcLite_ModernoNeosys.dxtc-bottom > .dxtc-stripContainer .dxtc-leftIndent,
.dxtcLite_ModernoNeosys.dxtc-bottom > .dxtc-stripContainer .dxtc-spacer,
.dxtcLite_ModernoNeosys.dxtc-bottom > .dxtc-stripContainer .dxtc-rightIndent,
.dxtcLite_ModernoNeosys.dxtc-bottom > .dxtc-stripContainer .dxtc-sbWrapper,
.dxtcLite_ModernoNeosys.dxtc-bottom > .dxtc-stripContainer .dxtc-sbIndent,
.dxtcLite_ModernoNeosys.dxtc-bottom > .dxtc-stripContainer .dxtc-sbSpacer
{
    border-top: 4px solid #F0F0F0!important;
    border-bottom: 0px none transparent!important;
}
.dxtcLite_ModernoNeosys.dxtc-bottom > .dxtc-stripContainer .dxtc-leftIndent,
.dxtcLite_ModernoNeosys.dxtc-bottom > .dxtc-stripContainer .dxtc-spacer,
.dxtcLite_ModernoNeosys.dxtc-bottom > .dxtc-stripContainer .dxtc-rightIndent,
.dxtcLite_ModernoNeosys.dxtc-bottom > .dxtc-stripContainer .dxtc-sbWrapper,
.dxtcLite_ModernoNeosys.dxtc-bottom > .dxtc-stripContainer .dxtc-sbIndent,
.dxtcLite_ModernoNeosys.dxtc-bottom > .dxtc-stripContainer .dxtc-sbSpacer
{
	_border-bottom-color: #000001;
	_zoom: 1;
	_filter: progid:DXImageTransform.Microsoft.Chroma(color=#000001);
}
.dxtcLite_ModernoNeosys.dxtc-bottom.dxtc-noSpacing > .dxtc-stripContainer .dxtc-tab,
.dxtcLite_ModernoNeosys.dxtc-bottom.dxtc-noSpacing > .dxtc-stripContainer .dxtc-activeTab
{
	border-left-style: none;
}
.dxtcLite_ModernoNeosys.dxtc-bottom > .dxtc-stripContainer .dxtc-link 
{
	padding: 7px 15px 5px 16px;
}
.dxtcLite_ModernoNeosys.dxtc-bottom > .dxtc-content
{
    border: 1px solid #d1d1d1;
}
.dxtcLite_ModernoNeosys.dxtc-bottom > .dxtc-stripContainer .dxtc-sb
{
	margin-top: 3px;
}
/* left */
.dxtcLite_ModernoNeosys.dxtc-left > .dxtc-stripContainer
{
	padding-left: 3px;
}
.dxtcLite_ModernoNeosys.dxtc-left > .dxtc-stripContainer .dxtc-tab,
.dxtcLite_ModernoNeosys.dxtc-left > .dxtc-stripContainer .dxtc-activeTab,
.dxtcLite_ModernoNeosys.dxtc-left > .dxtc-stripContainer .dxtc-leftIndent,
.dxtcLite_ModernoNeosys.dxtc-left > .dxtc-stripContainer .dxtc-spacer,
.dxtcLite_ModernoNeosys.dxtc-left > .dxtc-stripContainer .dxtc-rightIndent
{
	float: none;
	*float: left;
	clear: none;
	*clear: both;
	width: auto;
	height: auto;
}
.dxtcLite_ModernoNeosys.dxtc-left > .dxtc-stripContainer .dxtc-tab,
.dxtcLite_ModernoNeosys.dxtc-left > .dxtc-stripContainer .dxtc-activeTab,
.dxtcLite_ModernoNeosys.dxtc-left.dxtc-noSpacing > .dxtc-stripContainer .dxtc-activeTab.dxtc-lead,
.dxtcLite_ModernoNeosys.dxtc-left > .dxtc-stripContainer .dxtc-leftIndent,
.dxtcLite_ModernoNeosys.dxtc-left > .dxtc-stripContainer .dxtc-spacer,
.dxtcLite_ModernoNeosys.dxtc-left > .dxtc-stripContainer .dxtc-rightIndent,
.dxtcLite_ModernoNeosys.dxtc-left > .dxtc-stripContainer .dxtc-sbWrapper,
.dxtcLite_ModernoNeosys.dxtc-left > .dxtc-stripContainer .dxtc-sbIndent,
.dxtcLite_ModernoNeosys.dxtc-left > .dxtc-stripContainer .dxtc-sbSpacer
{
    border-right: 4px solid #F0F0F0!important;
    border-bottom: 0 none transparent!important;
}
.dxtcLite_ModernoNeosys.dxtc-left.dxtc-noSpacing > .dxtc-stripContainer .dxtc-tab,
.dxtcLite_ModernoNeosys.dxtc-left.dxtc-noSpacing > .dxtc-stripContainer .dxtc-activeTab
{
	border-top-style: none;
}
.dxtcLite_ModernoNeosys.dxtc-left > .dxtc-stripContainer .dxtc-activeTab,
.dxtcLite_ModernoNeosys.dxtc-left.dxtc-noSpacing > .dxtc-stripContainer .dxtc-activeTab.dxtc-lead
{
	background-color: white;
}
.dxtcLite_ModernoNeosys.dxtc-left > .dxtc-stripContainer .dxtc-link 
{
	padding: 8px 14px 8px 13px;
}
.dxtcLite_ModernoNeosys.dxtc-left > .dxtc-stripContainer .dxtc-leftIndent,
.dxtcLite_ModernoNeosys.dxtc-left > .dxtc-stripContainer .dxtc-spacer,
.dxtcLite_ModernoNeosys.dxtc-left > .dxtc-stripContainer .dxtc-rightIndent
{
	border: none;
	border-right: 1px solid #AECAF0;
	border-left: 1px solid transparent;
	width: auto;
	_border-left-color: #000001;
	_zoom: 1;
	_filter: progid:DXImageTransform.Microsoft.Chroma(color=#000001);
}
.dxtcLite_ModernoNeosys.dxtc-left > .dxtc-stripContainer .dxtc-leftIndent,
.dxtcLite_ModernoNeosys.dxtc-left > .dxtc-stripContainer .dxtc-rightIndent
{
	height: 5px;
}
.dxtcLite_ModernoNeosys.dxtc-left > .dxtc-stripContainer .dxtc-spacer
{
	height: 1px;
}
.dxtcLite_ModernoNeosys.dxtc-left > .dxtc-content
{
    border: 1px solid #d1d1d1;
	float: left;
	clear: none;
}
/* right */
.dxtcLite_ModernoNeosys.dxtc-right > .dxtc-stripContainer
{
	padding-right: 3px;
}
.dxtcLite_ModernoNeosys.dxtc-right > .dxtc-stripContainer .dxtc-tab,
.dxtcLite_ModernoNeosys.dxtc-right > .dxtc-stripContainer .dxtc-activeTab,
.dxtcLite_ModernoNeosys.dxtc-right > .dxtc-stripContainer .dxtc-leftIndent,
.dxtcLite_ModernoNeosys.dxtc-right > .dxtc-stripContainer .dxtc-spacer,
.dxtcLite_ModernoNeosys.dxtc-right > .dxtc-stripContainer .dxtc-rightIndent
{
	float: none;
	*float: left;
	clear: none;
	*clear: both;
	width: auto;
	height: auto;
}
.dxtcLite_ModernoNeosys.dxtc-right > .dxtc-stripContainer .dxtc-tab,
.dxtcLite_ModernoNeosys.dxtc-right > .dxtc-stripContainer .dxtc-activeTab,
.dxtcLite_ModernoNeosys.dxtc-right.dxtc-noSpacing > .dxtc-stripContainer .dxtc-activeTab.dxtc-lead,
.dxtcLite_ModernoNeosys.dxtc-right > .dxtc-stripContainer .dxtc-leftIndent,
.dxtcLite_ModernoNeosys.dxtc-right > .dxtc-stripContainer .dxtc-spacer,
.dxtcLite_ModernoNeosys.dxtc-right > .dxtc-stripContainer .dxtc-rightIndent,
.dxtcLite_ModernoNeosys.dxtc-right > .dxtc-stripContainer .dxtc-sbWrapper,
.dxtcLite_ModernoNeosys.dxtc-right > .dxtc-stripContainer .dxtc-sbIndent,
.dxtcLite_ModernoNeosys.dxtc-right > .dxtc-stripContainer .dxtc-sbSpacer
{
    border-left: 4px solid #F0F0F0!important;
    border-bottom: 0 none transparent!important;
}
.dxtcLite_ModernoNeosys.dxtc-right > .dxtc-stripContainer .dxtc-activeTab,
.dxtcLite_ModernoNeosys.dxtc-right.dxtc-noSpacing > .dxtc-stripContainer .dxtc-activeTab.dxtc-lead
{
	background-color: white;
}
.dxtcLite_ModernoNeosys.dxtc-right.dxtc-noSpacing > .dxtc-stripContainer .dxtc-tab,
.dxtcLite_ModernoNeosys.dxtc-right.dxtc-noSpacing > .dxtc-stripContainer .dxtc-activeTab
{
	border-top-style: none;
	border-right-style: none;
}
.dxtcLite_ModernoNeosys.dxtc-right > .dxtc-stripContainer .dxtc-link 
{
	padding: 8px 13px 8px 14px;
}
.dxtcLite_ModernoNeosys.dxtc-right > .dxtc-stripContainer .dxtc-leftIndent,
.dxtcLite_ModernoNeosys.dxtc-right > .dxtc-stripContainer .dxtc-spacer,
.dxtcLite_ModernoNeosys.dxtc-right > .dxtc-stripContainer .dxtc-rightIndent
{
	border: none;
	border-left: 1px solid #AECAF0;
	border-right: 1px solid transparent;
	_border-right-color: #000001;
	_zoom: 1;
	_filter: progid:DXImageTransform.Microsoft.Chroma(color=#000001);
}
.dxtcLite_ModernoNeosys.dxtc-right > .dxtc-stripContainer .dxtc-leftIndent,
.dxtcLite_ModernoNeosys.dxtc-right > .dxtc-stripContainer .dxtc-rightIndent
{
	height: 5px;
}
.dxtcLite_ModernoNeosys.dxtc-right > .dxtc-stripContainer .dxtc-spacer
{
	height: 1px;
}
.dxtcLite_ModernoNeosys.dxtc-right > .dxtc-content
{
    border: 1px solid #d1d1d1;
	float: left;
	clear: none;
}
/* Tabs only */
.dxtcLite_ModernoNeosys.dxtc-top > ul:only-child 
{
    border-bottom: 1px solid #d1d1d1;
}
.dxtcLite_ModernoNeosys.dxtc-bottom > ul:only-child 
{
    border-top: 1px solid #d1d1d1;
}
.dxtcLite_ModernoNeosys.dxtc-left > ul:only-child
{
    border-right: 1px solid #d1d1d1;
}
.dxtcLite_ModernoNeosys.dxtc-right > ul:only-child
{
    border-left: 1px solid #d1d1d1;
}
/* Services rules */
.dxtcLite_ModernoNeosys.dxtc-noTabs > .dxtc-content
{
	border: 1px solid #d1d1d1!important;
}
/* -- ASPxTitleIndex -- */
.dxtiControl_ModernoNeosys 
{
	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
	color: #2B2B2B;
	text-decoration: none;
	background-color: #FFFFFF;
}
.dxtiControl_ModernoNeosys a
{
	color: #009C49;
	text-decoration: none;
}
.dxtiControl_ModernoNeosys a:hover
{
	text-decoration: underline;
}
.dxtiItem_ModernoNeosys
{
	font-weight: normal;
	color: #009C49;
	white-space: nowrap;
	padding: 6px!important;
}
.dxtiIndexPanelItem_ModernoNeosys a
{
	color: #009C49;
	text-decoration: none;
	padding: 0px 2px;
}
.dxtiIndexPanelItem_ModernoNeosys a:hover
{
	text-decoration: underline;
}

.dxtiGroupHeader_ModernoNeosys,
.dxtiGroupHeaderCategorized_ModernoNeosys
{
	color: #7e7e7e; 
}
.dxtiGroupHeaderCategorized_ModernoNeosys
{
	padding: 7px 6px!important;
}
.dxtiGroupHeader_ModernoNeosys table 
{
	width: 100%;
}
/* - GroupHeaderText - */
.dxtiGroupHeaderText_ModernoNeosys
{
    	white-space: nowrap;
    	text-decoration: none;
	padding: 7px 6px!important;
}
.dxtiGroupHeaderTextCategorized_ModernoNeosys
{
	text-decoration: none;
	font-weight: normal;
}
/* - FilterBox - */
.dxtiFilterBoxInfoText_ModernoNeosys
{
	text-decoration: none;
	font-weight: normal;
	font-size: 0.79em;
	color: #7D7D7D;
}
.dxtiFilterBoxEdit_ModernoNeosys
{
	background: #fafafa;
	border: 1px solid #d1d1d1;
	padding: 0 2px;
	width: 180px;
	height: 30px;

	-webkit-box-shadow: inset 0px 2px 3px 0px rgba(0, 0, 0, 0.05);
    box-shadow: inset 0px 2px 3px 0px rgba(0, 0, 0, 0.05);
}
.dxtiFilterBox_ModernoNeosys,
.dxtiFilterBox_ModernoNeosys table
{
	color: #232323;
	font-weight: normal;
	text-decoration: none;
}
.dxtiFilterBox_ModernoNeosys
{
	padding: 10px 0;
}
/* - IndexPanel - */
.dxtiIndexPanel_ModernoNeosys
{
	background: #fafafa;
    font-size: 1.14em;
	color: #009C49;
	border-top: 1px Solid #d1d1d1;
	border-bottom: 1px Solid #d1d1d1;
	padding: 5px 6px!important;
}
.dxtiIndexPanel_ModernoNeosys td
{
	padding-bottom: expression("1px");
}

.dxtiIndexPanelItem_ModernoNeosys,
.dxtiCurrentIndexPanelItem_ModernoNeosys
{
	padding: 0px 3px;
}
.dxtiIndexPanelItem_ModernoNeosys
{
	color: #009C49;
}
.dxtiCurrentIndexPanelItem_ModernoNeosys
{
	background-color: #009C49;
	color: white;
}
/* - BackToTop - */
.dxtiBackToTop_ModernoNeosys
{
	font-size: 0.86em;
	text-decoration: none;
	padding: 12px 6px;
}
/* Disabled */
.dxtiDisabled_ModernoNeosys,
.dxtiDisabled_ModernoNeosys a
{
	color: #A6A6A6;
	cursor: default!important;
}

/* -- ASPxUploadControl -- */
.dxucControl_ModernoNeosys,
.dxucEditArea_ModernoNeosys,
input[type="text"].dxucEditArea_ModernoNeosys /*Bootstrap correction*/
{
	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
	color: #2B2B2B;
}
.dxucEditArea_ModernoNeosys,
input[type="text"].dxucEditArea_ModernoNeosys /*Bootstrap correction*/
{
    padding: 1px;
}
body input.dxucEditArea_ModernoNeosys /*Bootstrap correction*/
{
    color: black;
}
.dxucControl_ModernoNeosys .dxucInputs_ModernoNeosys
{
}
.dxucTextBox_ModernoNeosys
{
	background-color: #fafafa;
	border: 1px Solid #d1d1d1;	
	padding: 7px 2px 6px;

	-webkit-box-shadow: inset 0px 3px 7px -5px rgba(0, 0, 0, 0.05);
   	box-shadow: inset 0px 4px 5px -3px rgba(0, 0, 0, 0.05);
}
.dxucTextBox_ModernoNeosys .dxucEditArea_ModernoNeosys
{
	margin: 0px;
	background-color: white;
}
.dxucNullText_ModernoNeosys .dxucEditArea_ModernoNeosys
{
    color: #a6a6a6;
}
.dxucTextBox_ModernoNeosys a
{
    display: block;
}
.dxucErrorCell_ModernoNeosys
{
	color: Red;
	text-align: left;
	padding: 3px 0;
}
.dxucButton_ModernoNeosys
{
    padding: 9px 0 7px;
}
.dxucButton_ModernoNeosys,
.dxucButton_ModernoNeosys a
{
	color: #009C49;
	white-space: nowrap;
}
.dxucButton_ModernoNeosys a:hover
{
	color: #2B2B2B;
}
.dxucBrowseButton_ModernoNeosys,
.dxucBrowseButton_ModernoNeosys a
{
	color: #2C4D79;
	cursor: pointer;
	white-space: nowrap;
	text-decoration: none;
}
.dxucControl_ModernoNeosys a[unselectable=on]
{
    user-select: none;
	-moz-user-select: -moz-none;
	-khtml-user-select: none;
	-webkit-user-select: none;
}
.dxucBrowseButton_ModernoNeosys
{
	padding: 8px 14px 7px;

	border: 1px solid #d1d1d1;
	background: #eaeaea;
	background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pg0KPHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIHZpZXdCb3g9IjAgMCAxIDEiIHByZXNlcnZlQXNwZWN0UmF0aW89Im5vbmUiPg0KICA8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWQtdWNnZy1nZW5lcmF0ZWQiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIiB4MT0iMCUiIHkxPSIwJSIgeDI9IjAlIiB5Mj0iMTAwJSI+DQogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iI0VBRUFFQSIgc3RvcC1vcGFjaXR5PSIxIi8+DQogICAgPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjREZERkRGIiBzdG9wLW9wYWNpdHk9IjEiLz4NCiAgPC9saW5lYXJHcmFkaWVudD4NCiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4NCjwvc3ZnPg0K);
	background: -ms-linear-gradient(top, rgba(234,234,234,1) 0%, rgba(223,223,223,1) 100%);
	background: -moz-linear-gradient(top, rgba(234,234,234,1) 0%, rgba(223,223,223,1) 100%);
	background: -o-linear-gradient(top, rgba(234,234,234,1) 0%, rgba(223,223,223,1) 100%);
	background: -webkit-linear-gradient(top, rgba(234,234,234,1) 0%, rgba(223,223,223,1) 100%);
	background: linear-gradient(to bottom, rgba(234,234,234,1) 0%, rgba(223,223,223,1) 100%);

	box-shadow: inset 0px 1px 0px 0px rgba(255,255,255,0.35);
	-webkit-box-shadow: inset 0px 1px 0px 0px rgba(255,255,255,0.35);
}
.dxucBrowseButton_ModernoNeosys a
{
	color: #2B2B2B;
}
.dxucBrowseButton_ModernoNeosys.dxbf
{
    padding: 7px 13px 6px;
}
.dxucBrowseButton_ModernoNeosys.dxbf a
{
    border: 1px dotted #2C4D79;
}
.dxucBrowseButtonHover_ModernoNeosys
{
	border: 1px solid #9FBD92;
	background: #1d85cd;
	background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pg0KPHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIHZpZXdCb3g9IjAgMCAxIDEiIHByZXNlcnZlQXNwZWN0UmF0aW89Im5vbmUiPg0KICA8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWQtdWNnZy1nZW5lcmF0ZWQiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIiB4MT0iMCUiIHkxPSIwJSIgeDI9IjAlIiB5Mj0iMTAwJSI+DQogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iIzFEODVDRCIgc3RvcC1vcGFjaXR5PSIxIi8+DQogICAgPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjMDg2Q0IzIiBzdG9wLW9wYWNpdHk9IjEiLz4NCiAgPC9saW5lYXJHcmFkaWVudD4NCiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4NCjwvc3ZnPg0K);
	background: -ms-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: -moz-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: -o-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: -webkit-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: linear-gradient(to bottom, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	color: white;
}
.dxucBrowseButtonHover_ModernoNeosys a
{
	color: white;
}
.dxucBrowseButtonPressed_ModernoNeosys
{
	border: 1px solid #cccccc;
	background: #dcdcdc;
	color: #a7a7a7;
}
.dxucBrowseButtonPressed_ModernoNeosys a
{
	color: #a7a7a7;
}
.dxuc-IE7 .dxucTextBox_ModernoNeosys .dxucEditArea_ModernoNeosys
{
    margin-left: -3px;
}
/* UploadControl - ProgressBar */
.dxucProgressBar_ModernoNeosys,
.dxucProgressBar_ModernoNeosys td
{
	color: #9f9f9f;
}
.dxucProgressBar_ModernoNeosys .dxucPBMainCell,
.dxucProgressBar_ModernoNeosys td.dx
{
	padding: 5px;
}
.dxucProgressBar_ModernoNeosys
{
	background-color: #fafafa;
	border: 1px Solid #d1d1d1;
	
	-webkit-box-shadow: inset 0px 2px 3px 0px rgba(0, 0, 0, 0.05);
    box-shadow: inset 0px 2px 3px 0px rgba(0, 0, 0, 0.05);
}
.dxucProgressBarIndicator_ModernoNeosys
{
	background: #009C49;
}
/* Silverlight Plugin Link */
.dxucSilverlightPluginLinkPanel_ModernoNeosys {
	font-size: 0.86em;
	color: #7d7d7d;
	text-align: left;
}
.dxucSilverlightPluginLinkPanel_ModernoNeosys td {
	padding: 0px 3px;
	vertical-align: middle;
}
.dxucSilverlightPluginLinkPanel_ModernoNeosys a {
	color: #5689C5;
}
/* Disabled */
.dxucDisabled_ModernoNeosys,
.dxucDisabled_ModernoNeosys a
{
	color: #A6A6A6;
	cursor: default;
}
.dxucTextBoxDisabled_ModernoNeosys
{
	border-color: #D1D1D1;
}
.dxucButtonDisabled_ModernoNeosys a,
.dxucButtonDisabled_ModernoNeosys a:hover
{
	color: #A6A6A6;
}
.dxucControl_ModernoNeosys .dxucBrowseButtonDisabled_ModernoNeosys a
{
	color: #C3C3C3;
}
.dxucBrowseButtonDisabled_ModernoNeosys
{
	background: #e7e7e7;
	color: #C3C3C3;
	border-color: #d3d3d3;
	cursor: default;
}

/* -- ASPxSplitter -- */
.dxsplControl_ModernoNeosys
{
	background-color: White;
	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
	color: #2B2B2B;
}
.dxsplPane_ModernoNeosys,
.dxsplPaneCollapsed_ModernoNeosys
{
	border: 1px solid #d1d1d1;
}
.dxsplVSeparator_ModernoNeosys,
.dxsplHSeparator_ModernoNeosys,
.dxsplVSeparator_ModernoNeosys,
.dxsplHSeparator_ModernoNeosys,
.dxsplVSeparatorCollapsed_ModernoNeosys,
.dxsplHSeparatorCollapsed_ModernoNeosys
{
	border: 1px solid white;
}
.dxsplPane_ModernoNeosys,
.dxsplPaneCollapsed_ModernoNeosys
{
	border-width: 1px;
}
.dxsplPaneCollapsed_ModernoNeosys
{
	border-right-width: 0px;
	border-bottom-width: 0px;
}
.dxsplVSeparator_ModernoNeosys
{
	border-top-width: 0px;
	border-bottom-width: 0px;
}
.dxsplHSeparator_ModernoNeosys
{
	border-left-width: 0px;
	border-right-width: 0px;
}
.dxsplVSeparatorHover_ModernoNeosys
{
	cursor: w-resize;
}
.dxsplHSeparatorHover_ModernoNeosys
{
	cursor: n-resize;
}
.dxsplVSeparator_ModernoNeosys,
.dxsplHSeparator_ModernoNeosys
{
	font-size: 0;
}
.dxsplVSeparatorHover_ModernoNeosys,
.dxsplVSeparatorButtonHover_ModernoNeosys
{
	background: #009C49;
}
.dxsplHSeparatorHover_ModernoNeosys,
.dxsplHSeparatorButtonHover_ModernoNeosys
{
	background: #009C49;
}
.dxsplVSeparatorCollapsed_ModernoNeosys,
.dxsplHSeparatorCollapsed_ModernoNeosys
{
	cursor: default!important;
}
.dxsplVSeparatorButton_ModernoNeosys
{
	cursor: pointer;
	padding: 5px 0px;
}
.dxsplHSeparatorButton_ModernoNeosys
{
	cursor: pointer;
	padding: 0px 5px;
	margin: 0px 14px;
}
.dxsplDisabled_ModernoNeosys .dxsplVSeparatorButton_ModernoNeosys,
.dxsplDisabled_ModernoNeosys .dxsplHSeparatorButton_ModernoNeosys
{
	cursor: default;
}
.dxsplVSeparatorHover_ModernoNeosys,
.dxsplHSeparatorHover_ModernoNeosys,
.dxsplVSeparatorButtonHover_ModernoNeosys,
.dxsplHSeparatorButtonHover_ModernoNeosys
{
	background-color: #009C49;
}
.dxsplVSeparator_ModernoNeosys,
.dxsplVSeparatorButtonHover_ModernoNeosys
{
	background-repeat: repeat-y;
	background-position: top left;
}
.dxsplHSeparator_ModernoNeosys,
.dxsplHSeparatorButtonHover_ModernoNeosys
{
	background-repeat: repeat-x;
	background-position: top left;
}
.dxsplResizingPointer_ModernoNeosys
{
	background: url('splResizingPointer.gif') repeat;
	font-size: 0;
	line-height: 0px;
}
.dxsplControl_ModernoNeosys .dxsplLCC
{
	padding: 8px;
}

/* -- ASPxTreeView -- */
.dxtvControl_ModernoNeosys
{
	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
	color: #2B2B2B;
	float: left;
}
.dxtvControl_ModernoNeosys li 
{
	overflow-y: hidden;
}
.dxtvControl_ModernoNeosys ul 
{
	list-style-type: none;
	margin: 0;
	padding: 0;
	overflow-y: hidden;
}

.dxtvControl_ModernoNeosys a 
{
	color: #2B2B2B;
	text-decoration: none;
}

.dxtvControl_ModernoNeosys .dxtv-ln
{
	vertical-align: top;
}

.dxtvControl_ModernoNeosys .dxtv-nd
{
	float: left;
	margin-top: 1px;
	padding: 9px 8px;
	cursor: pointer;
	display: block;
	text-decoration: none;
	outline: 0 none;
}

.dxtvControl_ModernoNeosys .dxtv-elbNoLn,
.dxtvControl_ModernoNeosys .dxtv-elb
{
	width: 26px;
	height: 21px;
	vertical-align: top;
	float: left;
}

.dxtvControl_ModernoNeosys .dxtv-btn 
{
	margin: 9px 10px;
	cursor: pointer;
}

.dxtvControl_ModernoNeosys .dxtv-subnd 
{
	margin-left: 28px;
}

.dxtvControl_ModernoNeosys .dxtv-ndImg 
{
	margin:  0 6px 0 4px;
	cursor: pointer;
}

.dxtvControl_ModernoNeosys .dxtv-ndTxt 
{
	white-space: nowrap;
	cursor: pointer;
}

.dxtvControl_ModernoNeosys .dxtv-ndChk
{
	margin:  -3px 6px -3px 2px;
	cursor: default;
}
  
.dxtvControl_ModernoNeosys .dxtv-ndTmpl
{
	float: left;
	white-space: nowrap;
}

.dxtvControl_ModernoNeosys .dxtv-ndSel
{
	background-color: #dcdcdc;
	cursor: default;
}

.dxtvControl_ModernoNeosys .dxtv-ndHov 
{
	background-color: #009C49;
	color: white;
	cursor: pointer;
}

.dxtvControl_ModernoNeosys .dxtv-clr,
.dxtvControl_ModernoNeosys .dxtv-clrIE7
{
	clear: both;
	font-size: 0;
	display: block;
	height: 0;
	visibility: hidden;
	width: 0;
} 

.dxtvControl_ModernoNeosys .dxtv-clr
{
	line-height: 0;
}

.dxtvControl_ModernoNeosys.dxtvRtl,
.dxtvControl_ModernoNeosys.dxtvRtl .dxtv-nd, 
.dxtvControl_ModernoNeosys.dxtvRtl .dxtv-elbNoLn,
.dxtvControl_ModernoNeosys.dxtvRtl .dxtv-elb,
.dxtvControl_ModernoNeosys.dxtvRtl .dxtv-ndTmpl
{
	float: right;
}

.dxtvControl_ModernoNeosys.dxtvRtl .dxtv-elb,
.dxtvControl_ModernoNeosys.dxtvRtl .dxtv-ln 
{
	background-position: right top;
}

.dxtvControl_ModernoNeosys.dxtvRtl .dxtv-elb
{
}

.dxtvControl_ModernoNeosys.dxtvRtl .dxtv-btn 
{
	margin: 9px 10px 0 0;
}

.dxtvControl_ModernoNeosys.dxtvRtl .dxtv-subnd 
{
	margin: 0 28px 0 0;
}

.dxtvControl_ModernoNeosys.dxtvRtl .dxtv-ndImg 
{
    margin: 0 4px 0 6px;
}

.dxtvControl_ModernoNeosys.dxtvRtl.OperaRtlFix .dxtv-btn
{
	margin: 5px 10px 0 0;
}

.dxtvControl_ModernoNeosys.dxtvRtl.OperaRtlFix .dxtv-subnd 
{
	overflow-x: hidden;
}

.dxtvControl_ModernoNeosys.dxtvRtl .dxtv-ndChk
{
    margin: 0 4px 0 8px;
}

.dxtvDisabled_ModernoNeosys,
.dxtvControl_ModernoNeosys .dxtvDisabled_ModernoNeosys,
.dxtvDisabled_ModernoNeosys a,
.dxtvDisabled_ModernoNeosys .dxtv-ndTxt,
.dxtvDisabled_ModernoNeosys .dxtv-ndImg,
.dxtvDisabled_ModernoNeosys .dxtv-btn,
.dxtvDisabled_ModernoNeosys .dxtv-nd
{
	color: #A6A6A6;
	cursor: default;
} 

/* ASPxFileManager */
.dxfmControl_ModernoNeosys 
{
	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
	color: #2B2B2B;

	outline: 0px;
}
.dxfmDisabled_ModernoNeosys
{
	color: #A6A6A6;
}

/* FileManager - Splitter */
.dxfmControl_ModernoNeosys .dxsplControl_ModernoNeosys
{
	border: 1px solid #d1d1d1;
}
.dxfmControl_ModernoNeosys .dxsplPane_ModernoNeosys
{
	border-width: 0px;
}
.dxfmControl_ModernoNeosys .dxsplLCC {
	outline-width: 0px;
	padding: 2px 4px;
}
.dxfmControl_ModernoNeosys.dxfm-dst .dxsplVSeparator_ModernoNeosys
{
	background: none;
	border-right: 1px solid #d1d1d1;
	width: 1px!important;
}
.dxfmControl_ModernoNeosys.dxfm-dst.dxfm-rtl .dxsplVSeparator_ModernoNeosys
{
	background-position-x: left;
}
.dxfmControl_ModernoNeosys.dxfm-tch .dxsplVSeparator_ModernoNeosys
{
	border: 1px solid #d1d1d1;
	border-width: 0 1px;
}
.dxfmControl_ModernoNeosys .dxsplHSeparator_ModernoNeosys
{
	border: 0;
	background: #d1d1d1;
}
.dxfmControl_ModernoNeosys .dxfm-filePane .dxsplLCC
{
    padding: 0;
} 

/* FileManager - TreeView */
.dxfmControl_ModernoNeosys .dxtvControl_ModernoNeosys 
{
	margin-left: -5px;
}
.dxfmControl_ModernoNeosys .dxtvControl_ModernoNeosys .dxfm-folderSI
{
	border: 1px dotted #888888;
	padding: 8px 7px;
}

/* FileManager - File */
.dxfmControl_ModernoNeosys div.dxfm-file 
{
	float: left;
	text-align: center;
	cursor: pointer;
	white-space: nowrap;
	padding: 6px;
	margin: 4px;
}
.dxfmControl_ModernoNeosys.dxfm-rtl div.dxfm-file 
{
	float: right;
}
.dxfmDisabled_ModernoNeosys .dxfm-file
{
	cursor: default;
}
.dxfmControl_ModernoNeosys .dxfm-fileSI,
.dxfmControl_ModernoNeosys .dxfm-fileSA
{
    background-color: #dcdcdc;
}
.dxfmControl_ModernoNeosys div.dxfm-fileH
{
	background-color: #009C49;
	color: white;
}
.dxfmControl_ModernoNeosys div.dxfm-fileF
{
	background-color: #e5e5e5;
}

.dxfmControl_ModernoNeosys .dxfm-content .dxfm-highlight
{
	background-color: #FFE7A2;
	background-repeat: repeat;
	color: #333333;
	font-weight: bold;
}
.dxfmControl_ModernoNeosys .fileContainer
{
    margin: 4px;
}
 
/* FileManager - GridView */
.dxfmControl_ModernoNeosys .dxgvControl_ModernoNeosys .dxgvTable_ModernoNeosys,
.dxfmControl_ModernoNeosys .dxgvControl_ModernoNeosys .dxgvHSDC,
.dxfmControl_ModernoNeosys .dxgvControl_ModernoNeosys .dxgvFSDC,
.dxfmControl_ModernoNeosys .dxgvControl_ModernoNeosys .dxgvCSD
{
    border: 0px;
}
.dxfmControl_ModernoNeosys .dxgvTable_ModernoNeosys td.dxgv 
{
    border-bottom-width: 0px;
}
.dxfmControl_ModernoNeosys .dxgvDataRow_ModernoNeosys td.dxgv,
.dxfmControl_ModernoNeosys .dxgvSelectedRow_ModernoNeosys td.dxgv,
.dxfmControl_ModernoNeosys .dxgvFocusedRow_ModernoNeosys td.dxgv
{
    padding: 8px 10px 9px;
}
.dxfmControl_ModernoNeosys .dxfm-fileThumb img
{
    margin: 1px 0 -3px -4px;
}
.dxfmControl_ModernoNeosys .dxgvControl_ModernoNeosys
{
    background-color: White;
    outline: none;
}
.dxfmControl_ModernoNeosys .dxgvControl_ModernoNeosys .dxgvHSDC
{
	border-top-width: 0!important; 
}
.dxfmControl_ModernoNeosys .dxgvHeader_ModernoNeosys
{
	border-top-width: 0!important; 
}
.dxfmControl_ModernoNeosys .dxgvTable_ModernoNeosys tr.dxfm-fileSA
{
    background-color: #dcdcdc;
}     
.dxfmControl_ModernoNeosys .dxgvTable_ModernoNeosys tr.dxfm-fileSI
{
    background-color: #E9E9E9;
}
.dxfmControl_ModernoNeosys .dxfm-fileF.dxgvFocusedRow_ModernoNeosys
{
    background-color: #e5e5e5;
}
.dxfmControl_ModernoNeosys .dxgvTable_ModernoNeosys tr.dxfm-fileH
{
	background-color: #009C49;
	color: white;
}
.dxfmControl_ModernoNeosys tr.dxgvFocusedRow_ModernoNeosys
{
    background-color: White;
}
.dxgvHeader_ModernoNeosys.dxfmGridHeader
{
    background: White;
    border-color: #d1d1d1;
}

/* FileManager - Toolbar */
.dxfmControl_ModernoNeosys .dxfm-toolbar table.dxfm
{
	width: 100%;
}
.dxfmControl_ModernoNeosys .dxfm-toolbar .dxfm-filter
{
	text-align: right;
	vertical-align: top;
	white-space: nowrap;
}
.dxfmControl_ModernoNeosys.dxfm-rtl .dxfm-toolbar .dxfm-filter
{
	text-align: left;
}
.dxfmControl_ModernoNeosys .dxfm-toolbar .dxfm-filter input,
.dxfmControl_ModernoNeosys .dxfm-toolbar .dxfm-path input
{
	background: #fafafa;
	border: 1px solid #d1d1d1;
    padding-top: 2px;
    padding-bottom: 3px;

	-webkit-box-shadow: inset 0px 2px 3px 0px rgba(0, 0, 0, 0.05);
    box-shadow: inset 0px 2px 3px 0px rgba(0, 0, 0, 0.05);
}
.dxfmControl_ModernoNeosys .dxfm-toolbar .dxfm-filter input
{
	margin: 5px 5px 1px 3px;
}
.dxfmControl_ModernoNeosys .dxfm-toolbar .dxfm-path
{
    margin-top: -3px;
}

/* FileManager - Toolbar - Light */
.dxfmControl_ModernoNeosys .dxfm-toolbar .dxsplLCC 
{
	padding: 5px;
}
.dxfmControl_ModernoNeosys .dxfm-toolbar .dxmLite_ModernoNeosys .dxm-main
{
	margin-top: 1px;
	border-width: 0px;
	background: transparent none;
}
.dxfmControl_ModernoNeosys .dxfm-toolbar .dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-separator 
{
	padding: 1px 8px 1px;
    margin: 0;
}
.dxfmControl_ModernoNeosys .dxfm-toolbar .dxmLite_ModernoNeosys .dxfm-path 
{
	padding-left: 2px;
}
.dxfmControl_ModernoNeosys .dxfm-toolbar .dxmLite_ModernoNeosys .dxfm-path input
{
	margin: 2px 8px -2px 4px;
}
.dxfmControl_ModernoNeosys.dxfm-rtl .dxfm-toolbar .dxmLite_ModernoNeosys .dxfm-path input
{
	margin: 2px 4px -2px 8px;
}

/* FileManager - UploadPanel */
.dxfmControl_ModernoNeosys .dxfm-uploadPanel 
{
	background-color: #fafafa;
	text-align: right;
}
.dxfmControl_ModernoNeosys.dxfm-rtl .dxfm-uploadPanel 
{
	text-align: left;
}
.dxfmControl_ModernoNeosys .dxfm-uploadPanel table.dxfm-uploadPanelTable
{
	display: inline-block;
	margin-right: 2px;
	margin-top: 1px;
}
.dxfmControl_ModernoNeosys .dxfm-uploadPanel table.dxfm-uploadPanelTable .dxucControl_ModernoNeosys 
{
	margin-right: 10px;
	margin-top: 6px;
}
.dxfmControl_ModernoNeosys.dxfm-rtl .dxfm-uploadPanel table.dxfm-uploadPanelTable .dxucControl_ModernoNeosys 
{
	margin-right: 0px;
	margin-left: 10px;
}
.dxfmControl_ModernoNeosys .dxfm-uploadPanel table.dxfm-uploadPanelTable .dxucTextBox_ModernoNeosys
{
	padding: 5px 2px 5px;
}
.dxfmControl_ModernoNeosys .dxfm-uploadPanel table.dxfm-uploadPanelTable .dxucBrowseButton_ModernoNeosys
{
    padding: 6px 14px 6px;
}
.dxfmControl_ModernoNeosys .dxfm-uploadPanel table.dxfm-uploadPanelTable .dxucBrowseButton_ModernoNeosys.dxbf
{
    padding: 5px 13px 5px;
}
.dxfmControl_ModernoNeosys .dxfm-uploadPanel table.dxfm-uploadPanelTable td.dxfm-uploadPanelTableBCell
{
	padding: 13px 0 0;
    vertical-align: top;
}
.dxfmControl_ModernoNeosys .dxfm-uploadPanel table.dxfm-uploadPanelTable td.dxfm-uploadPanelTableBCell a 
{
	color: #009C49;
}
.dxfmControl_ModernoNeosys .dxfm-uploadPanel table.dxfm-uploadPanelTable td.dxfm-uploadPanelTableBCell a:hover
{
	color: #2B2B2B;
}
.dxfmControl_ModernoNeosys .dxfm-uploadPanel table.dxfm-uploadPanelTable td.dxfm-uploadPanelTableBCell a.dxfm-uploadDisable 
{
	color: #A6A6A6;
	text-decoration: none;
	cursor: default;
}
.dxfmControl_ModernoNeosys .dxfm-uploadPanel.dxsplPane_ModernoNeosys .dxucSilverlightPluginLinkPanel_ModernoNeosys 
{
	margin-top: -8px;
}

/* FileManager - Create, Rename input */
.dxfmControl_ModernoNeosys .dxfm-cInput,
.dxfmControl_ModernoNeosys .dxfm-rInput
{
	background: #fafafa;
	border: 1px solid #d1d1d1;
	padding: 0 2px;

	-webkit-box-shadow: inset 0px 2px 3px 0px rgba(0, 0, 0, 0.05);
    box-shadow: inset 0px 2px 3px 0px rgba(0, 0, 0, 0.05);
}
.dxfmControl_ModernoNeosys .dxtv-nd .dxfm-rInput {
    height: 31px!important;
}

/* FileManager - Move PopupControl */
.dxfmControl_ModernoNeosys .dxpc-content 
{
	padding: 5px 0px 0px;
}
.dxfmControl_ModernoNeosys .dxpc-content .dxfm-mpFoldersC 
{
	overflow: auto;
	padding: 0px 0px 20px 5px;
}
.dxfmControl_ModernoNeosys .dxpc-content .dxfm-mpButtonC
{
	margin-top: 5px;
	border-top: 1px solid #d1d1d1;
	text-align: right;
	background-color: #fafafa;
	padding: 10px 15px 10px 10px;
}
.dxfmControl_ModernoNeosys .dxpc-content .dxfm-mpButtonC a 
{
	margin-left: 12px;
	color: #009C49;
    text-decoration: none;
}
.dxfmControl_ModernoNeosys .dxpc-content .dxfm-mpButtonC a:hover
{
	color: #2B2B2B;
    text-decoration: underline;
}

/* ASPxImageSlider */
.dxisControl_ModernoNeosys
{
    width: 660px;
    height: 505px;
    outline: 0;
    user-select: none;
	-moz-user-select: -moz-none;
	-khtml-user-select: none;
	-webkit-user-select: none;

    font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
}
.dxisControl_ModernoNeosys .dxis-playPauseBtnWrapper
{
    top: 0;
    right: 0;
    padding: 6px 12px 6px 14px;
    position: absolute;
    background-color: #333333;
    background-color: rgba(0, 0, 0, 0.75);
    cursor: pointer;
}
*[dir="rtl"].dxisControl_ModernoNeosys .dxis-playPauseBtnWrapper {
    right: auto;
    left: 0;
}
.dxisControl_ModernoNeosys > .dxis-passePartout
{
    position: relative;
    background-color: #000000;
    width: 100%;
    height:100%;
}
.dxisControl_ModernoNeosys .dxis-imageArea
{
    margin: 0 auto;
    position: relative;
    overflow: hidden;
}
.dxisControl_ModernoNeosys .dxis-slidePanel,
.dxisControl_ModernoNeosys .dxis-nbSlidePanel,
.dxisControl_ModernoNeosys .dxis-nbSlidePanelWrapper
{
    position: absolute;
}
.dxisControl_ModernoNeosys .dxis-nbSlidePanelWrapper
{
    overflow: hidden;
}
.dxisControl_ModernoNeosys .dxis-nbTop,
.dxisControl_ModernoNeosys .dxis-nbBottom,
.dxisControl_ModernoNeosys .dxis-nbLeft,
.dxisControl_ModernoNeosys .dxis-nbRight,
.dxisControl_ModernoNeosys .dxis-nbDotsBottom,
.dxisControl_ModernoNeosys .dxis-nbDotsTop,
.dxisControl_ModernoNeosys .dxis-nbDotsLeft,
.dxisControl_ModernoNeosys .dxis-nbDotsRight
{
    position: relative;
}
.dxisControl_ModernoNeosys .dxis-nbTop
{
    padding-bottom: 5px;
}
.dxisControl_ModernoNeosys .dxis-nbBottom
{
    padding-top: 5px;
}
.dxisControl_ModernoNeosys .dxis-nbLeft
{
    padding-right: 5px;
}
.dxisControl_ModernoNeosys .dxis-nbRight
{
    padding-left: 5px;
}
.dxisControl_ModernoNeosys .dxis-nbDotsBottom,
.dxisControl_ModernoNeosys .dxis-nbDotsTop
{
    padding: 20px 0;
}
.dxisControl_ModernoNeosys .dxis-nbDotsLeft
{
    padding-right: 20px;
}
.dxisControl_ModernoNeosys .dxis-nbDotsRight
{
    padding-left: 20px;
}
.dxisControl_ModernoNeosys .dxis-nbLeft,
.dxisControl_ModernoNeosys .dxis-nbRight,
.dxisControl_ModernoNeosys .dxis-nbDotsLeft,
.dxisControl_ModernoNeosys .dxis-nbDotsRight
{
    float: left;
}
.dxisControl_ModernoNeosys .dxis-item,
.dxisControl_ModernoNeosys .dxis-nbItem
{
    background-image: url('isLoading.gif');
    background-repeat: no-repeat;
    background-position:center center;
}
.dxisControl_ModernoNeosys .dxis-nbItem .dxis-nbHoverItem
{
    display: none;
    position: absolute;
    border: 2px solid #9f9f9f;
}
.dxisControl_ModernoNeosys .dxis-nbItem .dxis-nbHoverItem > div
{
    border: 2px solid #ffffff;
}
.dxisControl_ModernoNeosys .dxis-nbItem.dxis-hover .dxis-nbHoverItem
{
    display: block;
}
.dxisControl_ModernoNeosys .dxis-item,
.dxisControl_ModernoNeosys .dxis-nbItem,
.dxisControl_ModernoNeosys .dxis-nbDotItem
{
    position: absolute;
    overflow: hidden;
}
.dxisControl_ModernoNeosys .dxis-nbItem,
.dxisControl_ModernoNeosys .dxis-nbDotItem
{
    cursor: pointer;
}
.dxisControl_ModernoNeosys .dxis-nbDotItemSelected,
.dxisControl_ModernoNeosys .dxis-nbDotItemDisabled
{
    cursor: default;
}
.dxisControl_ModernoNeosys .dxis-nbItem
{
    width: 90px;
    height: 60px;
    background-color: black;
}
.dxisControl_ModernoNeosys .dxis-itemTextArea
{
    left: 0;
    bottom: 0;
    width: 100%;
    position: absolute;
    cursor: default;
    padding: 22px;
    color: white;
    background-color: #333333;
    background-color: rgba(0, 0, 0, 0.75);
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}
.dxisControl_ModernoNeosys .dxis-nbSelectedItem
{
    z-index: 1;
    cursor: default;
    position: absolute;
    border: 2px solid black;
    background-color: rgba(255, 255, 255, 0.00);
}
.dxisControl_ModernoNeosys .dxis-nbSelectedItem > div
{
    border: 2px solid White;
}
.dxisControl_ModernoNeosys .dxis-prevBtnVertWrapper,
.dxisControl_ModernoNeosys .dxis-prevBtnVertOutsideWrapper,
.dxisControl_ModernoNeosys .dxis-nextBtnVertWrapper,
.dxisControl_ModernoNeosys .dxis-nextBtnVertOutsideWrapper,
.dxisControl_ModernoNeosys .dxis-prevBtnHorWrapper,
.dxisControl_ModernoNeosys .dxis-prevBtnHorOutsideWrapper,
.dxisControl_ModernoNeosys .dxis-nextBtnHorWrapper,
.dxisControl_ModernoNeosys .dxis-nextBtnHorOutsideWrapper
{
    cursor: pointer;
    z-index: 1;
    position: absolute;
    background-color: #333333;
    background-color: rgba(0, 0, 0, 0.75);
}
.dxisControl_ModernoNeosys .dxis-prevBtnHorWrapperDisabled,
.dxisControl_ModernoNeosys .dxis-prevBtnHorOutsideWrapperDisabled,
.dxisControl_ModernoNeosys .dxis-nextBtnHorWrapperDisabled,
.dxisControl_ModernoNeosys .dxis-nextBtnHorOutsideWrapperDisabled,
.dxisControl_ModernoNeosys .dxis-prevBtnVertWrapperDisabled,
.dxisControl_ModernoNeosys .dxis-prevBtnVertOutsideWrapperDisabled,
.dxisControl_ModernoNeosys .dxis-nextBtnVertWrapperDisabled,
.dxisControl_ModernoNeosys .dxis-nextBtnVertOutsideWrapperDisabled
{
    cursor: default;
}
.dxisControl_ModernoNeosys .dxis-prevBtnHorWrapper,
.dxisControl_ModernoNeosys .dxis-prevBtnHorOutsideWrapper
{
    left: 0px;
}
.dxisControl_ModernoNeosys .dxis-nextBtnHorWrapper,
.dxisControl_ModernoNeosys .dxis-nextBtnHorOutsideWrapper
{
    right: 0px;
}
.dxisControl_ModernoNeosys .dxis-prevBtnVertWrapper,
.dxisControl_ModernoNeosys .dxis-prevBtnVertOutsideWrapper
{
    top: 0px;
}
.dxisControl_ModernoNeosys .dxis-nextBtnVertWrapper,
.dxisControl_ModernoNeosys .dxis-nextBtnVertOutsideWrapper
{
    bottom: 0px;
}
.dxisControl_ModernoNeosys > .dxis-passePartout > .dxis-prevBtnHorWrapper,
.dxisControl_ModernoNeosys > .dxis-passePartout > .dxis-prevBtnHorOutsideWrapper,
.dxisControl_ModernoNeosys > .dxis-passePartout > .dxis-nextBtnHorWrapper,
.dxisControl_ModernoNeosys > .dxis-passePartout > .dxis-nextBtnHorOutsideWrapper
{
    top: 50%;
    margin-top: -32px;
}
.dxisControl_ModernoNeosys > .dxis-passePartout > .dxis-prevBtnVertWrapper,
.dxisControl_ModernoNeosys > .dxis-passePartout > .dxis-prevBtnVertOutsideWrapper,
.dxisControl_ModernoNeosys > .dxis-passePartout > .dxis-nextBtnVertWrapper,
.dxisControl_ModernoNeosys > .dxis-passePartout > .dxis-nextBtnVertOutsideWrapper
{
    left: 50%;
    margin-left: -30px;
}
.dxisControl_ModernoNeosys .dxis-prevBtnHor
{
    margin: 16px 12px 16px 8px;
}
.dxisControl_ModernoNeosys .dxis-nextBtnHor
{
    margin: 16px 8px 16px 12px;
}
.dxisControl_ModernoNeosys .dxis-prevBtnVert
{
    margin: 8px 16px 12px 16px;
}
.dxisControl_ModernoNeosys .dxis-nextBtnVert
{
    margin: 12px 16px 8px 16px;
}
.dxisControl_ModernoNeosys > .dxis-nbBottom > .dxis-prevBtnHorWrapper,
.dxisControl_ModernoNeosys > .dxis-nbBottom > .dxis-prevBtnHorOutsideWrapper,
.dxisControl_ModernoNeosys > .dxis-nbBottom > .dxis-nextBtnHorWrapper,
.dxisControl_ModernoNeosys > .dxis-nbBottom > .dxis-nextBtnHorOutsideWrapper,
.dxisControl_ModernoNeosys > .dxis-nbTop > .dxis-prevBtnHorWrapper,
.dxisControl_ModernoNeosys > .dxis-nbTop > .dxis-prevBtnHorOutsideWrapper,
.dxisControl_ModernoNeosys > .dxis-nbTop > .dxis-nextBtnHorWrapper,
.dxisControl_ModernoNeosys > .dxis-nbTop > .dxis-nextBtnHorOutsideWrapper
{
    height: inherit;
}
.dxisControl_ModernoNeosys > .dxis-nbLeft > .dxis-prevBtnVertWrapper,
.dxisControl_ModernoNeosys > .dxis-nbLeft > .dxis-prevBtnVertOutsideWrapper,
.dxisControl_ModernoNeosys > .dxis-nbLeft > .dxis-nextBtnVertWrapper,
.dxisControl_ModernoNeosys > .dxis-nbLeft > .dxis-nextBtnVertOutsideWrapper,
.dxisControl_ModernoNeosys > .dxis-nbRight > .dxis-prevBtnVertWrapper,
.dxisControl_ModernoNeosys > .dxis-nbRight > .dxis-prevBtnVertOutsideWrapper,
.dxisControl_ModernoNeosys > .dxis-nbRight > .dxis-nextBtnVertWrapper,
.dxisControl_ModernoNeosys > .dxis-nbRight > .dxis-nextBtnVertOutsideWrapper
{
    width: inherit;
}
.dxisControl_ModernoNeosys .dxis-prevPageBtnHor,
.dxisControl_ModernoNeosys .dxis-prevPageBtnHorOutside,
.dxisControl_ModernoNeosys .dxis-nextPageBtnHor,
.dxisControl_ModernoNeosys .dxis-nextPageBtnHorOutside
{
    top: 50%;
    position: relative;
    margin: -11px 7px 0 7px;
}
.dxisControl_ModernoNeosys .dxis-prevPageBtnVert,
.dxisControl_ModernoNeosys .dxis-prevPageBtnVertOutside,
.dxisControl_ModernoNeosys .dxis-nextPageBtnVert,
.dxisControl_ModernoNeosys .dxis-nextPageBtnVertOutside
{
    margin: 7px auto 6px;
}
.dxisControl_ModernoNeosys .dxis-prevBtnHorWrapperPressed > .dxis-prevBtnHor,
.dxisControl_ModernoNeosys .dxis-prevBtnHorOutsideWrapperPressed > .dxis-prevBtnHor,
.dxisControl_ModernoNeosys .dxis-nextBtnHorWrapperPressed > .dxis-nextBtnHor,
.dxisControl_ModernoNeosys .dxis-nextBtnHorOutsideWrapperPressed > .dxis-nextBtnHor,
.dxisControl_ModernoNeosys .dxis-prevBtnHorWrapperPressed > .dxis-prevPageBtnHor,
.dxisControl_ModernoNeosys .dxis-prevBtnHorWrapperPressed > .dxis-prevPageBtnHorOutside,
.dxisControl_ModernoNeosys .dxis-prevBtnHorOutsideWrapperPressed > .dxis-prevPageBtnHor,
.dxisControl_ModernoNeosys .dxis-prevBtnHorOutsideWrapperPressed > .dxis-prevPageBtnHorOutside,
.dxisControl_ModernoNeosys .dxis-nextBtnHorWrapperPressed > .dxis-nextPageBtnHor,
.dxisControl_ModernoNeosys .dxis-nextBtnHorWrapperPressed > .dxis-nextPageBtnHorOutside,
.dxisControl_ModernoNeosys .dxis-nextBtnHorOutsideWrapperPressed > .dxis-nextPageBtnHor,
.dxisControl_ModernoNeosys .dxis-nextBtnHorOutsideWrapperPressed > .dxis-nextPageBtnHorOutside,
.dxisControl_ModernoNeosys .dxis-prevBtnVertWrapperPressed > .dxis-prevBtnVert,
.dxisControl_ModernoNeosys .dxis-prevBtnVertOutsideWrapperPressed > .dxis-prevBtnVert,
.dxisControl_ModernoNeosys .dxis-nextBtnVertWrapperPressed > .dxis-nextBtnVert,
.dxisControl_ModernoNeosys .dxis-nextBtnVertOutsideWrapperPressed > .dxis-nextBtnVert,
.dxisControl_ModernoNeosys .dxis-prevBtnVertWrapperPressed > .dxis-prevPageBtnVert,
.dxisControl_ModernoNeosys .dxis-prevBtnVertWrapperPressed > .dxis-prevPageBtnVertOutside,
.dxisControl_ModernoNeosys .dxis-prevBtnVertOutsideWrapperPressed > .dxis-prevPageBtnVert,
.dxisControl_ModernoNeosys .dxis-prevBtnVertOutsideWrapperPressed > .dxis-prevPageBtnVertOutside,
.dxisControl_ModernoNeosys .dxis-nextBtnVertWrapperPressed > .dxis-nextPageBtnVert,
.dxisControl_ModernoNeosys .dxis-nextBtnVertWrapperPressed > .dxis-nextPageBtnVertOutside,
.dxisControl_ModernoNeosys .dxis-nextBtnVertOutsideWrapperPressed > .dxis-nextPageBtnVert,
.dxisControl_ModernoNeosys .dxis-nextBtnVertOutsideWrapperPressed > .dxis-nextPageBtnVertOutside,
.dxisControl_ModernoNeosys .dxis-prevBtnVertPressed,
.dxisControl_ModernoNeosys .dxis-nextBtnVertPressed,
.dxisControl_ModernoNeosys .dxis-nextBtnHorPressed,
.dxisControl_ModernoNeosys .dxis-prevBtnHorPressed,
.dxisControl_ModernoNeosys .dxis-prevPageBtnVertPressed,
.dxisControl_ModernoNeosys .dxis-prevPageBtnVertOutsidePressed,
.dxisControl_ModernoNeosys .dxis-nextPageBtnVertPressed,
.dxisControl_ModernoNeosys .dxis-nextPageBtnVertOutsidePressed,
.dxisControl_ModernoNeosys .dxis-prevPageBtnHorPressed,
.dxisControl_ModernoNeosys .dxis-prevPageBtnHorOutsidePressed,
.dxisControl_ModernoNeosys .dxis-nextPageBtnHorPressed,
.dxisControl_ModernoNeosys .dxis-nextPageBtnHorOutsidePressed,
.dxisControl_ModernoNeosys .dxis-prevBtnVertDisabled,
.dxisControl_ModernoNeosys .dxis-nextBtnVertDisabled,
.dxisControl_ModernoNeosys .dxis-nextBtnHorDisabled,
.dxisControl_ModernoNeosys .dxis-prevBtnHorDisabled,
.dxisControl_ModernoNeosys .dxis-prevPageBtnVertDisabled,
.dxisControl_ModernoNeosys .dxis-prevPageBtnVertOutsideDisabled,
.dxisControl_ModernoNeosys .dxis-nextPageBtnVertDisabled,
.dxisControl_ModernoNeosys .dxis-nextPageBtnVertOutsideDisabled,
.dxisControl_ModernoNeosys .dxis-prevPageBtnHorDisabled,
.dxisControl_ModernoNeosys .dxis-prevPageBtnHorOutsideDisabled,
.dxisControl_ModernoNeosys .dxis-nextPageBtnHorDisabled,
.dxisControl_ModernoNeosys .dxis-nextPageBtnHorOutsideDisabled
{
    opacity: 0.25;
    filter: progid:DXImageTransform.Microsoft.Alpha(Style=0, Opacity=25);
}
.dxisControl_ModernoNeosys .dxis-nextBtnHorOutsideWrapper,
.dxisControl_ModernoNeosys .dxis-prevBtnHorOutsideWrapper,
.dxisControl_ModernoNeosys .dxis-nextBtnVertOutsideWrapper,
.dxisControl_ModernoNeosys .dxis-prevBtnVertOutsideWrapper {
	background-color: transparent;
}
.dxisControl_ModernoNeosys .dxis-nextPageBtnHorOutside,
.dxisControl_ModernoNeosys .dxis-prevPageBtnHorOutside {
	margin-top: -14px;
}
/* ASPxImageGallery */
.dxigControl_ModernoNeosys
{
	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
	color: #2B2B2B;
}
.dxigControl_ModernoNeosys td.dxigCtrl
{
	padding: 0px;
}
.dxigContent_ModernoNeosys
{
	padding: 12px 6px;
	border-bottom: 1px Solid #d1d1d1;
	border-top: 1px Solid #d1d1d1;
}
.dxigEmptyData_ModernoNeosys
{
	color: #4F4F4F;
	padding: 12px 40px;
}
.dxigEmptyItem_ModernoNeosys
{
	text-align: left;
	vertical-align: top;
}
.dxigFlowItem_ModernoNeosys
{
    float: left;
}
.dxigItem_ModernoNeosys
{
    background-repeat: no-repeat;
    background-position:center center;
    background-image: url('igLoading.gif');
}
.dxigItem_ModernoNeosys a
{
    outline: 0;
}
.dxigItem_ModernoNeosys a > img
{
    display: block;
}
.dxigPagerPanel_ModernoNeosys
{
	padding-top: 8px;
	padding-bottom: 8px;
}
.dxigItem_ModernoNeosys .dxig-thumbnailBorder
{
    top: 0;
    display: none;
    position: absolute;
    border: 1px solid gray;
    border: 1px solid rgba(0, 0, 0, 0.20);
}
.dxigItem_ModernoNeosys .dxig-thumbnailWrapper,
.dxigFlowItem_ModernoNeosys
{
    width: 0;
    height: 0;
    overflow: hidden;
    position: relative;
}
.dxigControl_ModernoNeosys .dxig-thumbnailTextArea
{
    left: 0;
    bottom: 0;
    width: 100%;
    position: absolute;
    padding: 10px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    color: white;
    background-color: #333333;
    background-color: rgba(0, 0, 0, 0.75);
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    display: none;
}
.dxigControl_ModernoNeosys .dxpcLite_ModernoNeosys .dxpc-mainDiv,
.dxigControl_ModernoNeosys .dxpcLite_ModernoNeosys .dxpc-contentWrapper
{
    border: 0;
    background-color: #000;
    background-color: rgba(0, 0, 0, 0.9);
}
.dxigControl_ModernoNeosys .dxpcLite_ModernoNeosys .dxpc-content
{
    padding: 0;
}
.dxigControl_ModernoNeosys .dxig-imageSliderWrapper
{
    overflow: hidden;
    padding: 50px 65px;
}
.dxigControl_ModernoNeosys.dxTouchUI  .dxig-imageSliderWrapper
{
    padding: 0;
}
.dxigControl_ModernoNeosys.dxTouchUI .dxig-imageSliderWrapper
{
	padding: 0;
}
.dxigControl_ModernoNeosys .dxig-imageSliderWrapper .dxisControl_ModernoNeosys  .dxis-item > img
{
    box-shadow: 0 0 50px rgb(0,0,0);
    -moz-box-shadow: 0 0 50px rgb(0,0,0);
    -webkit-box-shadow: 0 0 50px rgb(0,0,0);
}
.dxigControl_ModernoNeosys .dxig-bottomPanel
{
    left: 0;
    bottom: 0;
    width: 100%;
    position: absolute;
}
.dxigControl_ModernoNeosys .dxig-bottomPanel > .dxig-overflowPanel
{
    overflow: hidden;
}
.dxigControl_ModernoNeosys .dxig-bottomPanel .dxisControl_ModernoNeosys
{
    position: relative;
}
.dxigControl_ModernoNeosys .dxisControl_ModernoNeosys > .dxis-passePartout
{
    background-color: transparent;
}
.dxigControl_ModernoNeosys .dxisControl_ModernoNeosys .dxis-nbBottom
{
    padding: 4px 4px 0 4px;
}
.dxigControl_ModernoNeosys .dxisControl_ModernoNeosys .dxis-nbSelectedItem
{
    border: 2px solid #fff;
}
.dxigControl_ModernoNeosys .dxisControl_ModernoNeosys .dxis-nbItemHover
{
    border: 2px solid #9F9F9F
}
.dxigControl_ModernoNeosys .dxisControl_ModernoNeosys .dxis-nbSelectedItem > div,
.dxigControl_ModernoNeosys .dxisControl_ModernoNeosys .dxis-nbItemHover > div
{
    border: 2px solid #000;
}
.dxigControl_ModernoNeosys .dxig-bottomPanel .dxis-nbBottom > .dxis-prevBtnHorWrapper,
.dxigControl_ModernoNeosys .dxig-bottomPanel .dxis-nbBottom > .dxis-nextBtnHorWrapper
{
    width: 43px;
    background: url('igNavBtnsBack.png') repeat-y;
}
.dxigControl_ModernoNeosys .dxig-bottomPanel .dxis-nbBottom > .dxis-prevBtnHorWrapper
{
    background-position: 0 0;
}
.dxigControl_ModernoNeosys .dxig-bottomPanel .dxis-nbBottom > .dxis-nextBtnHorWrapper
{
    background-position: -39px 0;
}
.dxigControl_ModernoNeosys .dxig-bottomPanel .dxis-nbBottom > .dxis-prevBtnHorWrapperDisabled,
.dxigControl_ModernoNeosys .dxig-bottomPanel .dxis-nbBottom > .dxis-nextBtnHorWrapperDisabled
{
    display: none;
}
.dxigControl_ModernoNeosys .dxig-bottomPanel .dxis-nbBottom > .dxis-prevBtnHorWrapper .dxis-prevPageBtnHor,
.dxigControl_ModernoNeosys .dxig-bottomPanel .dxis-nbBottom > .dxis-nextBtnHorWrapper .dxis-nextPageBtnHor
{
    position: absolute;
}
.dxigControl_ModernoNeosys .dxig-bottomPanel .dxis-nbBottom > .dxis-prevBtnHorWrapper .dxis-prevPageBtnHor
{
}
.dxigControl_ModernoNeosys .dxig-bottomPanel .dxis-nbBottom > .dxis-nextBtnHorWrapper .dxis-nextPageBtnHor
{
    right: 0;
}
.dxigControl_ModernoNeosys .dxig-fullscreenViewerTextArea
{
    z-index: 1;
    padding: 10px;
    text-align: center;
    color: white;
}
.dxigControl_ModernoNeosys .dxig-prevButtonArea,
.dxigControl_ModernoNeosys .dxig-nextButtonArea
{
    top: 0;
    position: absolute;
}
.dxigControl_ModernoNeosys .dxig-prevButtonArea
{
    left: 0;
}
.dxigControl_ModernoNeosys .dxig-nextButtonArea
{
    right: 0;
}
.dxigControl_ModernoNeosys .dxig-prevButton,
.dxigControl_ModernoNeosys .dxig-nextButton
{
    top: 50%;
    cursor: pointer;
    position: relative;
    margin: -32px 11px 0;
}
.dxigControl_ModernoNeosys .dxig-prevButtonDisabled,
.dxigControl_ModernoNeosys .dxig-nextButtonDisabled
{
    cursor: auto;
    opacity: 0.5;
    filter: progid:DXImageTransform.Microsoft.Alpha(Style=0, Opacity=50);
}
.dxigControl_ModernoNeosys .dxig-closeButtonWrapper,
.dxigControl_ModernoNeosys .dxig-playPauseButtonWrapper
{
    padding: 15px;
    cursor: pointer;
    position: absolute;
}
.dxigControl_ModernoNeosys .dxig-closeButtonWrapper
{
    top: 4px;
    right: 7px;
}
.dxigControl_ModernoNeosys .dxig-closeButtonWrapper > .dxig-closeButton
{
    z-index: 1;
}
.dxigControl_ModernoNeosys .dxig-playPauseButtonWrapper
{
    top: 2px;
    right: 48px;
}
*[dir="rtl"].dxigControl_ModernoNeosys .dxig-closeButtonWrapper {
    left: 7px;
    right: auto;
}
*[dir="rtl"].dxigControl_ModernoNeosys .dxig-playPauseButtonWrapper {
    left: 48px;
    right: auto;
}
.dxigControl_ModernoNeosys .dxig-overflowPanel .dxig-navigationBarMarker
{
    left: 50%;
    bottom: 0;
    margin-left: -10px;
    position: absolute;
    opacity: 0.7;
    filter: progid:DXImageTransform.Microsoft.Alpha(Style=0, Opacity=70);
}
.dxigControl_ModernoNeosys .dxisControl_ModernoNeosys .dxis-nbBottom,
.dxigControl_ModernoNeosys .dxig-fullscreenViewerTextArea
{
    background-color: #000;
    background-color: rgba(0, 0, 0, 0.8);
}
.dxigEPContainer_ModernoNeosys
{
    height: 58px;
	text-align: center;
}
.dxigEPContainer_ModernoNeosys  div
{
	padding-top: 20px;
}
.dxigEPContainer_ModernoNeosys a
{
	color: #3F66A0;
}
.dxigEPContainer_ModernoNeosys  a:hover
{
	color: #F39128;
}

/* Removes flicking in iOS Safari*/
.dxfmControl_ModernoNeosys,
.dxnbGroupHeader_ModernoNeosys,
.dxnbGroupHeaderCollapsed_ModernoNeosys,
.dxnbGroupContent_ModernoNeosys > TABLE > TBODY > TR,
.dxtcTab_ModernoNeosys,
.dxtcActiveTab_ModernoNeosys,
.dxtv-nd_ModernoNeosys
{
	-webkit-tap-highlight-color: rgba(0,0,0,0);
}

/* Form Layout */
.dxflFormLayout_ModernoNeosys {
    display: table;
	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
	color: #2B2B2B;
}

.dxflButtonItemSys .dxflVATSys.dxflCaptionCell_ModernoNeosys { padding-top: 10px; }
.dxflButtonItemSys .dxflVAMSys.dxflCaptionCell_ModernoNeosys { padding-bottom: 1px; }
.dxflTextItemSys .dxflVATSys.dxflCaptionCell_ModernoNeosys { padding-top: 0px; }
.dxflTextEditItemSys .dxflVATSys.dxflCaptionCell_ModernoNeosys { padding-top: 10px; }
.dxflCheckBoxItemSys .dxflVATSys.dxflCaptionCell_ModernoNeosys { padding-top: 9px; }
.dxflRadioButtonItemSys .dxflVATSys.dxflCaptionCell_ModernoNeosys { padding-top: 10px; }
.dxflCheckBoxListItemSys .dxflVATSys.dxflCaptionCell_ModernoNeosys { padding-top: 17px;  }
.dxflRadioButtonListItemSys .dxflVATSys.dxflCaptionCell_ModernoNeosys { padding-top: 18px; }
.dxflListBoxItemSys .dxflVATSys.dxflCaptionCell_ModernoNeosys { padding-top: 12px; }
.dxflTrackBarItemSys .dxflVATSys.dxflCaptionCell_ModernoNeosys { padding-top: 13px; }
.dxflProgressBarItemSys .dxflVATSys.dxflCaptionCell_ModernoNeosys { padding-top: 11px; }
.dxflMemoItemSys .dxflVATSys.dxflCaptionCell_ModernoNeosys { padding-top: 5px; }
.dxflCustomItemSys .dxflVATSys.dxflCaptionCell_ModernoNeosys { padding-top: 10px; }

html[xmlns*=""] .dxflTextEditItemSys .dxflVATSys.dxflCaptionCell_ModernoNeosys {   /*IE 8*/
    padding-top: 8px;
}

html[xmlns*=""] .dxflTextEditItemSys .dxflVATSys.dxflCaptionCell_ModernoNeosys {   /*IE 7*/
    *padding-top: 10px;
}

.dxflMemoItemSys .dxflVATSys.dxflCaptionCell_ModernoNeosys {   /*IE 7*/
    *padding-top: 6px;
}

@media screen and (-webkit-min-device-pixel-ratio:0) {   /*Chrome, Safari*/
    .dxflTextEditItemSys .dxflVATSys.dxflCaptionCell_ModernoNeosys {
        padding-top: 11px;
    }
    .dxflMemoItemSys .dxflVATSys.dxflCaptionCell_ModernoNeosys {
        padding-top: 6px;
    }
}

.dxflTextEditItemSys.dxflSafariItemSys .dxflVATSys.dxflCaptionCell_ModernoNeosys {   /*Safari*/
    padding-top: 9px;
}

.dxflMemoItemSys.dxflSafariItemSys .dxflVATSys.dxflCaptionCell_ModernoNeosys {   /*Safari*/
    padding-top: 5px;
}

noindex:-o-prefocus, body:first-of-type .dxflTextEditItemSys .dxflVATSys.dxflCaptionCell_ModernoNeosys {   /*Opera*/
    padding-top: 11px;
}

noindex:-o-prefocus, body:first-of-type .dxflMemoItemSys .dxflVATSys.dxflCaptionCell_ModernoNeosys {   /*Opera*/
    padding-top: 6px;
}

.dxflCLTSys .dxflCaptionCell_ModernoNeosys,
.dxflCLBSys .dxflCaptionCell_ModernoNeosys {
    padding: 3px 0;
}

.dxflCLLSys .dxflCaptionCell_ModernoNeosys,
*[dir="rtl"].dxflFormLayout_ModernoNeosys .dxflCLRSys .dxflCaptionCell_ModernoNeosys {
    padding-left: 0px;
    padding-right: 6px;
}
.dxflCLRSys .dxflCaptionCell_ModernoNeosys,
*[dir="rtl"].dxflFormLayout_ModernoNeosys .dxflCLLSys .dxflCaptionCell_ModernoNeosys {
    padding-right: 0px;
    padding-left: 6px;
}
.dxflCaptionCell_ModernoNeosys {
    white-space: nowrap;
    line-height: 16px;
    height: 100%;
    width: 1%;
}
.dxflNestedControlCell_ModernoNeosys {
    height: 0;
}

.dxflCheckBoxItemSys.dxflItem_ModernoNeosys .dxichCellSys.dxeBase_ModernoNeosys
{
    padding-left: 1px;
}
*[dir="rtl"] .dxflCheckBoxItemSys.dxflItem_ModernoNeosys .dxichCellSys.dxeBase_ModernoNeosys
{
    padding-right: 1px;
}

.dxflEmptyItem_ModernoNeosys {
    height: 21px;
}

.dxflItem_ModernoNeosys { padding: 4px 0; width: 100%; }
.dxflItem_ModernoNeosys > table.dxflItemTable_ModernoNeosys { width: 100%; }
.dxflGroup_ModernoNeosys { padding: 6px 12px; width: 100%;}
.dxflGroup_ModernoNeosys > table.dxflGroupTable_ModernoNeosys { width: 100%; }
.dxflGroupCell_ModernoNeosys { padding: 0 20px; }

.dxflGroupBox_ModernoNeosys { 
	display: table;
	border-collapse: separate;
    width: 100%;
    border: 1px Solid #d1d1d1;
    padding: 0 0 12px;
    margin: 18px 0; 
}
.dxflGroupBox_ModernoNeosys.dxflHeadingLineGroupBoxSys {
    border-width: 1px 0 0;
}
.dxflGroupBox_ModernoNeosys.dxflHeadingLineGroupBoxSys.dxflWithCaptionSys {
    margin-top: 36px!important;
}
.dxflHeadingLineGroupBoxSys > .dxflGroup_ModernoNeosys {
    margin-top: -6px;
}

.dxflGroupCell_ModernoNeosys > .dxtcControl_ModernoNeosys { margin: 0px; }

.dxflGroupBox_ModernoNeosys > .dxflGroup_ModernoNeosys { margin-top: -9px; padding: 0 11px; }
.dxflGroupBox_ModernoNeosys > .dxflGroup_ModernoNeosys tr:first-child > .dxflGroupCell_ModernoNeosys > .dxflItem_ModernoNeosys { padding-top: 9px; }
.dxflGroupBox_ModernoNeosys > .dxflGroup_ModernoNeosys tr:first-child > .dxflGroupCell_ModernoNeosys > .dxtcControl_ModernoNeosys { margin-top: 10px; }
.dxflGroupBox_ModernoNeosys > .dxflGroup_ModernoNeosys tr:last-child > .dxflGroupCell_ModernoNeosys > .dxflItem_ModernoNeosys { padding-bottom: 0px; }

.dxflGroup_ModernoNeosys tr:first-child > .dxflGroupCell_ModernoNeosys > .dxflGroupBox_ModernoNeosys { margin-top: 13px; }
.dxflGroup_ModernoNeosys tr:last-child > .dxflGroupCell_ModernoNeosys > .dxflGroupBox_ModernoNeosys { margin-bottom: 0px; }

.dxtcPageContent_ModernoNeosys > div > .dxflGroup_ModernoNeosys { padding-top: 0px; padding-bottom: 0px; }
.dxtcPageContent_ModernoNeosys > div > .dxflGroup_ModernoNeosys tr:first-child > .dxflGroupCell_ModernoNeosys > .dxflItem_ModernoNeosys { padding-top: 12px; }
.dxtcPageContent_ModernoNeosys > div > .dxflGroup_ModernoNeosys tr:last-child > .dxflGroupCell_ModernoNeosys > .dxflItem_ModernoNeosys { padding-bottom: 12px; }

.dxflGroupBoxCaption_ModernoNeosys 
{
    background-color: White;
    color: #808080;
    display: inline-block;  
    left: 9px;
    line-height: 16px;
    padding: 0px 3px 0px 3px;  
    position: relative;
    top: -12px;
}

.dxflGroupBox_ModernoNeosys.dxflHeadingLineGroupBoxSys .dxflGroupBoxCaption_ModernoNeosys
{
    top: -32px!important;
}

*[dir="rtl"].dxflFormLayout_ModernoNeosys .dxflGroupBoxCaption_ModernoNeosys {
    padding: 0px 3px 0px 7px;
    left: 0px;
    right: 9px;
}

.dxflGroupBox_ModernoNeosys > div.dxflGroup_ModernoNeosys:first-child,
.dxflGroupBox_ModernoNeosys > table.dxflGroup_ModernoNeosys:first-child {
    margin-top: 0px;
    padding-top: 7px;
}

.dxflHeadingLineGroupBoxSys > .dxflGroupBoxCaption_ModernoNeosys {
    top: -19px;
}

.dxflOptional_ModernoNeosys {
	color: gray;
	font-style: normal;
}
.dxflRequired_ModernoNeosys {
	color: green;
	font-style: normal;
}
.dxflInternalEditorTable_ModernoNeosys {
    width: 100%;
}
.dxflHelpText_ModernoNeosys {
    color: #9F9F9F;
	font-size: 0.86em;
}
.dxflHelpText_ModernoNeosys.dxflHHelpTextSys {
    padding: 0 6px;
}
.dxflHelpText_ModernoNeosys.dxflTHelpTextSys {
    padding: 6px 0 2px;
}
.dxflHelpText_ModernoNeosys.dxflBHelpTextSys {
    padding: 2px 0 6px;
}

/* Ribbon */
.dxrControl_ModernoNeosys {
	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
	color: #2B2B2B;
}
.dxrControl_ModernoNeosys .dxr-tabContent {
    border: 1px solid #f0f0f0;
    border-top-width: 0;
    height: 144px;
    background: #ffffff;
}
.dxrControl_ModernoNeosys.dxr-grLabelsHidden .dxr-tabContent {
    height: 110px;
}
.dxrControl_ModernoNeosys.dxr-tabsHidden .dxr-tabContent {
    border-top-width: 1px;
}
.dxrControl_ModernoNeosys, 
.dxrControl_ModernoNeosys a {
    color: #2B2B2B;
}

.dxrControl_ModernoNeosys .dxr-itemDisabled.dxr-item,
.dxrControl_ModernoNeosys .dxr-groupLabelDisabled.dxr-groupLabel,
.dxrControl_ModernoNeosys .dxr-grExpBtn.dxr-grExpBtnDisabled,
.dxrControl_ModernoNeosys .dxr-grExpBtn.dxr-grExpBtnDisabled .dxr-img32,
.dxrControl_ModernoNeosys .dxr-minBtn.dxr-minBtnDisabled {
    cursor: default;
    color: #A6A6A6;
}

/* Ribbon Popup */
.dxrControl_ModernoNeosys .dxr-minPopupWindow {
    padding: 0;
}
.dxrControl_ModernoNeosys .dxr-minPopup.dxpc-mainDiv {
    border: 0;
}
.dxrControl_ModernoNeosys .dxr-groupPopup .dxpc-mainDiv {
    border: 1px solid #cfcfcf;
}

/* Ribbon Group List */
.dxrControl_ModernoNeosys .dxr-groupList {
    padding: 0;
    margin: 0;
    height: 100%;
}

.dxrControl_ModernoNeosys .dxr-groupList .dxr-groupSep {
    height: 100%;
    list-style: none;
}

.dxrControl_ModernoNeosys .dxr-groupList .dxr-groupSep b {
    display: block;
    margin: 4px 0;
    width: 1px;
    height: 136px;
    background: #d1d1d1;
}

.dxrControl_ModernoNeosys.dxr-grLabelsHidden .dxr-groupList .dxr-groupSep b {
    height: 102px;
}

/* Ribbon Groups */
.dxrControl_ModernoNeosys .dxr-groupList .dxr-group {
    margin: 3px 3px 0 3px;
    text-align: center;
}
.dxrControl_ModernoNeosys .dxr-groupContent {
    height: 110px;
}
.dxrControl_ModernoNeosys .dxr-groupLabel {
    text-align: center;
    color: #7e7e7e;
    line-height: 15px;
    padding: 8px 6px;
    overflow: hidden;
    white-space: nowrap;
    -ms-text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    text-overflow: ellipsis;
}
.dxrControl_ModernoNeosys .dxr-grExpBtn,
.dxrControl_ModernoNeosys .dxr-grExpBtn .dxr-img32 {
    cursor: pointer;
}
.dxrControl_ModernoNeosys .dxr-grExpBtn {
    height: 130px;
    text-align: center;
}
.dxrControl_ModernoNeosys .dxr-groupPopupWindow {
    padding: 2px 3px;
}

/* Ribbon Item Separator */
.dxrControl_ModernoNeosys .dxr-blRegItems .dxr-itemSep b {
    height: 23px;
    margin: 6px auto;
}
.dxrControl_ModernoNeosys .dxr-blLrgItems .dxr-itemSep b {
    height: 92px;
    margin: 8px auto;
}
.dxrControl_ModernoNeosys .dxr-blRegItems .dxr-itemSep b,
.dxrControl_ModernoNeosys .dxr-blLrgItems .dxr-itemSep b {
    display: block;
    background: #d1d1d1;
    width: 1px;
}
.dxrControl_ModernoNeosys .dxr-blRegItems .dxr-itemSep,
.dxrControl_ModernoNeosys .dxr-blLrgItems .dxr-itemSep {
    width: 3px;
}

/* Ribbon Item */
.dxrControl_ModernoNeosys .dxr-blRegItems .dxr-item,
.dxrControl_ModernoNeosys .dxr-blHorItems .dxr-item {
    height: 26px;
    padding: 2px 3px;
    margin-bottom: 3px;
}
.dxrControl_ModernoNeosys .dxr-blLrgItems .dxr-item,
.dxrControl_ModernoNeosys.dxr-grLabelsHidden .dxr-grExpBtn {
    height: 106px;
    text-align: center;
    overflow: hidden;
}
.dxrControl_ModernoNeosys .dxr-item {
    border: 1px solid transparent;
}
.dxrControl_ModernoNeosys .dxr-blRegItems .dxr-item {
    white-space: nowrap;
    overflow: hidden;
    margin: 2px;
}
.dxrControl_ModernoNeosys .dxr-blHorItems .dxr-item {
    white-space: nowrap;
    margin: 2px;
}
.dxrControl_ModernoNeosys .dxr-blHorItems .dxr-item .dxr-lblText {
    padding: 0 3px 0px 2px;
}
.dxrControl_ModernoNeosys .dxr-blHorItems .dxr-item .dxr-popOut {
    height: 100%;
    margin-left: 1px;
}

/* Ribbon Item Label */
.dxrControl_ModernoNeosys .dxr-blLrgItems .dxr-item .dxr-label {
    border-top: 1px solid transparent;
    width: 100%;
}
.dxrControl_ModernoNeosys .dxr-blLrgItems .dxr-item .dxr-label .dxr-lblContent,
.dxrControl_ModernoNeosys .dxr-grExpBtn .dxr-lblContent {
    margin: 4px 3px;
    text-align: center;
    vertical-align: middle;
}

.dxrControl_ModernoNeosys .dxr-blLrgItems .dxr-item.dxr-itemHover.dxr-ddMode .dxr-label {
    border-top: 1px solid #9FBD92;
}
.dxrControl_ModernoNeosys .dxr-blLrgItems .dxr-item.dxr-itemPressed.dxr-ddMode .dxr-label {
    border-top: 1px solid #cccccc;
}
.dxrControl_ModernoNeosys .dxr-blHorItems .dxr-item .dxr-label {
    margin: 3px 0;
}

/* Ribbon Item Label PopOut */
.dxrControl_ModernoNeosys .dxr-blRegItems .dxr-item .dxr-popOut,
.dxrControl_ModernoNeosys .dxr-blHorItems .dxr-item .dxr-popOut {
    padding: 13px 4px 12px;
    margin: -7px 0 -4px;
    border-left: 1px solid transparent;
    font-size: 0;
}
.dxrControl_ModernoNeosys .dxr-blLrgItems .dxr-item .dxr-label .dxr-popOut,
.dxrControl_ModernoNeosys .dxr-grExpBtn .dxr-popOut {
    margin: 0 5px;
}
.dxrControl_ModernoNeosys .dxr-blRegItems .dxr-item.dxr-itemHover.dxr-ddMode .dxr-label .dxr-popOut,
.dxrControl_ModernoNeosys .dxr-blHorItems .dxr-item.dxr-itemHover.dxr-ddMode .dxr-label .dxr-popOut {
    border-left: 1px solid #9FBD92;
}
.dxrControl_ModernoNeosys .dxr-blRegItems .dxr-item.dxr-itemPressed.dxr-ddMode .dxr-label .dxr-popOut,
.dxrControl_ModernoNeosys .dxr-blHorItems .dxr-item.dxr-itemPressed.dxr-ddMode .dxr-label .dxr-popOut {
    border-left: 1px solid #cccccc;
}

/* Ribbon Editors Item */
.dxrControl_ModernoNeosys .dxr-item.dxr-edtItem {
    height: 32px;
    border: 0;
    padding: 0;
}
.dxrControl_ModernoNeosys .dxr-item.dxr-edtItem .dxeTextBox_ModernoNeosys td.dxic
{
	padding: 3px 2px 5px 7px!important;
}
.dxrControl_ModernoNeosys .dxr-item.dxr-edtItem .dxeButtonEdit_ModernoNeosys td.dxic 
{
	padding: 3px 2px 5px 7px!important;
}
.dxrControl_ModernoNeosys .dxr-item.dxr-edtItem .dxeButtonEditButton_ModernoNeosys 
{
    padding: 10px 10px;
}
.dxrControl_ModernoNeosys .dxr-item.dxr-edtItem .dxeSpinIncButton_ModernoNeosys, 
.dxrControl_ModernoNeosys .dxr-item.dxr-edtItem .dxeSpinDecButton_ModernoNeosys 
{
    padding: 4px 10px;
}
.dxrControl_ModernoNeosys .dxr-item.dxr-edtItem .dxeSpinLargeIncButton_ModernoNeosys, 
.dxrControl_ModernoNeosys .dxr-item.dxr-edtItem .dxeSpinLargeDecButton_ModernoNeosys 
{
    padding: 9px 10px;
}
.dxrControl_ModernoNeosys .dxr-item.dxr-edtItem .dxeBase_ModernoNeosys .dxichCellSys,
.dxrControl_ModernoNeosys .dxr-item.dxr-edtItem .dxeBase_ModernoNeosys.dxichCellSys
{
    padding: 3px 3px 1px;
}
.dxrControl_ModernoNeosys .dxr-item.dxr-edtItem .dxeBase_ModernoNeosys .dxichTextCellSys
{
    padding: 2px 0 1px;
}
.dxrControl_ModernoNeosys .dxr-item.dxr-edtItem .dxr-label 
{
    padding-right: 3px;
}
/* Ribbon Item Types */
.dxrControl_ModernoNeosys .dxr-buttonItem,
.dxrControl_ModernoNeosys .dxr-grExpBtn 
{
    border-radius: 2px;
    border: 1px solid transparent;
    cursor: pointer;
    margin-top: 1px;
}
.dxrControl_ModernoNeosys .dxr-blLrgItems .dxr-buttonItem,
.dxrControl_ModernoNeosys .dxr-grExpBtn
 {
    min-width: 70px;
}
.dxrControl_ModernoNeosys .dxr-buttonItem.dxr-itemChecked,
.dxrControl_ModernoNeosys .dxr-buttonItem.dxr-itemPressed,
.dxrControl_ModernoNeosys .dxr-grExpBtn.dxr-grExpBtnPressed {
	border: 1px solid #cccccc;
	background: #dcdcdc;
	color: #a7a7a7;

	box-shadow: inset 0px 1px 1px 0px rgba(0,0,0,0.05);
	-webkit-box-shadow: inset 0px 1px 1px 0px rgba(0,0,0,0.05);
}
.dxrControl_ModernoNeosys .dxr-buttonItem.dxr-itemHover,
.dxrControl_ModernoNeosys .dxr-grExpBtn.dxr-grExpBtnHover {
	border: 1px solid #9FBD92;
	background: #1d85cd;
	background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pg0KPHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIHZpZXdCb3g9IjAgMCAxIDEiIHByZXNlcnZlQXNwZWN0UmF0aW89Im5vbmUiPg0KICA8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWQtdWNnZy1nZW5lcmF0ZWQiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIiB4MT0iMCUiIHkxPSIwJSIgeDI9IjAlIiB5Mj0iMTAwJSI+DQogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iIzFEODVDRCIgc3RvcC1vcGFjaXR5PSIxIi8+DQogICAgPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjMDg2Q0IzIiBzdG9wLW9wYWNpdHk9IjEiLz4NCiAgPC9saW5lYXJHcmFkaWVudD4NCiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4NCjwvc3ZnPg0K);
	background: -ms-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: -moz-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: -o-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: -webkit-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: linear-gradient(to bottom, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	color: white;

	box-shadow: inset 0px 1px 0px 0px rgba(255,255,255,0.35);
	-webkit-box-shadow: inset 0px 1px 0px 0px rgba(255,255,255,0.35);
}
.dxrControl_ModernoNeosys .dxr-img32,
.dxrControl_ModernoNeosys .dxr-blLrgItems .dxr-colorBtn .dxr-colorBtnNoImg32 {
    width: 32px;
    height: 32px;
    margin: 11px auto 10px auto;
}
.dxrControl_ModernoNeosys .dxr-img16,
.dxrControl_ModernoNeosys .dxr-blRegItems .dxr-colorBtn .dxr-colorBtnNoImg16,
.dxrControl_ModernoNeosys .dxr-blHorItems .dxr-colorBtn .dxr-colorBtnNoImg16 {
    width: 16px;
    height: 16px;
    margin: 5px;
}
.dxrControl_ModernoNeosys .dxr-ddImageContainer {
    display: inline-block;
}
.dxrControl_ModernoNeosys .dxr-blLrgItems .dxr-ddImageContainer {
    width: 100%;
}

/* Ribbon Color Button */
.dxrControl_ModernoNeosys .dxr-colorBtn .dxr-colorDiv {
    height: 4px;
    width: 16px;
    display: block;
    margin: -6px 0 2px 5px;
    position: relative;
}

.dxrControl_ModernoNeosys .dxr-blLrgItems .dxr-colorBtn .dxr-colorDiv {
    width: 32px;
    margin: -10px auto 6px auto;
}

.dxrControl_ModernoNeosys .dxr-blLrgItems .dxr-colorBtn .dxr-colorBtnNoImg32 {
    display: block;
    margin-bottom: 4px;
}

.dxrControl_ModernoNeosys .dxr-blLrgItems .dxr-colorBtn .dxr-colorBtnNoImg32 .dxr-colorDiv,
.dxrControl_ModernoNeosys .dxr-blRegItems .dxr-colorBtn .dxr-colorBtnNoImg16 .dxr-colorDiv,
.dxrControl_ModernoNeosys .dxr-blHorItems .dxr-colorBtn .dxr-colorBtnNoImg16 .dxr-colorDiv {
    height: 100%;
    width: 100%;
    margin: 0px;
}

.dxrControl_ModernoNeosys .dxpc-content.dxr-itemDDPopup {
    padding: 0;
}

.dxrControl_ModernoNeosys .dxeColorTable_ModernoNeosys {
    border: none;
}

.dxrControl_ModernoNeosys .dxr-minBtn {
    cursor: pointer;
    display: inline-block;
    font-size: 0;
    text-align:center;
    vertical-align: middle;

	border: 1px solid Transparent;
	padding: 1px;
	margin: 2px 2px 0 0;
}
.dxrControl_ModernoNeosys .dxr-minBtnHover {
	border: 1px solid #9FBD92;
	background: #1d85cd;
	background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pg0KPHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIHZpZXdCb3g9IjAgMCAxIDEiIHByZXNlcnZlQXNwZWN0UmF0aW89Im5vbmUiPg0KICA8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWQtdWNnZy1nZW5lcmF0ZWQiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIiB4MT0iMCUiIHkxPSIwJSIgeDI9IjAlIiB5Mj0iMTAwJSI+DQogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iIzFEODVDRCIgc3RvcC1vcGFjaXR5PSIxIi8+DQogICAgPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjMDg2Q0IzIiBzdG9wLW9wYWNpdHk9IjEiLz4NCiAgPC9saW5lYXJHcmFkaWVudD4NCiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4NCjwvc3ZnPg0K);
	background: -ms-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: -moz-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: -o-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: -webkit-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: linear-gradient(to bottom, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	color: white;
	border-radius: 2px;
	-webkit-border-radius: 2px;

	box-shadow: inset 0px 1px 0px 0px rgba(255,255,255,0.35), 0px 1px 3px 0px rgba(0,0,0,0.1);
	-webkit-box-shadow: inset 0px 1px 0px 0px rgba(255,255,255,0.35), 0px 1px 3px 0px rgba(0,0,0,0.1);
}
.dxrControl_ModernoNeosys .dxr-minBtnPressed {
	border: 1px solid #cccccc;
	background: #dcdcdc;
	border-radius: 2px;
	-webkit-border-radius: 2px;

	box-shadow: inset 0px 1px 0px 0px rgba(255,255,255,0.35), 0px 1px 3px 0px rgba(0,0,0,0.1);
	-webkit-box-shadow: inset 0px 1px 0px 0px rgba(255,255,255,0.35), 0px 1px 3px 0px rgba(0,0,0,0.1);
}
.dxrControl_ModernoNeosys .dxr-minBtn img {
    vertical-align: middle;
}

/* Ribbon TabControl */
.dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top .dxtc-leftIndent, 
.dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top .dxtc-spacer, 
.dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top .dxtc-rightIndent, 
.dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top .dxtc-sbWrapper, 
.dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top .dxtc-sbIndent, 
.dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top .dxtc-sbSpacer
.dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top .dxtc-spacer.dxr-fileTabSpacing {
    border-bottom-color: #f0f0f0;
}
.dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top .dxtc-tab, 
.dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top .dxtc-leftIndent, 
.dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top .dxtc-spacer, 
.dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top .dxtc-rightIndent, 
.dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top .dxtc-sbIndent, 
.dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top .dxtc-sbSpacer {
    padding-top: 0px;
}
.dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top .dxtc-tab {
}
.dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top .dxtc-tab.dxtc-tabHover {
}
.dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top .dxtc-activeTab {
}
.dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top .dxtc-tab .dxtc-link {
}
.dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top .dxtc-activeTab .dxtc-link {
}
.dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top .dxtc-tab.dxtc-tabHover .dxtc-link {
}
.dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top .dxr-fileTab .dxtc-link,
.dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top .dxr-fileTab.dxtc-tabHover .dxtc-link {
}
.dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top .dxr-fileTab {
    background: #e5e5e5;
}
.dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top .dxtc-tabHover.dxr-fileTab {
    background: #009C49;
}
.dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top .dxr-fileTabPressed.dxr-fileTab {
}
.dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top .dxtcLiteDisabled_ModernoNeosys .dxtc-link {
    color: #A6A6A6;
}
.dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top .dxtc-tab .dxtc-link,
.dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top .dxtc-activeTab .dxtc-link {
}
.dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top .dxtc-leftIndent {
    width: 0px;
}
.dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top .dxtc-rightIndent {
    text-align: right;
}

/* DocumentViewer Ribbon */
.dxrControl_ModernoNeosys .dxr-tmplItem .dxxrdvrPageNumberComboBox {
    width: 130px;
}
.dxrControl_ModernoNeosys .dxr-tmplItem .dxxrdvrCurrentPageLabel {
    padding: 0 0 4px 0;
    display: block;
}
.dxrControl_ModernoNeosys .dxr-tmplItem .dxxrdvrPageCountLabel {
    padding: 4px 0 0 0;
    display: block;
}

/* ASPxImageZoom */
.dxizControl_ModernoNeosys {
    display: none;
    border: 1px solid #d8d8d8;
}
.dxizControl_ModernoNeosys .dxiz-hint {
    padding: 3px;
    top: 0;
    position: absolute;
}
.dxizControl_ModernoNeosys .dxiz-hint > span {
    margin-left: 3px;
    color: gray;
	vertical-align: top;
    font: 12px Tahoma, Geneva, sans-serif;
}
.dxizControl_ModernoNeosys .dxiz-EWCloseButton {
	top: 0;
	right: 1px;
	cursor: pointer;
	position: absolute;
}
.dxizControl_ModernoNeosys .dxiz-expandWindow .dxpc-content {
    height: inherit;
}
.dxizControl_ModernoNeosys .dxiz-wrapper {
    position: relative;
}
.dxizControl_ModernoNeosys .dxiz-wrapper > img,
.dxizControl_ModernoNeosys .dxiz-clipPanel > img,
.dxizControl_ModernoNeosys .dxiz-expandWindow .dxpc-content > img {
    display: block;
}
.dxizControl_ModernoNeosys .dxiz-clipPanel > img {
    position: absolute;
}
.dxizControl_ModernoNeosys .dxiz-clipPanel {
    overflow: hidden;
    position: relative;
}
.dxizControl_ModernoNeosys .dxiz-clipPanel.dxiz-inside {
    top: 0;
    position: absolute;
    opacity: 0;
    filter: progid:DXImageTransform.Microsoft.Alpha(Style=0, Opacity=0);
}
.dxizControl_ModernoNeosys .dxpcLite .dxpc-content {
    padding: 0;
}

.dxizControl_ModernoNeosys .dxiz-lens {
    overflow: hidden;
    top: 0;
    position: absolute;
}
.dxizControl_ModernoNeosys .dxiz-lens > .dxiz-pc {
    opacity: 0;
    filter: progid:DXImageTransform.Microsoft.Alpha(Style=0, Opacity=0);
	z-index: 1;
    position: absolute;
}
.dxizControl_ModernoNeosys .dxiz-lens .dxiz-llp,
.dxizControl_ModernoNeosys .dxiz-lens .dxiz-lrp,
.dxizControl_ModernoNeosys .dxiz-lens .dxiz-ltp,
.dxizControl_ModernoNeosys .dxiz-lens .dxiz-lbp,
.dxizControl_ModernoNeosys .dxiz-lens .dxiz-lcp {
    position: absolute;
}
.dxizControl_ModernoNeosys .dxiz-lens .dxiz-lcp {
	background: white;
	outline: 1px solid #969292;
	filter: progid:DXImageTransform.Microsoft.Alpha(Style=0, Opacity=50);
	background: rgba(255,255,255,0.5);
    outline: 1px solid rgba(128,128,128,0.5);
}
.dxizControl_ModernoNeosys .dxiz-lens.outside .dxiz-lcp {
    background: transparent;
	outline: 1px solid black;
    outline: 1px solid rgba(0,0,0,0.5);
}
.dxizControl_ModernoNeosys .dxiz-lens.outside .dxiz-llp,
.dxizControl_ModernoNeosys .dxiz-lens.outside .dxiz-lrp,
.dxizControl_ModernoNeosys .dxiz-lens.outside .dxiz-ltp,
.dxizControl_ModernoNeosys .dxiz-lens.outside .dxiz-lbp {
    opacity: 0.25;
    filter: progid:DXImageTransform.Microsoft.Alpha(Style=0, Opacity=25);
    background-color: #000;
}
.dxizControl_ModernoNeosys .dxizLoadingPanel_ModernoNeosys {
    position: absolute;
}

.dxisControl_ModernoNeosys.dxis-zoomNavigator .dxis-nbItem {
	width: 75px;
    height: 75px;
}
