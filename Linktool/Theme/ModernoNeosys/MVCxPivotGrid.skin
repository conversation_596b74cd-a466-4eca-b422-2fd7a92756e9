<%@ Register TagPrefix="dx" Namespace="DevExpress.Data" Assembly="DevExpress.Data.v24.1, Version=24.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" %>
<%@ Register Assembly="DevExpress.Web.ASPxPivotGrid.v24.1, Version=24.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Namespace="DevExpress.Web.ASPxPivotGrid" TagPrefix="dx" %>
<%@ Register Assembly="DevExpress.PivotGrid.v24.1.Core, Version=24.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Namespace="DevExpress.XtraPivotGrid" TagPrefix="dx" %>
<%@ Register TagPrefix="dxmvc" Namespace="DevExpress.Web.Mvc" Assembly="DevExpress.Web.Mvc.v24.1, Version=24.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" %>

<dxmvc:MVCxPivotGrid runat="server" >
    <OptionsPager CurrentPageNumberFormat="{0}">
    </OptionsPager>

    <OptionsCustomization CustomizationWindowWidth="200" CustomizationWindowHeight="220"> 
    </OptionsCustomization>

    <Images SpriteCssFilePath="{0}/sprite.css">
        <TreeViewNodeLoadingPanel Url="PivotGrid/tvNodeLoading.gif"></TreeViewNodeLoadingPanel>
    </Images>

    <Styles CssPostfix="ModernoNeosys" CssFilePath="{0}/styles.css">
    </Styles>
</dxmvc:MVCxPivotGrid>

