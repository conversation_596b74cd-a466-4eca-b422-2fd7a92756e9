<%@ Register TagPrefix="dx" Namespace="DevExpress.Data" Assembly="DevExpress.Data.v24.1, Version=24.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" %>
<%@ Register Assembly="DevExpress.Web.ASPxHtmlEditor.v24.1, Version=24.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Namespace="DevExpress.Web.ASPxHtmlEditor" TagPrefix="dx" %>
<%@ Register TagPrefix="dxmvc" Namespace="DevExpress.Web.Mvc" Assembly="DevExpress.Web.Mvc.v24.1, Version=24.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" %>

<dxmvc:MVCxHtmlEditor runat="server" Width="723px" CssFilePath="{0}/styles.css" CssPostfix="ModernoNeosys">
    <Styles CssPostfix="ModernoNeosys" CssFilePath="{0}/styles.css">
    </Styles>

    <StylesToolbars>
        <Toolbar ItemSpacing="1px"></Toolbar>
    </StylesToolbars>

    <StylesFileManager>
        <Toolbar Height="46px"></Toolbar>
        <UploadPanel Height="50px"></UploadPanel>
    </StylesFileManager>

    <Images SpriteCssFilePath="{0}/sprite.css">
    </Images>

    <ImagesFileManager>
        <FolderContainerNodeLoadingPanel Url="Web/tvNodeLoading.gif"></FolderContainerNodeLoadingPanel>
    </ImagesFileManager>
</dxmvc:MVCxHtmlEditor>

