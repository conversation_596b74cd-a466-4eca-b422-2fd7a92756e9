<%@ Register TagPrefix="dx" Namespace="DevExpress.Data" Assembly="DevExpress.Data.v24.1, Version=24.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" %>
<%@ Register TagPrefix="dx" Namespace="DevExpress.Web" Assembly="DevExpress.Web.v24.1, Version=24.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" %>

<%@ Register TagPrefix="dxmvc" Namespace="DevExpress.Web.Mvc" Assembly="DevExpress.Web.Mvc.v24.1, Version=24.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" %>

<dxmvc:MVCxGridLookup runat="server" SpriteCssFilePath="{0}/sprite.css" CssPostfix="ModernoNeosys" CssFilePath="{0}/styles.css">
    <GridViewProperties>
        <SettingsPager CurrentPageNumberFormat="{0}"></SettingsPager>
        <Settings GridLines="Vertical"></Settings>

        <SettingsPopup>
            <HeaderFilter MinWidth="280px" MinHeight="150px"></HeaderFilter>
        </SettingsPopup>
    </GridViewProperties>

    <GridViewImages SpriteCssFilePath="{0}/sprite.css">
    </GridViewImages>

</dxmvc:MVCxGridLookup>

