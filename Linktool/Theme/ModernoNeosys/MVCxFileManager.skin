<%@ Register TagPrefix="dx" Namespace="DevExpress.Data" Assembly="DevExpress.Data.v24.1, Version=24.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" %>
<%@ Register TagPrefix="dx" Namespace="DevExpress.Web" Assembly="DevExpress.Web.v24.1, Version=24.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" %>
<%@ Register TagPrefix="dxmvc" Namespace="DevExpress.Web.Mvc" Assembly="DevExpress.Web.Mvc.v24.1, Version=24.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" %>

<dxmvc:MVCxFileManager runat="server" CssFilePath="{0}/styles.css" CssPostfix="ModernoNeosys">
    <Styles CssPostfix="ModernoNeosys" CssFilePath="{0}/styles.css">
        <Toolbar Height="46px"></Toolbar>
        <UploadPanel Height="50px"></UploadPanel>
    </Styles>
    <StylesDetailsView>
        <CommandColumn Width="46px"></CommandColumn>
    </StylesDetailsView>
    <SettingsFileList>
        <DetailsViewSettings ThumbnailSize="24px">
        </DetailsViewSettings>
    </SettingsFileList>

    <Images SpriteCssFilePath="{0}/sprite.css">
        <FolderContainerNodeLoadingPanel Url="Web/tvNodeLoading.gif"></FolderContainerNodeLoadingPanel>
    </Images>
</dxmvc:MVCxFileManager>

