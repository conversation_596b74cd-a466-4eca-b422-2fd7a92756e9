using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Xml.Linq;

namespace NeoSysLCS.Linktool.Helpers
{
    public class XMLHelper
    {
        public static string GetUebersetzungFromXmlField(XDocument xDocument, string element, int _currentLang)
        {
            string uebersetzung = xDocument.Root.Descendants("Sprache").Where(x => x.FirstAttribute.Value == _currentLang.ToString()).Descendants(element).Single().Value;
            if (string.IsNullOrEmpty(uebersetzung))
            {
                uebersetzung = (xDocument.Descendants("Sprache").Where(x => x.FirstAttribute.Value == "1").Descendants(element).Single().Value != "" ?
                    uebersetzung = xDocument.Descendants("Sprache").Where(x => x.FirstAttribute.Value == "1").Descendants(element).Single().Value : null)
                    ?? (xDocument.Descendants("Sprache").Where(x => x.FirstAttribute.Value == "2").Descendants(element).Single().Value != "" ?
                    uebersetzung = xDocument.Descendants("Sprache").Where(x => x.FirstAttribute.Value == "2").Descendants(element).Single().Value : null)
                    ?? (xDocument.Descendants("Sprache").Where(x => x.FirstAttribute.Value == "3").Descendants(element).Single().Value != "" ?
                    uebersetzung = xDocument.Descendants("Sprache").Where(x => x.FirstAttribute.Value == "3").Descendants(element).Single().Value : null)
                    ?? (xDocument.Descendants("Sprache").Where(x => x.FirstAttribute.Value == "4").Descendants(element).Single().Value != "" ?
                    uebersetzung = xDocument.Descendants("Sprache").Where(x => x.FirstAttribute.Value == "4").Descendants(element).Single().Value : null)
                    ?? "Keine Übersetzung vorhanden.";
            }

            return uebersetzung;
        }

        public static XDocument CreateNewUebersetzung(List<string> elements)
        {
            XDocument uebersetzung = new XDocument(
                new XElement("Uebersetzung",
                    new XElement("Sprache", new XAttribute("ID", 1)),
                    new XElement("Sprache", new XAttribute("ID", 2)),
                    new XElement("Sprache", new XAttribute("ID", 3)),
                    new XElement("Sprache", new XAttribute("ID", 4))));


            foreach (var element in elements)
            {
                uebersetzung.Element("Uebersetzung").Elements("Sprache").Where(e => e.Attribute("ID").Value == "1").FirstOrDefault().Add(new XElement(element));
                uebersetzung.Element("Uebersetzung").Elements("Sprache").Where(e => e.Attribute("ID").Value == "2").FirstOrDefault().Add(new XElement(element));
                uebersetzung.Element("Uebersetzung").Elements("Sprache").Where(e => e.Attribute("ID").Value == "3").FirstOrDefault().Add(new XElement(element));
                uebersetzung.Element("Uebersetzung").Elements("Sprache").Where(e => e.Attribute("ID").Value == "4").FirstOrDefault().Add(new XElement(element));
            }

            return uebersetzung;
        }

        public static string UpdateUebersetzung(XDocument xDocument, Dictionary<string, string> elements, int spracheID)
        {
            foreach (var element in elements)
            {
                xDocument.Element("Uebersetzung").Elements("Sprache")
                    .Where(e => e.Attribute("ID").Value == spracheID.ToString()).FirstOrDefault()
                    .SetElementValue(element.Key, element.Value ?? "");
            }

            return xDocument.ToString();
        }

        public static string GetAllUebersetzungFromXmlField(XDocument xDocument, string element, int _currentLang)
        {
            string uebersetzung = xDocument.Root.Descendants("Sprache").Where(x => x.FirstAttribute.Value == _currentLang.ToString()).Descendants(element).Single().Value;
            return uebersetzung;
        }
    }
}