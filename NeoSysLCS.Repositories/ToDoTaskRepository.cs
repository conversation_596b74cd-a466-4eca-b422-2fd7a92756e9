using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Linq.Expressions;
using System.Web;
using System.Web.WebPages;
using System.Xml.Linq;
using Microsoft.AspNet.Identity;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories.Helper;
using NeoSysLCS.Repositories.ViewModels;


namespace NeoSysLCS.Repositories
{
    /// <summary>
    /// The repository of Task To-do's
    /// </summary>
    public class ToDoTaskRepository
    {
        private readonly IGenericRepository<ToDoTask> _genericToDoTaskRepository;
        private readonly NeoSysLCS_Dev _context;
        private readonly UnitOfWork unitOfWork;

        /// <summary>
        /// Initializes a new instance.
        /// </summary>
        /// <param name="context">The context.</param>
        public ToDoTaskRepository(NeoSysLCS_Dev context)
        {
            _genericToDoTaskRepository = new GenericRepository<ToDoTask>(context);
            _context = context;
            unitOfWork = new UnitOfWork();
        }

        public void Insert(ToDoTask item)
        {
            _genericToDoTaskRepository.Insert(item);
        }


        /// <summary>
        /// Marks ToDoTask as synced.
        /// </summary>
        public void MaskToDoTaskAsSynced(string taskId)
        {
            // mark task as synced;
        }
    }
}