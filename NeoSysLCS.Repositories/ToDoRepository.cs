using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Linq.Expressions;
using System.Web;
using System.Web.WebPages;
using System.Xml.Linq;
using Microsoft.AspNet.Identity;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories.Helper;
using NeoSysLCS.Repositories.ViewModels;


namespace NeoSysLCS.Repositories
{
    /// <summary>
    /// The repository of To-do's
    /// </summary>
    public class ToDoRepository
    {
        private readonly IGenericRepository<Kunde> _genericKundeRepository;
        private readonly IGenericRepository<Offer> _genericOfferRepository;
        private readonly NeoSysLCS_Dev _context;
        private readonly UnitOfWork unitOfWork;

        /// <summary>
        /// Initializes a new instance.
        /// </summary>
        /// <param name="context">The context.</param>
        public ToDoRepository(NeoSysLCS_Dev context)
        {
            _genericKundeRepository = new GenericRepository<Kunde>(context);
            _genericOfferRepository = new GenericRepository<Offer>(context);
            _context = context;
            unitOfWork = new UnitOfWork();
        }
        

        /// <summary>
        /// Gets all to-do view models.
        /// </summary>
        /// <returns>Returns the view models</returns>
        public IQueryable<ToDoViewModel> GetAllToDoViewModels()
        {
            return GetAllToDoViewModels("");
        }

        /// <summary>
        /// Gets to-do view models for User.
        /// </summary>
        /// <returns>Returns the view models</returns>
        public IQueryable<ToDoViewModel> GetToDoViewModelsForUser(string userId)
        {
            return GetAllToDoViewModels(userId);
        }

        /// <summary>
        /// Gets all to-do view models.
        /// </summary>
        /// <returns>Returns the view models</returns>
        public IQueryable<ToDoViewModel> GetAllToDoViewModels(string userId)
        {

            var todoCustomers = from x in _context.Kunden
                where 
                    (x.ProjektleiterID == userId &&
                        (
                            x.Status == KundeStatus.Analysis ||
                            x.Status == KundeStatus.Inquiry || 
                            x.Status == KundeStatus.DocumentReceived ||                        
                            x.Status == KundeStatus.QSClosed
                         )
                    )
                    || 
                    (
                       x.ProjektleiterQSID == userId &&
                       x.Status == KundeStatus.QS
                    )
                select x;

            todoCustomers.ToList();

            var inTenDays = DateTime.Today.AddDays(10);
            var todoOffers = from y in _context.Offer
                where (y.Status == OfferStatus.New || y.Status == OfferStatus.Sent || y.Status == OfferStatus.Qs) && y.ProjectManagerID == userId
                             select y;

            List<ToDoViewModel> kundeResult = todoCustomers.Include(c => c.Projektleiter).ToList().Where(x => FilterCustomerAndConstructMessageAndDueDate(x, userId).Item1).Select(x =>
                {
                    var messageAndDueDate = FilterCustomerAndConstructMessageAndDueDate(x, userId);
                    return new ToDoViewModel()
                    {
                        Name = x.Name,
                        ErstelltAm = x.ErstelltAm,
                        ErstelltVonID = x.ErstelltVonID,
                        ErstelltVon = (x.ErstelltVon != null) ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname) : null,
                        BearbeitetAm = x.BearbeitetAm,
                        BearbeitetVonID = x.BearbeitetVonID,
                        BearbeitetVon = (x.BearbeitetVon != null) ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname) : null,
                        
                        ProjektleiterID = x.ProjektleiterID,
                        EducationDate = x.EducationDate,
                        ProjektleiterName = (x.Projektleiter != null) ? (x.Projektleiter.Vorname + " " + x.Projektleiter.Nachname) : null,
                        IsOffer = false,
                        InquiryDate = x.InquiryDate,
                        DocumentReceivedDate = x.DocumentReceivedDate,
                        ContractCreatedDate = x.ContractCreatedDate,
                        AnalyseDate = x.AnalysisDate,
                        DueDate = messageAndDueDate.Item3,
                        Message = messageAndDueDate.Item2
                    };
                }
            ).ToList();

            var offers = todoOffers.Include(o => o.ProjectManager).Include(o => o.ExistingCustomer).ToList().Where(x => FilterOfferAndConstructMessageAndDueDate(x).Item1).ToList();

            List<ToDoViewModel> offerResult = todoOffers.Include(o => o.ProjectManager).Include(o => o.ExistingCustomer).ToList().Where(x => FilterOfferAndConstructMessageAndDueDate(x).Item1).Select(y =>
                {
                    var messageAndDueDate = FilterOfferAndConstructMessageAndDueDate(y);
                    return new ToDoViewModel()
                    {
                        Name = y.ExistingCustomer != null ? y.ExistingCustomer.Name : y.NewCustomerName,
                        ErstelltAm = y.ErstelltAm,
                        ErstelltVonID = y.ErstelltVonID,
                        ErstelltVon = (y.ErstelltVon != null) ? (y.ErstelltVon.Vorname + " " + y.ErstelltVon.Nachname) : null,
                        BearbeitetAm = y.BearbeitetAm,
                        BearbeitetVonID = y.BearbeitetVonID,
                        BearbeitetVon = (y.BearbeitetVon != null) ? (y.BearbeitetVon.Vorname + " " + y.BearbeitetVon.Nachname) : null,
                        ProjektleiterID = y.ProjectManagerID,
                        ProjektleiterName = (y.ProjectManager != null) ? (y.ProjectManager.Vorname + " " + y.ProjectManager.Nachname) : null,
                        IsOffer = true,
                        OfferConsultationDate = y.ConsultationAt,
                        DueDate = messageAndDueDate.Item3,
                        Message = messageAndDueDate.Item2
                    };
                }
            ).ToList();

            return kundeResult.Concat(offerResult).AsQueryable();

        }

        private Tuple<bool, string, DateTime?> FilterCustomerAndConstructMessageAndDueDate(Kunde customer, string userId)
        {
            if (customer.Status == KundeStatus.Analysis)
            {
                //  (Datum Analyse +20 Tage) Ab Datum Analyse und Status in Analyse, Due Date: Datum Dokument Bericht als Datum anzeigen
                if (
                    customer.AnalysisDate?.AddDays(20) <= DateTime.Today &&
                    customer.AnalysisDate?.AddDays(45) > DateTime.Today &&
                    customer.AnalysisStatus == KundeAnalysisStatus.Analysis
                    )
                {
                    return Tuple.Create(true, "Bericht erstellen und Dokument freigeben", customer.DocumentReportDate);
                }

                // (Datum Analyse +30 Tage) Ab Datum Analyse und Status nicht in "Vertrag erstellt", Due Date: Vertrag erstellt
                if (
                    customer.AnalysisDate?.AddDays(30) <= DateTime.Today &&
                    customer.AnalysisStatus != KundeAnalysisStatus.ContractCreated
                    )
                {
                    return Tuple.Create(true, "Vertrag Kunde " + customer.Name + " erstellen", customer.ContractCreatedDate);
                }

                // (Datum Schulung +45 Tage) Ab Datum Analyse und Status in Analyse, Due Date: Datum Schuiung
                if (
                    customer.AnalysisDate?.AddDays(45) <= DateTime.Today && 
                    customer.AnalysisStatus == KundeAnalysisStatus.Analysis
                    )
                {
                    return Tuple.Create(true, "Schulung Kunde " + customer.Name + "durchführen", customer.EducationDate);
                }

                // (Datum Analyse +1 Jahr) If Status != Vertrag zurück und Heute > Datum "Vertrag erstellt" + 60 Tage
                if (
                    customer.AnalysisStatus != KundeAnalysisStatus.ContractReturned && 
                    customer.ContractCreatedDate?.AddDays(60) < DateTime.Today
                    )
                {
                    return Tuple.Create(true, "Vertrag Kunde " + customer.Name + " nicht zurück", customer.ContractCreatedDate);
                }


            }
            // With document status "Anfrage"
            if (customer.Status == KundeStatus.Inquiry)
            {
                return Tuple.Create<bool, string, DateTime?>(true, "Anfrage Kunde " + customer.Name + " aktualisieren", null);
            }

            // With document status "Dokument erhalten"
            if (customer.Status == KundeStatus.DocumentReceived)
            {
                return Tuple.Create<bool, string, DateTime?>(true, "Kundendokument " + customer.Name + " aktualisieren", null);
            }

            // With document status "QS" and Current User = Projektleiter QS
            if (customer.Status == KundeStatus.QS && customer.ProjektleiterQSID == userId)
            {
                return Tuple.Create<bool, string, DateTime?>(true, "QS abschliessen", null);
            }

            // With document status "QS" and Current User = Projektleiter QS
            if (customer.Status == KundeStatus.QSClosed)
            {
                return Tuple.Create<bool, string, DateTime?>(true, "Kundendokument versenden", null);
            }

            return Tuple.Create<bool, string, DateTime?>(false, "", null);

        }

        private Tuple<bool, string, DateTime?> FilterOfferAndConstructMessageAndDueDate(Offer offer)
        {
            if ((offer.Status == OfferStatus.New || offer.Status == OfferStatus.Qs) && offer.ReceivedAt <= DateTime.Today)
            {
                return Tuple.Create<bool, string, DateTime?>(true, "Offerte erstellen", null);
            }
            if (offer.ConsultationAt < DateTime.Today.AddDays(10) && offer.Status == OfferStatus.New)
            {
                return Tuple.Create<bool, string, DateTime?>(true, "Rückfrage Offerte", null);
            }
            return Tuple.Create<bool, string, DateTime?>(false, "", null);
        }



        private void logTime4Debug()
        {
            DateTime localDate = DateTime.Now;
            String cultureName = "fr-FR";
            var culture = new CultureInfo(cultureName);
            Debug.WriteLine("Local date and time: {0}, {1:G}", localDate.ToString(culture), localDate.Kind);
        }
    }
}