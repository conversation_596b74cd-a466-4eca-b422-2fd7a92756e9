using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Web;
using System.Web.WebPages;
using Microsoft.AspNet.Identity;
using Microsoft.AspNet.Identity.EntityFramework;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories.Helper;
using NeoSysLCS.Repositories.ViewModels;


namespace NeoSysLCS.Repositories
{
    /// <summary>
    /// The repository for users
    /// </summary>
    public class UserRepository
    {
        private readonly IGenericRepository<ApplicationUser> _genericUserRepository;
        private readonly IGenericRepository<Standort> _genericStandortRepository;
        private readonly NeoSysLCS_Dev _context;


        /// <summary>
        /// Initializes a new instance.
        /// </summary>
        /// <param name="contextCandidate">The context candidate.</param>
        public UserRepository(NeoSysLCS_Dev contextCandidate)
        {
            _genericUserRepository = new GenericRepository<ApplicationUser>(contextCandidate);
            _genericStandortRepository = new GenericRepository<Standort>(contextCandidate);
            _context = contextCandidate;
            
        }

        /// <summary>
        /// Gets the user view model by kunde id.
        /// </summary>
        /// <param name="kundeId">The kunde id.</param>
        /// <returns>Returns the view models</returns>
        public IQueryable<UserViewModel> GetUserViewModelByKundeId(List<ApplicationRole> availableRoles, int? kundeId, bool displayDeactivated = false)
        {
            var userViewModels = new List<UserViewModel>();

            var users = displayDeactivated ? _genericUserRepository.Get(u => u.KundeID == kundeId, null, "ErstelltVon,Roles,WriteStandorte") : _genericUserRepository.Get(u => u.KundeID == kundeId && !u.LockoutEndDateUtc.HasValue, null, "ErstelltVon,Roles,WriteStandorte");

            foreach (var user in users)
            {
                var userViewModel = new UserViewModel
                {
                    UserName = user.UserName,
                    Vorname = user.Vorname,
                    Nachname = user.Nachname,
                    Id = user.Id,
                    Email = user.Email,
                    SpracheID = (int)user.SpracheID,
                    KundeID = (int)user.KundeID,
                    ErstelltVonID = user.ErstelltVonID,
                    BearbeitetVonID = user.BearbeitetVonID,
                    LockoutEndDate = (user.LockoutEndDateUtc == null),
                    WriteStandorte = user.WriteStandorte != null ? (from x in _context.ApplicationUsers
                                                                    where x.Id == user.Id
                                                                    from z in x.WriteStandorte
                                                                    select new StandortViewModel()
                                                                    {
                                                                        StandortID = z.StandortID,
                                                                        Name = z.Name
                                                                    }).ToList() : null,
                    Hint = Resources.Properties.Resources.View_UserAdmin_Hint,
                    NewsletterPeriod = user.NewsletterPeriod ?? 6,
                    NewsletterPeriodGrid = user.NewsletterPeriod,
                    TermsAccepted = user.TermsAccepted,
                    IsTermsAccepted = (user.TermsAccepted != null),
                };

                if (user.BearbeitetVon != null)
                {
                    userViewModel.BearbeitetVon = user.BearbeitetVon.FullName;
                    userViewModel.BearbeitetAm = user.BearbeitetAm;
                }

                if (user.ErstelltVon != null)
                {
                    userViewModel.ErstelltVon = user.ErstelltVon.FullName;
                    userViewModel.ErstelltAm = user.ErstelltAm;
                }

                string usersRoleNames = "";
                string usersDisplayRoleNames = "";
                List<ApplicationRoleViewModel> applicationRoleViewModels = new List<ApplicationRoleViewModel>();
                foreach (var role in user.Roles)
                {
                    foreach (var roleCandidate in availableRoles)
                    {
                        if (role.RoleId == roleCandidate.Id)
                        {
                            usersRoleNames += roleCandidate.Name + ",";
                            usersDisplayRoleNames += roleCandidate.Displayname + ",";
                            applicationRoleViewModels.Add(new ApplicationRoleViewModel() { Name = roleCandidate.Name, Displayname = roleCandidate.Displayname });
                            break;
                        }
                    }
                }

                if (!usersRoleNames.IsEmpty() && !usersDisplayRoleNames.IsEmpty())
                {
                    userViewModel.GUIRoles = usersRoleNames.Substring(0, usersRoleNames.Length - 1);
                    userViewModel.Roles = usersDisplayRoleNames.Substring(0, usersDisplayRoleNames.Length - 1);
                }
                userViewModel.NewsletterRole = usersRoleNames.Contains(Role.Newsletter);

                userViewModel.ApplicationRoles = applicationRoleViewModels.AsQueryable();

                userViewModels.Add(userViewModel);
            }

            return userViewModels.AsQueryable();
        }

        public ApplicationUser GetById(string id)
        {
            return _genericUserRepository.Get(e => e.Id == id).FirstOrDefault();
        }

        /// <summary>
        /// Gets all user view models.
        /// </summary>
        /// <param name="userid">The userid.</param>
        /// <returns>Returns the view models</returns>
        public IQueryable<UserViewModel> GetAllUserViewModels(List<ApplicationRole> availableRoles, string userid = "", bool displayAll = false)
        {
            IEnumerable<ApplicationUser> users = null;
            users = userid == "" ? 
                (displayAll ? _genericUserRepository.Get(null, null, "ErstelltVon,Roles,WriteStandorte,Kunde,Sprache") : _genericUserRepository.Get(u => !u.LockoutEndDateUtc.HasValue, null, "ErstelltVon,Roles,WriteStandorte,Kunde,Sprache")) 
                : (displayAll ? _genericUserRepository.Get(u => u.Id == userid, null, "ErstelltVon,Roles,WriteStandorte,Kunde,Sprache") : _genericUserRepository.Get(u => u.Id == userid && !u.LockoutEndDateUtc.HasValue, null, "ErstelltVon,Roles,WriteStandorte,Kunde,Sprache"));

            var userViewModels = new List<UserViewModel>();

            foreach (var user in users)
            {
                var userViewModel = new UserViewModel
                {
                    UserName = user.UserName,
                    Vorname = user.Vorname,
                    Nachname = user.Nachname,
                    Id = user.Id,
                    Email = user.Email,
                    SpracheID = (int)user.SpracheID,
                    Sprache = user.Sprache?.Name,
                    KundeID = (int)user.KundeID,
                    Kundename = user.Kunde?.Name,
                    ErstelltVonID = user.ErstelltVonID,
                    BearbeitetVonID = user.BearbeitetVonID,
                    LockoutEndDate = (user.LockoutEndDateUtc == null),
                    WriteStandorte = user.WriteStandorte != null ? (from x in _context.ApplicationUsers
                                                                    where x.Id == user.Id
                                                                    from z in x.WriteStandorte
                                                                    select new StandortViewModel()
                                                                    {
                                                                        StandortID = z.StandortID,
                                                                        Name = z.Name
                                                                    }).ToList() : null,
                    Hint = Resources.Properties.Resources.View_UserAdmin_Hint,
                    NewsletterPeriod = user.NewsletterPeriod ?? 6,
                    NewsletterPeriodGrid = user.NewsletterPeriod,
                    TermsAccepted = user.TermsAccepted,
                    IsTermsAccepted = (user.TermsAccepted != null),
                };

                if (user.BearbeitetVon != null)
                {
                    userViewModel.BearbeitetVon = user.BearbeitetVon.FullName;
                    userViewModel.BearbeitetAm = user.BearbeitetAm;
                }
                else
                {
                    userViewModel.BearbeitetVon = "";
                }

                if (user.ErstelltVon != null)
                {
                    userViewModel.ErstelltVon = user.ErstelltVon.FullName;
                    userViewModel.ErstelltAm = user.ErstelltAm;
                }
                else
                {
                    userViewModel.ErstelltVon = "";
                }

                var usersRoleNames = "";
                var usersDisplayRoleNames = "";
                List<ApplicationRoleViewModel> applicationRoleViewModels = new List<ApplicationRoleViewModel>();
                foreach (var role in user.Roles)
                {
                    foreach (var roleCandidate in availableRoles)
                    {
                        if (role.RoleId == roleCandidate.Id)
                        {
                            usersRoleNames += roleCandidate.Name + ",";
                            usersDisplayRoleNames += roleCandidate.Displayname + ",";
                            applicationRoleViewModels.Add(new ApplicationRoleViewModel() { Name = roleCandidate.Name, Displayname = roleCandidate.Displayname });
                            break;
                        }
                    }
                }

                if (!usersRoleNames.IsEmpty() && !usersDisplayRoleNames.IsEmpty())
                {
                    userViewModel.GUIRoles = usersRoleNames.Substring(0, usersRoleNames.Length - 1);
                    userViewModel.Roles = usersDisplayRoleNames.Substring(0, usersDisplayRoleNames.Length - 1);

                }

                userViewModel.NewsletterRole = usersRoleNames.Contains(Role.Newsletter);

                userViewModel.ApplicationRoles = applicationRoleViewModels.AsQueryable();

                userViewModels.Add(userViewModel);
            }

            return userViewModels.AsQueryable();
        }

        public IQueryable<UserViewModel> GetAllUserViewModelsCurrentUserByStandortId(int standortId)
        {
            //var roleManager = new RoleManager<IdentityRole>(new RoleStore<IdentityRole>(new NeoSysLCS_Dev()));
            //var availableRoles = roleManager.Roles.ToList();

            Standort standort = _genericStandortRepository.GetByID(standortId);
            int kundeId = (int) standort.KundeID;

            IEnumerable<ApplicationUser> users = _genericUserRepository.Get(u => !u.LockoutEndDateUtc.HasValue && u.KundeID == kundeId, null, "ErstelltVon");

            var userViewModels = new List<UserViewModel>();

            foreach (var user in users)
            {
                var userViewModel = new UserViewModel
                {
                    UserName = user.UserName,
                    Vorname = user.Vorname,
                    Nachname = user.Nachname,
                    FullName = user.FullName,
                    Id = user.Id,
                    Email = user.Email,
                    SpracheID = (int)user.SpracheID,
                    KundeID = (int)user.KundeID,
                    ErstelltVonID = user.ErstelltVonID,
                    BearbeitetVonID = user.BearbeitetVonID,
                    LockoutEndDate = (user.LockoutEndDateUtc == null),
                    Hint = Resources.Properties.Resources.View_UserAdmin_Hint,
                    NewsletterPeriod = user.NewsletterPeriod ?? 6,
                    NewsletterPeriodGrid = user.NewsletterPeriod,
                    TermsAccepted = user.TermsAccepted,
                    IsTermsAccepted = (user.TermsAccepted != null),
                };

                if (user.BearbeitetVon != null)
                {
                    userViewModel.BearbeitetVon = user.BearbeitetVon.FullName;
                    userViewModel.BearbeitetAm = user.BearbeitetAm;
                }
                else
                {
                    userViewModel.BearbeitetVon = "";
                }

                if (user.ErstelltVon != null)
                {
                    userViewModel.ErstelltVon = user.ErstelltVon.FullName;
                    userViewModel.ErstelltAm = user.ErstelltAm;
                }
                else
                {
                    userViewModel.ErstelltVon = "";
                }

                //var usersRoleNames = "";
                //foreach (var role in user.Roles)
                //{
                //    foreach (var roleCandidate in availableRoles)
                //    {
                //        if (role.RoleId == roleCandidate.Id)
                //        {
                //            usersRoleNames += roleCandidate.Name + ",";
                //            break;
                //        }
                //    }
                //}
                //if (!usersRoleNames.IsEmpty())
                //    userViewModel.GUIRoles = usersRoleNames.Substring(0, usersRoleNames.Length - 1);

                userViewModels.Add(userViewModel);
            }

            return userViewModels.AsQueryable();
        }

        public IQueryable<ApplicationUser> GetApplicationUsersByKundeId(int kundeId)
        {
            IEnumerable<ApplicationUser> users = _genericUserRepository.Get(u => !u.LockoutEndDateUtc.HasValue && u.KundeID == kundeId, null, "ErstelltVon");
            return users.AsQueryable();
        }

        public UserViewModel GetUserViewModelById(string userId)
        {
            var roleManager = new RoleManager<IdentityRole>(new RoleStore<IdentityRole>(new NeoSysLCS_Dev()));
            var availableRoles = roleManager.Roles.ToList();

            var user = _genericUserRepository.Get(u => u.Id == userId, null, "ErstelltVon,Roles,WriteStandorte,ApplicationUserPrivacyPolicies").FirstOrDefault();

            var userViewModel = new UserViewModel()
            {
                UserName = user.UserName,
                Vorname = user.Vorname,
                Nachname = user.Nachname,
                Id = user.Id,
                Email = user.Email,
                SpracheID = (int)user.SpracheID,
                KundeID = (int)user.KundeID,
                ErstelltVonID = user.ErstelltVonID,
                BearbeitetVonID = user.BearbeitetVonID,
                //Roles = user.Roles,
                LockoutEndDate = (user.LockoutEndDateUtc == null),
                WriteStandorte = user.WriteStandorte != null ? (from x in _context.ApplicationUsers
                                                                where x.Id == user.Id
                                                                from z in x.WriteStandorte
                                                                select new StandortViewModel()
                                                                {
                                                                    StandortID = z.StandortID,
                                                                    Name = z.Name
                                                                }).ToList() : null,
                Hint = Resources.Properties.Resources.View_UserAdmin_Hint,
                NewsletterPeriod = user.NewsletterPeriod ?? 6,
                NewsletterPeriodGrid = user.NewsletterPeriod,
                TermsAccepted = user.TermsAccepted,
                IsTermsAccepted = (user.TermsAccepted != null),
                PrivacyPolicies = user.ApplicationUserPrivacyPolicies != null && user.ApplicationUserPrivacyPolicies.Any() ? user.ApplicationUserPrivacyPolicies.Select(item =>
                {
                    return new ApplicationUserPrivacyPolicyViewModel()
                    {
                        PrivacyPolicyID = item.PrivacyPolicyID,
                        UserID = item.UserID,
                        PrivacyPolicyVersion = item.PrivacyPolicyVersion,
                        AcceptedAt = item.AcceptedAt
                    };
                }).ToList() : null
            };

                if (user.BearbeitetVon != null)
                {
                    userViewModel.BearbeitetVon = user.BearbeitetVon.FullName;
                    userViewModel.BearbeitetAm = user.BearbeitetAm;
                }

                if (user.ErstelltVon != null)
                {
                    userViewModel.ErstelltVon = user.ErstelltVon.FullName;
                    userViewModel.ErstelltAm = user.ErstelltAm;
                }

                var usersRoleNames = "";
                foreach (var role in user.Roles)
                {
                    foreach (var roleCandidate in availableRoles)
                    {
                        if (role.RoleId == roleCandidate.Id)
                        {
                            usersRoleNames += roleCandidate.Name + ",";
                            break;
                        }
                    }
                }
                if (!usersRoleNames.IsEmpty())
                {
                    userViewModel.GUIRoles = usersRoleNames.Substring(0, usersRoleNames.Length - 1);
                }
                userViewModel.NewsletterRole = usersRoleNames.Contains(Role.Newsletter);

            return userViewModel;
        }

        /// <summary>
        /// Updates the user from the specified view model.
        /// </summary>
        /// <param name="UserViewModel">The user view model.</param>

        public void Update(ApplicationUser Appluser, DateTime? lockoutdate)
        {
            
            ApplicationUser user = _genericUserRepository.GetByID(Appluser.Id);
            user.UserName = Appluser.Email;
            user.Email = Appluser.Email;
            user.SpracheID = Appluser.SpracheID;
            user.Vorname = Appluser.Vorname;
            user.Nachname = Appluser.Nachname;
            user.KundeID = Appluser.KundeID;
            user.BearbeitetVonID = HttpContext.Current.User.Identity.GetUserId();
            user.BearbeitetAm = DateTime.Now;
            user.LockoutEndDateUtc = lockoutdate;
            user.WriteStandorte = new List<Standort>();
            user.NewsletterPeriod = Appluser.NewsletterPeriod;
            user.TermsAccepted = Appluser.TermsAccepted;


            _genericUserRepository.Update(user);


        }

        public IEnumerable<ApplicationUser> GetAllByShortcut(int shortcutId)
        {
            IEnumerable<ApplicationUser> user = _genericUserRepository.Get(u => u.Shortcuts.Any(tk => tk.ShortcutID == shortcutId), null, "Shortcuts");
            return user;
        }

        public string GetUserIdByEmail(string email)
        {
            var user = _genericUserRepository.Get(x => x.Email == email);
            if (user.Any())
            {
                return user.FirstOrDefault().Id;
            }

            else
            {
                return "";
            }
            

        }

        public ApplicationUser GetUserByEmail(string email)
        {
            return _genericUserRepository.Get(x => x.Email == email).FirstOrDefault();
        }

        /// <summary>
        /// Gets users for newsletter selection based on search criteria
        /// </summary>
        /// <param name="criteria">Search criteria</param>
        /// <returns>Users matching the criteria for newsletter selection</returns>
        public IQueryable<NewsletterUserSelectionViewModel> GetUsersForNewsletterSelection(NewsletterUserSearchViewModel criteria)
        {
            var newsletterRoleId = _context.ApplicationRoles
                .Where(r => r.Name == Role.Newsletter)
                .Select(r => r.Id)
                .FirstOrDefault();

            var query = _context.ApplicationUsers
                .Include("Kunde")
                .Include("Sprache")
                .Include("Roles")
                .Where(u => u.Roles.Any(r => r.RoleId == newsletterRoleId));

            // Apply active/inactive filter
            if (!criteria.IncludeInactive)
            {
                query = query.Where(u => !u.LockoutEndDateUtc.HasValue);
            }

            // Apply search filters
            if (!string.IsNullOrWhiteSpace(criteria.FirstName))
            {
                query = query.Where(u => u.Vorname.Contains(criteria.FirstName));
            }

            if (!string.IsNullOrWhiteSpace(criteria.LastName))
            {
                query = query.Where(u => u.Nachname.Contains(criteria.LastName));
            }

            if (!string.IsNullOrWhiteSpace(criteria.Email))
            {
                query = query.Where(u => u.Email.Contains(criteria.Email));
            }

            // Create the result query without using anonymous types in a way that EF can't translate
            var result = query.Select(u => new NewsletterUserSelectionViewModel
            {
                Id = u.Id,
                FirstName = u.Vorname,
                LastName = u.Nachname,
                Email = u.Email,
                CustomerName = u.Kunde.Name,
                Language = u.Sprache.Name,
                NewsletterPeriod = u.NewsletterPeriod,
                IsActive = !u.LockoutEndDateUtc.HasValue,
                LastNewsletterSent = _context.ApplicationUserNewsletterHistories
                    .Where(h => h.UserID == u.Id)
                    .OrderByDescending(h => h.DateSent)
                    .Select(h => (DateTime?)h.DateSent)
                    .FirstOrDefault()
            });

            return result;
        }

        public IEnumerable<ApplicationRoleViewModel> GetApplicationRoleComboBox(int currentUserKundeId)
        {
            List<ApplicationRoleViewModel> viewModels = new List<ApplicationRoleViewModel>();

            if (currentUserKundeId == 1)
            {
                viewModels.Add(new ApplicationRoleViewModel() { Name = "AUDITOR", Displayname = "Auditor" });
                viewModels.Add(new ApplicationRoleViewModel() { Name = "ACCOUNTABLE", Displayname = "Editor" });
                viewModels.Add(new ApplicationRoleViewModel() { Name = "OBJECTGURU", Displayname = "Objekt-Guru" });
                viewModels.Add(new ApplicationRoleViewModel() { Name = "PROJECTMANAGER", Displayname = "Projektleiter" });
                viewModels.Add(new ApplicationRoleViewModel() { Name = "ADMIN", Displayname = "Systemadmin Neosys" });
                viewModels.Add(new ApplicationRoleViewModel() { Name = "SUPERUSER", Displayname = "Systemadministrator" });
                viewModels.Add(new ApplicationRoleViewModel() { Name = "READONLY", Displayname = "User" });
                viewModels.Add(new ApplicationRoleViewModel() { Name = "NEWSLETTER", Displayname = "Newsletter" });
            }
            else
            {
                viewModels.Add(new ApplicationRoleViewModel() { Name = "AUDITOR", Displayname = "Auditor" });
                viewModels.Add(new ApplicationRoleViewModel() { Name = "ACCOUNTABLE", Displayname = "Editor" });
                viewModels.Add(new ApplicationRoleViewModel() { Name = "SUPERUSER", Displayname = "Systemadministrator" });
                viewModels.Add(new ApplicationRoleViewModel() { Name = "READONLY", Displayname = "User" });
                viewModels.Add(new ApplicationRoleViewModel() { Name = "NEWSLETTER", Displayname = "Newsletter" });
            }

            return viewModels;        
        }

    }
}