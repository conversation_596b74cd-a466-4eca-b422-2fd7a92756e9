using System;
using System.Collections.Generic;
using System.Data.Entity;
using NeoSysLCS.DomainModel.Models;
using System.Globalization;
using System.Linq;
using System.Web;
using System.Xml.Linq;
using Microsoft.AspNet.Identity;
using NeoSysLCS.Repositories.Helper;
using NeoSysLCS.Repositories.ViewModels;

namespace NeoSysLCS.Repositories
{
    public class ApplicationUserPrivacyPolicyRepository
    {
        private readonly NeoSysLCS_Dev _context;

        public ApplicationUserPrivacyPolicyRepository(NeoSysLCS_Dev context)
        {
            _context = context;
        }

        public void AddOrUpdatePolicy(ApplicationUserPrivacyPolicy policy)
        {
            try
            {
                var existingPolicy = _context.ApplicationUserPrivacyPolicy
                                            .FirstOrDefault(p => p.UserID == policy.UserID && p.PrivacyPolicyID == policy.PrivacyPolicyID);

                if (existingPolicy == null)
                {
                    _context.ApplicationUserPrivacyPolicy.Add(policy);
                }
                else
                {
                    existingPolicy.PrivacyPolicyVersion = policy.PrivacyPolicyVersion;
                    existingPolicy.AcceptedAt = policy.AcceptedAt;
                }

                _context.SaveChanges();
            }
            catch (System.Data.Entity.Validation.DbEntityValidationException e)
            {
                // This block is to gather details about the validation errors
                foreach (var eve in e.EntityValidationErrors)
                {
                    Console.WriteLine("Entity of type \"{0}\" in state \"{1}\" has the following validation errors:",
                        eve.Entry.Entity.GetType().Name, eve.Entry.State);
                    foreach (var ve in eve.ValidationErrors)
                    {
                        Console.WriteLine("- Property: \"{0}\", Error: \"{1}\"",
                            ve.PropertyName, ve.ErrorMessage);
                    }
                }
                throw;  // You can re-throw the exception if you cannot handle it
            }
        }

        // Method to check if a specific record exists
        public bool Exists(string userId, int privacyPolicyId, int privacyPolicyVersion)
        {
            return _context.ApplicationUserPrivacyPolicy.Any(aupp =>
                aupp.UserID == userId &&
                aupp.PrivacyPolicyID == privacyPolicyId &&
                aupp.PrivacyPolicyVersion == privacyPolicyVersion);
        }

    }
}