using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Web;
using Microsoft.AspNet.Identity;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories.ViewModels;
using System.Data.Entity.SqlServer;
using System.Xml.Linq;
using NeoSysLCS.Repositories.Helper;

namespace NeoSysLCS.Repositories
{
    /// <summary>
    /// The repository for kundendokument erlassfassungen
    /// </summary>
    public class KundendokumentErlassfassungenRepository
    {
        private readonly NeoSysLCS_Dev _context;
        private readonly IGenericRepository<KundendokumentErlassfassung> _genericKundendokumentErlassfassungRepository;
        private readonly IGenericRepository<Erlassfassung> _genericErlassfassungRepository;
        private readonly UnitOfWork _unitOfWork;
        private int _currentLang;

        /// <summary>
        /// Initializes a new instance.
        /// </summary>
        /// <param name="contextCandidate">The context candidate.</param>
        public KundendokumentErlassfassungenRepository(NeoSysLCS_Dev contextCandidate)
        {
            _context = contextCandidate;
            _genericKundendokumentErlassfassungRepository = new GenericRepository<KundendokumentErlassfassung>(contextCandidate);
            _genericErlassfassungRepository = new GenericRepository<Erlassfassung>(contextCandidate);
            _unitOfWork = new UnitOfWork(contextCandidate);

            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            Sprache sprache = _context.Sprachen.FirstOrDefault(s => s.Lokalisierung == language);
            _currentLang = sprache.SpracheID;
        }

        /// <summary>
        /// Updates the qs flags of the kundendokument erlassfassung.
        /// </summary>
        /// <param name="viewModel">The view model.</param>
        public void UpdateQs(KundendokumentErlassfassungViewModel viewModel)
        {
            var entity = _genericKundendokumentErlassfassungRepository.GetByID(viewModel.KundendokumentErlassfassungID);
            entity.QsFreigabe = viewModel.QsFreigabe;
            _genericKundendokumentErlassfassungRepository.Update(entity);
        }

        /// <summary>
        /// Gets the kundendokument erlassfassungen by kundendokument id.
        /// </summary>
        /// <param name="kundendokumentID">The kundendokument id.</param>
        /// <returns>Returns the kundendokument erlassfassungen</returns>
        public List<KundendokumentErlassfassung> GetKundendokumentErlassfassungenByKundendokument(int? kundendokumentID)
        {
            if (!kundendokumentID.HasValue)
            {
                return new List<KundendokumentErlassfassung>();
            }

            var kundendokumentErlassfassungen =
                    _genericKundendokumentErlassfassungRepository.Get(f => f.KundendokumentID == kundendokumentID,
                        null, "Erlassfassung").ToList();

            return kundendokumentErlassfassungen;
        }

        /// <summary>
        /// Gets the kundendokument erlassfassung view models by kundendokument.
        /// </summary>
        /// <param name="kundendokumentID">The kundendokument id.</param>
        /// <returns>Returns the view models.</returns>
        public IQueryable<KundendokumentErlassfassungViewModel> GetKundendokumentErlassfassungViewModelsByKundendokument(int? kundendokumentID)
        {
            int? kundenId = _genericKundendokumentErlassfassungRepository.GetKundeIdOfCurrentUser();

            var erlFassungen = (from x in _context.KundendokumentErlassfassungen
                                from erlF in _context.Erlassfassungen.Where(curErlF => curErlF.ErlassfassungID == x.ErlassfassungID)
                                from erl in _context.Erlasse.Where(curErl => curErl.ErlassID == erlF.ErlassID)
                                from hg in _context.Herausgebers.Where(curHg => curHg.HerausgeberID == erl.HerausgeberId)
                                where (kundenId == null && x.KundendokumentID == kundendokumentID) || (x.KundeID == kundenId && x.KundendokumentID == kundendokumentID)
                   select new KundendokumentErlassfassungViewModel()
                   {
                       KundendokumentErlassfassungID = x.KundendokumentErlassfassungID,
                       KundendokumentID = x.KundendokumentID,
                       Betroffen = x.Betroffen,
                       ErlassID = x.Erlassfassung.ErlassID,
                       HerausgeberID = x.Erlassfassung.Erlass.HerausgeberId,
                       Inkrafttretung = x.Erlassfassung.Inkrafttretung,
                       Beschluss = x.Erlassfassung.Beschluss,
                       Status = x.Status,
                       QsFreigabe = x.QsFreigabe,
                       ErlassfassungID = x.ErlassfassungID,
                       ErstelltAm = x.ErstelltAm,
                       ErstelltVon = (x.ErstelltVon != null) ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname) : null,
                       ErstelltVonID = x.ErstelltVonID,
                       BearbeitetAm = x.BearbeitetAm,
                       BearbeitetVonID = x.BearbeitetVonID,
                       BearbeitetVon = (x.BearbeitetVon != null) ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname) : null,
                       HerausgeberName = (_currentLang == 1 ? (hg.NameDE ?? hg.NameFR ?? hg.NameIT ?? hg.NameEN) : null) ??
                                     (_currentLang == 2 ? (hg.NameFR ?? hg.NameDE ?? hg.NameIT ?? hg.NameEN) : null) ??
                                     (_currentLang == 3 ? (hg.NameIT ?? hg.NameDE ?? hg.NameFR ?? hg.NameEN) : null) ??
                                     (_currentLang == 4 ? (hg.NameEN ?? hg.NameDE ?? hg.NameFR ?? hg.NameIT) : null),
                       erlassfassungUebersetzungen = erlF.Uebersetzung,
                       erlassUebersetzungen = erl.Uebersetzung,
                       Kommentar = x.Kommentar,
                   }).ToList();

            foreach (var erlFassung in erlFassungen)
            {
                XDocument uebersetzungErlassfassung = XDocument.Parse(erlFassung.erlassfassungUebersetzungen);
                erlFassung.BetroffenKommentar = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "BetroffenKommentar", _currentLang);
                erlFassung.NichtBetroffenKommentar = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "NichtBetroffenKommentar", _currentLang);
                erlFassung.InternerKommentar = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "InternerKommentar", _currentLang);
                erlFassung.Quelle = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "Quelle", _currentLang);
                erlFassung.InternerKommentar = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "InternerKommentar", _currentLang);

                XDocument uebersetzungErl = XDocument.Parse(erlFassung.erlassUebersetzungen);
                erlFassung.ErlassTitel = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErl, "Titel", _currentLang);
                erlFassung.ErlassAbkuerzung = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErl, "ErlassAbkuerzung", _currentLang);
            }

            return erlFassungen.AsQueryable();
        }

        public IQueryable<MasterKundendokumentErlassfassungViewModel> GetAllKundendokumentErlassfassungViewModels(List<int> kundenDokumentIDs, int spracheID)
        {
            int? kundenId = _genericKundendokumentErlassfassungRepository.GetKundeIdOfCurrentUser();

            var erlFassungen = (from x in _context.KundendokumentErlassfassungen
                                from erlF in _context.Erlassfassungen.Where(curErlF => curErlF.ErlassfassungID == x.ErlassfassungID)
                                from erl in _context.Erlasse.Where(curErl => curErl.ErlassID == erlF.ErlassID)
                                from hg in _context.Herausgebers.Where(curHg => curHg.HerausgeberID == erl.HerausgeberId)
                                from kd in _context.Kundendokument.Where(curKd => curKd.KundendokumentID == x.KundendokumentID)
                                from std in _context.Standorte.Where(curStd => curStd.StandortID == kd.StandortID)
                                where (kundenDokumentIDs.Contains(x.KundendokumentID))
                                select new MasterKundendokumentErlassfassungViewModel()
                                {
                                    KundendokumentErlassfassungID = x.KundendokumentErlassfassungID,
                                    KundendokumentID = x.KundendokumentID,
                                    StandortID = std.StandortID,
                                    StandortName = std.Name,
                                    Betroffen = x.Betroffen,
                                    ErlassID = x.Erlassfassung.ErlassID,
                                    ErlassSrNummer = x.Erlassfassung.Erlass.SrNummer ?? "",
                                    HerausgeberID = x.Erlassfassung.Erlass.HerausgeberId,
                                    Inkrafttretung = x.Erlassfassung.Inkrafttretung,
                                    Beschluss = x.Erlassfassung.Beschluss,
                                    Status = x.Status,
                                    QsFreigabe = x.QsFreigabe,
                                    ErlassfassungID = x.ErlassfassungID,
                                    ErstelltAm = x.ErstelltAm,
                                    ErstelltVon = (x.ErstelltVon != null) ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname) : null,
                                    ErstelltVonID = x.ErstelltVonID,
                                    BearbeitetAm = x.BearbeitetAm,
                                    BearbeitetVonID = x.BearbeitetVonID,
                                    BearbeitetVon = (x.BearbeitetVon != null) ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname) : null,
                                    HerausgeberName = (spracheID == 1 ? (hg.NameDE ?? hg.NameFR ?? hg.NameIT ?? hg.NameEN) : null) ??
                                                         (spracheID == 2 ? (hg.NameFR ?? hg.NameDE ?? hg.NameIT ?? hg.NameEN) : null) ??
                                                         (spracheID == 3 ? (hg.NameIT ?? hg.NameDE ?? hg.NameFR ?? hg.NameEN) : null) ??
                                                         (spracheID == 4 ? (hg.NameEN ?? hg.NameDE ?? hg.NameFR ?? hg.NameIT) : null),
                                    erlassfassungUebersetzungen = erlF.Uebersetzung,
                                    erlassUebersetzungen = erl.Uebersetzung,
                                    Kommentar = x.Kommentar,
                                    MassnahmeNewCount = _context.Massnahme
                                            .Where(m => m.OriginID == x.KundendokumentErlassfassungID && m.Status == MassnahmeStatus.New)
                                            .Count(),
                                    MassnahmeInProgressCount = _context.Massnahme
                                            .Where(m => m.OriginID == x.KundendokumentErlassfassungID && m.Status == MassnahmeStatus.InProgress)
                                            .Count(),
                                    MassnahmeFinishedCount = _context.Massnahme
                                            .Where(m => m.OriginID == x.KundendokumentErlassfassungID && m.Status == MassnahmeStatus.Finished)
                                            .Count()
                                }).ToList();

            foreach (var kudoErlassfassung in erlFassungen)
            {
                XDocument uebersetzungErlassfassung = XDocument.Parse(kudoErlassfassung.erlassfassungUebersetzungen);
                kudoErlassfassung.Quelle = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "Quelle", spracheID);
                kudoErlassfassung.InternerKommentar = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "InternerKommentar", spracheID);
                kudoErlassfassung.NichtBetroffenKommentar = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "NichtBetroffenKommentar", spracheID);
                if (kudoErlassfassung.Betroffen)
                {
                    kudoErlassfassung.RelevanterKommentar =
                        (XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "LinkBetroffenKommentar", spracheID) != "---"
                        ? XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "LinkBetroffenKommentar", spracheID)
                        : XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "BetroffenKommentar", spracheID));
                }
                else
                {
                    kudoErlassfassung.RelevanterKommentar =
                        XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "LinkNichtBetroffenKommentar", spracheID) != "---"
                        ? XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "LinkNichtBetroffenKommentar", spracheID)
                        : XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "NichtBetroffenKommentar", spracheID);
                }


                XDocument uebersetzungErlass = XDocument.Parse(kudoErlassfassung.erlassUebersetzungen);
                kudoErlassfassung.ErlassQuelle = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlass, "Quelle", spracheID);
                //kudoErlassfassung.InternerKommentar = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlass, "InternerKommentar", _currentLang);
                kudoErlassfassung.Kerninhalte = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlass, "Kerninhalte", spracheID);
                kudoErlassfassung.ErlassTitel = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlass, "Titel", spracheID);
                kudoErlassfassung.ErlassAbkuerzung = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlass, "ErlassAbkuerzung", spracheID);
            }

            return erlFassungen.AsQueryable();
        }

        public List<KundendokumentErlassfassungViewModel> GetKundendokumentErlassfassungViewModels(int? kundendokumentID, int spracheID)
        {
            int? kundenId = _genericKundendokumentErlassfassungRepository.GetKundeIdOfCurrentUser();
            var kudoErlassfassungen = (from x in _context.KundendokumentErlassfassungen
                                       from erlF in _context.Erlassfassungen.Where(curErlF => curErlF.ErlassfassungID == x.ErlassfassungID)
                                       from erl in _context.Erlasse.Where(curErl => curErl.ErlassID == erlF.ErlassID)
                                       from hg in _context.Herausgebers.Where(curHg => curHg.HerausgeberID == erl.HerausgeberId)
                                       where (kundenId == null && x.KundendokumentID == kundendokumentID) || (x.KundeID == kundenId && x.KundendokumentID == kundendokumentID)
                                       select new KundendokumentErlassfassungViewModel()
                                       {
                                           KundendokumentErlassfassungID = x.KundendokumentErlassfassungID,
                                           KundendokumentID = x.KundendokumentID,
                                           Betroffen = x.Betroffen,
                                           ErlassID = x.Erlassfassung.ErlassID,
                                           HerausgeberID = x.Erlassfassung.Erlass.HerausgeberId,
                                           Inkrafttretung = x.Erlassfassung.Inkrafttretung,
                                           Beschluss = x.Erlassfassung.Beschluss,
                                           Status = x.Status,
                                           QsFreigabe = x.QsFreigabe,
                                           ErlassfassungID = x.ErlassfassungID,
                                           ErstelltAm = x.ErstelltAm,
                                           ErstelltVon = (x.ErstelltVon != null) ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname) : null,
                                           ErstelltVonID = x.ErstelltVonID,
                                           BearbeitetAm = x.BearbeitetAm,
                                           BearbeitetVonID = x.BearbeitetVonID,
                                           BearbeitetVon = (x.BearbeitetVon != null) ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname) : null,
                                           HerausgeberName = (spracheID == 1 ? (hg.NameDE ?? hg.NameFR ?? hg.NameIT ?? hg.NameEN) : null) ??
                                                         (spracheID == 2 ? (hg.NameFR ?? hg.NameDE ?? hg.NameIT ?? hg.NameEN) : null) ??
                                                         (spracheID == 3 ? (hg.NameIT ?? hg.NameDE ?? hg.NameFR ?? hg.NameEN) : null) ??
                                                         (spracheID == 4 ? (hg.NameEN ?? hg.NameDE ?? hg.NameFR ?? hg.NameIT) : null),
                                           erlassfassungUebersetzungen = erlF.Uebersetzung,
                                           erlassUebersetzungen = erl.Uebersetzung,
                                           //ErlassQuelle = erl.Uebersetzung,
                                           ErlassSrNummer = x.Erlassfassung.Erlass.SrNummer ?? "",
                                           Kommentar = x.Kommentar,
                                           MassnahmeNewCount = _context.Massnahme
                                            .Where(m => m.OriginID == x.KundendokumentErlassfassungID && m.Status == MassnahmeStatus.New)
                                            .Count(),
                                           MassnahmeInProgressCount = _context.Massnahme
                                            .Where(m => m.OriginID == x.KundendokumentErlassfassungID && m.Status == MassnahmeStatus.InProgress)
                                            .Count(),
                                           MassnahmeFinishedCount = _context.Massnahme
                                            .Where(m => m.OriginID == x.KundendokumentErlassfassungID && m.Status == MassnahmeStatus.Finished)
                                            .Count()
                                       }).ToList();

            foreach (var kudoErlassfassung in kudoErlassfassungen)
            {
                XDocument uebersetzungErlassfassung = XDocument.Parse(kudoErlassfassung.erlassfassungUebersetzungen);
                kudoErlassfassung.Quelle = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "Quelle", spracheID);
                kudoErlassfassung.InternerKommentar = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "InternerKommentar", spracheID);
                kudoErlassfassung.NichtBetroffenKommentar = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "NichtBetroffenKommentar", spracheID);
                if (kudoErlassfassung.Betroffen)
                {
                    kudoErlassfassung.RelevanterKommentar =
                        (XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "LinkBetroffenKommentar", spracheID) != "---"
                        ? XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "LinkBetroffenKommentar", spracheID)
                        : XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "BetroffenKommentar", spracheID));
                }
                else
                {
                    kudoErlassfassung.RelevanterKommentar =
                        XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "LinkNichtBetroffenKommentar", spracheID) != "---"
                        ? XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "LinkNichtBetroffenKommentar", spracheID)
                        : XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "NichtBetroffenKommentar", spracheID);
                }


                XDocument uebersetzungErlass = XDocument.Parse(kudoErlassfassung.erlassUebersetzungen);
                kudoErlassfassung.ErlassQuelle = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlass, "Quelle", spracheID);
                //kudoErlassfassung.InternerKommentar = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlass, "InternerKommentar", _currentLang);
                kudoErlassfassung.Kerninhalte = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlass, "Kerninhalte", spracheID);
                kudoErlassfassung.ErlassTitel = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlass, "Titel", spracheID);
                kudoErlassfassung.ErlassAbkuerzung = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlass, "Abkuerzung", spracheID);
            }

            return kudoErlassfassungen;
        }

        /// <summary>
        /// Gets the kundendokument erlassfassung view models by kundendokument for the excel export.
        /// </summary>
        /// <param name="kundendokumentID">The kundendokument id.</param>
        /// <returns>Returns the view models.</returns>
        public IQueryable<KundendokumentErlassfassungViewModel> GetKundendokumentErlassfassungViewModelsByKundendokumentToExport(int? kundendokumentID)
        {
            int? kundenId = _genericKundendokumentErlassfassungRepository.GetKundeIdOfCurrentUser();
            var kudoErlassfassungen = (from x in _context.KundendokumentErlassfassungen
                   from erlF in _context.Erlassfassungen.Where(curErlf => curErlf.ErlassfassungID == x.ErlassfassungID)
                   from erl in _context.Erlasse.Where(curErl => curErl.ErlassID == erlF.ErlassID)
                   from hrg in _context.Herausgebers.Where(curHrg => curHrg.HerausgeberID == erl.HerausgeberId)
                   where (kundenId == null && x.KundendokumentID == kundendokumentID) || (x.KundeID == kundenId && x.KundendokumentID == kundendokumentID)
                   select new KundendokumentErlassfassungViewModel()
                   {
                       KundendokumentErlassfassungID = x.KundendokumentErlassfassungID,
                       HerausgeberID = hrg.HerausgeberID,
                       OrderID = 3,
                       ErlassSrNummer = erl.SrNummer ?? "Keine Nummer vorhanden",
                       ErlassSrNummerFR = erl.SrNummer ?? "Keine Nummer vorhanden",
                       ErlassSrNummerIT = erl.SrNummer ?? "Keine Nummer vorhanden",
                       ErlassSrNummerEN = erl.SrNummer ?? "Keine Nummer vorhanden",
                       erlassUebersetzungen = erl.Uebersetzung,
                       Beschluss = erlF.Beschluss,
                       erlassfassungUebersetzungen = erlF.Uebersetzung,
                       Inkrafttretung = erlF.Inkrafttretung,
                       ErlassfassungID = x.ErlassfassungID,
                       Betroffen = x.Betroffen,
                       Kommentar = x.Kommentar,
                       Status = x.Status,
                       StatusFR = x.Status,
                       StatusIT = x.Status,
                       StatusEN = x.Status,
                       RelevanterKommentarLinkText = "Info",
                       RelevanterKommentarLinkTextFR = "Info",
                       RelevanterKommentarLinkTextIT = "Info",
                       RelevanterKommentarLinkTextEN = "Info",
                       ErlassQuelleLinkText = "1",
                       ErlassQuelleLinkTextFR = "1",
                       ErlassQuelleLinkTextIT = "1",
                       ErlassQuelleLinkTextEN = "1",
                   }).ToList();


            foreach (var kudoErlassfassung in kudoErlassfassungen)
            {
                XDocument uebersetzungErlass = XDocument.Parse(kudoErlassfassung.erlassUebersetzungen);
                kudoErlassfassung.ErlassTitel = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlass, "Titel", 1);
                kudoErlassfassung.ErlassTitelFR = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlass, "Titel", 2);
                kudoErlassfassung.ErlassTitelIT = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlass, "Titel", 3);
                kudoErlassfassung.ErlassTitelEN = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlass, "Titel", 4);
                kudoErlassfassung.ErlassQuelle = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlass, "Quelle", 1);
                kudoErlassfassung.ErlassQuelleFR = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlass, "Quelle", 2);
                kudoErlassfassung.ErlassQuelleIT = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlass, "Quelle", 3);
                kudoErlassfassung.ErlassQuelleEN = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlass, "Quelle", 4);

                XDocument uebersetzungErlassfassung = XDocument.Parse(kudoErlassfassung.erlassfassungUebersetzungen);
                kudoErlassfassung.BeschlussQuelle = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "Quelle", 1);
                kudoErlassfassung.BeschlussQuelleFR = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "Quelle", 2);
                kudoErlassfassung.BeschlussQuelleIT = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "Quelle", 3);
                kudoErlassfassung.BeschlussQuelleEN = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "Quelle", 4);
                kudoErlassfassung.InkraftretenQuelle = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "Quelle", 1);
                kudoErlassfassung.InkraftretenQuelleFR = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "Quelle", 2);
                kudoErlassfassung.InkraftretenQuelleIT = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "Quelle", 3);
                kudoErlassfassung.InkraftretenQuelleEN = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "Quelle", 4);
                kudoErlassfassung.BetroffenKommentar = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "BetroffenKommentar", 1);
                kudoErlassfassung.BetroffenKommentarFR = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "BetroffenKommentar", 2);
                kudoErlassfassung.BetroffenKommentarIT = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "BetroffenKommentar", 3);
                kudoErlassfassung.BetroffenKommentarEN = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "BetroffenKommentar", 4);
                kudoErlassfassung.LinkBetroffenKommentar = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "LinkBetroffenKommentar", 1);
                kudoErlassfassung.LinkBetroffenKommentarFR = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "LinkBetroffenKommentar", 2);
                kudoErlassfassung.LinkBetroffenKommentarIT = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "LinkBetroffenKommentar", 3);
                kudoErlassfassung.LinkBetroffenKommentarEN = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "LinkBetroffenKommentar", 4);
                kudoErlassfassung.NichtBetroffenKommentar = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "NichtBetroffenKommentar", 1);
                kudoErlassfassung.NichtBetroffenKommentarFR = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "NichtBetroffenKommentar", 2);
                kudoErlassfassung.NichtBetroffenKommentarIT = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "NichtBetroffenKommentar", 3);
                kudoErlassfassung.NichtBetroffenKommentarEN = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "NichtBetroffenKommentar", 4);
                kudoErlassfassung.LinkNichtBetroffenKommentar = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "LinkNichtBetroffenKommentar", 1);
                kudoErlassfassung.LinkNichtBetroffenKommentarFR = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "LinkNichtBetroffenKommentar", 2);
                kudoErlassfassung.LinkNichtBetroffenKommentarIT = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "LinkNichtBetroffenKommentar", 3);
                kudoErlassfassung.LinkNichtBetroffenKommentarEN = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "LinkNichtBetroffenKommentar", 4);
                kudoErlassfassung.InternerKommentar = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "InternerKommentar", 1);
                kudoErlassfassung.InternerKommentarFR = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "InternerKommentar", 2);
                kudoErlassfassung.InternerKommentarIT = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "InternerKommentar", 3);
                kudoErlassfassung.InternerKommentarEN = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "InternerKommentar", 4);

            }

            return kudoErlassfassungen.AsQueryable();
        }

        public IQueryable<KundendokumentErlassfassungViewModel> GetKundendokumentErlassfassungViewModelsByKundendokumentToQuenticExport(int? kundendokumentID)
        {
            int? kundenId = _genericKundendokumentErlassfassungRepository.GetKundeIdOfCurrentUser();

            Kundendokument kundendokument = _unitOfWork.KundendokumentRepository.GetByID(kundendokumentID.Value);
            DateTime lastReview = kundendokument.BearbeitetAm != null ? kundendokument.BearbeitetAm.Value : kundendokument.ErstelltAm.Value;
            var kudoErlassfassungen = (from x in _context.KundendokumentErlassfassungen
                    from erlF in _context.Erlassfassungen where erlF.ErlassfassungID == x.ErlassfassungID
                    from erl in _context.Erlasse where erl.ErlassID == erlF.ErlassID
                    from hg in _context.Herausgebers where hg.HerausgeberID == erl.HerausgeberId
                    from et in _context.Erlasstypen where et.ErlasstypID == erl.ErlasstypID
                    where (kundenId == null && x.KundendokumentID == kundendokumentID) || (x.KundeID == kundenId && x.KundendokumentID == kundendokumentID)
                    select new KundendokumentErlassfassungViewModel()
                    {
                        KundendokumentErlassfassungID = x.KundendokumentErlassfassungID,
                        ErlassSrNummer = erl.SrNummer,
                        erlassUebersetzungen = erl.Uebersetzung,
                        //ErlassAbkuerzung = erl.Uebersetzung,
                        //ErlassTitel = erl.Uebersetzung,
                        HerausgeberName = (_currentLang == 1 ? (hg.NameDE ?? hg.NameFR ?? hg.NameIT ?? hg.NameEN) : null) ??
                                     (_currentLang == 2 ? (hg.NameFR ?? hg.NameDE ?? hg.NameIT ?? hg.NameEN) : null) ??
                                     (_currentLang == 3 ? (hg.NameIT ?? hg.NameDE ?? hg.NameFR ?? hg.NameEN) : null) ??
                                     (_currentLang == 4 ? (hg.NameEN ?? hg.NameDE ?? hg.NameFR ?? hg.NameIT) : null),
                        Erlasstyp = (_currentLang == 1 ? (et.TitelDE ?? et.TitelFR ?? et.TitelIT ?? et.TitelEN) : null) ??
                                     (_currentLang == 2 ? (et.TitelFR ?? et.TitelDE ?? et.TitelIT ?? et.TitelEN) : null) ??
                                     (_currentLang == 3 ? (et.TitelIT ?? et.TitelDE ?? et.TitelFR ?? et.TitelEN) : null) ??
                                     (_currentLang == 4 ? (et.TitelEN ?? et.TitelDE ?? et.TitelFR ?? et.TitelIT) : null),
                        ErlassID = erl.ErlassID,
                        //Sachgebiete = erl.Uebersetzung,
                        //verantwortliche Stellen
                        //Erscheinungsort
                        //Erschienen am
                        Inkrafttretung = (from erlv in erl.Erlassversionen
                                          select erlv.Inkrafttretung).OrderBy(x => x).FirstOrDefault(),
                        LastChange = (from erlv in erl.Erlassversionen
                                      select erlv.Inkrafttretung).OrderByDescending(x => x).FirstOrDefault(),
                        // ^ order by desc
                        Norm = "gültig",
                        NoRelevance = "x",
                        ReviewDatum = lastReview,
                    }).ToList().GroupBy(x => x.ErlassID).Select(group => group.First()).AsQueryable();

            foreach (var kudoErlassfassung in kudoErlassfassungen)
            {
                XDocument erlassUebersetzungen = XDocument.Parse(kudoErlassfassung.erlassUebersetzungen);
                kudoErlassfassung.ErlassAbkuerzung = XMLHelper.GetUebersetzungFromXmlField(erlassUebersetzungen, "Abkuerzung", _currentLang);
                kudoErlassfassung.ErlassTitel = XMLHelper.GetUebersetzungFromXmlField(erlassUebersetzungen, "Titel", _currentLang);
                kudoErlassfassung.ErlassSrNummer += (", " + kudoErlassfassung.ErlassTitel);
                kudoErlassfassung.Sachgebiete = XMLHelper.GetUebersetzungFromXmlField(erlassUebersetzungen, "Sachgebiete", _currentLang);
                kudoErlassfassung.Kerninhalte = XMLHelper.GetUebersetzungFromXmlField(erlassUebersetzungen, "Kerninhalte", _currentLang);
                kudoErlassfassung.Quelle = XMLHelper.GetUebersetzungFromXmlField(erlassUebersetzungen, "Quelle", _currentLang);
            }

            return kudoErlassfassungen;
        }

        public KundendokumentErlassfassung GetByID(int id)
        {
            return _genericKundendokumentErlassfassungRepository.GetByID(id);
        }

        public void Update(KundendokumentErlassfassung kundendokumentErlassfassung)
        {
            kundendokumentErlassfassung.BearbeitetAm = DateTime.Now;
            kundendokumentErlassfassung.BearbeitetVonID = HttpContext.Current.User.Identity.GetUserId();
            _genericKundendokumentErlassfassungRepository.Update(kundendokumentErlassfassung);
        }

        /// <summary>
        /// Gets the kundendokument erlassfassung view models by erlassfassung and kundendokument.
        /// </summary>
        /// <param name="kundendokumentID">The kundendokument id.</param>
        /// <param name="erlassfassungID">The erlassfassung id.</param>
        /// <returns>Returns the view models</returns>
        public KundendokumentErlassfassungViewModel GetKundendokumentErlassfassungViewModelsByErlassfassung(int? kundendokumentID, int erlassfassungID, int spracheID)
        {
            if (!kundendokumentID.HasValue)
            {
                return new KundendokumentErlassfassungViewModel();
            }

            var viewModel = (from x in _context.KundendokumentErlassfassungen
                             from erlF in _context.Erlassfassungen where erlF.ErlassfassungID == x.ErlassfassungID
                             where x.KundendokumentID == kundendokumentID && x.ErlassfassungID == erlassfassungID
                             select new KundendokumentErlassfassungViewModel()
                             {
                                 KundendokumentErlassfassungID = x.KundendokumentErlassfassungID,
                                 ErlassfassungID = x.ErlassfassungID,
                                 KundendokumentID = x.KundendokumentID,
                                 Beschluss = x.Erlassfassung.Beschluss,
                                 Inkrafttretung = x.Erlassfassung.Inkrafttretung,
                                 Betroffen = x.Betroffen,
                                 erlassfassungUebersetzungen = erlF.Uebersetzung,
                                 Kommentar = x.Kommentar,
                             }).FirstOrDefault();

            if (viewModel != null)
            {
                XDocument uebersetzungErlassfassung = XDocument.Parse(viewModel.erlassfassungUebersetzungen);
                viewModel.BetroffenKommentar = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "BetroffenKommentar", spracheID);
                viewModel.NichtBetroffenKommentar = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "NichtBetroffenKommentar", spracheID);
                // Fixed NEOS-375 Link auf Kommentar wird nicht angezeigt
                viewModel.LinkBetroffenKommentar = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "LinkBetroffenKommentar", spracheID);
                viewModel.LinkNichtBetroffenKommentar = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "LinkNichtBetroffenKommentar", spracheID);
                viewModel.Quelle = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "Quelle", spracheID);
                viewModel.Quelle = viewModel.Quelle != null ? ((!viewModel.Quelle.Contains("http://") && !viewModel.Quelle.Contains("https://")) ? "https://" + viewModel.Quelle : viewModel.Quelle) : "";
            }
            //if no kundendokumenterlassfassung exists, take the newest erlassfassung
            else
            {
                var erlassfassung = (from x in _context.Erlassfassungen
                            from erlF in _context.Erlassfassungen where erlF.ErlassfassungID == x.ErlassfassungID
                            where x.ErlassfassungID == erlassfassungID
                            select new KundendokumentErlassfassungViewModel()
                            {
                                ErlassfassungID = x.ErlassfassungID,
                                Beschluss = x.Beschluss,
                                Inkrafttretung = x.Inkrafttretung,
                                Betroffen = true,
                                BetroffenKommentar = Resources.Properties.Resources.Old_Erlassfassung_Kommentar,
                                erlassfassungUebersetzungen = erlF.Uebersetzung,
                            }).OrderByDescending(x => x.Inkrafttretung).FirstOrDefault();

                XDocument uebersetzungErlassfassung = XDocument.Parse(erlassfassung.erlassfassungUebersetzungen);
                erlassfassung.Quelle = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "Quelle", spracheID);
                erlassfassung.Quelle = erlassfassung.Quelle != null ? ((!erlassfassung.Quelle.Contains("http://") && !erlassfassung.Quelle.Contains("https://")) ? "https://" + erlassfassung.Quelle : erlassfassung.Quelle) : "";
                viewModel = erlassfassung;
            }

            return viewModel;

        }

        /// <summary>
        /// Gets the kundendokument erlassfassung view models by standort.
        /// </summary>
        /// <param name="standortID">The standort ids.</param>
        /// <returns>Returns the view models</returns>
        public IQueryable<KundendokumentErlassfassungViewModel> GetKundendokumentErlassfassungViewModelsByStandort(int? standortID)
        {
            if (!standortID.HasValue)
            {
                return new List<KundendokumentErlassfassungViewModel>().AsQueryable();
            }

            var kundendokumentErlassfassungen =
                  _genericKundendokumentErlassfassungRepository.Get(f => f.Kundendokument.StandortID == standortID &&
                      f.Kundendokument.Status == KundendokumentStatus.Approved, q => q.OrderByDescending(e => e.KundendokumentID),
                      "Erlassfassung.ErlassfassungUebersetzungen.Sprache,Erlassfassung.Erlass.ErlassUebersetzungen.Sprache,Kundendokument");

            kundendokumentErlassfassungen = kundendokumentErlassfassungen.GroupBy(e => e.ErlassfassungID).Select(e => e.First());

            int? kundenId = _genericKundendokumentErlassfassungRepository.GetKundeIdOfCurrentUser();
            var erlFassungen = (from x in kundendokumentErlassfassungen
                                from erlF in _context.Erlassfassungen
                                where erlF.ErlassfassungID == x.ErlassfassungID
                                from erl in _context.Erlasse
                                where erl.ErlassID == erlF.ErlassID
                                from hg in _context.Herausgebers
                                where hg.HerausgeberID == erl.HerausgeberId
                                select new KundendokumentErlassfassungViewModel()
                                {
                                    KundendokumentErlassfassungID = x.KundendokumentErlassfassungID,
                                    KundendokumentID = x.KundendokumentID,
                                    Betroffen = x.Betroffen,
                                    ErlassID = x.Erlassfassung.ErlassID,
                                    HerausgeberID = x.Erlassfassung.Erlass.HerausgeberId,
                                    Inkrafttretung = x.Erlassfassung.Inkrafttretung,
                                    Beschluss = x.Erlassfassung.Beschluss,
                                    Status = x.Status,
                                    QsFreigabe = x.QsFreigabe,
                                    ErlassfassungID = x.ErlassfassungID,
                                    ErstelltAm = x.ErstelltAm,
                                    ErstelltVon = (x.ErstelltVon != null) ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname) : null,
                                    ErstelltVonID = x.ErstelltVonID,
                                    BearbeitetAm = x.BearbeitetAm,
                                    BearbeitetVonID = x.BearbeitetVonID,
                                    BearbeitetVon = (x.BearbeitetVon != null) ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname) : null,
                                    ErlassSrNummer = x.Erlassfassung.Erlass.SrNummer ?? "",
                                    HerausgeberName =   (_currentLang == 1 ? (hg.NameDE ?? hg.NameFR ?? hg.NameIT ?? hg.NameEN) : null) ??
                                                        (_currentLang == 2 ? (hg.NameFR ?? hg.NameDE ?? hg.NameIT ?? hg.NameEN) : null) ??
                                                        (_currentLang == 3 ? (hg.NameIT ?? hg.NameDE ?? hg.NameFR ?? hg.NameEN) : null) ??
                                                        (_currentLang == 4 ? (hg.NameEN ?? hg.NameDE ?? hg.NameFR ?? hg.NameIT) : null),
                                    erlassfassungUebersetzungen = erlF.Uebersetzung,
                                    erlassUebersetzungen = erl.Uebersetzung,
                                    ErlassAbkuerzung = erl.Uebersetzung,
                                    Kommentar = x.Kommentar,
                                }).ToList();

            foreach (var erlFassung in erlFassungen)
            {
                XDocument uebersetzungErlassfassung = XDocument.Parse(erlFassung.erlassfassungUebersetzungen);
                erlFassung.BetroffenKommentar = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "BetroffenKommentar", _currentLang);
                erlFassung.NichtBetroffenKommentar = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "NichtBetroffenKommentar", _currentLang);
                erlFassung.Quelle = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "Quelle", _currentLang);

                XDocument uebersetzungErlass = XDocument.Parse(erlFassung.erlassUebersetzungen);
                erlFassung.ErlassTitel = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlass, "Titel", _currentLang);
                erlFassung.ErlassAbkuerzung = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlass, "Abkuerzung", _currentLang);
            }

            return erlFassungen.AsQueryable();
        }

        /// <summary>
        /// Deletes the kundendokument erlassfassung with the specified id.
        /// </summary>
        /// <param name="id">The id.</param>
        public void Delete(int id)
        {
            _genericKundendokumentErlassfassungRepository.Delete(id);
        }

        public void DeleteRange(IEnumerable<KundendokumentErlassfassung> entitiesToDelete)
        {
            _genericKundendokumentErlassfassungRepository.DeleteRange(entitiesToDelete);
        }

        /// <summary>
        /// Gets all erlassfassung view models by the specified erlassfassung ids.
        /// </summary>
        /// <param name="erlassfassungIds">The erlassfassung ids.</param>
        /// <returns>Returns the view models.</returns>
        public IQueryable<KundendokumentErlassfassungViewModel> GetAllKundendokumentErlassfassungViewModelByErlassfassungIds(ISet<int> erlassfassungIds)
        {
            var erlFassungen = (from x in _context.Erlassfassungen
                                from erl in _context.Erlasse
                                where erl.ErlassID == x.ErlassID
                                where erlassfassungIds.Contains(x.ErlassfassungID)
                   && (x.Kommentar == null || x.Kommentar.PLFreigabe == true)
                   select new KundendokumentErlassfassungViewModel()
                   {
                       ErlassID = x.ErlassID,
                       Inkrafttretung = x.Inkrafttretung,
                       Beschluss = x.Beschluss,
                       ErlassfassungID = x.ErlassfassungID,
                       ErstelltAm = x.ErstelltAm,
                       ErstelltVon = (x.ErstelltVon != null) ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname) : null,
                       ErstelltVonID = x.ErstelltVonID,
                       BearbeitetAm = x.BearbeitetAm,
                       BearbeitetVonID = x.BearbeitetVonID,
                       BearbeitetVon = (x.BearbeitetVon != null) ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname) : null,
                       erlassfassungUebersetzungen = x.Uebersetzung,
                       erlassUebersetzungen = erl.Uebersetzung,
                   }).ToList();

            foreach (var erlFassung in erlFassungen)
            {
                XDocument uebersetzungErlassfassung = XDocument.Parse(erlFassung.erlassfassungUebersetzungen);
                erlFassung.Quelle = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "Quelle", _currentLang);
                erlFassung.BetroffenKommentar = (XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "LinkBetroffenKommentar", _currentLang) != "---"
                                                ? (XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "LinkBetroffenKommentar", _currentLang))
                                                : XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "BetroffenKommentar", _currentLang));
                erlFassung.NichtBetroffenKommentar = (XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "LinkNichtBetroffenKommentar", _currentLang) != "---"
                                                     ? (XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "LinkNichtBetroffenKommentar", _currentLang))
                                                     : XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "NichtBetroffenKommentar", _currentLang));
                erlFassung.InternerKommentar = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlassfassung, "InternerKommentar", _currentLang);

                XDocument uebersetzungErlass = XDocument.Parse(erlFassung.erlassUebersetzungen);
                erlFassung.ErlassTitel = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlass, "Titel", _currentLang);
                erlFassung.ErlassAbkuerzung = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlass, "ErlassAbkuerzung", _currentLang);
            }

            return erlFassungen.AsQueryable();
        }

        /// <summary>
        /// Sets all QS Freigabe flags of the kundendokument forderungsversionen of the specified kundendokument to true.
        /// </summary>
        /// <param name="kundendokumentID">The kundendokument id.</param>
        public void ApproveAll(int kundendokumentID)
        {
            var erlassversionen =
                _genericKundendokumentErlassfassungRepository.Get(f => f.KundendokumentID == kundendokumentID &&
                                                                       f.Status != KundendokumentItemStatus.Removed &&
                                                                       f.Status != KundendokumentItemStatus.Replaced);

            foreach (var erlassversion in erlassversionen)
            {
                erlassversion.QsFreigabe = true;
                erlassversion.BearbeitetAm = DateTime.Now;
                erlassversion.BearbeitetVonID = HttpContext.Current.User.Identity.GetUserId();
                _context.Entry(erlassversion).State = System.Data.Entity.EntityState.Modified;
            }
            _context.SaveChanges();
        }
    }
}