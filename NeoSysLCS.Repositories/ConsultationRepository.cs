using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Linq.Expressions;
using System.Web;
using System.Xml.Linq;
using Microsoft.AspNet.Identity;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories.Helper;
using NeoSysLCS.Repositories.Interfaces;
using NeoSysLCS.Repositories.ViewModels;

namespace NeoSysLCS.Repositories
{
    public class ConsultationRepository : IReloadable<Consultation>
    {
        private readonly IGenericRepository<Consultation> _genericConsultationRepository;
        private readonly IGenericRepository<Erlass> _genericErlassRepository;
        private readonly UnitOfWork _unitOfWork;
        private readonly NeoSysLCS_Dev _context;
        private int _currentLang;

        public static readonly Expression<Func<Consultation, bool>> AllConsultations = x => true;

        public static readonly Expression<Func<Consultation, bool>> CompletedConsultationsInNext30Days = x =>
            x.Status == ConsultationStatus.Completed
            && (x.CompletedDate.Value.Date >= DateTime.Now.Date && x.CompletedDate.Value.Date <= DateTime.Now.AddMonths(1).Date);

        public ConsultationRepository(NeoSysLCS_Dev context)
        {
            _context = context;
            _genericConsultationRepository = new GenericRepository<Consultation>(context);
            _genericErlassRepository = new GenericRepository<Erlass>(context);
           _unitOfWork = new UnitOfWork(context);

            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            Sprache sprache = _context.Sprachen.FirstOrDefault(s => s.Lokalisierung == language);
            _currentLang = sprache.SpracheID;
        }

        public Consultation GetByID(object id)
        {
            return _genericConsultationRepository.GetByID(id); 
        }

        public void Reload(Consultation entityToReload)
        {
            _genericConsultationRepository.Reload(entityToReload);
        }

        public IQueryable<ConsultationViewModel> GetConsultationViewModels(Expression<Func<Consultation, bool>> filter = null, int spracheID = 0)
        {
            if (filter == null)
            {
                filter = AllConsultations;
            }

            if (spracheID != 0)
            {
                _currentLang = spracheID;
            }

            var consultations = _context.Consultation
                                .Where(filter)
                                .Select(x => new ConsultationViewModel()
                                {
                                    ConsultationId = x.ConsultationId,
                                    Uebersetzung = x.Uebersetzung,
                                    EntryDate = x.EntryDate,
                                    OpenedDate = x.OpenedDate,
                                    Deadline = x.Deadline,
                                    CompletedDate = x.CompletedDate,
                                    Status = x.Status,
                                    ErstelltAm = x.ErstelltAm,
                                    ErstelltVon = (x.ErstelltVon != null)
                                        ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname)
                                        : null,
                                    ErstelltVonID = x.ErstelltVonID,
                                    BearbeitetAm = x.BearbeitetAm,
                                    BearbeitetVonID = x.BearbeitetVonID,
                                    BearbeitetVon = (x.BearbeitetVon != null)
                                        ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname)
                                        : null,
                                    ErlasseCollection = x.Erlasse.AsQueryable(),
                                    ErlasseCount = x.Erlasse.Count(),
                                }).ToList();

            foreach (ConsultationViewModel consultation in consultations)
            {
                XDocument uebersetzung = XDocument.Parse(consultation.Uebersetzung);

                consultation.Title = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Titel", _currentLang);
                consultation.Quelle = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Quelle", _currentLang);
                consultation.Abkuerzung = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Abkuerzung", _currentLang);
            }

            return consultations.AsQueryable();

        }

        public IQueryable<ConsultationViewModel> GetDashboardConsultationViewModels(int kundeID)
        {

            var nowPlus30Days = DateTime.Now.AddMonths(1);
            var tommorrow = DateTime.Today.AddDays(1);

            KundeStatus status = _unitOfWork.KundeRepository.GetByID(kundeID).Status;
            List<int> erlassIdByKunde = new List<int>();

            if (status == KundeStatus.Newsletter)
            {
                List<StandortViewModel> standorte = _unitOfWork.StandortRepository.GetAllStandortViewModels(kundeID).ToList();
                foreach (StandortViewModel standort in standorte)
                {
                    // use same method as when creating a new document
                    erlassIdByKunde.AddRange(_unitOfWork.ForderungsversionRepository.GetKundendokumentForderungsversionViewModelsForNewsletterCustomer(standort.StandortID).Select(x => x.ErlassID).Distinct().ToList());
                }
                erlassIdByKunde = erlassIdByKunde.Distinct().ToList();
            }
            else
            {
                erlassIdByKunde = (from o in _context.KundendokumentErlassfassungen
                                       where (
                                           o.Kundendokument.KundeID == kundeID &&
                                           o.Kundendokument.Status == KundendokumentStatus.Approved &&
                                           o.Kundendokument.PublizierenAm < tommorrow
                                       )
                                       select o).Select(x => x.Erlassfassung.ErlassID).Distinct().ToList();
            }

            var consultations = _context.Consultation
                                .Where(x => (x.CompletedDate == null || (x.Status == ConsultationStatus.Completed &&  x.CompletedDate >= DateTime.Today && x.CompletedDate <= nowPlus30Days))
                                    && x.Erlasse.Any(e => erlassIdByKunde.Contains(e.ErlassID)))
                                .Select(x => new ConsultationViewModel()
                                {
                                    ConsultationId = x.ConsultationId,
                                    Uebersetzung = x.Uebersetzung,
                                    EntryDate = x.EntryDate,
                                    OpenedDate = x.OpenedDate,
                                    Deadline = x.Deadline,
                                    CompletedDate = x.CompletedDate,
                                    Status = x.Status,
                                    ErstelltAm = x.ErstelltAm,
                                    ErstelltVon = (x.ErstelltVon != null)
                                        ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname)
                                        : null,
                                    ErstelltVonID = x.ErstelltVonID,
                                    BearbeitetAm = x.BearbeitetAm,
                                    BearbeitetVonID = x.BearbeitetVonID,
                                    BearbeitetVon = (x.BearbeitetVon != null)
                                        ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname)
                                        : null,
                                    ErlasseCollection = x.Erlasse.AsQueryable(),
                                    ErlasseCount = x.Erlasse.Count(),
                                }).ToList();

            foreach (var consultation in consultations)
            {
                XDocument uebersetzung = XDocument.Parse(consultation.Uebersetzung);

                consultation.Title = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Titel", _currentLang);
                consultation.Quelle = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Quelle", _currentLang);
                consultation.Abkuerzung = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Abkuerzung", _currentLang);

                List<ErlassViewModel> erlassViewModels = new List<ErlassViewModel>();
                foreach (Erlass erlass in consultation.ErlasseCollection)
                {
                    erlassViewModels.Add(_unitOfWork.ErlassRepository.GetErlassViewModelById(erlass.ErlassID));
                }
                consultation.Erlasse = erlassViewModels.AsQueryable();

                // get current massnahme for this kommentar to add the counter
                List<MassnahmeViewModel> massnahmeViewModels = _unitOfWork.MassnahmeRepository.GetByOriginID(consultation.ConsultationId, kundeID);
                consultation.MassnahmeNewCount = massnahmeViewModels.Where(x => x.Status == MassnahmeStatus.New).Count();
                consultation.MassnahmeInProgressCount = massnahmeViewModels.Where(x => x.Status == MassnahmeStatus.InProgress).Count();
                consultation.MassnahmeFinishedCount = massnahmeViewModels.Where(x => x.Status == MassnahmeStatus.Finished).Count();
            }

            return consultations.AsQueryable();

        }

        public void Delete(int consultationId)
        {
            var consultation = _genericConsultationRepository.Get(c => c.ConsultationId == consultationId, null, "Erlasse").FirstOrDefault();
            consultation?.Erlasse.Clear();
            _genericConsultationRepository.Update(consultation);
            _genericConsultationRepository.Delete(consultationId);
        }


        public void Update(ConsultationViewModel viewModel)
        {
            var consultation = _genericConsultationRepository.GetByID(viewModel.ConsultationId);
            consultation.EntryDate = viewModel.EntryDate;
            consultation.OpenedDate = viewModel.OpenedDate;
            consultation.Deadline = viewModel.Deadline;
            consultation.BearbeitetAm = DateTime.Now;
            consultation.BearbeitetVonID = HttpContext.Current.User.Identity.GetUserId();

            
            // Set CompletedDate if status changed to Completed
            if (consultation.Status != viewModel.Status && viewModel.Status == ConsultationStatus.Completed)
            {
                consultation.CompletedDate = DateTime.Now;
            }
            else
            {
                consultation.CompletedDate = viewModel.CompletedDate;
            }

            var uebersetzung = XDocument.Parse(consultation.Uebersetzung);
            Dictionary<string, string> elements = new Dictionary<string, string>();
            elements.Add("Titel", viewModel.Title);
            elements.Add("Quelle", viewModel.Quelle);
            consultation.Uebersetzung = XMLHelper.UpdateUebersetzung(uebersetzung, elements, _currentLang);


            consultation.Status = viewModel.Status;
            _genericConsultationRepository.Update(consultation);
        }

        public void Insert(ConsultationViewModel viewModel)
        {
            var consultation = new Consultation
            {
                EntryDate = viewModel.EntryDate,
                OpenedDate = viewModel.OpenedDate,
                Deadline = viewModel.Deadline,
                CompletedDate = viewModel.CompletedDate,
                Status = viewModel.Status,
                ErstelltAm = DateTime.Now,
                ErstelltVonID = HttpContext.Current.User.Identity.GetUserId(),
            };

            List<string> list = new List<string>();
            list.Add("Titel");
            list.Add("Quelle");

            XDocument uebersetzung = XMLHelper.CreateNewUebersetzung(list);

            Dictionary<string, string> elements = new Dictionary<string, string>();
            elements.Add("Titel", viewModel.Title);
            elements.Add("Quelle", viewModel.Quelle);

            consultation.Uebersetzung = XMLHelper.UpdateUebersetzung(uebersetzung, elements, _currentLang);

            _genericConsultationRepository.Insert(consultation);
        }

        public void SaveConsultationErlasse(string selectedIDs, int consultationId)
        {
            var consultation = _genericConsultationRepository.Get(c => c.ConsultationId == consultationId, null, "Erlasse").FirstOrDefault();

            var ids = selectedIDs.Split(',');

            if (selectedIDs != "")
            {
                foreach (var id in ids)
                {
                    Erlass erlass = _unitOfWork.ErlassRepository.GetByID(Convert.ToInt32(id));
                    if (!consultation.Erlasse.Contains(erlass))
                    {
                        consultation.Erlasse.Add(erlass);
                    }
                }
            }

            if (consultation != null && consultation.Erlasse.Count == 0)
            {
                consultation.Erlasse = null;
            }

            _genericConsultationRepository.Update(consultation);
            _unitOfWork.Save();
        }

        public IEnumerable<ConsultationViewModel> GetConsultationUebersetzungenByConsultation(int id)
        {
            Consultation consultation = _genericConsultationRepository.GetByID(id);

            List<int> sprachIds = _context.Sprachen.Select(x => x.SpracheID).ToList();
            List<ConsultationViewModel> uebersetzungen = new List<ConsultationViewModel>();
            XDocument xml = XDocument.Parse(consultation.Uebersetzung);

            foreach (int sprachId in sprachIds)
            {
                ConsultationViewModel uebersetzung = new ConsultationViewModel();
                uebersetzung.ConsultationUebersetzungID = id.ToString() + "_" + sprachId.ToString();
                uebersetzung.Title = XMLHelper.GetAllUebersetzungFromXmlField(xml, "Titel", sprachId);
                uebersetzung.Quelle = XMLHelper.GetAllUebersetzungFromXmlField(xml, "Quelle", sprachId);
                uebersetzung.SpracheID = sprachId;
                uebersetzungen.Add(uebersetzung);
            }

            return uebersetzungen;
        }

        public void InsertUebersetzungConsultation(ConsultationViewModel uebersetzung, int ConsultationId)
        {
            Consultation consultation = _genericConsultationRepository.GetByID(ConsultationId);

            var xml = XDocument.Parse(consultation.Uebersetzung);
            Dictionary<string, string> elements = new Dictionary<string, string>();
            elements.Add("Titel", uebersetzung.Title);
            elements.Add("Quelle", uebersetzung.Quelle);

            consultation.Uebersetzung = XMLHelper.UpdateUebersetzung(xml, elements, uebersetzung.SpracheID);

            _genericConsultationRepository.Update(consultation);
        }

        public IQueryable<ConsultationViewModel> GetConsultationViewModelsNewsletter(int kundeID, int spracheID, DateTime lastNewsletterSentTime)
        {
            var tommorrow = DateTime.Today.AddDays(1);
            var nowPlus30Days = DateTime.Now.AddMonths(1);
            var erlassIdByKunde = (from o in _context.KundendokumentErlassfassungen
                                    where (
                                        o.Kundendokument.KundeID == kundeID &&
                                        o.Kundendokument.Status == KundendokumentStatus.Approved &&
                                        o.Kundendokument.PublizierenAm < tommorrow
                                    )
                                    select o).Select(x => x.Erlassfassung.ErlassID).Distinct().ToList();

            var consultations = _context.Consultation
                                .Where(x => x.EntryDate > lastNewsletterSentTime && x.Erlasse.Any(e => erlassIdByKunde.Contains(e.ErlassID)) 
                                    && x.CompletedDate == null || (x.Status == ConsultationStatus.Completed && x.CompletedDate >= DateTime.Today && x.CompletedDate <= nowPlus30Days))
                                .Select(x => new ConsultationViewModel()
                                {
                                    ConsultationId = x.ConsultationId,
                                    Uebersetzung = x.Uebersetzung,
                                    EntryDate = x.EntryDate,
                                    OpenedDate = x.OpenedDate,
                                    Deadline = x.Deadline,
                                    CompletedDate = x.CompletedDate,
                                    Status = x.Status,
                                    ErstelltAm = x.ErstelltAm,
                                    ErstelltVon = (x.ErstelltVon != null)
                                        ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname)
                                        : null,
                                    ErstelltVonID = x.ErstelltVonID,
                                    BearbeitetAm = x.BearbeitetAm,
                                    BearbeitetVonID = x.BearbeitetVonID,
                                    BearbeitetVon = (x.BearbeitetVon != null)
                                        ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname)
                                        : null,
                                    ErlasseCollection = x.Erlasse.AsQueryable(),
                                    ErlasseCount = x.Erlasse.Count(),
                                }).ToList();

            foreach (ConsultationViewModel consultation in consultations)
            {
                XDocument uebersetzung = XDocument.Parse(consultation.Uebersetzung);

                consultation.Title = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Titel", spracheID);
                consultation.Quelle = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Quelle", spracheID);
            }

            return consultations.AsQueryable();

        }
    }
}