using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Microsoft.AspNet.Identity;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories.Interfaces;
using NeoSysLCS.Repositories.ViewModels;

namespace NeoSysLCS.Repositories
{
    public class ShortcutRepository
    {
        private readonly UnitOfWork _unitOfWork;
        private readonly NeoSysLCS_Dev _context;
        private readonly IGenericRepository<Shortcut> _genericShortcutRepository;
        private readonly IGenericRepository<Standort> _genericStandortRepository;
        private readonly IGenericRepository<ApplicationUser> _genericApplicationRepository;
        private readonly IGenericRepository<StandortObjekt> _genericStandortObjektRepository;

        public ShortcutRepository(NeoSysLCS_Dev contextCandidate)
        {
            _context = contextCandidate;
            _genericShortcutRepository = new GenericRepository<Shortcut>(contextCandidate);
            _genericStandortRepository = new GenericRepository<Standort>(contextCandidate);
            _genericApplicationRepository = new GenericRepository<ApplicationUser>(contextCandidate);
            _genericStandortObjektRepository = new GenericRepository<StandortObjekt>(contextCandidate);
            _unitOfWork = new UnitOfWork(contextCandidate);
        }

        public IQueryable<ShortcutViewModel> GetAllShortcutViewModelsByStandortId(int standortID)
        {
            var activeShortcuts = _genericShortcutRepository.Get(sh => sh.StandortID == standortID, null, "Users");

            List<ShortcutViewModel> activeStandortShortcuts = activeShortcuts.ToList().Select(s => new ShortcutViewModel
            {
                Name = s.Name,
                User = s.Users.AsQueryable(),
                StandortID = s.StandortID,
                ShortcutID = s.ShortcutID,
            }).ToList();

            return activeStandortShortcuts.AsQueryable();
        }

        public IQueryable<ShortcutViewModel> GetAllShortcutViewModelsByStandortIds(IEnumerable<int> standortIDs, bool addDefaultShortcut = false)
        {
            var activeShortcuts = _genericShortcutRepository.Get(sh => standortIDs.Contains(sh.StandortID), null, "Users");

            List<ShortcutViewModel> activeStandortShortcuts = activeShortcuts.ToList().Select(s => new ShortcutViewModel
            {
                Name = s.Name,
                User = s.Users.AsQueryable(),
                StandortID = s.StandortID,
                ShortcutID = s.ShortcutID,
            }).ToList();

            if (addDefaultShortcut)
            {
                ShortcutViewModel defaultShortcut = new ShortcutViewModel();
                defaultShortcut.Name = " ";
                defaultShortcut.ShortcutID = 1;
                defaultShortcut.StandortID = 1;
                activeStandortShortcuts.Add(defaultShortcut);
            }

            return activeStandortShortcuts
                .OrderBy(s => s.Name)
                .AsQueryable();
        }

        public IQueryable<ShortcutViewModel> GetShortcutCombobox(int standortID)
        {
            var activeShortcuts = _genericShortcutRepository.Get(sh => sh.StandortID == standortID, null, "Users");

            List<ShortcutViewModel> activeStandortShortcuts = activeShortcuts.ToList().Select(s => new ShortcutViewModel
            {
                Name = s.Name,
                User = s.Users.AsQueryable(),
                StandortID = s.StandortID,
                ShortcutID = s.ShortcutID,
            }).ToList();

            ShortcutViewModel defaultShortcut = new ShortcutViewModel();
            defaultShortcut.Name = " ";
            defaultShortcut.ShortcutID = 1;
            defaultShortcut.StandortID = 1;

            activeStandortShortcuts.Add(defaultShortcut);

            return activeStandortShortcuts.AsQueryable();
        }

        public Shortcut GetByID(object id)
        {
            int shortcutId = (int) id;
            return (from x in _context.Shortcut
                    where x.ShortcutID == shortcutId
                    select x).FirstOrDefault();
        }

        public void SaveUserShortcut(string selectedIDs, int shortcutID)
        {
            Shortcut shortcut = _genericShortcutRepository.GetByID(shortcutID);
            //pflicht.Objekte.Clear();
            string[] ids = selectedIDs.Split(',');

            if (selectedIDs != "")
            {
                foreach (var id in ids)
                {
                    ApplicationUser obj = _genericApplicationRepository.GetByID(id);
                    if (!shortcut.Users.Contains(obj))
                    {
                        shortcut.Users.Add(obj);
                    }
                }
            }

            foreach (var pfo in shortcut.Users.ToArray())
            {
                if (pfo != null)
                {
                    if (!ids.ToList().Contains(pfo.Id.ToString()))
                    {
                        shortcut.Users.Remove(pfo);
                    }
                }
            }
            if (shortcut.Users.Count == 0)
            {
                shortcut.Users = null;
            }

            _genericShortcutRepository.Update(shortcut);
        }

        public void Insert(ShortcutViewModel viewModel)
        {
            var shortcut = new Shortcut
            {
                Name = viewModel.Name,
                Users = (viewModel.User != null) ? viewModel.User.ToList() : new List<ApplicationUser>(),
                StandortID = viewModel.StandortID,
                ErstelltAm = DateTime.Now,
                ErstelltVonID = HttpContext.Current.User.Identity.GetUserId(),
            };

            _genericShortcutRepository.Insert(shortcut);
        }

        public void Update(ShortcutViewModel viewModel)
        {
            var shortcut = _genericShortcutRepository.GetByID(viewModel.ShortcutID);
            shortcut.StandortID = viewModel.StandortID;
            shortcut.ShortcutID = viewModel.ShortcutID;
            shortcut.Name = viewModel.Name;
            //standort.InterneNotiz = viewModel.InterneNotiz;
            if (viewModel.User != null)
                shortcut.Users = viewModel.User.ToList();

            shortcut.BearbeitetAm = DateTime.Now;
            shortcut.BearbeitetVonID = HttpContext.Current.User.Identity.GetUserId();

            _genericShortcutRepository.Update(shortcut);
        }

        public void Delete(int shortcutID)
        {
            var shortcut = _genericShortcutRepository.GetByID(shortcutID);
            shortcut.Users.Clear();

            _genericShortcutRepository.Update(shortcut);

            List<KundendokumentForderungsversion> kudoForderungsVersionen = _unitOfWork.KundendokumentForderungsversionRepository.GetKundendokumentForderungsversionenByShortcutId(shortcutID).ToList();
            foreach (KundendokumentForderungsversion version in kudoForderungsVersionen)
            {
                version.Shortcut = null;
                _unitOfWork.KundendokumentForderungsversionRepository.Update(version);
            }

            List<StandortObjekt> standortObjekte = _unitOfWork.StandortObjektRepository.GetStandortObjekteByShortcutId(shortcutID).ToList();
            foreach (StandortObjekt standortObjekt in standortObjekte)
            {
                standortObjekt.Shortcut = null;
                _genericStandortObjektRepository.Update(standortObjekt);
            }

            _genericShortcutRepository.Delete(shortcutID);
        }

        internal List<int> GetAllShortcutIdsByStandortAndUserId(string userID, int standortID)
        {
            return (from x in _context.Shortcut
                    where x.StandortID == standortID && x.Users.Any(u => u.Id == userID)
                    select x.ShortcutID).ToList();
        }

        public List<int> GetAllShortcutIdsByUserId(string userID)
        {
            return (from x in _context.Shortcut
                    where x.Users.Any(u => u.Id == userID)
                    select x.ShortcutID).ToList();
        }
    }
}