using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using NeoSysLCS.DomainModel.Models;

namespace NeoSysLCS.Repositories.ViewModels
{
    public class ObjektkategorieViewModel : BaseViewModel
    {
        public int ObjektkategorieID { get; set; }

        [Display(Name = "Name")]
        [Required(ErrorMessage = "Objektkategoriename wird benötigt!")] //TODO
        public string Name { get; set; }

        [Display(Name = "Parent")]
        public int? ParentObjektkategorieID { get; set; }

        [Display(Name = "ParentName")]
        public string ParentObjektkategorieName { get; set; }

        public string ObjektkategorieUebersetzungID { get; set; }
        public int SpracheID { get; set; }
    }


}