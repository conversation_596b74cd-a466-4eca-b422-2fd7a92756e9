using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace NeoSysLCS.Repositories.ViewModels
{
    public class SpracheViewModel : BaseViewModel
    {

        public int SpracheID { get; set; }

        public string Name { get; set; }

        public override bool Equals(object obj)
        {
            return ((SpracheViewModel)obj).SpracheID == SpracheID;
        }
        public override int GetHashCode()
        {
            return SpracheID.GetHashCode();
        }
    }
}