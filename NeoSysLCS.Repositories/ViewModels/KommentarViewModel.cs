using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using NeoSysLCS.DomainModel.Models;

namespace NeoSysLCS.Repositories.ViewModels
{
    public class KommentarViewModel : BaseViewModel
    {
        public KommentarViewModel()
        {

        }

        public int KommentarID { get; set; }
        public DateTime? Eintrag { get; set; }
        public DateTime Beschluss { get; set; }
        public DateTime Inkrafttretung { get; set; }
        public string Nummer { get; set; }
        public int? HauptErlassID { get; set; }
        public string ProjektleiterID { get; set; }
        public string ProjektleiterName { get; set; }
        public KommentarStatus Status { get; set; }
        public Boolean? Newsletter { get; set; }
        public DateTime? NewsletterDate { get; set; }
        public IQueryable<Erlass> Erlasse { get; set; }
        public int ErlasseCount { get; set; }
        public IQueryable<Rechtsbereich> Rechtsbereiche { get; set; }
        public int RechtsbereicheCount { get; set; }
        public IQueryable<Objekt> Objekte { get; set; }
        public int ObjekteCount { get; set; }
        public string Quelle { get; set; }
        public string BetroffenKommentar { get; set; }
        public string LinkBetroffenKommentar { get; set; }
        public string NichtBetroffenKommentar { get; set; }
        public string LinkNichtBetroffenKommentar { get; set; }
        public string InternerKommentar { get; set; }
        public IQueryable<int> ObjektIds { get; set; }
        public bool? PLFreigabe { get; set; }
        public DateTime? PLFreigabeDate { get; set; }
        public string Uebersetzung { get; set; }
        public string ShortText { get; set; }
        public string KommentarUebersetzungID { get; set; }
        public int SpracheID { get; set; }
        public string KommentarImage { get; set; }
        public string ProjektleiterQSID { get; set; }
        public string ProjektleiterQSName { get; set; }
    }
}