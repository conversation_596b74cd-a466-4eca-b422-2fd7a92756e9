using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace NeoSysLCS.Repositories.ViewModels
{
    public class LegalComplianceViewModel
    {
        public int LegalComplianceID { get; set; }
        public int StandortID { get; set; }
        public int KundendokumentID { get; set; }
        public int Total { get; set; }
        public int Erfuellt { get; set; }
        public int NichtErfuellt { get; set; }
        public int InAbklaerung { get; set; }
        public int NichtBearbeitet { get; set; }
        public string Erfuellung { get; set; }
        public string Month { get; set; }
        public DateTime ErstelltAm { get; set; }
    }
}