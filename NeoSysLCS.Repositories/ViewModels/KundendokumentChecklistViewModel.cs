using System;
using System.ComponentModel.DataAnnotations;
using NeoSysLCS.DomainModel.Models;

namespace NeoSysLCS.Repositories.ViewModels
{
    public class KundendokumentChecklistViewModel : BaseViewModel
    {
        public int KundendokumentChecklistID { get; set; }

        public string ChecklistTitle { get; set; }

        public string ChecklistDescription { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_KundendokumentErlassfassung_Status")]
        public KundendokumentItemStatus Status { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Kundendokument_Singular")]
        public int KundendokumentID { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Herausgeber_Singular")]
        public int HerausgeberID { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Herausgeber_Singular")]
        public string HerausgeberName { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_Singular")]
        public int ErlassID { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_Titel")]
        public string ErlassTitel { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_SrNummer")] 
        public string ErlassSrNummer { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_Abkuerzung")] 
        public string ErlassAbkuerzung { get; set; }
        public string erlassUebersetzungen { get; set; }
        public string checklistUebersetzungen { get; set; }
        public string ErlassQuelle { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_KundendokumentForderungsversion_LetzterPruefungszeitpunkt")]
        public DateTime? LetzterPruefZeitpunkt { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_KundendokumentForderungsversion_NaechstePruefungAm")]
        public DateTime? NaechstePruefungAm { get; set; }
        public int ErlassfassungID { get; set; }

        public int KundendokumentForderungsversionID { get; set; }
    }
}