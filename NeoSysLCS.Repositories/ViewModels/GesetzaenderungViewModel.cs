using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using NeoSysLCS.DomainModel.Models;

namespace NeoSysLCS.Repositories.ViewModels
{
    public class GesetzaenderungViewModel : BaseViewModel
    {
        public GesetzaenderungViewModel()
        {

        }
        public int GesetzaenderungID { get; set; }
        public string ErlassName { get; set; }
        public string ErlassNummer { get; set; }
        public string ErlassAbkuerzung { get; set; }
        public string ErlassUebersetzung { get; set; }
        public string ErlassQuelle { get; set; }
        public ICollection<Erlass> ErlasseCollection { get; set; }
        public IQueryable<ErlassViewModel> Erlasse { get; set; }
        public int ErlasseCount { get; set; }
        public Erlass HauptErlass { get; set; }
        public DateTime Beschluss { get; set; }
        public DateTime Inkrafttretung { get; set; }
        public IQueryable<StandortViewModel> Standorte { get; set; }
        public int StandortID { get; set; }
        public string StandortIDs { get; set; }
        public string StandortName { get; set; }
        public string KommentarUebersetzung { get; set; }
        public string KommentarShortText { get; set; }
        public string Herausgeber { get; set; }
        public string KommentarImage { get; set; }
        public string KommentarQuelle { get; set; }
        public int KundeID { get; set; }
        public DateTime? NewsletterDate { get; set; }
        public DateTime? PLFreigabeDate { get; set; }
        public int MassnahmeNewCount { get; set; }
        public int MassnahmeInProgressCount { get; set; }
        public int MassnahmeFinishedCount { get; set; }
    }
}