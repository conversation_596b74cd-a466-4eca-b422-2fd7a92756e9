using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Microsoft.AspNet.Identity;
using System.ComponentModel;

namespace NeoSysLCS.Repositories.ViewModels
{
    public class IndexViewModel
    {
        public bool HasPassword { get; set; }
        public IList<UserLoginInfo> Logins { get; set; }
    }

    public class ChangePasswordViewModel
    {
        public string UserId { get; set; }

        [DataType(DataType.Password)]
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "ChangePasswordViewModel_OldPassword_DisplayName")]
        public string OldPassword { get; set; }

        [DataType(DataType.Password)]
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "ChangePasswordViewModel_NewPassword_DisplayName")]
        public string NewPassword { get; set; }

        [DataType(DataType.Password)]
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "ChangePasswordViewModel_ConfirmPassword_DisplayName")]
        [Compare("NewPassword", ErrorMessage = null, ErrorMessageResourceType = typeof(Resources.Properties.Resources), ErrorMessageResourceName = "SetPasswordViewModel_ConfirmPassword_CompareErrorMessage")]
        public string ConfirmPassword { get; set; }

        public bool isValid { get; set; }

        [DefaultValue(true)]
        public string ErrorText { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Email_Label_Email")]
        //[Required]
        [EmailAddress]
        public string Email { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Confirm_Email")]
        //[Required]
        [EmailAddress]
        [Compare("Email", ErrorMessage = null, ErrorMessageResourceType = typeof(Resources.Properties.Resources), ErrorMessageResourceName = "ResetPasswordViewModel_ConfirmEmail_CompareErrorMessage")]
        public string confirmEmail { get; set; }
    }

    public class DeleteCookiesViewModel
    {
        public string UserId { get; set; }

        [DataType(DataType.Password)]
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "ChangePasswordViewModel_OldPassword_DisplayName")]
        public string OldPassword { get; set; }

        public bool isValid { get; set; }

        [DefaultValue(true)]
        public string ErrorText { get; set; }
    }
}