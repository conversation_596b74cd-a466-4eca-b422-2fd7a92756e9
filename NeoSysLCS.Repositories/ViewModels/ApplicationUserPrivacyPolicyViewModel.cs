using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using NeoSysLCS.DomainModel.Models;

namespace NeoSysLCS.Repositories.ViewModels
{
    public class ApplicationUserPrivacyPolicyViewModel : BaseViewModel
    {

        public string UserID { get; set; }

        public int PrivacyPolicyID { get; set; }

        public int PrivacyPolicyVersion { get; set; }

        public DateTime AcceptedAt { get; set; }
    }
}