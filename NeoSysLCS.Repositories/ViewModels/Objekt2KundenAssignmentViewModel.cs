
using System.Collections.Generic;

namespace NeoSysLCS.Repositories.ViewModels
{
    public class Objekt2KundenAssignmentViewModel
    {
        public int ObjektID { get; set; }
        public int KundeID { get; set; }
        public int StandortID { get; set; }

        public override bool Equals(object obj)
        {
            Objekt2KundenAssignmentViewModel candidate = obj as Objekt2KundenAssignmentViewModel;

            if (candidate == null)
            {
                return false;
            }

            if (candidate.ObjektID == ObjektID && candidate.KundeID == KundeID && candidate.StandortID == StandortID)
            {
                return true;
            }

            return false;
        }

        public override int GetHashCode()
        {
            const int prime = 251;

            return ((ObjektID * prime) + <PERSON>ndeID) * prime + StandortID;
        }
    }
}