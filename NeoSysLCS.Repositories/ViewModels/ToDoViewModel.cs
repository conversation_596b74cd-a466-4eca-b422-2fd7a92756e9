using System;
using System.Collections.Generic;
using NeoSysLCS.DomainModel.Models;

namespace NeoSysLCS.Repositories.ViewModels
{
    public class ToDoViewModel : BaseViewModel
    {

        public ToDoViewModel()
        {
        }

        public string Name { get; set; }

        public string ProjektleiterID { get; set; }
        public string ProjektleiterName { get; set; }

        public DateTime? OfferConsultationDate { get; set; }

        public DateTime? AnalyseDate { get; set; }

        public DateTime? EducationDate { get; set; }

        public DateTime? InquiryDate { get; set; }

        public DateTime? DocumentReceivedDate { get; set; }

        public DateTime? ContractCreatedDate { get; set; }

        public DateTime? DueDate { get; set; }

        public bool IsOffer { get; set; }

        public string Message { get; set; }
    }
}