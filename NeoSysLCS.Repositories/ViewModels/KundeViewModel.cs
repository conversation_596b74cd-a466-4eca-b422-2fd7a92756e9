using System;
using System.Collections.Generic;
using NeoSysLCS.DomainModel.Models;

namespace NeoSysLCS.Repositories.ViewModels
{
    public class KundeViewModel : BaseViewModel
    {

        public KundeViewModel()
        {
        }

        public int KundeID { get; set; }


        public string Name { get; set; }

        public string Beschreibung { get; set; }

        public DateTime? LetzteAktualisierung { get; set; } //date when document is set tu update state

        public IEnumerable<StandortViewModel> Standorte { get; set; }
        public ICollection<Kontakt> Kontakte { get; set; }

        public string ProjektleiterID { get; set; }
        public string ProjektleiterName { get; set; }

        public string ProjektleiterQSID { get; set; }
        public string ProjektleiterQSName { get; set; }

        public KundeAnalysisStatus AnalysisStatus { get; set; }

        public KundeStatus Status { get; set; }

        public DateTime? AnalysisDate { get; set; }

        public DateTime? DocumentReportDate { get; set; }

        public DateTime? EducationDate { get; set; }

        public DateTime? ContractCreatedDate { get; set; }

        public DateTime? ContractReturnedDate { get; set; }

        public DateTime? UpdateDate { get; set; } //date when document is set to update state

        public string ProjectNumberUpdate { get; set; }

        public DateTime? InquiryDate { get; set; }

        public DateTime? DocumentReceivedDate { get; set; }

        public DateTime? InvoiceDate { get; set; }

        public DateTime? InvoiceCreatedDate { get; set; }

        public int? Reccurence { get; set; }

        public string Link { get; set; }

        public int? Auftragsvolumen { get; set; }

        public string BillingInfo { get; set; }

        public string RechtsbereicheSumm { get; set; }

        public string HerausgeberSumm { get; set; }

        public string StandortobjekteSumm { get; set; }

        public string SprachenSumm { get; set; }

        public bool YearlyVisit { get; set; }
        public CustomerContractStatus CustomerContractStatus { get; set; }

        public static KundeViewModel fromKunde(Kunde x)
        {
            return new KundeViewModel()
            {
                KundeID = x.KundeID,
                Name = x.Name,
                Beschreibung = x.Beschreibung,
                ProjektleiterID = x.ProjektleiterID,
                ProjektleiterQSID = x.ProjektleiterQSID,
                Status = x.Status,
                AnalysisStatus = x.AnalysisStatus,
                LetzteAktualisierung = x.LetzteAktualisierung,
                UpdateDate = x.UpdateDate,
                ProjectNumberUpdate = (x.ProjectNumberUpdate == "" || x.ProjectNumberUpdate == null) ? " " : x.ProjectNumberUpdate,
                InquiryDate = x.InquiryDate,
                DocumentReceivedDate = x.DocumentReceivedDate,
                InvoiceDate = x.InvoiceDate,
                InvoiceCreatedDate = x.InvoiceCreatedDate,
                AnalysisDate = x.AnalysisDate,
                DocumentReportDate = x.DocumentReportDate,
                EducationDate = x.EducationDate,
                ContractCreatedDate = x.ContractCreatedDate,
                ContractReturnedDate = x.ContractReturnedDate,
                Reccurence = x.Reccurence,
                Link = (x.Link == "" || x.Link == null) ? " " : x.Link,
                Auftragsvolumen = x.Auftragsvolumen,
                BillingInfo = (x.BillingInfo == "" || x.BillingInfo == null) ? " " : x.BillingInfo,
                ErstelltAm = x.ErstelltAm,
                ErstelltVonID = x.ErstelltVonID,
                BearbeitetAm = x.BearbeitetAm,
                BearbeitetVonID = x.BearbeitetVonID,
                BearbeitetVon = (x.BearbeitetVon != null)
                        ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname)
                        : null,
                ErstelltVon = (x.ErstelltVon != null)
                        ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname)
                        : null,

                ProjektleiterName = (x.Projektleiter != null)
                        ? (x.Projektleiter.Vorname + " " + x.Projektleiter.Nachname)
                        : null,
                ProjektleiterQSName = (x.ProjektleiterQS != null)
                        ? (x.ProjektleiterQS.Vorname + " " + x.ProjektleiterQS.Nachname)
                        : null,
                YearlyVisit = x.YearlyVisit,
            };
        }
    }
}