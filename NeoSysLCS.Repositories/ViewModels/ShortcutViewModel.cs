using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using NeoSysLCS.DomainModel.Models;

namespace NeoSysLCS.Repositories.ViewModels
{
    public class ShortcutViewModel
    {
        public int ShortcutID { get; set; }
        public int StandortID { get; set; }
        public string Name { get; set; }
        public IQueryable<ApplicationUser> User { get; set; }
        //public object Users { get; internal set; }
    }
}