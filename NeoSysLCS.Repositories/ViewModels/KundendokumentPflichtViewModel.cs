using System;
using System.ComponentModel.DataAnnotations;
using NeoSysLCS.DomainModel.Models;

namespace NeoSysLCS.Repositories.ViewModels
{
    public class KundendokumentPflichtViewModel : BaseViewModel
    {
        public int KundendokumentPflichtID { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_Singular")]
        public int ErlassID { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_Singular")]
        public string ErlassTitel { get; set; }

         [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlassfassung_Singular")]
        public int? ErlassfassungID { get; set; }

         [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Pflicht_Beschreibung")]
        public string Beschreibung { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Pflicht_GueltigVon")]
        public DateTime GueltigVon { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Pflicht_GueltigBis")]
        public DateTime? GueltigBis { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Pflicht_Kommentar")]
        public string Kommentar { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Pflicht_Erfuellung")]
        public KundendokumentErfuellung? Erfuellung { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_KundendokumentPflicht_Erfuellungszeitpunkt")]
        public DateTime? Erfuellungszeitpunkt { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_KundendokumentPflicht_ErfuelltDurch")]
        public string ErfuelltDurch { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_KundendokumentPflicht_Verantwortlich")]
        public string Verantwortlich { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_KundendokumentPflicht_Ablageort")]
        public string Ablageort { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_KundendokumentPflicht_QsFreigabe")]
        public bool QsFreigabe { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_KundendokumentPflicht_Status")]
        public KundendokumentItemStatus Status { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Kundendokument_Singular")]
        public int KundendokumentID { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Pflicht_Singular")]
        public int PflichtID { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_StandortObjekt_Singular")]
        public int StandortObjektID { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_StandortObjekt_Singular")]
        public string StandortObjektTitel { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Kundenbezug_Singular")]
        public string Kundenbezug { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_KundendokumentForderungsversion_Relevant")]
        public bool Relevant { get; set; }
    }
}