using System;
using System.ComponentModel.DataAnnotations;
using NeoSysLCS.DomainModel.Models;

namespace NeoSysLCS.Repositories.ViewModels
{
    public class KundendokumentStandortobjektPflichtViewModel : BaseModel
    {
        public int PflichtID { get; set; }

        public int ErlassID { get; set; }

        public int ErlassfassungID { get; set; }

        public String ErlassTitel { get; set; }

        public int StandortObjektID { get; set; }

        public String StandortObjektTitel { get; set; }

        [Display(Name = "Beschreibung (TRANSLATE ME!)")]
        public string Beschreibung { get; set; }

        [Display(Name = "<PERSON><PERSON><PERSON><PERSON> von (TRANSLATE ME!)")]
        public DateTime GueltigVon { get; set; }

        [Display(Name = "<PERSON><PERSON><PERSON><PERSON> bis (TRANSLATE ME!)")]
        public DateTime? GueltigBis { get; set; }

        public string Kundenbezug { get; set; }

        public string ID { get; set; }
    }
}