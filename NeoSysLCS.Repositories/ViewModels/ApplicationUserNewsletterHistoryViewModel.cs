using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using NeoSysLCS.DomainModel.Models;

namespace NeoSysLCS.Repositories.ViewModels
{
    public class ApplicationUserNewsletterHistoryViewModel : BaseViewModel
    {
        public string ID { get; set; }

        public DateTime DateSent { get; set; }

        public string UserID { get; set; }

        public string NewsletterFileLink { get; set; }


    }
}