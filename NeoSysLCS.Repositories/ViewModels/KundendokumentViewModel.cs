using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using NeoSysLCS.DomainModel.Migrations;
using NeoSysLCS.DomainModel.Models;

namespace NeoSysLCS.Repositories.ViewModels
{
    public class KundendokumentViewModel : BaseViewModel
    {
        public KundendokumentViewModel()
        {

        }

        public int KundendokumentID { get; set; }
        public int StandortID { get; set; }

        public string StandortName { get; set; }

        public bool IsInitialeVersion { get; set; }

        [Required]
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Kundendokument_PublizierenAm")]
        public DateTime? PublizierenAm { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Kundendokument_QsFreigabe")]
        public bool HasQsFreigabe { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Kundendokument_Freigabe")]
        public bool HasFreigabe { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Kundendokument_QsKommentar")]
        public string QsKommentar { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Kundendokument_Status")]
        public KundendokumentStatus Status { get; set; }

        public ICollection<KundendokumentForderungsversion> KundendokumentForderungen { get; set; }
        public ICollection<KundendokumentErlassfassung> KundendokumentErlassfassungen { get; set; }
        public ICollection<KundendokumentPflicht> KundendokumentPflichten { get; set; }
        public ICollection<KundendokumentChecklist> KundendokumentChecklists { get; set; }
    }
}
