
using System;
using System.Collections.Generic;
using NeoSysLCS.DomainModel.Models;

namespace NeoSysLCS.Repositories.ViewModels
{
    public class AuswertungForderungenViewModel : BaseViewModel
    {
        

        public AuswertungForderungenViewModel()
        {
            Forderungsversionen = new List<Forderungsversion>();
            Erlasse = new List<Erlass>();
        }
        
        public int ForderungsversionID { get; set; }
        public int ForderungID { get; set; }
        public int? VorversionID { get; set; } // Vorversion
        public int? NachfolgeversionID { get; set; } // Nachfolgeversion
        public int? VersionsNummer { get; set; }
        public bool? QsFreigabe { get; set; }
        public bool? Freigabe { get; set; }
        public DateTime? Inkrafttretung { get; set; }
        public DateTime? Aufhebung { get; set; }
        public string ErlassTitel { get; set; }
        public int ErlassID { get; set; }
        public int? ArtikelID { get; set; }
        public int? ErlassfassungID { get; set; }
    

        public ICollection<Forderungsversion> Forderungsversionen { get; set; }
        public ICollection<Erlass> Erlasse { get; set; }
    }
}