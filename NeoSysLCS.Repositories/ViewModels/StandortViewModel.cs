using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using NeoSysLCS.DomainModel.Models;
using System;

namespace NeoSysLCS.Repositories.ViewModels
{
    [Serializable]
    public class StandortViewModel : BaseViewModel
    {
        public int StandortID { get; set; }

        [Required(AllowEmptyStrings = false)]
        [Display(Name = "Standortname")]
        public string Name { get; set; }

        public int KundeID { get; set; }
        public string InterneNotiz { get; set; }

        public virtual IQueryable<Rechtsbereich> Rechtsbereiche { get; set; }
        public virtual IQueryable<Sprache> Sprachen { get; set; }
        public virtual IQueryable<Herausgeber> Herausgeber { get; set; }
        public virtual IQueryable<Kontakt> Kontakte { get; set; }
        public virtual ICollection<StandortObjekt> StandortObjekte { get; set; }
        public string KundeName { get; set; }
        public virtual ICollection<Kundendokument> Kundendokumente { get; set; }
        public bool Read { get; set; }
        public bool Write { get; set; }
        public string Uebersetzung { get; set; }
        public string StandortBerichtUebersetzungId { get; set; }
        public int StandortBerichtSprachenId { get; set; }
        public string StandortBericht { get; set; }
        public DateTime? ReportDate { get; set; }
        public string ErlassImportDoc { get; set; }
    }
}