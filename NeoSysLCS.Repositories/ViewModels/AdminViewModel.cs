using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Microsoft.AspNet.Identity.EntityFramework;
using NeoSysLCS.DomainModel.Models;
using System.ComponentModel;
using System.Linq;

namespace NeoSysLCS.Repositories.ViewModels
{
    [Serializable]
    public class UserViewModel : BaseViewModel
    {
        public string Id { get; set; }

        [Required(AllowEmptyStrings = false, ErrorMessageResourceType = typeof(Resources.Properties.Resources), ErrorMessageResourceName = "Fehler_FehlendeWerte")]
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_User_Email")]
        [EmailAddress]
        public string Email { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_User_UserName")]
        public string UserName { get; set; }

        [Required(AllowEmptyStrings = false, ErrorMessageResourceType = typeof(Resources.Properties.Resources), ErrorMessageResourceName = "Fehler_FehlendeWerte")]
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_User_Vorname")]
        public string Vorname { get; set; }

        [Required(AllowEmptyStrings = false, ErrorMessageResourceType = typeof(Resources.Properties.Resources), ErrorMessageResourceName = "Fehler_FehlendeWerte")]
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_User_Nachname")]
        public string Nachname { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_User_Password")]
        public string Password { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_User_ConfirmPassword")]
        public string ConfirmPassword { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Sprache_Singular")]
        public int SpracheID { get; set; }
        
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Sprache_Singular")]
        public string Sprache { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Kunde_Singular")]
        public int KundeID { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Kunde_Singular")]
        public string Kundename { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_LockoutEndDate_Singular")]
        public bool LockoutEndDate { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "View_Standorte_Write_Zugriff")]
        public ICollection<StandortViewModel> WriteStandorte { get; set; }

        public ICollection<StandortViewModel> ReadStandorte { get; set; }

        //make only GUIRoles required, GUIRoles will be parsed into Roles
        //public ICollection<IdentityUserRole> Roles { get; set; }

        // roles field for display only in grid
        public string Roles { get; set; }

        [Required(ErrorMessageResourceType = typeof(Resources.Properties.Resources), ErrorMessageResourceName = "Fehler_FehlendeWerte")]
        public string GUIRoles { get; set; }

        [DefaultValue("Test")]
        public string Hint { get; set; }

        public string FullName { get; set; }

        [DefaultValue(6)]
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_User_NewsletterPeriod")]
        public int? NewsletterPeriod { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_KundeStatus_Newsletter")]
        public int? NewsletterPeriodGrid { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_KundeStatus_Newsletter")]
        public bool NewsletterRole { get; set; }

        public DateTime? TermsAccepted { get; set; }

        public bool IsTermsAccepted { get; set; }

        public IQueryable<ApplicationRoleViewModel> ApplicationRoles { get; set; }

        public ICollection<ApplicationUserPrivacyPolicyViewModel> PrivacyPolicies { get; set; }
    }

}