using System;
using System.ComponentModel.DataAnnotations;
using NeoSysLCS.DomainModel.Models;

namespace NeoSysLCS.Repositories.ViewModels
{
    public class IndividuelleForderungViewModel : BaseViewModel
    {
        public int IndividuelleForderungID { get; set; }

        [Display(Name = "Entitaet_IndividuelleForderung_Beschreibung", ResourceType = typeof(Resources.Properties.Resources))]
        public string Beschreibung { get; set; }

        [Display(Name = "Entitaet_IndividuelleForderung_Kommentar", ResourceType = typeof(Resources.Properties.Resources))]
        public string Kommentar { get; set; }

        [Display(Name = "Entitaet_IndividuelleForderung_GueltigVon", ResourceType = typeof(Resources.Properties.Resources))]
        public DateTime GueltigVon { get; set; }

        [Display(Name = "Entitaet_IndividuelleForderung_GueltigBis", ResourceType = typeof(Resources.Properties.Resources))]
        public DateTime? GueltigBis { get; set; }

        [Display(Name = "Entitaet_Standort_Singular", ResourceType = typeof(Resources.Properties.Resources))]
        public int StandortID { get; set; }
        public Standort Standort { get; set; }

        [Display(Name = "Entitaet_IndividuelleForderung_Standortobjekt", ResourceType = typeof(Resources.Properties.Resources))]
        public string StandortObjekt { get; set; }

        [Display(Name = "Entitaet_IndividuelleForderung_Standortobjekt", ResourceType = typeof(Resources.Properties.Resources))]
        public string StandortObjektTitel { get; set; }

        [Display(Name = "Entitaet_Kundenbezug_Singular", ResourceType = typeof(Resources.Properties.Resources))]
        public string Kundenbezug { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_KundendokumentIndividuelleForderung_Erfuellung")]
        public KundendokumentErfuellung Erfuellung { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_KundendokumentIndividuelleForderung_Erfuellungszeitpunkt")]
        public DateTime? Erfuellungszeitpunkt { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_KundendokumentIndividuelleForderung_ErfuelltDurch")]
        public string ErfuelltDurch { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_KundendokumentIndividuelleForderung_Verantwortlich")]
        public string Verantwortlich { get; set; }
        
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_KundendokumentIndividuelleForderung_Ablageort")]
        public string Ablageort { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Kundendokument_Singular")]
        public int KundendokumentID { get; set; }
        public Massnahme Massnahme { get; set; }
        public int MassnahmeNewCount { get; set; }
        public int MassnahmeInProgressCount { get; set; }
        public int MassnahmeFinishedCount { get; set; }
    }
}