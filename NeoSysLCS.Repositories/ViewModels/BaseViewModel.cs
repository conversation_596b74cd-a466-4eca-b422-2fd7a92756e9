using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;
using NeoSysLCS.DomainModel.Models;

namespace NeoSysLCS.Repositories.ViewModels
{
    [Serializable]
    public class BaseViewModel
    {

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Base_ErstelltVon")]
        public string ErstelltVonID { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Base_BearbeitetVon")]
        public string BearbeitetVonID { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Base_ErstelltAm")]
        public DateTime? ErstelltAm { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Base_BearbeitetAm")]
        public DateTime? BearbeitetAm { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Base_BearbeitetVon")]
        public String BearbeitetVon { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Base_ErstelltVon")]
        public String ErstelltVon { get; set; }

    }
}