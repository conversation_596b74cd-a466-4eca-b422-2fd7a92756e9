using System;
using System.ComponentModel.DataAnnotations;

namespace NeoSysLCS.Repositories.ViewModels.Validations
{
    [AttributeUsage(AttributeTargets.Property |
                    AttributeTargets.Field, AllowMultiple = false)]
    public sealed class ErlassfassungInkrafttretungAttribute : ValidationAttribute
    {
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            ErlassfassungViewModel erlassfassungViewModel = validationContext.ObjectInstance as ErlassfassungViewModel;

            if (erlassfassungViewModel.Inkrafttretung < erlassfassungViewModel.Beschluss)
            {
                return new ValidationResult(Resources.Properties.Resources.Fehler_NeuerOderGleichBeschlussdatum); 
            }
            return ValidationResult.Success;
        }
    }
}