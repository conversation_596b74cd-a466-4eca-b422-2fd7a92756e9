using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.Linq;


namespace NeoSysLCS.Repositories.ViewModels.Validations
{
    public class ObjektNameUniqueAttribute : ValidationAttribute
    {
        IUnitOfWork _unitOfWork = new UnitOfWork();

        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            int? sprachId = null;
            int? objektId = null;

            if (value != null)
            {
                var name = value.ToString();

                var objektViewModel = validationContext.ObjectInstance as ObjektViewModel;
                if (objektViewModel != null)
                {
                    //current instance is objekt 
                    var lang = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
                    var sprache = _unitOfWork.SpracheRepository.Get(s => s.Lokalisierung == lang).FirstOrDefault();
                    sprachId = sprache.SpracheID;
                    objektId = objektViewModel.ObjektID;
                }
                else
                {
                    var objektUebersetzung = validationContext.ObjectInstance as ObjektViewModel;
                    if (objektUebersetzung != null)
                    {
                        //current instance is objektuebersetzung
                        sprachId = objektUebersetzung.SpracheID;
                        objektId = objektUebersetzung.ObjektID;
                    }
                }

                if (sprachId != null && objektId != null)
                {
                    //check if there is already a name in the same languege
                    if (_unitOfWork.ObjektRepository.CheckIfNameAlreadyExists(sprachId.Value, objektId.Value,
                        name))
                    {
                        return new ValidationResult(Resources.Properties.Resources.Validation_ObjektNameUnique_Error);
                    }
                }
            }

            return ValidationResult.Success;
        }
    }
}