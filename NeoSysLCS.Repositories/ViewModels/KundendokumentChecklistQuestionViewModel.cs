using System.Linq;
using NeoSysLCS.DomainModel.Models;

namespace NeoSysLCS.Repositories.ViewModels
{
    public class KundendokumentChecklistQuestionViewModel : BaseViewModel
    {

        public int KundendokumentChecklistQuestionID { get; set; }
        public int KundendokumentChecklistID { get; set; }
        public string Title { get; set; }
        public string SharedImage { get; set; }
        public string Translation { get; set; }
        public KundendokumentQuestionAnswer Answer { get; set; }
        public string HeaderTitle { get; set; }
        public int HeaderID { get; set; }
        public string Numeration { get; set; }
        public string ChecklistType { get; set; }
        public int KundendokumentForderungsversionID { get; set; }
        public int MassnahmeNewCount { get; set; }
        public int MassnahmeInProgressCount { get; set; }
        public int MassnahmeFinishedCount { get; set; }

    }
}