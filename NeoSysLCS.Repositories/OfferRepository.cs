using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Web;
using Microsoft.AspNet.Identity;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories.ViewModels;

namespace NeoSysLCS.Repositories
{
    public enum OfferCategory
    {
        ActiveNewCustomers,
        ActiveExistingCustomers,
        Inactive,
        Unknown
    }
    public class OfferRepository
    {

        public static readonly OfferStatus[] ActiveNewCustomersStatusArray = { OfferStatus.New, OfferStatus.Qs, OfferStatus.Sent, OfferStatus.Accepted };
        public static readonly OfferStatus[] InactiveStatusArray = { OfferStatus.Rejected };
        public static readonly OfferStatus[] ActiveExistingCustomersStatusArray = { OfferStatus.InProgress, OfferStatus.QsAdditionaWork, OfferStatus.Completed, OfferStatus.ContractCreated, OfferStatus.ContractReturned, OfferStatus.Closed };

        private readonly IGenericRepository<Offer> _genericOfferRepository;
        private readonly UnitOfWork _unitOfWork;

        private readonly NeoSysLCS_Dev _context;

        public OfferRepository(NeoSysLCS_Dev context)
        {
            _context = context;
            _unitOfWork = new UnitOfWork(context);
            _genericOfferRepository = new GenericRepository<Offer>(context);
        }

        /// <summary>
        /// Gets a enumerable of offers filtered by the specified filter
        /// </summary>
        /// <param name="filter">The filter.</param>
        /// <param name="orderBy">The order by.</param>
        /// <param name="includeProperties">The include properties.</param>
        /// <returns>Returns the kunden</returns>
        public IEnumerable<Offer> Get(Expression<Func<Offer, bool>> filter = null, Func<IQueryable<Offer>, IOrderedQueryable<Offer>> orderBy = null, string includeProperties = "")
        {
            return _genericOfferRepository.Get(filter, orderBy, includeProperties);
        }

        /// <summary>
        /// Gets the offer by id
        /// </summary>
        /// <param name="id">The id.</param>
        /// <returns>Returns theThe Offer</returns>
        public Offer GetByID(object id)
        {
            return _genericOfferRepository.GetByID(id);
        }

        /// <summary>
        /// Inserts the specified Offer.
        /// </summary>
        /// <param name="entity">The entity.</param>
        public Offer Insert(OfferViewModel viewModel)
        {
            Offer entity = new Offer();
            entity.ReceivedAt = viewModel.ReceivedAt;
            entity.ExistingCustomerID = viewModel.ExistingCustomerID;
            entity.NewCustomerName = viewModel.NewCustomerName;
            entity.ProjectManagerID = viewModel.ProjectManagerID;
            entity.Comment = viewModel.Comment;
            entity.SentAt = viewModel.SentAt;
            entity.OfferVolume = viewModel.OfferVolume;
            entity.Link = viewModel.Link;
            entity.Remark = viewModel.Remark;
            entity.ConsultationAt = viewModel.SentAt?.AddDays(30);
            SetContractAndInvoiceRelatedDateFields(entity, viewModel);
            entity.Status = ResolveOfferStatus(viewModel);
            entity.AcceptedAt = ResolveOffersAcceptanceDate(viewModel);


            _genericOfferRepository.Insert(entity);

            CreateNewCustomerCustomerIfStatusAccepted(entity);

            return entity;
        }

        private OfferStatus ResolveOfferStatus(OfferViewModel viewModel)
        {
            if (viewModel.ExistingCustomerID != null && OfferStatusChangedTo(viewModel, OfferStatus.Accepted))
            {
                return OfferStatus.InProgress;
            }
            return viewModel.Status;
        }

        private DateTime? ResolveOffersAcceptanceDate(OfferViewModel viewModel)
        {
            if (viewModel.ExistingCustomerID != null && OfferStatusChangedTo(viewModel, OfferStatus.Accepted))
            {
                return DateTime.Today;
            }

            return viewModel.AcceptedAt;
        }

        private void SetContractAndInvoiceRelatedDateFields(Offer entity, OfferViewModel viewModel)
        {

            entity.ContractCreatedAt = viewModel.ContractCreatedAt;
            entity.ContractReturnedAt = viewModel.ContractReturnedAt;
            entity.CompletedAt = viewModel.CompletedAt;
            if (OfferStatusChangedTo(viewModel, OfferStatus.ContractCreated))
            {
                entity.ContractCreatedAt = DateTime.Today;
                if (viewModel.UpdateDate != null && viewModel.Reccurence != null)
                {
                    entity.ContractReturnedAt = viewModel.UpdateDate?.AddDays((double)viewModel.Reccurence);
                }
            } else if (OfferStatusChangedTo(viewModel, OfferStatus.ContractReturned))
            {
                entity.ContractReturnedAt = DateTime.Today;
            } else if (OfferStatusChangedTo(viewModel, OfferStatus.Completed))
            {
                entity.CompletedAt = DateTime.Today;
            }

        }

        private bool OfferStatusChangedTo(OfferViewModel viewModel, OfferStatus status)
        {
            Offer offer = null;
            if (viewModel.OfferID != null)
            {
                offer = this._genericOfferRepository.GetByID(viewModel.OfferID);
            }

            var statusChanged =
                (offer != null && offer.Status != viewModel.Status) ||
                offer == null;

            return statusChanged && viewModel.Status == status;
        }


        public void SetCortecTaskCreatedDate(int offerId)
        {
            var offer = this._genericOfferRepository.GetByID(offerId);
            offer.CortecTaskCreatedAt = DateTime.Now;
            _genericOfferRepository.Update(offer);

        }

        /// <summary>
        /// Updates the offer from the specified view model.
        /// </summary>
        /// <param name="viewModel">The view model.</param>
        public Offer Update(OfferViewModel viewModel)
        {
            Offer offer = _genericOfferRepository.GetByID(viewModel.OfferID);

            offer.OfferID = viewModel.OfferID;
            offer.ReceivedAt = viewModel.ReceivedAt;
            offer.ExistingCustomerID = viewModel.ExistingCustomerID;
            offer.NewCustomerName = viewModel.NewCustomerName;
            offer.ProjectManagerID = viewModel.ProjectManagerID;
            offer.Comment = viewModel.Comment;
            offer.ConsultationAt = viewModel.ConsultationAt;

            if (viewModel.Status == OfferStatus.Sent && offer.Status != OfferStatus.Sent)
            {
                offer.SentAt = DateTime.Today;
                offer.ConsultationAt = DateTime.Today.AddDays(30);
            }
            else
            {                
                if (offer.SentAt != viewModel.SentAt)
                {
                    offer.ConsultationAt = viewModel.SentAt?.AddDays(30);
                }
                else if (offer.SentAt == null && viewModel.SentAt != null)
                {
                    offer.ConsultationAt = viewModel.SentAt?.AddDays(30);
                }
                else
                {
                    offer.ConsultationAt = viewModel.ConsultationAt;
                }
                offer.SentAt = viewModel.SentAt;
            }

            SetContractAndInvoiceRelatedDateFields(offer, viewModel);
            offer.Status = ResolveOfferStatus(viewModel);
            offer.OfferVolume = viewModel.OfferVolume;
            offer.Link = viewModel.Link;
            offer.BearbeitetAm = DateTime.Now;
            offer.BearbeitetVonID = HttpContext.Current.User.Identity.GetUserId();
            offer.Remark = viewModel.Remark;
            offer.AcceptedAt = ResolveOffersAcceptanceDate(viewModel);
            offer.InvoicedAt = viewModel.InvoicedAt;

            _genericOfferRepository.Update(offer);
            
            CreateNewCustomerCustomerIfStatusAccepted(offer);
            return offer;
        }

        /// <summary>
        /// Create customer if Offer status is set to Accepted
        /// and NewCustomerName is provided.
        /// </summary>
        /// <param name="offer"></param>
        private void CreateNewCustomerCustomerIfStatusAccepted(Offer offer)
        {
            if (ShouldSkipCreationOfNewCustomerForGivenOffer(offer)) return;

            Kunde customer = CreateNewCustomerForGivenOffer(offer);

            SetExistingCustomerRelationForOffer(offer, customer);
        }

        /// <summary>
        /// Returns true if customer shouldn't be created for given offer otherwise false.
        /// </summary>
        /// <param name="offer"></param>
        /// <returns></returns>
        private bool ShouldSkipCreationOfNewCustomerForGivenOffer(Offer offer)
        {
            // if status not Accepted or we already have customer then we don't have to do anything
            if (offer.Status != OfferStatus.Accepted || offer.ExistingCustomerID != null) return true;

            // if we don't have newCustomerName we don't do anything
            if (offer.NewCustomerName == null) return true;

            // if customer already exists we don't have to do anything
            var existingCustomer = _unitOfWork.KundeRepository.GetKundeByName(offer.NewCustomerName).FirstOrDefault();
            if (existingCustomer != null) return true;

            return false;
        }

        // TODO reuse this logic in KundeRepository
        private Kunde CreateNewCustomerForGivenOffer(Offer offer)
        {
            var newCustomer = new Kunde
            {
                ErstelltAm = DateTime.Now,
                Name = offer.NewCustomerName,
                Projektleiter = offer.ProjectManager,
                ProjektleiterID = offer.ProjectManagerID,
                Status = KundeStatus.Analysis,
                AnalysisStatus = KundeAnalysisStatus.New,
                Link = offer.Link,
                Auftragsvolumen = offer.OfferVolume
            };

            _unitOfWork.KundeRepository.Insert(newCustomer);
            return newCustomer;
        }

        /// <summary>
        /// Sets relation to existing customer on Offer entity.
        /// </summary>
        /// <param name="offer"></param>
        /// <param name="customer"></param>
        private void SetExistingCustomerRelationForOffer(Offer offer, Kunde customer)
        {
            offer.ExistingCustomer = customer;
            offer.ExistingCustomerID = customer.KundeID;
        }

        // NEOS-620 Not used at the moment since a reject offer should not set existing customer to inactive
        private void UpdateExistingCustomerStatusIfOfferRejected(Offer offer) 
        {
            if (offer.ExistingCustomerID == null) return;

            var existingCustomer = _unitOfWork.KundeRepository.GetByID(offer.ExistingCustomerID);
            if (existingCustomer == null) return;

            if (offer.Status == OfferStatus.Rejected)
            {
                _unitOfWork.KundeRepository.UpdateCustomerStatus(existingCustomer, KundeStatus.Inactive);
            }

        }


        /// <summary>
        /// Deletes the specified entity.
        /// </summary>
        /// <param name="entityToDelete">The entity to delete.</param>
        public void Delete(Offer entityToDelete)
        {
            _genericOfferRepository.Delete(entityToDelete);
        }

        /// <summary>
        /// Gets all offer view models.
        /// </summary>
        /// <returns>Returns the view models</returns>
        public IQueryable<OfferViewModel> GetOfferViewModels(OfferStatus[] statuses = null)
        {
            if (statuses == null)
            {
                statuses = ActiveNewCustomersStatusArray;
            }

            return (from x in _context.Offer
                    join y in _context.Kunden on x.ExistingCustomerID equals y.KundeID into ok
                    from subKunde in ok.DefaultIfEmpty()
                    where statuses.Contains(x.Status) 
                select new OfferViewModel()
                {
                    OfferID = x.OfferID,
                    ReceivedAt = x.ReceivedAt,
                    ExistingCustomerID = x.ExistingCustomerID,
                    ExistingCustomerName = x.ExistingCustomer != null ? x.ExistingCustomer.Name : null,
                    NewCustomerName = x.NewCustomerName,
                    ProjektleiterQSID = subKunde.ProjektleiterQSID,
                    ProjectManagerID = x.ProjectManagerID,
                    ProjectManagerName = (x.ProjectManager != null) ? (x.ProjectManager.Vorname + " " + x.ProjectManager.Nachname) : null,
                    Comment = x.Comment,
                    Status = x.Status,
                    SentAt = x.SentAt,
                    ConsultationAt = x.ConsultationAt,
                    OfferVolume = x.OfferVolume,
                    Link = x.Link,
                    ErstelltAm = x.ErstelltAm,
                    ErstelltVonID = x.ErstelltVonID,
                    BearbeitetAm = x.BearbeitetAm,
                    BearbeitetVonID = x.BearbeitetVonID,
                    BearbeitetVon = (x.BearbeitetVon != null) ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname) : null,
                    ErstelltVon = (x.ErstelltVon != null) ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname) : null,
                    Remark = x.Remark,
                    ProjectNumberUpdate = subKunde.ProjectNumberUpdate,
                    CompletedAt = x.CompletedAt,
                    InvoicedAt = x.InvoicedAt,
                    ContractCreatedAt = x.ContractCreatedAt,
                    ContractReturnedAt = x.ContractReturnedAt,
                    UpdateDate =subKunde.UpdateDate,
                    Reccurence = subKunde.Reccurence,
                    AcceptedAt = x.AcceptedAt
                });
        }

        /// <summary>
        /// Gets all offer view models.
        /// </summary>
        /// <returns>Returns the view models</returns>
        public OfferViewModel GetOfferViewModelById(int id)
        {

            return (from x in _context.Offer
                    where x.OfferID == id
                    select new OfferViewModel()
                    {
                        OfferID = x.OfferID,
                        ReceivedAt = x.ReceivedAt,
                        ExistingCustomerID = x.ExistingCustomerID,
                        ExistingCustomerName = x.ExistingCustomer != null ? x.ExistingCustomer.Name : null,
                        NewCustomerName = x.NewCustomerName,
                        ProjectManagerID = x.ProjectManagerID,
                        ProjectManagerName = (x.ProjectManager != null) ? (x.ProjectManager.Vorname + " " + x.ProjectManager.Nachname) : null,
                        Comment = x.Comment,
                        Status = x.Status,
                        SentAt = x.SentAt,
                        ConsultationAt = x.ConsultationAt,
                        OfferVolume = x.OfferVolume,
                        Link = x.Link,
                        ErstelltAm = x.ErstelltAm,
                        ErstelltVonID = x.ErstelltVonID,
                        BearbeitetAm = x.BearbeitetAm,
                        BearbeitetVonID = x.BearbeitetVonID,
                        BearbeitetVon = (x.BearbeitetVon != null) ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname) : null,
                        ErstelltVon = (x.ErstelltVon != null) ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname) : null,
                        Remark = x.Remark,
                        CompletedAt = x.CompletedAt,
                        InvoicedAt = x.InvoicedAt,
                        ContractCreatedAt = x.ContractCreatedAt,
                        ContractReturnedAt = x.ContractReturnedAt,
                        AcceptedAt = x.AcceptedAt
                    }).FirstOrDefault();
                   
        }

        public IQueryable<Offer> GetOffersByCustomerId(int customerId)
        {
            return (from x in _context.Offer
                where x.ExistingCustomerID == customerId
                select x).OrderBy(o => o.OfferID);
        }

        /// <summary>
        /// Gets all offer view models.
        /// </summary>
        /// <returns>Returns the view models</returns>
        public string GetProjectNumberByOfferId(int offerId)
        {

            var offer = (from x in _context.Offer
                join y in _context.Kunden on x.ExistingCustomerID equals y.KundeID into ok
                from subKunde in ok.DefaultIfEmpty()
                where x.OfferID == offerId
                    select new OfferViewModel()
            {
                OfferID = x.OfferID,
                ReceivedAt = x.ReceivedAt,
                ExistingCustomerID = x.ExistingCustomerID,
                ExistingCustomerName = x.ExistingCustomer != null ? x.ExistingCustomer.Name : null,
                NewCustomerName = x.NewCustomerName,
                ProjectManagerID = x.ProjectManagerID,
                ProjectManagerName = (x.ProjectManager != null) ? (x.ProjectManager.Vorname + " " + x.ProjectManager.Nachname) : null,
                Comment = x.Comment,
                Status = x.Status,
                SentAt = x.SentAt,
                ConsultationAt = x.ConsultationAt,
                OfferVolume = x.OfferVolume,
                Link = x.Link,
                ErstelltAm = x.ErstelltAm,
                ErstelltVonID = x.ErstelltVonID,
                BearbeitetAm = x.BearbeitetAm,
                BearbeitetVonID = x.BearbeitetVonID,
                BearbeitetVon = (x.BearbeitetVon != null) ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname) : null,
                ErstelltVon = (x.ErstelltVon != null) ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname) : null,
                Remark = x.Remark,
                ProjectNumberUpdate = subKunde.ProjectNumberUpdate,
                CompletedAt = x.CompletedAt,
                InvoicedAt = x.InvoicedAt,
                ContractCreatedAt = x.ContractCreatedAt,
                ContractReturnedAt = x.ContractReturnedAt,
                UpdateDate = subKunde.UpdateDate,
                Reccurence = subKunde.Reccurence,
                AcceptedAt = x.AcceptedAt
            }).FirstOrDefault();
            return offer != null ? offer.ProjectNumberUpdate : "";
        }

    }
}