using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Linq.Expressions;
using System.Web;
using System.Xml.Linq;
using Microsoft.AspNet.Identity;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories.Exceptions;
using NeoSysLCS.Repositories.Helper;
using NeoSysLCS.Repositories.Interfaces;
using NeoSysLCS.Repositories.ViewModels;


namespace NeoSysLCS.Repositories
{
    /// <summary>
    /// The Repository for Erlassfassungen
    /// </summary>
    public class ErlassfassungRepository : IReloadable<Erlassfassung>
    {
        private readonly NeoSysLCS_Dev _context;
        private readonly IGenericRepository<Erlassfassung> _genericErlassfassungRepository;
        private readonly UnitOfWork _unitOfWork;
        private int _currentLang;

        /// <summary>
        /// Initializes a new instance.
        /// </summary>
        /// <param name="contextCandidate">The context candidate.</param>
        public ErlassfassungRepository(NeoSysLCS_Dev contextCandidate)
        {
            _context = contextCandidate;
            _genericErlassfassungRepository = new GenericRepository<Erlassfassung>(contextCandidate);
            _unitOfWork = new UnitOfWork(contextCandidate);
            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            Sprache sprache = _context.Sprachen.FirstOrDefault(s => s.Lokalisierung == language);
            _currentLang = sprache.SpracheID;
        }

        /// <summary>
        /// Gets a eralssfassungen filtered by the specified filter.
        /// </summary>
        /// <param name="filter">The filter.</param>
        /// <param name="orderBy">The order by.</param>
        /// <param name="includeProperties">The include properties.</param>
        /// <returns>Returns the filtered erlassfassungen.</returns>
        public IEnumerable<Erlassfassung> Get(Expression<Func<Erlassfassung, bool>> filter = null, Func<IQueryable<Erlassfassung>, IOrderedQueryable<Erlassfassung>> orderBy = null, string includeProperties = "")
        {
            return _genericErlassfassungRepository.Get(filter, orderBy, includeProperties);
        }

        /// <summary>
        /// Gets the eralssfassung by id.
        /// </summary>
        /// <param name="id">The id.</param>
        /// <returns>Returns the erlassfassung.</returns>
        public Erlassfassung GetByID(object id)
        {
            return _genericErlassfassungRepository.GetByID(id);
        }

        /// <summary>
        /// Gets the view model by id.
        /// </summary>
        /// <param name="erlassfassungId">The erlassfassung id.</param>
        /// <returns>Returns the view model.</returns>
        public ErlassfassungViewModel GetViewModelByID(int erlassfassungId)
        {
            var erlassfassung = _genericErlassfassungRepository.Get(e => e.ErlassfassungID == erlassfassungId, null, null).FirstOrDefault();
            var viewModel = new ErlassfassungViewModel();
            if (erlassfassung != null)
            {
                viewModel.ErlassfassungID = erlassfassung.ErlassfassungID;
                viewModel.Beschluss = erlassfassung.Beschluss;
                viewModel.ErlassID = erlassfassung.ErlassID;
            }

            return viewModel;
        }

        /// <summary>
        /// Gets the view models by ids.
        /// </summary>
        /// <param name="erlassfassungId">The erlassfassung id.</param>
        /// <returns>Returns the view model.</returns>
        public IQueryable<Erlassfassung> GetByIDs(int[] erlassfassungIds)
        {
            return _genericErlassfassungRepository.Get(e => erlassfassungIds.Contains(e.ErlassfassungID), null, null);
        }

        /// <summary>
        /// Inserts the erlassfassung from the specified view model
        /// </summary>
        /// <param name="viewModel">The view model.</param>
        public void Insert(ErlassfassungViewModel viewModel)
        {
            Erlassfassung erlassversion = new Erlassfassung();
            erlassversion.ErlassID = viewModel.ErlassID;
            erlassversion.Beschluss = viewModel.Beschluss;
            erlassversion.Inkrafttretung = viewModel.Inkrafttretung;
            erlassversion.ErstelltAm = DateTime.Now;
            if (HttpContext.Current == null) // HttpContext.Current is null when code method is called from HangFire job ErlassImport
            {
                erlassversion.ErstelltVonID = "a303b999-6438-47cf-9e93-8505e91fe582"; // <EMAIL>
            }
            else
            {
                erlassversion.ErstelltVonID = HttpContext.Current.User.Identity.GetUserId();
            }

            List<string> list = new List<string>();
            list.Add("Quelle");
            list.Add("BetroffenKommentar");
            list.Add("NichtBetroffenKommentar");
            list.Add("LinkBetroffenKommentar");
            list.Add("LinkNichtBetroffenKommentar");
            list.Add("InternerKommentar");

            XDocument uebersetzung = XMLHelper.CreateNewUebersetzung(list);

            Dictionary<string, string> elements = new Dictionary<string, string>();
            elements.Add("Quelle", viewModel.Quelle);
            elements.Add("BetroffenKommentar", viewModel.BetroffenKommentar);
            elements.Add("NichtBetroffenKommentar", viewModel.NichtBetroffenKommentar);
            elements.Add("LinkBetroffenKommentar", viewModel.LinkBetroffenKommentar);
            elements.Add("LinkNichtBetroffenKommentar", viewModel.LinkNichtBetroffenKommentar);
            elements.Add("InternerKommentar", viewModel.InternerKommentar);

            erlassversion.Uebersetzung = XMLHelper.UpdateUebersetzung(uebersetzung, elements, _currentLang);
            
            _genericErlassfassungRepository.Insert(erlassversion);
        }

        /// <summary>
        /// Updates the erlassfassung from the specified view model.
        /// </summary>
        /// <param name="viewModel">The view model.</param>
        public void Update(ErlassfassungViewModel viewModel)
        {
            Erlassfassung erlassversion = _genericErlassfassungRepository.GetByID(viewModel.ErlassfassungID);
            erlassversion.Beschluss = viewModel.Beschluss;
            erlassversion.Inkrafttretung = viewModel.Inkrafttretung;
            erlassversion.BearbeitetAm = DateTime.Now;
            erlassversion.BearbeitetVonID = HttpContext.Current.User.Identity.GetUserId();

            if (erlassversion.Uebersetzung != "")
            {
                var uebersetzung = XDocument.Parse(erlassversion.Uebersetzung);

                Dictionary<string, string> elements = new Dictionary<string, string>();
                elements.Add("Quelle", viewModel.Quelle);
                elements.Add("BetroffenKommentar", viewModel.BetroffenKommentar);
                elements.Add("NichtBetroffenKommentar", viewModel.NichtBetroffenKommentar);
                if (viewModel.LinkBetroffenKommentar != "---")
                {
                    elements.Add("LinkBetroffenKommentar", viewModel.LinkBetroffenKommentar);
                }
                if (viewModel.LinkNichtBetroffenKommentar != "---")
                {
                    elements.Add("LinkNichtBetroffenKommentar", viewModel.LinkNichtBetroffenKommentar);
                }
                elements.Add("InternerKommentar", viewModel.InternerKommentar);
                erlassversion.Uebersetzung = XMLHelper.UpdateUebersetzung(uebersetzung, elements, _currentLang);
            }
            _genericErlassfassungRepository.Update(erlassversion);
        }

        /// <summary>
        /// Deletes the erlassfassung by the specified id.
        /// </summary>
        /// <param name="id">The id.</param>
        /// <exception cref="NeoSysLCS.Repositories.Exceptions.HasRelationsException">Thrown when the erlassfassung is still in use
        /// </exception>
        public void Delete(int id)
        {
            var obj = _genericErlassfassungRepository.Get(e => e.ErlassfassungID == id, null, "Forderungsversionen,Pflichten").FirstOrDefault();
            if (obj != null && obj.Forderungsversionen.Count > 0)
            {
                throw new HasRelationsException(Resources.Properties.Resources.Entitaet_Forderung_Plural, HasRelationsException.RelationType.ContainsEntities);
            }
            if (obj != null && obj.Pflichten.Count > 0)
            {
                throw new HasRelationsException(Resources.Properties.Resources.Entitaet_Pflicht_Plural, HasRelationsException.RelationType.ContainsEntities);
            }
            _genericErlassfassungRepository.Delete(id);
        }

        /// <summary>
        /// Reloads the specified erlassfassung from the database.
        /// </summary>
        /// <param name="entityToReload">The entity to reload.</param>
        public void Reload(Erlassfassung entityToReload)
        {
            _genericErlassfassungRepository.Reload(entityToReload);
        }

        /// <summary>
        /// Gets all erlassfassung view models.
        /// </summary>
        /// <param name="erlassId">The erlass identifier.</param>
        /// <returns>Returns the view models.</returns>
        public IQueryable<ErlassfassungViewModel> GetAllErlassfassungViewModels(int erlassId)
        {
            var erlassversion = (from x in _context.Erlassfassungen
                   where (x.ErlassID == erlassId)
                   select new ErlassfassungViewModel()
                   {
                       ErlassfassungID = x.ErlassfassungID,
                       Beschluss = x.Beschluss,
                       Inkrafttretung = x.Inkrafttretung,
                       ErstelltAm = x.ErstelltAm,
                       KommentarID = x.KommentarID,
                       PLFreigabe = x.Kommentar != null ? x.Kommentar.PLFreigabe == true : false,
                       ErstelltVonID = x.ErstelltVonID,
                       ErstelltVon = (x.ErstelltVon != null) ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname) : null,
                       BearbeitetAm = x.BearbeitetAm,
                       BearbeitetVonID = x.BearbeitetVonID,
                       BearbeitetVon = (x.BearbeitetVon != null) ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname) : null,
                       Uebersetzung = x.Uebersetzung,
                   }).ToList();

            foreach (var erlass in erlassversion)
            {
                XDocument uebersetzung = XDocument.Parse(erlass.Uebersetzung);

                erlass.Quelle = XMLHelper.GetUebersetzungBySpecificLanguageFromXmlField(uebersetzung, "Quelle", _currentLang);
                erlass.BetroffenKommentar = XMLHelper.GetUebersetzungBySpecificLanguageFromXmlField(uebersetzung, "BetroffenKommentar", _currentLang);
                erlass.NichtBetroffenKommentar = XMLHelper.GetUebersetzungBySpecificLanguageFromXmlField(uebersetzung, "NichtBetroffenKommentar", _currentLang);
                erlass.LinkBetroffenKommentar = XMLHelper.GetUebersetzungBySpecificLanguageFromXmlField(uebersetzung, "LinkBetroffenKommentar", _currentLang);
                erlass.LinkNichtBetroffenKommentar = XMLHelper.GetUebersetzungBySpecificLanguageFromXmlField(uebersetzung, "LinkNichtBetroffenKommentar", _currentLang);
                erlass.InternerKommentar = XMLHelper.GetUebersetzungBySpecificLanguageFromXmlField(uebersetzung, "InternerKommentar", _currentLang);
            }

            var query = erlassversion.AsQueryable();
            return query;
        }

        public IQueryable<ErlassfassungViewModel> GetErlassversionDetailPartial(int erlassId, int spracheID)
        {
            var erlassversion = (from x in _context.Erlassfassungen
                   where (x.ErlassID == erlassId)
                   select new ErlassfassungViewModel()
                   {
                       ErlassfassungID = x.ErlassfassungID,
                       Beschluss = x.Beschluss,
                       Inkrafttretung = x.Inkrafttretung,                       
                   });

            foreach (var erlass in erlassversion)
            {
                XDocument uebersetzung = XDocument.Parse(erlass.Uebersetzung);

                erlass.Quelle = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Quelle", spracheID);
                erlass.BetroffenKommentar = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "BetroffenKommentar", spracheID);
                erlass.NichtBetroffenKommentar = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "NichtBetroffenKommentar", spracheID);
                erlass.LinkBetroffenKommentar = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "LinkBetroffenKommentar", spracheID);
                erlass.LinkNichtBetroffenKommentar = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "LinkNichtBetroffenKommentar", spracheID);
                erlass.InternerKommentar = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "InternerKommentar", spracheID);
            }

            var query = erlassversion.AsQueryable();
            return query;
        }

        public void InsertKommentarErlassfassung(Kommentar kommentar, int erlassID)
        {
            Erlassfassung erlassversion = new Erlassfassung();
            erlassversion.ErlassID = erlassID;
            erlassversion.Beschluss = kommentar.Beschluss;
            erlassversion.Inkrafttretung = kommentar.Inkrafttretung;
            erlassversion.KommentarID = kommentar.KommentarID;
            erlassversion.ErstelltAm = DateTime.Now;
            erlassversion.ErstelltVonID = HttpContext.Current.User.Identity.GetUserId();

            erlassversion.Uebersetzung = kommentar.Uebersetzung;


            _genericErlassfassungRepository.Insert(erlassversion);
        }

        public IEnumerable<ErlassfassungViewModel> GetErlassfassungUebersetzungenByErlassfassung(int id)
        {
            Erlassfassung erlassfassung = _genericErlassfassungRepository.GetByID(id);

            List<int> sprachIds = _context.Sprachen.Select(x => x.SpracheID).ToList();
            List<ErlassfassungViewModel> uebersetzungen = new List<ErlassfassungViewModel>();
            XDocument xml = XDocument.Parse(erlassfassung.Uebersetzung);

            foreach (int sprachId in sprachIds)
            {
                ErlassfassungViewModel uebersetzung = new ErlassfassungViewModel();
                uebersetzung.ErlassfassungUebersetzungID = id.ToString() + "_" + sprachId.ToString();
                uebersetzung.Quelle = XMLHelper.GetAllUebersetzungFromXmlField(xml, "Quelle", sprachId);
                uebersetzung.BetroffenKommentar = XMLHelper.GetAllUebersetzungFromXmlField(xml, "BetroffenKommentar", sprachId);
                uebersetzung.NichtBetroffenKommentar = XMLHelper.GetAllUebersetzungFromXmlField(xml, "NichtBetroffenKommentar", sprachId);
                uebersetzung.LinkBetroffenKommentar = XMLHelper.GetAllUebersetzungFromXmlField(xml, "LinkBetroffenKommentar", sprachId);
                uebersetzung.LinkNichtBetroffenKommentar = XMLHelper.GetAllUebersetzungFromXmlField(xml, "LinkNichtBetroffenKommentar", sprachId);
                uebersetzung.InternerKommentar = XMLHelper.GetAllUebersetzungFromXmlField(xml, "InternerKommentar", sprachId);
                uebersetzung.SpracheID = sprachId;
                uebersetzung.ErlassfassungID = id;
                uebersetzungen.Add(uebersetzung);
            }

            return uebersetzungen.AsQueryable();
        }

        public void InsertUebersetzungErlassfassung(ErlassfassungViewModel uebersetzung, int erlassfassungID, int sprache)
        {
            Erlassfassung erlassfassung = _genericErlassfassungRepository.GetByID(erlassfassungID);

            var xml = XDocument.Parse(erlassfassung.Uebersetzung);
            Dictionary<string, string> elements = new Dictionary<string, string>();
            elements.Add("Quelle", uebersetzung.Quelle);
            elements.Add("BetroffenKommentar", uebersetzung.BetroffenKommentar);
            elements.Add("NichtBetroffenKommentar", uebersetzung.NichtBetroffenKommentar);
            elements.Add("LinkBetroffenKommentar", uebersetzung.LinkBetroffenKommentar);
            elements.Add("LinkNichtBetroffenKommentar", uebersetzung.LinkNichtBetroffenKommentar);
            elements.Add("InternerKommentar", uebersetzung.InternerKommentar);

            erlassfassung.Uebersetzung = XMLHelper.UpdateUebersetzung(xml, elements, sprache);

            _genericErlassfassungRepository.Update(erlassfassung);
        }

        public int getLatestErlassfassungIdByErlass(int erlassID)
        {
            return (from x in _context.Erlassfassungen
                    where x.ErlassID == erlassID
                    select x.ErlassfassungID).ToList().OrderByDescending(o => o).First();
        }

    }
}