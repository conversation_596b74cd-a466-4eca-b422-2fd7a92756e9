using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Xml.Linq;

namespace NeoSysLCS.Repositories.Helper
{
    public class XMLHelper
    {
        public static string GetUebersetzungFromXmlField(XDocument xDocument, string element, int currentLang, bool useFallback = true)
        {
            // Try to get translation for the current language.
            string translation = TryGetElementValue(xDocument, element, currentLang);

            // If translation is empty or null, try fallback languages.
            if (string.IsNullOrEmpty(translation))
            {
                translation = useFallback ? GetFallbackTranslation(xDocument, element) : Resources.Properties.Resources.Fehler_Keine_Uebersetzung;
            }

            return translation;
        }

        private static string TryGetElementValue(XDocument doc, string elementName, int langId)
        {
            // Check if the element exists for the given language ID and return its value.
            return doc.Descendants("Sprache")
                .FirstOrDefault(x => x.FirstAttribute.Value == langId.ToString())?
                .Descendants(elementName)
                .Select(x => x.Value)
                .FirstOrDefault() ?? string.Empty;
        }

        private static string GetFallbackTranslation(XDocument doc, string elementName)
        {
            // Define the fallback language IDs in order of priority.
            int[] fallbackLanguages = new int[] { 1, 2, 3, 4 };

            // Loop through fallback languages and return the first non-empty translation found.
            foreach (int langId in fallbackLanguages)
            {
                string result = TryGetElementValue(doc, elementName, langId);
                if (!string.IsNullOrEmpty(result))
                {
                    return result;
                }
            }

            // Return an error message if no translation found in any language.
            return Resources.Properties.Resources.Fehler_Keine_Uebersetzung;
        }

        public static string GetUebersetzungBySpecificLanguageFromXmlField(XDocument xDocument, string element, int _currentLang)
        {
            string uebersetzung = xDocument.Root.Descendants("Sprache").Where(x => x.FirstAttribute.Value == _currentLang.ToString()).Descendants(element).Single().Value;
            if (string.IsNullOrEmpty(uebersetzung))
            {
                uebersetzung = (xDocument.Descendants("Sprache").Where(x => x.FirstAttribute.Value == _currentLang.ToString()).Descendants(element).Single().Value != "" ?
                    uebersetzung = xDocument.Descendants("Sprache").Where(x => x.FirstAttribute.Value == _currentLang.ToString()).Descendants(element).Single().Value : null);
            }

            return uebersetzung;
        }

        public static XDocument CreateNewUebersetzung(List<string> elements)
        {
            XDocument uebersetzung = new XDocument(
                new XElement("Uebersetzung",
                    new XElement("Sprache", new XAttribute("ID", 1)),
                    new XElement("Sprache", new XAttribute("ID", 2)),
                    new XElement("Sprache", new XAttribute("ID", 3)),
                    new XElement("Sprache", new XAttribute("ID", 4))));


            foreach (var element in elements)
            {
                uebersetzung.Element("Uebersetzung").Elements("Sprache").Where(e => e.Attribute("ID").Value == "1").FirstOrDefault().Add(new XElement(element));
                uebersetzung.Element("Uebersetzung").Elements("Sprache").Where(e => e.Attribute("ID").Value == "2").FirstOrDefault().Add(new XElement(element));
                uebersetzung.Element("Uebersetzung").Elements("Sprache").Where(e => e.Attribute("ID").Value == "3").FirstOrDefault().Add(new XElement(element));
                uebersetzung.Element("Uebersetzung").Elements("Sprache").Where(e => e.Attribute("ID").Value == "4").FirstOrDefault().Add(new XElement(element));
            }

            return uebersetzung;
        }

        public static string UpdateUebersetzung(XDocument xDocument, Dictionary<string, string> elements, int spracheID)
        {
            foreach (var element in elements)
            {
                xDocument.Element("Uebersetzung").Elements("Sprache")
                    .Where(e => e.Attribute("ID").Value == spracheID.ToString()).FirstOrDefault()
                    .SetElementValue(element.Key, element.Value ?? "");
            }

            return xDocument.ToString();
        }

        public static string GetAllUebersetzungFromXmlField(XDocument xDocument, string element, int currentLang)
        {
            if (xDocument?.Root == null)
            {
                // Handle the case where the document or the root element is null
                return "Error: Document or root element is null.";
            }

            try
            {
                // Attempt to get the translation value
                var languageElement = xDocument.Root
                    .Descendants("Sprache")
                    .FirstOrDefault(x => x.FirstAttribute != null && x.FirstAttribute.Value == currentLang.ToString());

                if (languageElement == null)
                {
                    // Handle the case where no language element matches the current language
                    return "Error: No matching language element found.";
                }

                var translationElement = languageElement.Descendants(element).SingleOrDefault();

                return (translationElement != null && !string.IsNullOrEmpty(translationElement.Value)) ? translationElement.Value : Resources.Properties.Resources.Fehler_Keine_Uebersetzung;
            }
            catch (InvalidOperationException ex)
            {
                // Handle the case where more than one element was found when expecting a single one
                return $"Error: An exception occurred - {ex.Message}";
            }
        }

    }
}