using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity;
using NeoSysLCS.DomainModel.Models;
using System.Globalization;
using System.Linq;
using System.Web;
using System.Xml.Linq;
using Microsoft.AspNet.Identity;
using NeoSysLCS.Repositories.Helper;
using NeoSysLCS.Repositories.ViewModels;

namespace NeoSysLCS.Repositories
{
    public class PrivacyPolicyRepository
    {
        private readonly NeoSysLCS_Dev _context;
        private readonly IGenericRepository<PrivacyPolicy> _genericPrivacyPolicyRepository;
        private readonly IGenericRepository<ApplicationUser> _genericUserRepository;

        private readonly IUnitOfWork _unitOfWork;
        private int _currentLang;

        public PrivacyPolicyRepository(NeoSysLCS_Dev context)
        {
            _context = context;
            _genericPrivacyPolicyRepository = new GenericRepository<PrivacyPolicy>(context);
            _genericUserRepository = new GenericRepository<ApplicationUser>(context);
            _unitOfWork = new UnitOfWork();

            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            Sprache sprache = _context.Sprachen.FirstOrDefault(s => s.Lokalisierung == language);

            _currentLang = sprache.SpracheID;

        }

        public IQueryable<PrivacyPolicyViewModel> GetAllViewModels(int preferedLanguage = -1)
        {
            var lang = preferedLanguage < 0 ? _currentLang : preferedLanguage;
            var policies = (from x in _context.PrivacyPolicy
                select new PrivacyPolicyViewModel()
                {
                    PrivacyPolicyID = x.ID,
                    ChangedAt = x.ChangedAt,
                    Uebersetzung = x.Uebersetzung,
                }).ToList();

            foreach (var policy in policies)
            {
                XDocument uebersetzung = XDocument.Parse(policy.Uebersetzung);

                policy.Name = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Name", lang);
                policy.Quelle = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Quelle", lang);
                var version = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Version", lang, false);
                policy.Version = version != Resources.Properties.Resources.Fehler_Keine_Uebersetzung ? version : "0";

            }

            return policies.AsQueryable();
        }

        public IQueryable<PrivacyPolicyViewModel> GetNotAcceptedViewModels(string userId)
        {
            var policies = (from x in _context.PrivacyPolicy
                select new PrivacyPolicyViewModel()
                {
                    PrivacyPolicyID = x.ID,
                    ChangedAt = x.ChangedAt,
                    Uebersetzung = x.Uebersetzung,
                }).ToList();

            foreach (var policy in policies)
            {
                XDocument uebersetzung = XDocument.Parse(policy.Uebersetzung);

                policy.Name = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Name", _currentLang);
                policy.Quelle = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Quelle", _currentLang);
                var version = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Version", _currentLang, false);
                policy.Version = version != Resources.Properties.Resources.Fehler_Keine_Uebersetzung ? version : "0";
            }

            ICollection<PrivacyPolicyViewModel> notAcceptedPolicies = new Collection<PrivacyPolicyViewModel>();
            foreach (var policy in policies)
            {
                var acceptedByUser = _unitOfWork.ApplicationUserPrivacyPolicyRepository.Exists(userId, policy.PrivacyPolicyID,
                    Int32.Parse(policy.Version));
                if (!acceptedByUser)
                {
                    notAcceptedPolicies.Add(policy);
                }
            }

            return notAcceptedPolicies.AsQueryable();
        }

        public void Insert(PrivacyPolicyViewModel viewModel)
        {
            PrivacyPolicy pp = new PrivacyPolicy();
            pp.ID = viewModel.PrivacyPolicyID;
            pp.ChangedAt = DateTime.Now;
            pp.ErstelltAm = DateTime.Now;
            pp.ErstelltVonID = HttpContext.Current.User.Identity.GetUserId();

            List<string> list = new List<string>();
            list.Add("Name");
            list.Add("Quelle");
            list.Add("Version");

            XDocument uebersetzung = XMLHelper.CreateNewUebersetzung(list);

            Dictionary<string, string> elements = new Dictionary<string, string>();
            elements.Add("Name", viewModel.Name);
            elements.Add("Quelle", viewModel.Quelle);
            elements.Add("Version", "1");

            pp.Uebersetzung = XMLHelper.UpdateUebersetzung(uebersetzung, elements, _currentLang);

            _genericPrivacyPolicyRepository.Insert(pp);
        }

        public void Update(PrivacyPolicyViewModel viewModel)
        {
            PrivacyPolicy pp = _genericPrivacyPolicyRepository.GetByID(viewModel.PrivacyPolicyID);
            pp.ID = viewModel.PrivacyPolicyID;
            pp.ChangedAt = DateTime.Now;
            pp.BearbeitetAm = DateTime.Now;
            pp.BearbeitetVonID = HttpContext.Current.User.Identity.GetUserId();

            var uebersetzung = XDocument.Parse(pp.Uebersetzung);

            var version = ExtractFileVersionFromUebersetzungen(uebersetzung, _currentLang);

            version++;

            Dictionary<string, string> elements = new Dictionary<string, string>();
            elements.Add("Name", viewModel.Name);
            elements.Add("Quelle", viewModel.Quelle);
            elements.Add("Version", version.ToString());

            pp.Uebersetzung = XMLHelper.UpdateUebersetzung(uebersetzung, elements, _currentLang);

            _genericPrivacyPolicyRepository.Update(pp);
        }

        public void Delete(int id)
        {
            _genericPrivacyPolicyRepository.Delete(id);
            ResetPolicyAcceptedForAllUsers();
        }

        public IQueryable<PrivacyPolicyViewModel> GetUebersetzungenByID(int id)
        {
            PrivacyPolicy pp = _genericPrivacyPolicyRepository.GetByID(id);

            List<int> sprachIds = _context.Sprachen.Select(x => x.SpracheID).ToList();
            List<PrivacyPolicyViewModel> uebersetzungen = new List<PrivacyPolicyViewModel>();
            XDocument xml = XDocument.Parse(pp.Uebersetzung);

            foreach (int sprachId in sprachIds)
            {
                PrivacyPolicyViewModel uebersetzung = new PrivacyPolicyViewModel();
                uebersetzung.PrivacyPolicyUebersetzungID = id.ToString() + "_" + sprachId.ToString();
                uebersetzung.Name = XMLHelper.GetAllUebersetzungFromXmlField(xml, "Name", sprachId);
                uebersetzung.Quelle = XMLHelper.GetAllUebersetzungFromXmlField(xml, "Quelle", sprachId);
                uebersetzung.Version = XMLHelper.GetAllUebersetzungFromXmlField(xml, "Version", sprachId);

                uebersetzung.SpracheID = sprachId;
                uebersetzung.PrivacyPolicyID = id;
                uebersetzungen.Add(uebersetzung);
            }

            return uebersetzungen.AsQueryable();
        }

        public void InsertUebersetzung(PrivacyPolicyViewModel uebersetzung, int id)
        {
            PrivacyPolicy pp = _genericPrivacyPolicyRepository.GetByID(id);

            var xml = XDocument.Parse(pp.Uebersetzung);
            Dictionary<string, string> elements = new Dictionary<string, string>();
            elements.Add("Name", uebersetzung.Name);
            elements.Add("Quelle", uebersetzung.Quelle);
            elements.Add("Version", uebersetzung.Version);

            pp.Uebersetzung = XMLHelper.UpdateUebersetzung(xml, elements, uebersetzung.SpracheID);

            _genericPrivacyPolicyRepository.Update(pp);
        }

        public void UpdateUebersetzung(PrivacyPolicyViewModel viewModel)
        {
            PrivacyPolicy pp = _genericPrivacyPolicyRepository.GetByID(viewModel.PrivacyPolicyID);

            var uebersetzung = XDocument.Parse(pp.Uebersetzung);

            Dictionary<string, string> elements = new Dictionary<string, string>();
            elements.Add("Name", viewModel.Name);
            elements.Add("Quelle", viewModel.Quelle);
            elements.Add("Version", viewModel.Version);


            pp.Uebersetzung = XMLHelper.UpdateUebersetzung(uebersetzung, elements, viewModel.SpracheID);

           _genericPrivacyPolicyRepository.Update(pp);
        }

        public void UpdateUploadedFileUrl(int privacyPolicyId, string field, string fileUrl, int langId)
        {
            PrivacyPolicy pp = _genericPrivacyPolicyRepository.GetByID(privacyPolicyId);
            var languageId = langId != -1 ? langId : _currentLang;

            var uebersetzung = XDocument.Parse(pp.Uebersetzung);
            var version = ExtractFileVersionFromUebersetzungen(uebersetzung, languageId);
            version++;

            Dictionary<string, string> elements = new Dictionary<string, string>();
            elements.Add(field, fileUrl);
            elements.Add("Version", version.ToString());

            pp.Uebersetzung = XMLHelper.UpdateUebersetzung(uebersetzung, elements, languageId);

            _genericPrivacyPolicyRepository.Update(pp);

            ResetPolicyAcceptedForAllUsers();
        }

        public void ResetPolicyAcceptedForAllUsers()
        {
            _context.Database.ExecuteSqlCommand($"UPDATE [AspNetUsers] SET [TermsAccepted] = NULL");
        }

        private IEnumerable<ApplicationUser> getAllApplicationUsers()
        {
            return _genericUserRepository.Get(u => u.TermsAccepted.HasValue, null, "ErstelltVon");
        }

        private int ExtractFileVersionFromUebersetzungen(XDocument uebersetzung, int lang)
        {
            var version = 0;
            var versionString = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Version", lang, false);
            if (versionString != Resources.Properties.Resources.Fehler_Keine_Uebersetzung)
            {
                version = Convert.ToInt32(versionString);
            }

            return version;
        }


    }
}