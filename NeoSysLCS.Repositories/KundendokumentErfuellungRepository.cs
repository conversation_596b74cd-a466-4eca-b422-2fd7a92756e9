using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;

namespace NeoSysLCS.Repositories
{
    public class KundendokumentErfuellungRepository
    {
        private readonly NeoSysLCS_Dev _context;

        public KundendokumentErfuellungRepository(NeoSysLCS_Dev contextCandidate)
        {
            _context = contextCandidate;
        }

        public IEnumerable<KundendokumentErfuellungViewModel> GetAllKundendokumentErfuellungViewModels()
        {
            return from KundendokumentErfuellung value in Enum.GetValues(typeof(KundendokumentErfuellung))
                   select new KundendokumentErfuellungViewModel
                   {
                       ID = (int)value,
                       Description = value.GetTranslation()
                   };
        }
    }
}