using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Web;
using System.Xml.Linq;
using Microsoft.AspNet.Identity;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories.Exceptions;
using NeoSysLCS.Repositories.Helper;
using NeoSysLCS.Repositories.ViewModels;

namespace NeoSysLCS.Repositories
{
    /// <summary>
    /// The repository for objektkategorien.
    /// </summary>
    public class ObjektkategorieRepository
    {
        private readonly IGenericRepository<Objektkategorie> _genericObjektkategorieRepository;
        private readonly UnitOfWork _unitOfWork;
        private readonly NeoSysLCS_Dev _context;
        private int _currentLang;

        /// <summary>
        /// Initializes a new instance.
        /// </summary>
        /// <param name="contextCandidate">The context candidate.</param>
        public ObjektkategorieRepository(NeoSysLCS_Dev contextCandidate)
        {
            _context = contextCandidate;
            _genericObjektkategorieRepository = new GenericRepository<Objektkategorie>(contextCandidate);
            _unitOfWork = new UnitOfWork(contextCandidate);

            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            Sprache sprache = _context.Sprachen.FirstOrDefault(s => s.Lokalisierung == language);
            _currentLang = sprache.SpracheID;
        }

        /// <summary>
        /// Gets all objektkategorie uebersetzungen of the specified language.
        /// </summary>
        /// <param name="sprachID">The sprach id.</param>
        /// <returns>Returns the objektkategorien</returns>
        public IQueryable<NewStandortObjektViewModel> GetTreeObjectsBySprachID(int sprachID)
        {
            var viewModels = new List<NewStandortObjektViewModel>();

            var parentObjekte = (from x in _context.Objektkategorien
                                 select new NewStandortObjektViewModel()
                                 {
                                     ObjektkategorieID = x.ObjektkategorieID.ToString(),
                                     Name = (_currentLang == 1 ? (x.NameDE ?? x.NameFR ?? x.NameIT ?? x.NameEN) : null) ??
                                                  (_currentLang == 2 ? (x.NameFR ?? x.NameDE ?? x.NameIT ?? x.NameEN) : null) ??
                                                  (_currentLang == 3 ? (x.NameIT ?? x.NameDE ?? x.NameFR ?? x.NameEN) : null) ??
                                                  (_currentLang == 4 ? (x.NameEN ?? x.NameDE ?? x.NameFR ?? x.NameIT) : null),
                                     ParentObjektkategorieID = x.ParentObjektkategorieID.ToString()
                                 }).ToList();

            var objekte = (from x in _context.Objekte
                           select new NewStandortObjektViewModel()
                           {
                               ObjektID = x.ObjektID,
                               ObjektkategorieID = x.ObjektkategorieID + "_" + x.ObjektID,
                               ParentObjektkategorieID = x.ObjektkategorieID.ToString(),
                               Uebersetzung = x.Uebersetzung,
                           }).ToList();

            foreach (var objekt in objekte)
            {
                XDocument uebersetzung = XDocument.Parse(objekt.Uebersetzung);

                objekt.Name = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Name", _currentLang);
                objekt.Beschreibung = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Beschreibung", _currentLang);
            }

            viewModels.AddRange(parentObjekte);
            viewModels.AddRange(objekte);

            return viewModels.AsQueryable();
        }

        /// <summary>
        /// Gets all objektkategorie uebersetzungen of the specified language.
        /// </summary>
        /// <param name="sprachID">The sprach id.</param>
        /// <returns>Returns the objektkategorien</returns>
        public IQueryable<NewStandortObjektViewModel> GetTreeObjectsByKundendokument(int kundendokumentID, int sprachID)
        {
            var objektIDs = (from x in _context.KundendokumentForderungsversionen
                             where x.KundendokumentID == kundendokumentID && x.Relevant
                             select x.StandortObjekt.ObjektID).Distinct().ToList();

            var standortObjektIDs = (from x in _context.KundendokumentForderungsversionen
                                     where x.KundendokumentID == kundendokumentID && x.Relevant && objektIDs.Contains(x.StandortObjekt.ObjektID)
                                     select x.StandortObjektID).Distinct().ToList();

            var viewModels = new List<NewStandortObjektViewModel>();
            //NewStandortObjektViewModel viewModel = null;

            var parentObjekte = (from x in _context.Objektkategorien
                                 where x.Objekte.Any(o => objektIDs.Contains(o.ObjektID))
                                 select new NewStandortObjektViewModel()
                                 {
                                     ObjektkategorieID = x.ObjektkategorieID.ToString(),
                                     Name = (sprachID == 1 ? (x.NameDE ?? x.NameFR ?? x.NameIT ?? x.NameEN) : null) ??
                                              (sprachID == 2 ? (x.NameFR ?? x.NameDE ?? x.NameIT ?? x.NameEN) : null) ??
                                              (sprachID == 3 ? (x.NameIT ?? x.NameDE ?? x.NameFR ?? x.NameEN) : null) ??
                                              (sprachID == 4 ? (x.NameEN ?? x.NameDE ?? x.NameFR ?? x.NameIT) : null),
                                     ParentObjektkategorieID = x.ParentObjektkategorieID.ToString()
                                 }).ToList();

            var standortObjekte = (from x in _context.StandortObjekte
                                  where standortObjektIDs.Contains(x.StandortObjektID)
                                  select new StandortObjektViewModel
                                  {
                                      Uebersetzung = x.Uebersetzung,                                      
                                      ObjektID = x.ObjektID,
                                  }).ToList();

            foreach (var standortObjekt in standortObjekte)
            {
                XDocument uebersetzung = XDocument.Parse(standortObjekt.Uebersetzung);

                standortObjekt.Name = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Name", sprachID);
                standortObjekt.Beschreibung = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Beschreibung", sprachID);
            }

            var objekte = (from x in _context.Objekte
                           where objektIDs.Contains(x.ObjektID)
                           select new NewStandortObjektViewModel()
                           {
                               ObjektID = x.ObjektID,
                               Uebersetzung = x.Uebersetzung,
                               ObjektkategorieID = x.ObjektkategorieID + "_" + x.ObjektID,
                               ParentObjektkategorieID = x.ObjektkategorieID.ToString(),
                           }).ToList();

            foreach (var objekt in objekte)
            {
                if ((standortObjekte.Where(x => x.ObjektID == objekt.ObjektID.Value)) != null)
                {
                    StandortObjektViewModel viewModel = standortObjekte.Where(x => x.ObjektID == objekt.ObjektID.Value).FirstOrDefault();

                    objekt.Name = viewModel.Name;
                    objekt.Beschreibung = viewModel.Beschreibung;
                }
                else
                {
                    XDocument uebersetzung = XDocument.Parse(objekt.Uebersetzung);

                    objekt.Name = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Name", _currentLang);
                    objekt.Beschreibung = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Beschreibung", _currentLang);
                }
            }




            viewModels.AddRange(parentObjekte);
            viewModels.AddRange(objekte);

            //Check for missing ParentObjektkategorien --> Some top level categories could be missing because there are only 
            //subcategories and no Standortobjekt for them
            var parentObjektkategorieIds = (from x in viewModels where x.ParentObjektkategorieID != "" select x.ParentObjektkategorieID).ToList();
            var objektkategorieIds = (from x in viewModels select x.ObjektkategorieID).ToList();
            List<String> parentObjektkategorieIdsDistinct = new List<String>();
            foreach (var parentObjektkategorieId in parentObjektkategorieIds)
            {                
                if(!objektkategorieIds.Contains(parentObjektkategorieId))
                {
                    parentObjektkategorieIdsDistinct.Add(parentObjektkategorieId);
                }               
            }

            var missingParentObjektkategorie = (from x in _context.Objektkategorien
                                                where parentObjektkategorieIdsDistinct.Contains(x.ObjektkategorieID.ToString())
                                                select new NewStandortObjektViewModel()
                                                {
                                                    ObjektkategorieID = x.ObjektkategorieID.ToString(),
                                                    Name = (sprachID == 1 ? (x.NameDE ?? x.NameFR ?? x.NameIT ?? x.NameEN) : null) ??
                                                             (sprachID == 2 ? (x.NameFR ?? x.NameDE ?? x.NameIT ?? x.NameEN) : null) ??
                                                             (sprachID == 3 ? (x.NameIT ?? x.NameDE ?? x.NameFR ?? x.NameEN) : null) ??
                                                             (sprachID == 4 ? (x.NameEN ?? x.NameDE ?? x.NameFR ?? x.NameIT) : null),
                                                    ParentObjektkategorieID = x.ParentObjektkategorieID.ToString()
                                                }).ToList();

            viewModels.AddRange(missingParentObjektkategorie);

            return viewModels.AsQueryable();
        }

        /// <summary>
        /// Retrieves a tree of objects based on the provided customer document IDs and language ID.
        /// </summary>
        /// <param name="kundenDokumentIDs">A list of customer document IDs.</param>
        /// <param name="sprachID">The language ID for translations.</param>
        /// <returns>An IQueryable of NewStandortObjektViewModel representing the tree of objects.</returns>
        public IQueryable<NewStandortObjektViewModel> GetTreeObjectsByKundendokumentIds(List<int> kundenDokumentIDs, int sprachID)
        {
            var objektIDs = (from x in _context.KundendokumentForderungsversionen
                             where kundenDokumentIDs.Contains(x.KundendokumentID) && x.Relevant
                             select x.StandortObjekt.ObjektID).Distinct().ToList();

            var standortObjektIDs = (from x in _context.KundendokumentForderungsversionen
                                     where kundenDokumentIDs.Contains(x.KundendokumentID) && x.Relevant && objektIDs.Contains(x.StandortObjekt.ObjektID)
                                     select x.StandortObjektID).Distinct().ToList();

            var viewModels = new List<NewStandortObjektViewModel>();
            //NewStandortObjektViewModel viewModel = null;

            var parentObjekte = (from x in _context.Objektkategorien
                                 where x.Objekte.Any(o => objektIDs.Contains(o.ObjektID))
                                 select new NewStandortObjektViewModel()
                                 {
                                     ObjektkategorieID = x.ObjektkategorieID.ToString(),
                                     Name = (sprachID == 1 ? (x.NameDE ?? x.NameFR ?? x.NameIT ?? x.NameEN) : null) ??
                                              (sprachID == 2 ? (x.NameFR ?? x.NameDE ?? x.NameIT ?? x.NameEN) : null) ??
                                              (sprachID == 3 ? (x.NameIT ?? x.NameDE ?? x.NameFR ?? x.NameEN) : null) ??
                                              (sprachID == 4 ? (x.NameEN ?? x.NameDE ?? x.NameFR ?? x.NameIT) : null),
                                     ParentObjektkategorieID = x.ParentObjektkategorieID.ToString()
                                 }).ToList();

            var standortObjekte = (from x in _context.StandortObjekte
                                   where standortObjektIDs.Contains(x.StandortObjektID)
                                   select new StandortObjektViewModel
                                   {
                                       Uebersetzung = x.Uebersetzung,
                                       ObjektID = x.ObjektID,
                                   }).ToList();

            foreach (var standortObjekt in standortObjekte)
            {
                XDocument uebersetzung = XDocument.Parse(standortObjekt.Uebersetzung);

                standortObjekt.Name = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Name", sprachID);
                standortObjekt.Beschreibung = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Beschreibung", sprachID);
            }

            var objekte = (from x in _context.Objekte
                           where objektIDs.Contains(x.ObjektID)
                           select new NewStandortObjektViewModel()
                           {
                               ObjektID = x.ObjektID,
                               Uebersetzung = x.Uebersetzung,
                               ObjektkategorieID = x.ObjektkategorieID + "_" + x.ObjektID,
                               ParentObjektkategorieID = x.ObjektkategorieID.ToString(),
                           }).ToList();

            foreach (var objekt in objekte)
            {
                if ((standortObjekte.Where(x => x.ObjektID == objekt.ObjektID.Value)) != null)
                {
                    StandortObjektViewModel viewModel = standortObjekte.Where(x => x.ObjektID == objekt.ObjektID.Value).FirstOrDefault();

                    objekt.Name = viewModel.Name;
                    objekt.Beschreibung = viewModel.Beschreibung;
                }
                else
                {
                    XDocument uebersetzung = XDocument.Parse(objekt.Uebersetzung);

                    objekt.Name = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Name", _currentLang);
                    objekt.Beschreibung = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Beschreibung", _currentLang);
                }
            }




            viewModels.AddRange(parentObjekte);
            viewModels.AddRange(objekte);

            //Check for missing ParentObjektkategorien --> Some top level categories could be missing because there are only 
            //subcategories and no Standortobjekt for them
            var parentObjektkategorieIds = (from x in viewModels where x.ParentObjektkategorieID != "" select x.ParentObjektkategorieID).ToList();
            var objektkategorieIds = (from x in viewModels select x.ObjektkategorieID).ToList();
            List<String> parentObjektkategorieIdsDistinct = new List<String>();
            foreach (var parentObjektkategorieId in parentObjektkategorieIds)
            {
                if (!objektkategorieIds.Contains(parentObjektkategorieId))
                {
                    parentObjektkategorieIdsDistinct.Add(parentObjektkategorieId);
                }
            }

            var missingParentObjektkategorie = (from x in _context.Objektkategorien
                                                where parentObjektkategorieIdsDistinct.Contains(x.ObjektkategorieID.ToString())
                                                select new NewStandortObjektViewModel()
                                                {
                                                    ObjektkategorieID = x.ObjektkategorieID.ToString(),
                                                    Name = (sprachID == 1 ? (x.NameDE ?? x.NameFR ?? x.NameIT ?? x.NameEN) : null) ??
                                                             (sprachID == 2 ? (x.NameFR ?? x.NameDE ?? x.NameIT ?? x.NameEN) : null) ??
                                                             (sprachID == 3 ? (x.NameIT ?? x.NameDE ?? x.NameFR ?? x.NameEN) : null) ??
                                                             (sprachID == 4 ? (x.NameEN ?? x.NameDE ?? x.NameFR ?? x.NameIT) : null),
                                                    ParentObjektkategorieID = x.ParentObjektkategorieID.ToString()
                                                }).ToList();

            viewModels.AddRange(missingParentObjektkategorie);

            return viewModels.AsQueryable();
        }

        /// <summary>
        /// Inserts the objektkategorie from the specified view model.
        /// </summary>
        /// <param name="viewModel">The view model.</param>
        public void Insert(ObjektkategorieViewModel viewModel)
        {
            var objektkategorie = new Objektkategorie();
            objektkategorie.ErstelltAm = DateTime.Now;
            objektkategorie.ErstelltVonID = HttpContext.Current.User.Identity.GetUserId();
            objektkategorie.ParentObjektkategorieID = viewModel.ParentObjektkategorieID;

            if (_currentLang == 1)
            {
                objektkategorie.NameDE = viewModel.Name;
            }
            if (_currentLang == 2)
            {
                objektkategorie.NameFR = viewModel.Name;
            }
            if (_currentLang == 3)
            {
                objektkategorie.NameIT = viewModel.Name;
            }
            if (_currentLang == 4)
            {
                objektkategorie.NameEN = viewModel.Name;
            }

            _genericObjektkategorieRepository.Insert(objektkategorie);
        }

        /// <summary>
        /// Updates the objectkategorie from the specified view model.
        /// </summary>
        /// <param name="viewModel">The view model.</param>
        public void Update(ObjektkategorieViewModel viewModel)
        {
            Objektkategorie objektkategorie = _genericObjektkategorieRepository.GetByID(viewModel.ObjektkategorieID);
            objektkategorie.ObjektkategorieID = viewModel.ObjektkategorieID;
            objektkategorie.ParentObjektkategorieID = viewModel.ParentObjektkategorieID;
            objektkategorie.BearbeitetAm = DateTime.Now;
            objektkategorie.BearbeitetVonID = HttpContext.Current.User.Identity.GetUserId();

            if (_currentLang == 1)
            {
                objektkategorie.NameDE = viewModel.Name;
            }
            if (_currentLang == 2)
            {
                objektkategorie.NameFR = viewModel.Name;
            }
            if (_currentLang == 3)
            {
                objektkategorie.NameIT = viewModel.Name;
            }
            if (_currentLang == 4)
            {
                objektkategorie.NameEN = viewModel.Name;
            }

            _genericObjektkategorieRepository.Update(objektkategorie);
        }

        public void UpdateObjektkategorieUebersetzung(ObjektkategorieViewModel viewModel)
        {
            Objektkategorie objektkategorie = _genericObjektkategorieRepository.GetByID(viewModel.ObjektkategorieID);
            objektkategorie.ObjektkategorieID = viewModel.ObjektkategorieID;

            if (viewModel.SpracheID == 1)
            {
                objektkategorie.NameDE = viewModel.Name;
            }
            if (viewModel.SpracheID == 2)
            {
                objektkategorie.NameFR = viewModel.Name;
            }
            if (viewModel.SpracheID == 3)
            {
                objektkategorie.NameIT = viewModel.Name;
            }
            if (viewModel.SpracheID == 4)
            {
                objektkategorie.NameEN = viewModel.Name;
            }

            _genericObjektkategorieRepository.Update(objektkategorie);
        }

        /// <summary>
        /// Deletes the objektkategorie by the specifed id.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <exception cref="NeoSysLCS.Repositories.Exceptions.HasRelationsException">Thrown when the objektkategorie has subkategories or is 
        /// used in objekte and cannot be deleted</exception>
        public void Delete(int id)
        {
            var objektkategorie = _genericObjektkategorieRepository.Get(e => e.ObjektkategorieID == id, null,
                "ChildObjektkategorien,Objekte").FirstOrDefault();
            if (objektkategorie != null && objektkategorie.Objekte.Count > 0)
            {
                throw new HasRelationsException(Resources.Properties.Resources.Entitaet_Objekt_Plural, HasRelationsException.RelationType.UsedByEntity);
            }
            if (objektkategorie != null && objektkategorie.ChildObjektkategorien.Count > 0)
            {
                throw new HasRelationsException(Resources.Properties.Resources.Entitaet_Objekt_Plural, HasRelationsException.RelationType.Custom, Resources.Properties.Resources.Fehler_EntitaetHasChildren);
            }
            _genericObjektkategorieRepository.Delete(id);
        }

        /// <summary>
        /// Gets all objektkategorie view models.
        /// </summary>
        /// <returns>Returns the view models</returns>
        public IQueryable<ObjektkategorieViewModel> GetAllObjektkategorieViewModels()
        {
            return (from x in _context.Objektkategorien
                    join tx in _context.Objektkategorien on x.ParentObjektkategorieID equals tx.ObjektkategorieID into parent
                    from ty in parent.DefaultIfEmpty()
                    select new ObjektkategorieViewModel()
                    {
                        ObjektkategorieID = x.ObjektkategorieID,
                        ParentObjektkategorieID = x.ParentObjektkategorieID,
                        ErstelltAm = x.ErstelltAm,
                        ErstelltVon = (x.ErstelltVon != null) ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname) : null,
                        ErstelltVonID = x.ErstelltVonID,
                        BearbeitetAm = x.BearbeitetAm,
                        BearbeitetVonID = x.BearbeitetVonID,
                        BearbeitetVon = (x.BearbeitetVon != null) ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname) : null,
                        Name = (_currentLang == 1 ? (x.NameDE ?? x.NameFR ?? x.NameIT ?? x.NameEN) : null) ??
                                          (_currentLang == 2 ? (x.NameFR ?? x.NameDE ?? x.NameIT ?? x.NameEN) : null) ??
                                          (_currentLang == 3 ? (x.NameIT ?? x.NameDE ?? x.NameFR ?? x.NameEN) : null) ??
                                          (_currentLang == 4 ? (x.NameEN ?? x.NameDE ?? x.NameFR ?? x.NameIT) : null),
                        ParentObjektkategorieName = ty != null ? ((_currentLang == 1 ? (ty.NameDE ?? ty.NameFR ?? ty.NameIT ?? ty.NameEN) : null) ??
                                                      (_currentLang == 2 ? (ty.NameFR ?? ty.NameDE ?? ty.NameIT ?? ty.NameEN) : null) ??
                                                      (_currentLang == 3 ? (ty.NameIT ?? ty.NameDE ?? ty.NameFR ?? ty.NameEN) : null) ??
                                                      (_currentLang == 4 ? (ty.NameEN ?? ty.NameDE ?? ty.NameFR ?? ty.NameIT) : null)) : null,
                    }).OrderBy(ok => ok.Name).ThenBy(ok => ok.ParentObjektkategorieName);
        }

        public IEnumerable<ObjektkategorieViewModel> GetObjektkategorieUebersetzungenByObjekt(int id)
        {
            List<ObjektkategorieViewModel> uebersetzungen = new List<ObjektkategorieViewModel>();

            uebersetzungen.Add((from x in _context.Objektkategorien where x.ObjektkategorieID == id select new ObjektkategorieViewModel() { ObjektkategorieID = id, ObjektkategorieUebersetzungID = "1_" + id, Name = x.NameDE ?? "", SpracheID = 1 }).FirstOrDefault());
            uebersetzungen.Add((from x in _context.Objektkategorien where x.ObjektkategorieID == id select new ObjektkategorieViewModel() { ObjektkategorieID = id, ObjektkategorieUebersetzungID = "2_" + id, Name = x.NameFR ?? "", SpracheID = 2 }).FirstOrDefault());
            uebersetzungen.Add((from x in _context.Objektkategorien where x.ObjektkategorieID == id select new ObjektkategorieViewModel() { ObjektkategorieID = id, ObjektkategorieUebersetzungID = "3_" + id, Name = x.NameIT ?? "", SpracheID = 3 }).FirstOrDefault());
            uebersetzungen.Add((from x in _context.Objektkategorien where x.ObjektkategorieID == id select new ObjektkategorieViewModel() { ObjektkategorieID = id, ObjektkategorieUebersetzungID = "4_" + id, Name = x.NameEN ?? "", SpracheID = 4 }).FirstOrDefault());

            return uebersetzungen;
        }
    }

}