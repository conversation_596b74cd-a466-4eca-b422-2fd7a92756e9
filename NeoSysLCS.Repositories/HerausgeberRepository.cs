using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Linq.Dynamic;
using System.Linq.Expressions;
using System.Web;
using Microsoft.AspNet.Identity;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories.Exceptions;
using NeoSysLCS.Repositories.ViewModels;

namespace NeoSysLCS.Repositories
{
    /// <summary>
    /// 
    /// </summary>
    public class HerausgeberRepository
    {
        private readonly IGenericRepository<Herausgeber> _genericHerausgeberRepository;
        private NeoSysLCS_Dev _context;
        private int _currentLang;

        /// <summary>
        /// Initializes a new instance.
        /// </summary>
        /// <param name="contextCandidate">The context candidate.</param>
        public HerausgeberRepository(NeoSysLCS_Dev contextCandidate)
        {
            _context = contextCandidate;
            _genericHerausgeberRepository = new GenericRepository<Herausgeber>(contextCandidate);

            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            Sprache sprache = _context.Sprachen.FirstOrDefault(s => s.Lokalisierung == language);
            _currentLang = sprache.SpracheID;
        }

        /// <summary>
        /// Gets all herausgeber by standort.
        /// </summary>
        /// <param name="standortId">The standort id.</param>
        /// <returns>Returns the herausgeber</returns>
        public IQueryable<HerausgeberViewModel> GetAllHerausgeberByStandort(int standortId)
        {
            return (from x in _context.Herausgebers
                    where (x.Standorte.Any(f => f.StandortID == standortId))
                    select new HerausgeberViewModel
                    {
                        HerausgeberID = x.HerausgeberID,
                        ErstelltAm = x.ErstelltAm,
                        ErstelltVon = (x.ErstelltVon != null) ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname) : null,
                        ErstelltVonID = x.ErstelltVonID,
                        BearbeitetAm = x.BearbeitetAm,
                        BearbeitetVonID = x.BearbeitetVonID,
                        BearbeitetVon = (x.BearbeitetVon != null) ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname) : null,
                        Name = (_currentLang == 1 ? (x.NameDE ?? x.NameFR ?? x.NameIT ?? x.NameEN) : null) ??
                                     (_currentLang == 2 ? (x.NameFR ?? x.NameDE ?? x.NameIT ?? x.NameEN) : null) ??
                                     (_currentLang == 3 ? (x.NameIT ?? x.NameDE ?? x.NameFR ?? x.NameEN) : null) ??
                                     (_currentLang == 4 ? (x.NameEN ?? x.NameDE ?? x.NameFR ?? x.NameIT) : null)
                    }).OrderBy(h => h.Name);
        }

        /// <summary>
        /// Gets the herausgeber by id.
        /// </summary>
        /// <param name="id">The id.</param>
        /// <returns>Returns the herausgeber</returns>
        public Herausgeber GetByID(object id)
        {
            return _genericHerausgeberRepository.GetByID(id);
        }

        /// <summary>
        /// Inserts the herausgeber from the specified view model.
        /// </summary>
        /// <param name="viewModel">The view model.</param>
        public void Insert(HerausgeberViewModel viewModel)
        {
            var herausgeber = new Herausgeber();
            herausgeber.ErstelltAm = DateTime.Now;
            herausgeber.ErstelltVonID = HttpContext.Current.User.Identity.GetUserId();

            if (_currentLang == 1)
            {
                herausgeber.NameDE = viewModel.Name;
            }
            if (_currentLang == 2)
            {
                herausgeber.NameFR = viewModel.Name;
            }
            if (_currentLang == 3)
            {
                herausgeber.NameIT = viewModel.Name;
            }
            if (_currentLang == 4)
            {
                herausgeber.NameEN = viewModel.Name;
            }

            _genericHerausgeberRepository.Insert(herausgeber);
        }

        /// <summary>
        /// Updates the herausgeber from the specified view model.
        /// </summary>
        /// <param name="viewModel">The view model.</param>
        public void Update(HerausgeberViewModel viewModel)
        {
            Herausgeber herausgeber = _genericHerausgeberRepository.GetByID(viewModel.HerausgeberID);
            herausgeber.HerausgeberID = viewModel.HerausgeberID;
            herausgeber.BearbeitetAm = DateTime.Now;
            herausgeber.BearbeitetVonID = HttpContext.Current.User.Identity.GetUserId();

            if (_currentLang == 1)
            {
                herausgeber.NameDE = viewModel.Name;
            }
            if (_currentLang == 2)
            {
                herausgeber.NameFR = viewModel.Name;
            }
            if (_currentLang == 3)
            {
                herausgeber.NameIT = viewModel.Name;
            }
            if (_currentLang == 4)
            {
                herausgeber.NameEN = viewModel.Name;
            }

            _genericHerausgeberRepository.Update(herausgeber);
        }


        /// <summary>
        /// Updates the herausgeber model.
        /// </summary>
        /// <param name="model">The model.</param>
        public void UpdateModel(Herausgeber herausgeber)
        {
            herausgeber.BearbeitetAm = DateTime.Now;
            herausgeber.BearbeitetVonID = "a303b999-6438-47cf-9e93-8505e91fe582"; // <EMAIL>
            _genericHerausgeberRepository.Update(herausgeber);
        }

        /// <summary>
        /// Deletes the herausgeber by the specified id.
        /// </summary>
        /// <param name="herausgeberID">The herausgeber id.</param>
        /// <exception cref="NeoSysLCS.Repositories.Exceptions.HasRelationsException">Thrown when the herausgeber is still in use and cannot be deleted.
        /// </exception>
        public void Delete(int herausgeberID)
        {
            Herausgeber herausgeber = Get(e => e.HerausgeberID == herausgeberID, null, "Erlasse,Standorte").FirstOrDefault();

            if (herausgeber != null && herausgeber.Erlasse.Count > 0)
            {
                throw new HasRelationsException(Resources.Properties.Resources.Entitaet_Erlass_Plural,
                    HasRelationsException.RelationType.UsedByEntity);
            }
            if (herausgeber != null && herausgeber.Standorte.Count > 0)
            {
                throw new HasRelationsException(Resources.Properties.Resources.Entitaet_Standort_Plural,
                    HasRelationsException.RelationType.UsedByEntity);
            }
            _genericHerausgeberRepository.Delete(herausgeber);
        }

        /// <summary>
        /// Gets an enumerable of herausgeber filtered by the specified filter.
        /// </summary>
        /// <param name="filter">The filter.</param>
        /// <param name="orderBy">The order by.</param>
        /// <param name="includeProperties">The include properties.</param>
        /// <returns>Returns the herausgeber</returns>
        public IQueryable<Herausgeber> Get(Expression<Func<Herausgeber, bool>> filter = null,
            Func<IQueryable<Herausgeber>, IOrderedQueryable<Herausgeber>> orderBy = null, string includeProperties = "")
        {
            return _genericHerausgeberRepository.Get(filter, null, includeProperties);
        }

        /// <summary>
        /// Gets all herausgeber view models.
        /// </summary>
        /// <returns>Returns the view models.</returns>
        public IQueryable<HerausgeberViewModel> GetAllHerausgeberViewModels(bool standortHerausgeber, bool isRowSelection = false, int standortID = 0)
        {
            List<HerausgeberViewModel> viewModels = (from x in _context.Herausgebers
                                                     select new HerausgeberViewModel
                                                     {
                                                         HerausgeberID = x.HerausgeberID,
                                                         ErstelltAm = x.ErstelltAm,
                                                         ErstelltVon = (x.ErstelltVon != null) ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname) : null,
                                                         ErstelltVonID = x.ErstelltVonID,
                                                         BearbeitetAm = x.BearbeitetAm,
                                                         BearbeitetVonID = x.BearbeitetVonID,
                                                         BearbeitetVon = (x.BearbeitetVon != null) ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname) : null,
                                                         Name = (_currentLang == 1 ? (x.NameDE ?? x.NameFR ?? x.NameIT ?? x.NameEN) : null) ??
                                                                      (_currentLang == 2 ? (x.NameFR ?? x.NameDE ?? x.NameIT ?? x.NameEN) : null) ??
                                                                      (_currentLang == 3 ? (x.NameIT ?? x.NameDE ?? x.NameFR ?? x.NameEN) : null) ??
                                                                      (_currentLang == 4 ? (x.NameEN ?? x.NameDE ?? x.NameFR ?? x.NameIT) : null),
                                                         StandortID = x.StandortID
                                                     }).ToList();

            // change result for row selection on Kunde --> StandortGridView
            if (isRowSelection)
            { 
                List<HerausgeberViewModel> standortSpecificViewModels = (from x in _context.Herausgebers
                                                                         where x.StandortID.Value == standortID
                                                                         select new HerausgeberViewModel
                                                                         {
                                                                             HerausgeberID = x.HerausgeberID,
                                                                             ErstelltAm = x.ErstelltAm,
                                                                             ErstelltVon = (x.ErstelltVon != null) ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname) : null,
                                                                             ErstelltVonID = x.ErstelltVonID,
                                                                             BearbeitetAm = x.BearbeitetAm,
                                                                             BearbeitetVonID = x.BearbeitetVonID,
                                                                             BearbeitetVon = (x.BearbeitetVon != null) ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname) : null,
                                                                             Name = (_currentLang == 1 ? (x.NameDE ?? x.NameFR ?? x.NameIT ?? x.NameEN) : null) ??
                                                                                          (_currentLang == 2 ? (x.NameFR ?? x.NameDE ?? x.NameIT ?? x.NameEN) : null) ??
                                                                                          (_currentLang == 3 ? (x.NameIT ?? x.NameDE ?? x.NameFR ?? x.NameEN) : null) ??
                                                                                          (_currentLang == 4 ? (x.NameEN ?? x.NameDE ?? x.NameFR ?? x.NameIT) : null),
                                                                             StandortID = x.StandortID
                                                                         }).ToList();
                if (standortSpecificViewModels.Any()) // if there is any specific Herausgeber only show them, otherwise show all Herausgeber without StandortID
                {
                    return standortSpecificViewModels.OrderBy(h => h.Name).AsQueryable();
                }
            }

            if (standortHerausgeber)
            {
                return viewModels.Where(x => x.StandortID.HasValue).OrderBy(h => h.Name).AsQueryable();
            }
            else
            {
                return viewModels.Where(x => !x.StandortID.HasValue).OrderBy(h => h.Name).AsQueryable();
            }
        }

        public IQueryable<HerausgeberViewModel> GetStandortHerausgeberComboBox(List<int> standorteIdentifiers)
        {
            return (from x in _context.Herausgebers
                    where (x.Standorte.Any(f => standorteIdentifiers.Contains(f.StandortID)))
                    select new HerausgeberViewModel()
                    {
                        HerausgeberID = x.HerausgeberID,
                        Name = 
                            (_currentLang == 1 ? (x.NameDE ?? x.NameFR ?? x.NameIT ?? x.NameEN) : null) ??
                            (_currentLang == 2 ? (x.NameFR ?? x.NameDE ?? x.NameIT ?? x.NameEN) : null) ??
                            (_currentLang == 3 ? (x.NameIT ?? x.NameDE ?? x.NameFR ?? x.NameEN) : null) ??
                            (_currentLang == 4 ? (x.NameEN ?? x.NameDE ?? x.NameFR ?? x.NameIT) : null)
                    }).OrderBy(h => h.Name);
        }

        public IEnumerable<HerausgeberViewModel> GetErlasseHerausgeberComboBox(int _currentLang, bool standortErlasse = false)
        {
            var viewModels = (from tx in _context.Herausgebers
                              select new HerausgeberViewModel()
                              {
                                  HerausgeberID = tx.HerausgeberID,
                                  Name = (_currentLang == 1 ? (tx.NameDE ?? tx.NameFR ?? tx.NameIT ?? tx.NameEN) : null) ??
                                                    (_currentLang == 2 ? (tx.NameFR ?? tx.NameDE ?? tx.NameIT ?? tx.NameEN) : null) ??
                                                    (_currentLang == 3 ? (tx.NameIT ?? tx.NameDE ?? tx.NameFR ?? tx.NameEN) : null) ??
                                                    (_currentLang == 4 ? (tx.NameEN ?? tx.NameDE ?? tx.NameFR ?? tx.NameIT) : null),
                                  StandortID = tx.StandortID
                              }).OrderBy(h => h.Name).ToList();

            if (standortErlasse)
            {
                return viewModels.Where(x => x.StandortID.HasValue).OrderBy(h => h.Name);
            }
            else
            {
                return viewModels.Where(x => !x.StandortID.HasValue).OrderBy(h => h.Name);
            }
        }

        public IEnumerable<HerausgeberViewModel> GetHerausgeberUebersetzungenByHerausgeber(int id)
        {
            List<HerausgeberViewModel> uebersetzungen = new List<HerausgeberViewModel>();

            uebersetzungen.Add((from x in _context.Herausgebers where x.HerausgeberID == id select new HerausgeberViewModel() { HerausgeberID = id, HerausgeberUebersetzungID = "1_" + id, Name = x.NameDE ?? "", SpracheID = 1 }).FirstOrDefault());
            uebersetzungen.Add((from x in _context.Herausgebers where x.HerausgeberID == id select new HerausgeberViewModel() { HerausgeberID = id, HerausgeberUebersetzungID = "2_" + id, Name = x.NameFR ?? "", SpracheID = 2 }).FirstOrDefault());
            uebersetzungen.Add((from x in _context.Herausgebers where x.HerausgeberID == id select new HerausgeberViewModel() { HerausgeberID = id, HerausgeberUebersetzungID = "3_" + id, Name = x.NameIT ?? "", SpracheID = 3 }).FirstOrDefault());
            uebersetzungen.Add((from x in _context.Herausgebers where x.HerausgeberID == id select new HerausgeberViewModel() { HerausgeberID = id, HerausgeberUebersetzungID = "4_" + id, Name = x.NameEN ?? "", SpracheID = 4 }).FirstOrDefault());

            return uebersetzungen;
        }

        public void UpdateHerausgeberUebersetzung(HerausgeberViewModel viewModel)
        {
            Herausgeber Herausgeber = _genericHerausgeberRepository.GetByID(viewModel.HerausgeberID);

            if (viewModel.SpracheID == 1)
            {
                Herausgeber.NameDE = viewModel.Name;
            }
            if (viewModel.SpracheID == 2)
            {
                Herausgeber.NameFR = viewModel.Name;
            }
            if (viewModel.SpracheID == 3)
            {
                Herausgeber.NameIT = viewModel.Name;
            }
            if (viewModel.SpracheID == 4)
            {
                Herausgeber.NameEN = viewModel.Name;
            }

            _genericHerausgeberRepository.Update(Herausgeber);
        }

        public void InsertWithMultipleLanguages(List<HerausgeberViewModel> viewModelList, int standortID)
        {
            var herausgeber = new Herausgeber();
            herausgeber.ErstelltAm = DateTime.Now;
            herausgeber.ErstelltVonID = "a303b999-6438-47cf-9e93-8505e91fe582"; // <EMAIL>
            herausgeber.StandortID = standortID;

            foreach (var vm in viewModelList)
            {
                switch (vm.SpracheID)
                {
                    case 1:
                    {
                        herausgeber.NameDE = vm.Name;
                        break;
                    }
                    case 2:
                    {
                        herausgeber.NameFR = vm.Name;
                        break;
                    }
                    case 3:
                    {
                        herausgeber.NameIT = vm.Name;
                        break;
                    }
                    case 4:
                    {
                        herausgeber.NameEN = vm.Name;
                        break;
                    }
                }
            }

            _genericHerausgeberRepository.Insert(herausgeber);
        }
    }
}