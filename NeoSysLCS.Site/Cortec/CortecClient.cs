using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using NeoSysLCS.Site.Cortec.Dtos;
using NeoSysLCS.Site.Utilities;

namespace NeoSysLCS.Site.Cortec
{
    class CortecClient : ICortecClient
    {
        private readonly HttpClient _httpClient;
        private readonly string _apiUrl;

        public CortecClient(string apiUrl)
        {
            _httpClient = new HttpClient();
            _apiUrl = apiUrl;
        }

        public CortecClient(string apiUrl, string apiKey)
        {
            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.Add("Authorization", apiKey);
            _apiUrl = apiUrl;
        }

        public async Task<List<CustomerDto>> FetchCustomers()
        {
            HttpResponseMessage response = await _httpClient.GetAsync(_apiUrl);
            response.EnsureSuccessStatusCode();

            return await response.Content.ReadAsAsync<List<CustomerDto>>();
        }

        public async Task<ToDoTaskRespDto> CreateToDoTask(ToDoTaskDto item)
        {
            var options = new JsonSerializerOptions
            {
                Converters = { new CortecClientDateFormatter() }
            };

            try
            {
                string jsonContent = JsonSerializer.Serialize(item, options);
                HttpContent content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
                HttpResponseMessage response = await _httpClient.PostAsync(_apiUrl, content);

                string correctedJson = "";
                if (response.IsSuccessStatusCode)
                {
                    string responseBody = await response.Content.ReadAsStringAsync();
                    // Correcting the JSON format by adding double quotes around property names
                    correctedJson = responseBody
                        .Replace("status:", "\"status\":")
                        .Replace("task_id :", "\"task_id\":");
                    return JsonSerializer.Deserialize<ToDoTaskRespDto>(correctedJson, options);
                }
                else
                {
                    // Handle failure
                    ExceptionUtility.LogInfo("Unsuccessful task **" + jsonContent + "** creation. Received " + correctedJson);
                }
            }
            catch (HttpRequestException e)
            {
                // Handle exception
                var exception = e.StackTrace;
                ExceptionUtility.LogException(e, exception);
            }
            return ToDoTaskRespDto.errorResponse();

        }
    }
}

