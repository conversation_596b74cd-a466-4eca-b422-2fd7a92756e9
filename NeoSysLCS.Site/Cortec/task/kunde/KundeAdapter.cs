using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.DomainModel.Models.NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;
using NeoSysLCS.Site.Cortec.task.offer;
using Newtonsoft.Json;

namespace NeoSysLCS.Site.Cortec.task.kunde
{
    public class KundeAdapter : ICortecTaskCreatable<KundeViewModel>
    {
        private readonly KundeViewModel _kunde;
        private readonly IUnitOfWork _unitOfWork;
        private readonly int _appUserCortecId;
        private int _createdByCortecId;

        public KundeAdapter()
        {
        }

        [JsonConstructor]
        public KundeAdapter(KundeViewModel kunde, IUnitOfWork unitOfWork, int appUserCortecId)
        {
            _kunde = kunde;
            _unitOfWork = unitOfWork;
            _appUserCortecId = appUserCortecId;
        }

        public KundeViewModel GetWrappedObject()
        {
            return _kunde;
        }

        public CortecTask InitializeCortecTask(int? createdById)
        {
            var cortecTask = new CortecTask();

            _createdByCortecId = createdById ?? _appUserCortecId;

            cortecTask.CreatedBy = _createdByCortecId;
            cortecTask.TaskCategoryId = 9;
            cortecTask.ProficenterId = 8;
            cortecTask.TaskScheduledForCreationAt = DateTime.Now;
            return cortecTask;
        }

        public int GetCreatedByCortecId()
        {
            return _createdByCortecId;
        }

        public int GetUsersCortecId(string emailAddress)
        {
            return _unitOfWork.UserRepository.GetById(_unitOfWork.UserRepository.GetUserIdByEmail(emailAddress)).CortecId ?? -1;
        }

        
        public IEnumerable<AbstractCortecTaskCreationStrategy> GetAllTaskCreationStrategies()
        {
            return new List<AbstractCortecTaskCreationStrategy>
            {
                new AnalyseNewKundeStrategy(true),
                new AnalyseProcessKundeStrategy(true),
                new AnalyseCompletedKundeStrategy(true),
                new AnalyseContractCreatedKundeStrategy(true),
                new AnalyseInactiveKundeStrategy(true),
                new AnalyseQSKundeStrategy(true),

                new AktualisierungInquiryKundeStrategy(true),
                new AktualisierungInquirySentKundeStrategy(true),
                new AktualisierungDocumentReceivedKundeStrategy(true),
                new AktualisierungQSKundeStrategy(true),
                new AktualisierungInactiveKundeStrategy(true),
                new AktualisierungVersendetKundeStrategy(true)
            };
        }

    }
}