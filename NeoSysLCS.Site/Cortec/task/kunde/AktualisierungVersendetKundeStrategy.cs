using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.DomainModel.Models.NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;

namespace NeoSysLCS.Site.Cortec.task.offer
{
    public class AktualisierungVersendetKundeStrategy : AbstractCortecTaskCreationStrategy
    {
        public AktualisierungVersendetKundeStrategy(bool postponeSending) : base(postponeSending) { }
        public override bool CanHandle<T>(ICortecTaskCreatable<T> creatable)
        {

            if (creatable.GetWrappedObject() is KundeViewModel kunde)
            {
                return (kunde.Status == KundeStatus.Sent || 
                        (
                            kunde.Status == KundeStatus.Update &&
                            kunde.Reccurence != null &&
                            kunde.DocumentReceivedDate == null &&
                            kunde.InvoiceDate.HasValue && kunde.InvoiceDate.Value.Date == DateTime.Now.Date
                            )
                        )
                         && kunde.Auftragsvolumen.HasValue;
            }

            return false;
        }

        public override List<int> TaskTemplateIds()
        {
            return new List<int> { 24 };
        }

        public override List<CortecTask> CreateTasks<T>(ICortecTaskCreatable<T> creatable)
        {
            var kunde = creatable.GetWrappedObject() as KundeViewModel;
            var customerName = kunde.Name;
            var projectNumber = kunde.ProjectNumberUpdate;
            var orderQuantity = kunde.Auftragsvolumen;
            var projectId = GetProjectId(projectNumber);
            var plId = _unitOfWork.UserRepository.GetById(kunde.ProjektleiterID).CortecId ?? -1;
            var now = DateTime.Now;

            var cortecTask = creatable.InitializeCortecTask(plId);
            cortecTask.ProjectNumber = projectNumber;
            cortecTask.ProjectID = projectId;
            cortecTask.Title = "Debitor GD" + customerName + "," + projectNumber + "," + orderQuantity + "; Aktualisierung";
            cortecTask.TaskTemplateId = TaskTemplateIds()[0];
            cortecTask.StartDate = now;
            cortecTask.EndDate = now.AddDays(5);
            cortecTask.TaskStateId = 1;
            cortecTask.TaskCategoryId = 2; // overwrite default value 9 set in the task initialization process
            cortecTask.AssignedTo = plId;
            cortecTask.WorkHours = 0.25M;
            cortecTask.Description = kunde.BillingInfo;
            AddTask(cortecTask);

            return HandleOrReturn();
        }
    }
}