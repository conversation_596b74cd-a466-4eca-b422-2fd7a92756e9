using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.DomainModel.Models.NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;

namespace NeoSysLCS.Site.Cortec.task.offer
{
    public class AktualisierungInactiveKundeStrategy : AbstractCortecTaskCreationStrategy
    {
        public AktualisierungInactiveKundeStrategy(bool postponeSending) : base(postponeSending){}
        public override bool CanHandle<T>(ICortecTaskCreatable<T> creatable)
        {

            if (creatable.GetWrappedObject() is KundeViewModel kunde)
            {
                return kunde.Status == KundeStatus.Inactive
                       && kunde.AnalysisStatus != KundeAnalysisStatus.Inactive
                       && kunde.Auftragsvolumen.HasValue
                       && !string.IsNullOrEmpty(kunde.ProjektleiterQSID);
            }

            return false;
        }

        public override List<int> TaskTemplateIds()
        {
            return new List<int> { 28 };
        }

        public override List<CortecTask> CreateTasks<T>(ICortecTaskCreatable<T> creatable)
        {
            var kunde = creatable.GetWrappedObject() as KundeViewModel;
            var customerName = kunde.Name;
            var projectNumber = kunde.ProjectNumberUpdate;
            var projectId = GetProjectId(projectNumber);
            var plId = _unitOfWork.UserRepository.GetById(kunde.ProjektleiterID).CortecId ?? -1;

            var cortecTask = creatable.InitializeCortecTask(plId);
            cortecTask.ProjectNumber = projectNumber;
            cortecTask.ProjectID = projectId;
            cortecTask.Title = customerName + " inaktiv";
            cortecTask.TaskTemplateId = TaskTemplateIds()[0];
            var now = DateTime.Now;
            cortecTask.StartDate = now;
            cortecTask.EndDate = now.AddDays(14);
            cortecTask.TaskStateId = 1;
            cortecTask.AssignedTo = _unitOfWork.UserRepository.GetUserByEmail("<EMAIL>")?.CortecId ?? -1;
            decimal workingHours = 0.25M;
            cortecTask.WorkHours = workingHours;
            AddTask(cortecTask);

            return HandleOrReturn();
        }
    }
}