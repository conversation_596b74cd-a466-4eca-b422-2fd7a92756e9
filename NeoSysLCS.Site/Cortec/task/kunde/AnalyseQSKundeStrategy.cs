using System;
using System.Collections.Generic;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.DomainModel.Models.NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories.ViewModels;

namespace NeoSysLCS.Site.Cortec.task.offer
{
    public class AnalyseQSKundeStrategy : AbstractCortecTaskCreationStrategy
    {
        public AnalyseQSKundeStrategy(bool postponeSending) : base(postponeSending) { }
        public override bool CanHandle<T>(ICortecTaskCreatable<T> creatable)
        {

            if (creatable.GetWrappedObject() is KundeViewModel kunde)
            {
                return kunde.Auftragsvolumen.HasValue
                    && kunde.Status == KundeStatus.Analysis
                    && kunde.AnalysisStatus == KundeAnalysisStatus.QS;
            }

            return false;
        }

        public override List<int> TaskTemplateIds()
        {
            return new List<int> { 10 };
        }

        public override List<CortecTask> CreateTasks<T>(ICortecTaskCreatable<T> creatable)
        {
            var kunde = creatable.GetWrappedObject() as KundeViewModel;
            var customerName = kunde.Name;
            var projectNumber = kunde.ProjectNumberUpdate;
            var projectId = GetProjectId(projectNumber);
            var plId = _unitOfWork.UserRepository.GetById(kunde.ProjektleiterID).CortecId ?? -1;
            var plQSId = _unitOfWork.UserRepository.GetById(kunde.ProjektleiterQSID).CortecId ?? -1;
            var now = DateTime.Now;

            var cortecTask = creatable.InitializeCortecTask(plId);
            cortecTask.ProjectNumber = projectNumber;
            cortecTask.ProjectID = projectId;
            cortecTask.Title = "QS Analyse " + customerName + " (Dokument, Bericht)";
            cortecTask.TaskTemplateId = TaskTemplateIds()[0];
            cortecTask.StartDate = now;
            cortecTask.EndDate = now.AddDays(5);
            cortecTask.TaskStateId = 4;
            cortecTask.AssignedTo = plQSId;
            decimal workingHours = (decimal)(kunde.Auftragsvolumen.Value * 0.10) / 187.5M;
            cortecTask.WorkHours = workingHours;
            AddTask(cortecTask);

            return HandleOrReturn();
        }
    }
}