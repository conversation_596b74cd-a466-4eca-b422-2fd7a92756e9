using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.DomainModel.Models.NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;
using NeoSysLCS.Site.Helpers;

namespace NeoSysLCS.Site.Cortec.task.offer
{
    public class NewCustomerNewOfferStrategy : AbstractCortecTaskCreationStrategy
    {
        public NewCustomerNewOfferStrategy(bool postponeSending) : base(postponeSending)
        {
        }
        public override bool CanHandle<T>(ICortecTaskCreatable<T> creatable)
        {
            if (creatable.GetWrappedObject() is OfferViewModel offer)
            {
                return !offer.ExistingCustomerID.HasValue 
                       && offer.Status == OfferStatus.New;
            }

            return false;
        }

        public override List<int> TaskTemplateIds()
        {
            return new List<int> { 5 };
        }

        public override List<CortecTask> CreateTasks<T>(ICortecTaskCreatable<T> creatable)
        {
            var offer = creatable.GetWrappedObject() as OfferViewModel;
            var customerName = offer.NewCustomerName;
            var projectNumber = NEW_CUSTOMER_PROJECT_NUMBER; 
            var projectId = NEW_CUSTOMER_PROJECT_ID;
            var plId = _unitOfWork.UserRepository.GetById(offer?.ProjectManagerID)?.CortecId ?? -1;

            var cortecTask = creatable.InitializeCortecTask(null);
            cortecTask.ProjectNumber = projectNumber;
            cortecTask.ProjectID = projectId;
            cortecTask.Title = "Offerte " + customerName + " erstellen";
            cortecTask.TaskTemplateId = TaskTemplateIds()[0];
            cortecTask.StartDate = DateTime.Now;
            cortecTask.EndDate = DateTime.Now.AddDays(5);
            cortecTask.TaskStateId = 1;
            cortecTask.AssignedTo = plId;
            cortecTask.WorkHours = 0.5M;

            AddTask(cortecTask);

            return HandleOrReturn();
        }
    }
}