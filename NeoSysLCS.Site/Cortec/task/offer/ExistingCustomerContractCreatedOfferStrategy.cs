using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.DomainModel.Models.NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;

namespace NeoSysLCS.Site.Cortec.task.offer
{
    public class ExistingCustomerContractCreatedOfferStrategy : AbstractCortecTaskCreationStrategy
    {
        public ExistingCustomerContractCreatedOfferStrategy(bool postponeSending) : base(postponeSending)
        {
        }

        public override bool CanHandle<T>(ICortecTaskCreatable<T> creatable)
        {

            if (creatable.GetWrappedObject() is OfferViewModel offer)
            {
                return offer.ExistingCustomerID.HasValue 
                       && offer.Status == OfferStatus.ContractCreated;
            }

            return false;
        }

        public override List<int> TaskTemplateIds()
        {
            return new List<int> { 17 };
        }

        public override List<CortecTask> CreateTasks<T>(ICortecTaskCreatable<T> creatable)
        {
            var offer = creatable.GetWrappedObject() as OfferViewModel;
            var kunde = GetCustomerById(offer.ExistingCustomerID);
            var customerName = kunde?.Name ?? "Unknown";
            var projectNumber = kunde?.ProjectNumberUpdate ?? "Unknown";
            var projectId = GetProjectId(projectNumber);
            var plId = _unitOfWork.UserRepository.GetById(offer.ProjectManagerID).CortecId ?? -1;

            var cortecTask = creatable.InitializeCortecTask(plId);
            cortecTask.ProjectNumber = projectNumber;
            cortecTask.ProjectID = projectId;
            cortecTask.Title = "Rückfrage Vertrag zusätzliche Arbeiten " + customerName;
            cortecTask.TaskTemplateId = TaskTemplateIds()[0];
            cortecTask.StartDate = offer.ContractReturnedAt.Value.AddDays(-180);
            cortecTask.EndDate = offer.ContractReturnedAt.Value;
            cortecTask.TaskStateId = 1;
            cortecTask.AssignedTo = plId;
            cortecTask.WorkHours = 0.25M;

            cortecTask.ReferenceObjectType = ReferenceObjectType.Customer;
            cortecTask.ReferenceObjectId = kunde.KundeID;
            cortecTask.TaskScheduledForCreationAt = kunde.ContractReturnedDate.Value.AddMonths(1);

            AddTask(cortecTask);

            return HandleOrReturn();
        }
    }
}