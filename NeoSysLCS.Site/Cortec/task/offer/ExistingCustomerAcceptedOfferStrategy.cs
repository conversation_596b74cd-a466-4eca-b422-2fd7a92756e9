using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.DomainModel.Models.NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;

namespace NeoSysLCS.Site.Cortec.task.offer
{
    public class ExistingCustomerAcceptedOfferStrategy : AbstractCortecTaskCreationStrategy
    {
        public ExistingCustomerAcceptedOfferStrategy(bool postponeSending) : base(postponeSending){}
        public override bool CanHandle<T>(ICortecTaskCreatable<T> creatable)
        {

            if (creatable.GetWrappedObject() is OfferViewModel offer)
            {
                // if status changed to Accepted it will immediately change to InProgress (see also code method OfferRepository.ResolveOfferStatus(OfferViewModel viewModel))
                return offer.ExistingCustomerID.HasValue 
                    && offer.Status == OfferStatus.InProgress;
            }

            return false;
        }

        public override List<int> TaskTemplateIds()
        {
            return new List<int> { 14 };
        }

        public override List<CortecTask> CreateTasks<T>(ICortecTaskCreatable<T> creatable)
        {
            var offer = creatable.GetWrappedObject() as OfferViewModel;
            var kunde = GetCustomerById(offer.ExistingCustomerID);
            var customerName = kunde?.Name ?? "Unknown";
            var projectNumber = kunde?.ProjectNumberUpdate ?? "Unknown";
            var projectId = GetProjectId(projectNumber);
            var plId = _unitOfWork.UserRepository.GetById(offer.ProjectManagerID).CortecId ?? -1;

            var cortecTask = creatable.InitializeCortecTask(plId);
            cortecTask.ProjectNumber = projectNumber;
            cortecTask.ProjectID = projectId;
            cortecTask.Title = "Zusätzliche Arbeiten " + customerName + " ausführen";
            cortecTask.TaskTemplateId = TaskTemplateIds()[0];
            cortecTask.StartDate = DateTime.Now;
            cortecTask.EndDate = DateTime.Now.AddDays(30);
            cortecTask.TaskStateId = 1;
            cortecTask.AssignedTo = plId;
            cortecTask.WorkHours = offer.OfferVolume / 187.5M;

            AddTask(cortecTask);

            return HandleOrReturn();
        }
    }
}