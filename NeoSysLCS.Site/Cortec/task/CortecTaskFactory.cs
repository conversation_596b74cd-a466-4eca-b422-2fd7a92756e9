using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using System.Web;
using NeoSysLCS.DomainModel.Models.NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Site.Cortec.Dtos;
using NeoSysLCS.Site.Utilities;

namespace NeoSysLCS.Site.Cortec.task
{
    public class CortecTaskFactory<T>
    {
        private readonly ICortecTaskCreatable<T> _creatable;
        private readonly ICortecClient _client;
        private readonly IUnitOfWork _unitOfWork;

        public CortecTaskFactory(ICortecTaskCreatable<T> creatable, IUnitOfWork unitOfWork)
        {
            _creatable = creatable;
            _client = CortecClientFactory.CreateTaskCreationClient();
            _unitOfWork = unitOfWork;
        }
        public void CreateCortecTasks()
        {
            var strategies = _creatable.GetAllTaskCreationStrategies();

            List<CortecTask> cortecTasks = new List<CortecTask>();
            foreach (var strategy in strategies)
            {
                if (strategy.CanHandle(_creatable))
                {
                    cortecTasks.AddRange(strategy.CreateTasks(_creatable));
                }
            }
            
        }
    }
}