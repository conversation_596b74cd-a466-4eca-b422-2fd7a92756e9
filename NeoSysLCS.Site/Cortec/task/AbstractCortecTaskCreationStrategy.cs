using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.DomainModel.Models.NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Site.Utilities;

namespace NeoSysLCS.Site.Cortec.task
{

    public abstract class AbstractCortecTaskCreationStrategy
    {
        private bool _postponeSending;
        private List<CortecTask> _tasks;
        protected readonly UnitOfWork _unitOfWork;

        protected readonly int NEW_CUSTOMER_PROJECT_ID = 1152; //"GD-Offerten"
        protected readonly string NEW_CUSTOMER_PROJECT_NUMBER = "2924.003";
        protected readonly int NEW_COMMENT_PROJECT_ID = 1154;
        protected readonly string NEW_COMMENT_PROJECT_NUMBER = "2924.005";
        protected readonly int NONEXISTING_PROJECT_ID = -1;

        protected AbstractCortecTaskCreationStrategy(bool postponeSending)
        {
            _postponeSending = postponeSending;
            _tasks = new List<CortecTask>();
            _unitOfWork = new UnitOfWork();
        }
        public abstract bool CanHandle<T>(ICortecTaskCreatable<T> creatable);
        public abstract List<CortecTask> CreateTasks<T>(ICortecTaskCreatable<T> creatable);
        public abstract List<int> TaskTemplateIds();

        protected int GetProjectId(string projectNumber)
        {
            if (string.IsNullOrEmpty(projectNumber))
            {
                return NONEXISTING_PROJECT_ID;
            }
            return _unitOfWork.KundeCortecRepository.GetKundeCortecByAuftragNr(projectNumber)?.AuftragID ??
                   NONEXISTING_PROJECT_ID;
        }

        protected Kunde GetCustomerById(int? customerId)
        {
            if (customerId == null)
            {
                return null;
            }
            return _unitOfWork.KundeRepository.GetByID(customerId);
        }

        protected void AddTask(CortecTask task)
        {
            _tasks.Add(task);

        }

        protected List<CortecTask> HandleOrReturn()
        {
            if (_postponeSending)
            {
                foreach (var task in _tasks)
                {
                    if (ConfigurationManager.AppSettings["AvoidCortecTaskDuplicates"] != "true" || !TaskAlreadyCreated(task))
                    {
                        _unitOfWork.CortecTaskRepository.InsertTask(task);
                    }
                }
                return new List<CortecTask>();
            }

            return _tasks;
        }

        private bool TaskAlreadyCreated(CortecTask cortecTask)
        {
            try
            {
                var matchingCortecTasks = _unitOfWork.CortecTaskRepository.GetALike(cortecTask).ToList();
                return matchingCortecTasks.Any();
            }
            catch (Exception e)
            {
                ExceptionUtility.LogException(e, "AbstractCortecTaskCreationStrategy.TaskAlreadyCreated => TemplateId: " + cortecTask.TaskTemplateId + "; ProjectId: " + cortecTask.ProjectID);
            }

            return false;
        }
    }
}
