using System;
using System.Collections.Generic;

namespace NeoSysLCS.Site.Cortec.Dtos
{
    public class CustomerDto
    {
        public List<CorrespondenceAddressDto> AdrKorrespondenz { get; set; }
        public int AuftragID { get; set; }
        public string AuftragNr { get; set; }
        public float? AuftragsVolumen { get; set; }
        public string Datum_Eingang { get; set; }
        public string EmailKorrespondenz { get; set; }
        public string Korrespondenz { get; set; }
        public int? KorrespondenzPersonID { get; set; }
        public string Kundenbezeichnung { get; set; }
        public string Kundennummer { get; set; }
        public string ProjektTitel { get; set; }
    }

    public class CorrespondenceAddressDto
    {
        public string StrasseNr { get; set; }
        public string PLZ { get; set; }
        public string Ort { get; set; }
    }
}