using System;
using System.Collections.Generic;
using NeoSysLCS.DomainModel.Models.NeoSysLCS.DomainModel.Models;

namespace NeoSysLCS.Site.Cortec.Dtos
{
    public class ToDoTaskRespDto
    {
        public string status { get; set; }
        public int task_id { get; set; }

        public static ToDoTaskRespDto errorResponse()
        {
            return new ToDoTaskRespDto
            {
                status = "nok",
                task_id = -1
            };
        }
    }

}