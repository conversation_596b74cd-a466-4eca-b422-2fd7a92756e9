using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Threading.Tasks;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.DomainModel.Models.NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Site.Cortec.Dtos;
using NeoSysLCS.Site.Cortec.task;
using NeoSysLCS.Site.Cortec.task.kommentar;
using NeoSysLCS.Site.Cortec.task.kunde;
using NeoSysLCS.Site.Cortec.task.offer;
using NeoSysLCS.Site.Utilities;

namespace NeoSysLCS.Site.Cortec
{
    public class CortecService
    {

        private readonly IUnitOfWork _unitOfWork;
        private readonly ICortecClient _client;

        public CortecService()
        {
            _unitOfWork = new UnitOfWork();
            _client = CortecClientFactory.CreateTaskCreationClient();
        }

        public void NewToDoTask(ToDoTask item)
        {
            
        }

        public async Task SendUnsentTasks()
        {
            var pendingTasks = _unitOfWork.CortecTaskRepository.GetPendingTasksForCreation().ToList();

            foreach (var cortecTask in pendingTasks)
            {
                if (!TaskIsStillValid(cortecTask))
                {
                    MarkTaskAsUnsuccessfullyProcessed(cortecTask);
                    SaveTaskUpdates(cortecTask);
                    continue;
                }
                var dto = ToDoTaskDto.fromCortecTask(cortecTask);
                try
                {
                    await ProcessTaskForCreation(cortecTask, dto);
                }
                catch (Exception ex)
                {
                    HandleTaskCreationError(cortecTask, ex);
                }
                finally
                {
                    SaveTaskUpdates(cortecTask);
                }
            }
        }

        private bool TaskIsStillValid(CortecTask cortecTask)
        {
            if (cortecTask.ReferenceObjectId == null || cortecTask.ReferenceObjectType == null) {
                return true;
            }
            switch (cortecTask.ReferenceObjectType)
            {
                case ReferenceObjectType.Customer:
                    var kunde = this._unitOfWork.KundeRepository.GetViewModelById((int)cortecTask.ReferenceObjectId);
                    var kundeAdapter = new KundeAdapter(kunde, _unitOfWork, -1);
                    return IsTaskValidForAdapter(kundeAdapter, cortecTask.TaskTemplateId);
                case ReferenceObjectType.Comment:
                    var comment = this._unitOfWork.KommentarRepository.GetKommentarViewModelById((int)cortecTask.ReferenceObjectId);
                    var kommentarAdapter = new KommentarAdapter(comment, _unitOfWork, -1);
                    return IsTaskValidForAdapter(kommentarAdapter, cortecTask.TaskTemplateId);
                case ReferenceObjectType.Offer:
                    var offer = this._unitOfWork.OfferRepository.GetOfferViewModelById((int)cortecTask.ReferenceObjectId);
                    var offerAdapter = new OfferAdapter(offer, _unitOfWork, -1);
                    return IsTaskValidForAdapter(offerAdapter, cortecTask.TaskTemplateId);
                default:
                    return true;
            }
        }

        private bool IsTaskValidForAdapter<T>(ICortecTaskCreatable<T> adapter, int taskTemplateId)
        {
            var isTaskValid = false;
            foreach (var strategy in adapter.GetAllTaskCreationStrategies())
            {
                if (strategy.CanHandle(adapter) && strategy.TaskTemplateIds().Contains(taskTemplateId))
                {
                    isTaskValid = true;
                    break;
                }
            }
            return isTaskValid;
        }

        private void MarkTaskAsUnsuccessfullyProcessed(CortecTask cortecTask)
        {
            cortecTask.TaskCreatedAt = DateTime.Now;
            cortecTask.ExternalTaskId = -2;
        } 

        private async Task ProcessTaskForCreation(CortecTask cortecTask, ToDoTaskDto dto)
        {
            cortecTask.TaskCreatedAt = DateTime.Now;
            var response = await _client.CreateToDoTask(dto);

            if (response.status.Equals("ok", StringComparison.OrdinalIgnoreCase))
            {
                cortecTask.ExternalTaskId = response.task_id;
            }
            else
            {
                LogTaskCreationFailure(dto, response.status);
                DecrementExternalTaskId(cortecTask);
            }
        }

        private void HandleTaskCreationError(CortecTask cortecTask, Exception ex)
        {
            DecrementExternalTaskId(cortecTask);
            ExceptionUtility.LogException(ex, "CortecService.SendUnsentTasks");
        }

        private void SaveTaskUpdates(CortecTask cortecTask)
        {
            _unitOfWork.CortecTaskRepository.UpdateTask(cortecTask);
            _unitOfWork.Save();
        }

        private void LogTaskCreationFailure(ToDoTaskDto dto, string status)
        {
            ExceptionUtility.LogInfo($"CortecTask creation (templateId: {dto.TaskTemplateId}, projectId: {dto.ProjectId}) failed. Status code is {status}");
        }

        private void DecrementExternalTaskId(CortecTask cortecTask)
        {
            cortecTask.ExternalTaskId = cortecTask.ExternalTaskId != null ? cortecTask.ExternalTaskId - 1 : 0;
        }
    }
}
