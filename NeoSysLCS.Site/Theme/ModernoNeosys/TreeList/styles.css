.dxtlControl_ModernoNeosys
{
	cursor: default;
	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
	color: #2B2B2B;
	border: 1px none #d1d1d1;
}
.dxtlControl_ModernoNeosys caption
{
	background: #F7FAFF none;
	color: #2B2B2B;
	border-bottom: 0;
	border: 1px solid #d1d1d1;
	font-weight: normal;
	text-align: center;
	padding: 5px 5px 6px;
}
.dxtlControl_ModernoNeosys .dxtlDataTable
{
	border: 1px solid #d1d1d1;
}

/* Indent cells */
.dxtlIndent_ModernoNeosys,
.dxtlIndentWithButton_ModernoNeosys
{
	vertical-align: top;
	background: white none no-repeat top center;
}
.dxtlIndent_ModernoNeosys
{
	padding: 0 11px;
}
.dxtlICheckBox_ModernoNeosys
{
	margin: auto;
}
.dxtlIndentWithButton_ModernoNeosys
{
	padding: 10px 4px;
}
.dxtlSelectionCell_ModernoNeosys
{
	border: 1px solid #d1d1d1;
	padding: 0 4px;
}

/* Tree-lines cells */
.dxtlLineRoot_ModernoNeosys
{
	background: White  repeat-y center top;
}
.dxtlLineFirst_ModernoNeosys
{
}
.dxtlLineMiddle_ModernoNeosys
{
}
.dxtlLineLast_ModernoNeosys
{
}
.dxtlLineFirstRtl_ModernoNeosys
{
}
.dxtlLineMiddleRtl_ModernoNeosys
{
}
.dxtlLineLastRtl_ModernoNeosys
{
}

/* Headers */
.dxtlControl_ModernoNeosys .dxtlHSDC 
{
    background: none;
    border: 1px Solid #d1d1d1;
}
.dxtlHeader_ModernoNeosys
{
	border: 1px Solid #d1d1d1;
	background: #fafafa none;
	padding: 8px 10px 7px;
	font-weight: normal;
}
.dxtlDataTable .dxtlHeader_ModernoNeosys
{
    border-top: 0;
    border-right: 0;
}
.dxtlControl_ModernoNeosys[dir=rtl] .dxtlDataTable .dxtlHeader_ModernoNeosys
{
    border-left: 0;
    border-right: 1px Solid #d1d1d1;
}
.dxtlDataTable .dxtlHeader_ModernoNeosys:first-child 
{
    border-left-width: 0;
}
.dxtlDataTable .dxtlHeader_ModernoNeosys:last-child 
{
    border-right-width: 0;
}
.dxtlHeader_ModernoNeosys table.dxtl
{
	border-collapse: collapse;
	width: 100%;
}
.dxtlHeader_ModernoNeosys td.dxtl
{
	padding: 0;
}
.dxtlHeader_ModernoNeosys,
.dxtlHeader_ModernoNeosys td.dxtl
{
    color: #7e7e7e;
	white-space: nowrap;
	text-align: left;
	overflow: hidden;
}

/* Nodes */
.dxtlNode_ModernoNeosys
{
	background: white;
}

.dxtlNode_ModernoNeosys:hover
{
	background-color:#009C49;
}

.dxtlAltNode_ModernoNeosys
{
	background-color: #f4f1e3;
}

.dxtlAltNode_ModernoNeosys:hover
{
	background-color:#009C49;
}

.dxtlSelectedNode_ModernoNeosys
{
	background-color: #f3f3f3;
}
.dxtlFocusedNode_ModernoNeosys
{
	background-color: #e5e5e5;
}
.dxtlInlineEditNode_ModernoNeosys
{
	background: white none;
}
.dxtlEditFormDisplayNode_ModernoNeosys
{
	background: #ECF4FE none;
}
.dxtlNode_ModernoNeosys td.dxtl, 
.dxtlAltNode_ModernoNeosys  td.dxtl, 
.dxtlSelectedNode_ModernoNeosys td.dxtl, 
.dxtlFocusedNode_ModernoNeosys td.dxtl,
.dxtlEditFormDisplayNode_ModernoNeosys td.dxtl,
.dxtlCommandCell_ModernoNeosys
{
	padding: 8px 10px;
	border: 1px solid #d1d1d1;
	white-space: nowrap;
	overflow: hidden;
}
.dxtlCommandCell_ModernoNeosys
{
	padding: 0 10px;
}
.dxtlInlineEditNode_ModernoNeosys td.dxtl
{
	border: 1px solid #d1d1d1;
	padding: 1px;
}
/* Scrolling */
.dxtlControl_ModernoNeosys .dxtlHSDC,
.dxtlControl_ModernoNeosys .dxtlCSD,
.dxtlControl_ModernoNeosys .dxtlFSDC
{
    border: 1px solid #d1d1d1;
}
.dxtlControl_ModernoNeosys .dxtlHSDC .dxtlCSD,
.dxtlControl_ModernoNeosys .dxtlFSDC .dxtlCSD,
.dxtlControl_ModernoNeosys .dxtlHSDC .dxtlDataTable,
.dxtlControl_ModernoNeosys .dxtlCSD .dxtlDataTable,
.dxtlControl_ModernoNeosys .dxtlFSDC .dxtlDataTable
{
    border: 0;
}
.dxtlControl_ModernoNeosys .dxtlCSD .dxtlDataTable .dxtlHSEC
{
    border-left: 1px solid #d1d1d1!important;
}
.dxtlControl_ModernoNeosys .dxtlHSDC .dxtlHeader_ModernoNeosys 
{
    border-bottom: 0;
}
.dxtlControl_ModernoNeosys .dxtlFSDC .dxtlFooter_ModernoNeosys td.dxtl,
.dxtlControl_ModernoNeosys .dxtlFSDC .dxtlFooter_ModernoNeosys .dxtlIndent_ModernoNeosys
{
    border-top: 0!important;
}
.dxtlControl_ModernoNeosys .dxtlHSDC + .dxtlCSD
{
    border-top: 0;
}
.dxtlControl_ModernoNeosys .dxtlCSD + .dxtlFSDC
{
    border-top: 0;
}

/* Preview */
.dxtlPreview_ModernoNeosys
{
	background-color: white;
	padding: 15px 15px 15px 20px;
	border: 1px solid #d1d1d1;
	overflow: hidden;
}

/* Footers */
.dxtlGroupFooter_ModernoNeosys
{
	background-color: white;
}
.dxtlFooter_ModernoNeosys td.dxtl,
.dxtlFooter_ModernoNeosys .dxtlIndent_ModernoNeosys,
.dxtlControl_ModernoNeosys .dxtlFSDC
{
	background-color: #fafafa;
}
.dxtlGroupFooter_ModernoNeosys td.dxtl,
.dxtlFooter_ModernoNeosys td.dxtl
{
	white-space: nowrap;
	border: 1px solid #d1d1d1;
	overflow: hidden;
}
.dxtlFooter_ModernoNeosys td.dxtl,
.dxtlFooter_ModernoNeosys .dxtlIndent_ModernoNeosys
{
    border-top: 1px solid #d1d1d1!important;
}
.dxtlFooter_ModernoNeosys td.dxtl
{
	padding: 12px 10px;
}
.dxtlGroupFooter_ModernoNeosys td.dxtl
{
    padding: 8px 10px;
}

/* Pagers */
.dxtlPagerTopPanel_ModernoNeosys,
.dxtlPagerBottomPanel_ModernoNeosys
{
	padding: 4px 0;
}
.dxtlPagerTopPanel_ModernoNeosys
{
	border-bottom: 1px none #d1d1d1;
}
.dxtlPagerBottomPanel_ModernoNeosys
{
    border-top: 1px none #d1d1d1;
}
.dxtlControl_ModernoNeosys .dxpLite_ModernoNeosys
{
	padding: 6px 0;
}

/* Editing */
.dxtlEditForm_ModernoNeosys
{
	border: 1px solid #d1d1d1;
	padding: 10px 15px;
}
.dxtlEditFormCaption_ModernoNeosys,
.dxtlEditFormEditCell_ModernoNeosys
{
	padding: 4px;
}
.dxtlEditFormCaption_ModernoNeosys
{
	padding-left: 10px;
	white-space: nowrap;
}
.dxtlError_ModernoNeosys
{
	background: #DBEBFF none;
	color: #FF0000;
	padding: 6px 10px;
	border: 1px solid #d1d1d1;
}
.dxtlPopupEditForm_ModernoNeosys
{
	padding: 12px;
}

/* Links */
.dxtlControl_ModernoNeosys a,
.dxtlCommandCell_ModernoNeosys a
{
	color: #009C49;
	text-decoration: underline;
}
.dxtlControl_ModernoNeosys a:hover
{
	color: #2b2b2b;
	text-decoration: underline;
}
.dxtlHeader_ModernoNeosys a,
.dxtlFocusedNode_ModernoNeosys a,
.dxtlSelectedNode_ModernoNeosys a
{
	color: #009C49;
}
.dxtlHeader_ModernoNeosys a:hover,
.dxtlFocusedNode_ModernoNeosys a:hover,
.dxtlSelectedNode_ModernoNeosys a:hover
{
	color: #2b2b2b;
}
.dxtlCommandCell_ModernoNeosys a
{
	margin-right: 3px;
}

/* Loading panel */
.dxtlLoadingPanel_ModernoNeosys
{
	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
	color: #2B2B2B;
	background-color: White;
	border: 1px solid #cfcfcf;
	box-shadow: 0px 1px 3px 0px rgba(0,0,0,0.1);
	-webkit-box-shadow: 0px 1px 3px 0px rgba(0,0,0,0.1);
}
.dxtlLoadingPanel_ModernoNeosys td.dx
{
	white-space: nowrap;
	text-align: center;
	padding: 15px 34px 15px 20px;
}
.dxtlLoadingPanel_ModernoNeosys .dxlp-loadingImage
{
	background-image: url('../Web/Loading.gif');
	height: 40px;
	width: 40px;
}

/* Disabled */
.dxtlDisabled_ModernoNeosys,
.dxtlDisabled_ModernoNeosys .dxtl_ModernoNeosys
{
	color: #A1A1A1;
	cursor: default;
}


