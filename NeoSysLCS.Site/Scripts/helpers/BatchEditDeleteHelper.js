/// <summary>
/// J<PERSON>-Helper for showing the entities marked for deletion
/// </summary>
/// <param name="gridViewId">Id of the current gridview</param>
/// <param name="idField">Name of the id field.</param>
/// <param name="titleField">Name of the title field.</param>
/// <param name="infoText">Text shown in the deletion box.</param>
BatchEditDeleteHelper = function (gridViewId, idField, titleField, infoText) {

    var gridView;
    var elementsToDelete = new Array();
    var infoDiv = '<div id="deleteSection_'+gridViewId +'" style="display: none;">' +
        '<div class="alert alert-warning">' +
            '<p>' + infoText +
                ' <strong><span id="deleteCount_' + gridViewId + '"></span><strong>' +
            '</p>' +
        '</div></div>';

    var __construct = function () {
        //gets the gridviewinstance
        gridView = eval(gridViewId);

        //register events
        RegisterOnClick();
        RegisterOnCancelEdit();
        gridView.BeginCallback.AddHandler(function (s, e) {
            $('#deleteSection_' +gridViewId).hide();
            if (e.command == 'UPDATEEDIT') {
                //on save the elements to delete need to be cleared
                $('#deleteCount_' + gridViewId).html("");
                elementsToDelete = new Array();
            }
        });
        gridView.EndCallback.AddHandler(function (s, e) {
            //Reregister the events
            RegisterOnClick();
            RegisterOnCancelEdit();
        });
    }

    var RegisterOnClick = function () {

        //clickevent
        var onDeleteClick = function () {
            var onclickCommand = $(this).parent().attr('data-args');
            var regex = /(\['Delete',)(-?\d+)/g;
            var match = regex.exec(onclickCommand);
            //remove current selection before selecting row to delete
            //gridView.UnselectAllRowsOnPage();
            gridView.SelectItem(match[match.length - 1], true);
            gridView.GetSelectedFieldValues(idField + ";" + titleField, GetSelectedFieldValuesCallback);
        }

        getGridDeleteButtons().each(function () {
           
            //remove old event
            $(this.children[0]).off('click');

            //regegister event
            $(this.children[0]).on('click', onDeleteClick);
        });
    }

    var RegisterOnCancelEdit = function () {

        //cancel edit click event
        var onCancelEdit = function () {
            $('#deleteSection_' + gridViewId).hide();
            $('#deleteCount_' + gridViewId).html("");
            elementsToDelete = new Array();
        }

        getGridCancelEditButtons().each(function () {
            //remove old event
            $(this.children[0]).off('click');

            //regegister event
            $(this.children[0]).on('click', onCancelEdit);
        });
    }

    var GetSelectedFieldValuesCallback = function (values) {

        //add panel if not yet added
        if ($('#deleteSection_' + gridViewId).length === 0) {
            $('#' + gridViewId).append(infoDiv);
        }

        for (var i = 0; i < values.length; i++) {
            var value = values[i][1];
            var key = values[i][0];
            if ($.inArray(key, elementsToDelete) === -1) {
                //current value is not yet showed in the panel: let append it and save it in the elementsToDelete

                if (value instanceof Date) {
                    //date formation
                    value = value.getDate() + '.' + (value.getMonth() + 1) + '.' + value.getFullYear() + " ";
                }
                if (elementsToDelete.length > 0) {
                    $('#deleteCount_' + gridViewId).append(', ');
                }
                if (value !== null && value.length > 50) {
                    $('#deleteCount_' + gridViewId).append(value.substr(0, 50) + '...');
                } else {
                    $('#deleteCount_' + gridViewId).append(value);
                }

                elementsToDelete.push(key);
            }

        }

        $('#deleteSection_' + gridViewId).show();
    }

    var getGridDeleteButtons = function () {
        //gets all deletebuttons 
        //Attention: check if still valid for new Devexpress versions
        var regexp = new RegExp("\\['Delete',-?\\d+(,|\\])");
        var buttons = $(gridView.GetMainElement()).find('a').filter(function () {
            return this.attributes["data-args"] && regexp.test(this.attributes["data-args"].textContent.toString());
        });
        return buttons;
    }

    var getGridCancelEditButtons = function () {
        //gets all canceledit buttons
        var regexp = new RegExp("\\[\\['CancelEdit'\\],-?\\d+(,|\\])");
        var buttons = $(gridView.GetMainElement()).find('a').filter(function () {
            return this.attributes["data-args"] && regexp.test(this.attributes["data-args"].textContent.toString());
        });
        return buttons;
    }

    __construct();
}