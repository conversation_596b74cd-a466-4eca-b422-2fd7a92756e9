using System.Web.Mvc;
using System.Web.Mvc.Filters;

namespace NeoSysLCS.Site.Filters
{
    public class PwdResetAuthenticationAttribute : ActionFilterAttribute, IAuthenticationFilter
    {
        public void OnAuthentication(AuthenticationContext filterContext)
        {

            //For demo purpose only. In real life your custom principal might be retrieved via different source. i.e context/request etc.
            
            filterContext.Principal = new MyAdminUserPrincipal(filterContext.HttpContext.User.Identity, new[] { "Admin" }, true);
        }

        public void OnAuthenticationChallenge(AuthenticationChallengeContext filterContext)
        {
            var authFlag = ((MyAdminUserPrincipal)filterContext.HttpContext.User).AuthFlag;
            var user = filterContext.HttpContext.User;

            if (!authFlag && !user.Identity.IsAuthenticated)
            {
                filterContext.Result = new HttpUnauthorizedResult();
            }
        }
    }

   
}