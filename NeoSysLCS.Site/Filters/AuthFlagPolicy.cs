using System.Web;
using FluentSecurity;
using FluentSecurity.Policy;

namespace NeoSysLCS.Site.Filters
{
    
    public class AuthFlagPolicy : ISecurityPolicy
    {
        
        public PolicyResult Enforce(ISecurityContext context)
        {

            HttpContext.Current.Session["_userid"] = HttpContext.Current.Request["userid"];

            bool authflag = HttpContext.Current.Session["Authflag"] != null && (bool) HttpContext.Current.Session["Authflag"];

            return authflag ?
                PolicyResult.CreateSuccessResult(this) : 
                PolicyResult.CreateFailureResult(this, "Access denied!");
        }
    }
    
}