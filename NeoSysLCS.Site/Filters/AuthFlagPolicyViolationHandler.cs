using System.Web.Mvc;
using FluentSecurity;

namespace NeoSysLCS.Site.Filters
{
    /// <summary>
    /// Custom Policy Violation Handler. See http://www.fluentsecurity.net/wiki/Policy-violation-handlers
    /// </summary>
    public class AuthFlagPolicyViolationHandler : IPolicyViolationHandler
    {
        public ActionResult Handle(PolicyViolationException exception)
        {
            
            return new RedirectResult("/Admin/UsersAdmin/PwdResetLogin" );
        }
    }

    public class DenyAccessPolicyViolationHandler : IPolicyViolationHandler
    {
        public ActionResult Handle(PolicyViolationException exception)
        {
            return new RedirectResult("/");
        }
    }
}