using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using DevExpress.Web.Mvc;
using FluentSecurity;
//using FluentSecurity.Caching;
using Microsoft.AspNet.Identity;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories.Helper;
using NeoSysLCS.Repositories.ViewModels;
using NeoSysLCS.Site.Areas.Admin.Controllers;
using NeoSysLCS.Site.Areas.Portal.Controllers;
using NeoSysLCS.Site.Controllers;
using NeoSysLCS.Site.Filters;
using NeoSysLCS.Site.Helpers;
using DashboardController = NeoSysLCS.Site.Areas.Admin.Controllers.DashboardController;

namespace NeoSysLCS.Site
{
    public class FilterConfig
    {
        static readonly SessionHelper sh = new SessionHelper();

        public static void RegisterGlobalFilters(GlobalFilterCollection filters)
        {

            filters.Add(new AuthorizeAttribute());
            filters.Add(new RequreSecureConnectionFilter());

            //Configuring Page Security using FluentSecurity
            #region PageSecurityConf

            SecurityConfigurator.Configure(conf =>
            {
                //default violation handler
                conf.DefaultPolicyViolationHandlerIs(() => new PolicyViolationHandlerHelper());

                //conf.Advanced.SetDefaultResultsCacheLifecycle(Cache.PerHttpSession);

                //Make only active for Developement purposes
                //conf.Advanced.IgnoreMissingConfiguration();

                //Config for Authentication Status and currentUserRoles
                #region default Config


                // Tell FluentSecurity where to obtain the user authentication status from
                conf.GetAuthenticationStatusFrom(() =>
                    HttpContext.Current.User.Identity.IsAuthenticated && sh.IsPrivacyPolicyAccepted());

                // Tell FluentSecurity where to obtain the user roles from
                conf.GetRolesFrom(() =>
                {
                    if (HttpContext.Current.User.Identity.IsAuthenticated)
                    {
                        var identityId = HttpContext.Current.User.Identity.GetUserId();

                        return getRoles(identityId);
                    }
                    return new[] { "" };
                });

                conf.ResolveServicesUsing(type =>
                {
                    if (type == typeof(IPolicyViolationHandler))
                    {
                        return new List<IPolicyViolationHandler>() {
                            new DenyAccessPolicyViolationHandler(),
                            new AuthFlagPolicyViolationHandler(),
                        };
                    }
                    return Enumerable.Empty<object>();
                });



                #endregion Default Config

                // Secure all action methods of all controllers
                conf.ForAllControllers().DenyAnonymousAccess();

                //SecurityConfig for Controller/ Views outside Areas
                #region noArea

                #region AccountController

                conf.For<AccountController>(x => x.Login(default(string))).AllowAny();
                conf.For<AccountController>(x => x.LogOff()).DenyAnonymousAccess();

                conf.For<PrivacyPolicyConfirmationController>(x => x.Index()).AllowAny();
                conf.For<PrivacyPolicyConfirmationController>(x => x.PrivacyPolicyConfirmationGridView()).AllowAny();
                conf.For<PrivacyPolicyConfirmationController>(x => x.AcceptPrivacyPolicy()).AllowAny();
                #endregion

                #region BaseController

                conf.For<BaseController>().DenyAnonymousAccess();

                #endregion

                #region HomeController
                conf.For<HomeController>(x => x.Index()).AllowAny();
                conf.For<HomeController>(x => x.Impressum()).AllowAny();
                conf.For<HomeController>(x => x.PasswordReset()).AllowAny();
                conf.For<HomeController>(x => x.PasswordForgot(default(ChangePasswordViewModel))).AllowAny();
                conf.For<HomeController>(x => x.HttpError403(default(string))).AllowAny();
                conf.For<HomeController>(x => x.HttpError404(default(string))).AllowAny();
                conf.For<HomeController>(x => x.HttpError500(default(string))).AllowAny();
                conf.For<HomeController>(x => x.GeneralError(default(string))).AllowAny();

                #endregion

                #region KundendokumentImportExportController

                conf.For<KundendokumentImportExportController>().DenyAnonymousAccess();

                #endregion

                #region ErlassImportController

                conf.For<ErlassImportController>().DenyAnonymousAccess();

                #endregion

                #region ManageController

                conf.For<ManageController>().DenyAnonymousAccess();

                #endregion

                #endregion noArea

                //SecurityConfig for Controller/ Views within Admin Area

                #region AdminArea

                #region ArtikelController

                conf.For<ArtikelController>().RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<ArtikelController>(artCont =>
                    artCont.ArtikelGridView(
                        default(int))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<ArtikelController>(artCont =>
                    artCont.ArtikelGridViewBatchEditUpdate(
                        default(MVCxGridViewBatchUpdateValues<ArtikelViewModel, int>), default(int)))
                    .RequireAnyRole(Role.ProjectManager);
                conf.For<ArtikelController>(artCont =>
                    artCont.ArtikelUebersetzungenGridView(
                        default(int), default(int))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<ArtikelController>(artCont =>
                    artCont.ArtikelUebersetzungenGridViewBatchEditUpdate(
                        default(MVCxGridViewBatchUpdateValues<ArtikelViewModel, int>), default(int), default(int)))
                    .RequireAnyRole(Role.ProjectManager);

                #endregion

                #region AuswertungenController

                conf.For<AuswertungenController>().RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<AuswertungenController>(auswCon =>
                    auswCon.ExportObjekt2KundenAssignmentTo()).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<AuswertungenController>(auswCon =>
                    auswCon.VerwendeteObjekte()).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<AuswertungenController>(auswCon =>
                    auswCon._Objekt2KundenAssignmentGridView()).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);

                #endregion

                #region DashboardController

                conf.For<DashboardController>().RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<DashboardController>(dashCon =>
                    dashCon.Index()).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<DashboardController>(dashCon =>
                    dashCon.KundeGridView()).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<DashboardController>(dashCon =>
                    dashCon.ObjektGridView()).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<DashboardController>(dashCon =>
                    dashCon.ErlassGridView()).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<DashboardController>(dashCon =>
                    dashCon.StandortGridView()).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);

                #endregion

                #region ErlasseController

                conf.For<ErlasseController>().RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<ErlasseController>(erlCon =>
                    erlCon.ErlasseGridViewBatchEditUpdate(
                        default(MVCxGridViewBatchUpdateValues<ErlassViewModel, int>), default(bool), default(bool))).RequireAnyRole(Role.ProjectManager);
                conf.For<ErlasseController>(erlCon =>
                    erlCon.ErlassUebersetzungenGridView(
                        default(int))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<ErlasseController>(erlCon =>
                    erlCon.ErlassUebersetzungenGridViewBatchEditUpdate(
                        default(MVCxGridViewBatchUpdateValues<ErlassViewModel, int>), default(int)))
                    .RequireAnyRole(Role.ProjectManager);
                conf.For<ErlasseController>(erlCon =>
                    erlCon.ExportTo(default(bool))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);

                #endregion

                #region ErlassfassungenController

                conf.For<ErlassfassungenController>().RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<ErlassfassungenController>(erlFasCon =>
                    erlFasCon.ErlassfassungenGridView(
                        default(int))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<ErlassfassungenController>(erlFasCon =>
                    erlFasCon.ErlassfassungenGridViewBatchEditUpdate(
                        default(MVCxGridViewBatchUpdateValues<ErlassfassungViewModel, int>), default(int)))
                    .RequireAnyRole(Role.ProjectManager);
                conf.For<ErlassfassungenController>(erlFasCon =>
                    erlFasCon.ErlassfassungUebersetzungenGridView(
                        default(int), default(int))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<ErlassfassungenController>(erlFasCon =>
                    erlFasCon.ErlassfassungUebersetzungenGridViewBatchEditUpdate(
                        default(MVCxGridViewBatchUpdateValues<ErlassfassungViewModel, int>), default(int),
                        default(int))).RequireAnyRole(Role.ProjectManager);

                #endregion

                #region ErlasstypenController

                conf.For<ErlasstypenController>().RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<ErlasstypenController>(erlTypCon =>
                    erlTypCon.ErlasstypenGridView()).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<ErlasstypenController>(erlTypCon =>
                    erlTypCon.ErlasstypenGridViewBatchEditUpdate(
                        default(MVCxGridViewBatchUpdateValues<ErlasstypViewModel, int>)))
                    .RequireAnyRole(Role.ProjectManager);
                conf.For<ErlasstypenController>(erlTypCon =>
                    erlTypCon.ErlasstypUebersetzungenGridView(
                        default(int))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<ErlasstypenController>(erlTypCon =>
                    erlTypCon.ErlasstypGridViewBatchEditUpdate(
                        default(MVCxGridViewBatchUpdateValues<ErlasstypViewModel, int>), default(int)))
                    .RequireAnyRole(Role.ProjectManager);

                #endregion

                #region ForderungenController

                conf.For<ForderungenController>().RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<ForderungenController>(forContr =>
                    forContr.CreateNewVersion(
                        default(ForderungsversionViewModel))).RequireAnyRole(Role.ProjectManager);
                conf.For<ForderungenController>(forContr =>
                    forContr.ExportTo(
                        default(int))).RequireAnyRole(Role.ProjectManager);
                conf.For<ForderungenController>(forContr =>
                    forContr.ForderungenGridView(
                        default(int))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<ForderungenController>(forContr =>
                    forContr.ForderungenGridViewBatchEditUpdate(
                        default(MVCxGridViewBatchUpdateValues<ForderungsversionViewModel, int>), default(int)))
                    .RequireAnyRole(Role.ProjectManager);
                conf.For<ForderungenController>(forContr =>
                    forContr.ForderungsversionUebersetzungenGridView(
                        default(int), default(int))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<ForderungenController>(forContr =>
                    forContr.ForderungsversionUebersetzungenGridViewBatchEditUpdate(
                        default(MVCxGridViewBatchUpdateValues<ForderungsversionViewModel, int>), default(int),
                        default(int))).RequireAnyRole(Role.ProjectManager);
                conf.For<ForderungenController>(forContr =>
                    forContr.Index(
                        default(int))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<ForderungenController>(forContr =>
                    forContr.NewVersion(
                        default(int), default(int))).RequireAnyRole(Role.ProjectManager);
                conf.For<ForderungenController>(forContr =>
                    forContr._NewVersionNew(
                        default(int), default(int))).RequireAnyRole(Role.ProjectManager);

                #endregion

                #region ForderungenObjekteController

                conf.For<ForderungObjekteController>().RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<ForderungObjekteController>(forObjCon =>
                    forObjCon.ForderungObjektePartial(
                        default(int), default(int?))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<ForderungObjekteController>(forObjCon =>
                    forObjCon.Index(
                        default(int), default(int?))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<ForderungObjekteController>(forObjCon =>
                    forObjCon.ObjekteSelectionPartial(
                        default(int), default(int?))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<ForderungObjekteController>(forObjCon =>
                    forObjCon.SaveObjects()).RequireAnyRole(Role.ObjectGuru);

                #endregion

                #region ForderungRechtsbereicheController

                conf.For<ForderungRechtsbereicheController>().RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<ForderungRechtsbereicheController>(forRecCon =>
                    forRecCon.ForderungRechtsbereichePartial(
                    default(int), default(int?))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<ForderungRechtsbereicheController>(forRecCon =>
                    forRecCon.Index(
                        default(int), default(int?))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<ForderungRechtsbereicheController>(forRecCon =>
                    forRecCon.RechtsbereichSelectionPartial(
                        default(int), default(int?))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<ForderungRechtsbereicheController>(forRecCon =>
                    forRecCon.SaveRechtsbereiche()).RequireAnyRole(Role.ProjectManager);
                #endregion

                #region HerausgeberController

                conf.For<HerausgeberController>().RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<HerausgeberController>(herCon =>
                    herCon.HerausgeberGridView(default(bool))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<HerausgeberController>(herCon =>
                    herCon.HerausgeberGridViewBatchEditUpdate(default(MVCxGridViewBatchUpdateValues<HerausgeberViewModel, int>), default(bool))).RequireAnyRole(Role.ProjectManager);
                conf.For<HerausgeberController>(herCon =>
                    herCon.Index()).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);

                #endregion

                #region KontakteController

                conf.For<KontakteController>().RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);

                conf.For<KontakteController>(konCon =>
                    konCon.KontakteGridViewBatchEditUpdate(
                        default(MVCxGridViewBatchUpdateValues<KontaktViewModel, int>), default(int)))
                    .RequireAnyRole(Role.ProjectManager);
                conf.For<KontakteController>(konCon =>
                    konCon.RechtsbereicheRowSelectionPartial(default(int))).RequireAnyRole(Role.ProjectManager);
                conf.For<KontakteController>(konCon =>
                     konCon.SaveKontaktRechtsbereiche()).RequireAnyRole(Role.ProjectManager);

                #endregion

                #region KundenController

                conf.For<KundenController>(konCon =>
                    konCon.SpracheRowSelectionPartial()).RequireAnyRole(Role.ProjectManager);

                conf.For<KundenController>(konCon =>
                     konCon.KontakteRowSelectionPartial(default(int))).RequireAnyRole(Role.ProjectManager);
                conf.For<KundenController>(konCon =>
                     konCon.RechtsbereichRowSelectionPartial()).RequireAnyRole(Role.ProjectManager);
                conf.For<KundenController>(konCon =>
                   konCon.HerausgeberRowSelectionPartial(default(int))).RequireAnyRole(Role.ProjectManager);
                conf.For<KundenController>().RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
               conf.For<KundenController>(kunCon =>
                    kunCon.CopyKundenStandortPartialView(
                        default(int), default(int))).RequireAnyRole(Role.ProjectManager);
                conf.For<KundenController>(kunCon =>
                    kunCon.Index(default(int))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<KundenController>(kunCon =>
                    kunCon.KundenKontaktePartialView(
                        default(int))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<KundenController>(kunCon =>
                    kunCon.KundenAktualisierungPartialView()).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<KundenController>(kunCon =>
                    kunCon.KundenAnalysePartialView()).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<KundenOfferController>(kunCon =>
                    kunCon.KundenOfferPartialView()).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<KundenController>(kunCon =>
                    kunCon.CopyKundeAktualisierungPartialView(
                        default(int))).RequireAnyRole(Role.Admin);
                conf.For<KundenController>(kunCon =>
                    kunCon.CopyKundeAnalysePartialView(
                        default(int))).RequireAnyRole(Role.Admin);
                conf.For<KundenController>(kunCon =>
                    kunCon.KundenStandortPartialView(
                        default(int), default(string), default(string), default(bool))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<KundenController>(kunCon =>
                    kunCon.ShowHerausgeberPopUp(
                        default(int), default(int))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<KundenController>(kunCon =>
                    kunCon.ShowKontaktePopUp(
                        default(int), default(int))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<KundenController>(kunCon =>
                    kunCon.ShowPopUp(
                        default(int), default(int))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<KundenController>(kunCon =>
                    kunCon.ShowRechtsbereichePopUp(
                        default(int), default(int))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<KundenController>(kunCon =>
                    kunCon.ShowSprachenPopUp(
                        default(int), default(int))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<KundenController>(kunCon =>
                    kunCon._KundenStandortPartialViewUpdate(
                        default(MVCxGridViewBatchUpdateValues<StandortViewModel, int>), default(int), default(string))).RequireAnyRole(Role.ProjectManager);
                conf.For<KundenController>(konCon =>
                   konCon.SaveStandortKontakte()).RequireAnyRole(Role.ProjectManager);
                conf.For<KundenController>(konCon =>
                    konCon.SaveStandortRechtsbereiche()).RequireAnyRole(Role.ProjectManager);
                conf.For<KundenController>(konCon =>
                    konCon.SaveStandortSprache()).RequireAnyRole(Role.ProjectManager);
                conf.For<KundenController>(konCon =>
                   konCon.SaveKundenHerausgeber()).RequireAnyRole(Role.ProjectManager);

                #endregion

                #region KundendokumenteController

                conf.For<KundendokumenteController>().RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<KundendokumenteController>(kunDokQsCon =>
                   kunDokQsCon.KundendokumenteGridView(
                       default(int))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<KundendokumenteController>(kunDokQsCon =>
                    kunDokQsCon.Index(
                        default(int), default(string))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<KundendokumenteController>(kunDokQsCon =>
                    kunDokQsCon.Delete(
                        default(int), default(int))).RequireAnyRole(Role.ProjectManager);
                conf.For<KundendokumenteController>(kunDokQsCon =>
                    kunDokQsCon.Update(
                        default(int))).RequireAnyRole(Role.ProjectManager);

                #endregion

                #region KundendokumenteQsController

                conf.For<KundendokumenteQsController>().RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<KundendokumenteQsController>(kunDokQsCon =>
                      kunDokQsCon.KundendokumentForderungenGridView(
                          default(int), default(int), default(bool))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<KundendokumenteQsController>(kunDokQsCon =>
                    kunDokQsCon.KundendokumentForderungenGridViewBatchEditUpdate(
                        default(MVCxGridViewBatchUpdateValues<KundendokumentForderungsversionViewModel, int>),
                        default(int), default(int))).RequireAnyRole(Role.ProjectManager);
                conf.For<KundendokumenteQsController>(kunDokQsCon =>
                    kunDokQsCon.KundendokumentPflichtenGridView(
                        default(int), default(int), default(bool))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<KundendokumenteQsController>(kunDokQsCon =>
                    kunDokQsCon.KundendokumentPflichtenGridViewBatchEditUpdate(
                        default(MVCxGridViewBatchUpdateValues<KundendokumentPflichtViewModel, int>), default(int),
                        default(int))).RequireAnyRole(Role.ProjectManager);
                conf.For<KundendokumenteQsController>(kunDokQsCon =>
                   kunDokQsCon.KundendokumentErlassfassungenGridView(
                       default(int), default(int))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<KundendokumenteQsController>(kunDokQsCon =>
                    kunDokQsCon.KundendokumentErlassfassungenGridViewBatchEditUpdate(
                        default(MVCxGridViewBatchUpdateValues<KundendokumentErlassfassungViewModel, int>), default(int),
                        default(int))).RequireAnyRole(Role.ProjectManager);
                conf.For<KundendokumenteQsController>(kunDokQsCon =>
                    kunDokQsCon.KundendokumenteQs(
                        default(int))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<KundendokumenteQsController>(kunDokQsCon =>
                    kunDokQsCon.KundendokumenteQsPageControl(
                        default(int))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<KundendokumenteQsController>(kunDokQsCon =>
                    kunDokQsCon.SaveKundendokumenteFreigabe(
                        default(KundendokumentViewModel))).RequireAnyRole(Role.ProjectManager);
                conf.For<KundendokumenteQsController>(kunDokQsCon =>
                    kunDokQsCon.SaveKundendokumenteQs(
                        default(KundendokumentViewModel))).RequireAnyRole(Role.ProjectManager);
                conf.For<KundendokumenteQsController>(kunDokQsCon =>
                    kunDokQsCon.ApproveAllForderungen(
                        default(int), default(int))).RequireAnyRole(Role.ProjectManager);
                #endregion

                #region KundendokumentUpdateController

                conf.For<KundendokumentUpdateController>().RequireAnyRole(Role.ProjectManager);
                conf.For<KundendokumentUpdateController>(kunDokCon =>
                    kunDokCon.KundendokumentEdit(
                        default(int))).RequireAnyRole(Role.ProjectManager);
                conf.For<KundendokumentUpdateController>(kunDokCon =>
                    kunDokCon.KundendokumentCreateUpdate(
                        default(int))).RequireAnyRole(Role.ProjectManager);
                conf.For<KundendokumentUpdateController>(kunDokCon =>
                    kunDokCon.KundendokumentCreateInitialVersion(
                        default(int))).RequireAnyRole(Role.ProjectManager);
                conf.For<KundendokumentUpdateController>(kunDokCon =>
                    kunDokCon.SelectForderungen(
                        default(int), default(string))).RequireAnyRole(Role.ProjectManager);
                //Die Pflichten werden im Moment nich genutzt-- > Schritt wird übersprungen //Patrick Schmed, 26.06.2017
                //conf.For<KundendokumentUpdateController>(kunDokCon =>
                //    kunDokCon.SelectPflichten(
                //       default(int), default(string), default(string))).RequireAnyRole(Role.ProjectManager);
                conf.For<KundendokumentUpdateController>(kunDokCon =>
                    kunDokCon.StandortobjektForderungen(
                        default(int), default(string), default(string))).RequireAnyRole(Role.ProjectManager);
                //Die Pflichten werden im Moment nich genutzt-- > Schritt wird übersprungen //Patrick Schmed, 26.06.2017
                //conf.For<KundendokumentUpdateController>(kunDokCon =>
                //    kunDokCon.StandortobjektPflichten(
                //        default(int), default(string), default(string))).RequireAnyRole(Role.ProjectManager);
                conf.For<KundendokumentUpdateController>(kunDokCon =>
                    kunDokCon.SelectErlassfassungen(
                        default(int), default(string))).RequireAnyRole(Role.ProjectManager);
                conf.For<KundendokumentUpdateController>(kunDokCon =>
                    kunDokCon.Create(
                        default(int), default(string))).RequireAnyRole(Role.ProjectManager);
                conf.For<KundendokumentUpdateController>(kunDokCon =>
                    kunDokCon.Update(
                        default(int), default(string))).RequireAnyRole(Role.ProjectManager);
                conf.For<KundendokumentUpdateController>(kunDokCon =>
                    kunDokCon._SelectErlassfassungenGridView(
                        default(int), default(string))).RequireAnyRole(Role.ProjectManager);
                conf.For<KundendokumentUpdateController>(kunDokCon =>
                    kunDokCon._SelectForderungenGridView(
                        default(int), default(string))).RequireAnyRole(Role.ProjectManager);
                //Die Pflichten werden im Moment nich genutzt-- > Schritt wird übersprungen //Patrick Schmed, 26.06.2017
                //conf.For<KundendokumentUpdateController>(kunDokCon =>
                //    kunDokCon._SelectPflichtenGridView(
                //        default(int), default(string))).RequireAnyRole(Role.ProjectManager);
                conf.For<KundendokumentUpdateController>(kunDokCon =>
                    kunDokCon._StandortobjektForderungenGridView(
                        default(int), default(string))).RequireAnyRole(Role.ProjectManager);
                //Die Pflichten werden im Moment nich genutzt-- > Schritt wird übersprungen //Patrick Schmed, 26.06.2017
                //conf.For<KundendokumentUpdateController>(kunDokCon =>
                //    kunDokCon._StandortobjektPflichtenGridView(
                //        default(int), default(string))).RequireAnyRole(Role.ProjectManager);

                #endregion

                #region KundeninformationController

                conf.For<KundeninformationController>().RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<KundeninformationController>(kunInfCon =>
                    kunInfCon.Index()).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<KundeninformationController>(kunInfCon =>
                    kunInfCon.KundeninformationUebersetzungenGridView()).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<KundeninformationController>(kunInfCon =>
                    kunInfCon.KundeninformationUebersetzungenGridViewBatchEditUpdate(default(MVCxGridViewBatchUpdateValues<AllgemeineKundeninformation, int>)))
                    .RequireAnyRole(Role.Admin);
                #endregion

                #region ObjekteController

                conf.For<ObjekteController>().RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<ObjekteController>(objCon =>
                    objCon.ExportTo()).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<ObjekteController>(objCon =>
                    objCon.Index()).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<ObjekteController>(objCon =>
                    objCon.ObjektUebersetzungenGridView(
                        default(int))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<ObjekteController>(objCon =>
                    objCon.ObjektUebersetzungenGridViewBatchEditingUpdate(
                        default(MVCxGridViewBatchUpdateValues<ObjektViewModel, int>), default(int))).RequireAnyRole(Role.ObjectGuru);
                conf.For<ObjekteController>(objCon =>
                    objCon.ObjekteGridView()).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<ObjekteController>(objCon =>
                    objCon.ObjekteGridViewBatchEditUpdate(
                        default(MVCxGridViewBatchUpdateValues<ObjektViewModel, int>))).RequireAnyRole(Role.ObjectGuru);

                #endregion

                #region ObjektForderungenController

                conf.For<ObjektForderungenController>().RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<ObjektForderungenController>(objForCon =>
                    objForCon.ForderungSelectionPartial(
                        default(int))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<ObjektForderungenController>(objForCon =>
                    objForCon.ObjektForderungenPartial(
                        default(int))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<ObjektForderungenController>(objForCon =>
                    objForCon.SaveForderungen()).RequireAnyRole(Role.ObjectGuru);

                #endregion

                #region ObjektkategorieController

                conf.For<ObjektkategorienController>().RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<ObjektkategorienController>(objKatCon =>
                    objKatCon.Index()).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<ObjektkategorienController>(objKatCon =>
                    objKatCon.ObjektkategorieUebersetzungenGridViewBatchEditingUpdate(
                        default(MVCxGridViewBatchUpdateValues<ObjektkategorieViewModel, int>), default(int))).RequireAnyRole(Role.ObjectGuru);
                conf.For<ObjektkategorienController>(objKatCon =>
                    objKatCon.ObjektkategorienGridView(
                        default(int?))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<ObjektkategorienController>(objKatCon =>
                    objKatCon.ObjektkategorienTreeList()).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<ObjektkategorienController>(objKatCon =>
                    objKatCon.ObjektkategorienTreeListDelete(
                        default(int))).RequireAnyRole(Role.ObjectGuru);
                conf.For<ObjektkategorienController>(objKatCon =>
                    objKatCon.ObjektkategorienTreeListMove(
                        default(int), default(int?))).RequireAnyRole(Role.ObjectGuru);
                conf.For<ObjektkategorienController>(objKatCon =>
                    objKatCon.ObjektkategrienNewForm(
                        default(ObjektkategorieViewModel))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);

                #endregion

                #region ObjektPflichtenController

                conf.For<ObjektPflichtenController>().RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<ObjektPflichtenController>(objPflCon =>
                    objPflCon.ObjektPflichtenPartial(
                        default(int))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<ObjektPflichtenController>(objPflCon =>
                    objPflCon.PflichtSelectionPartial(
                        default(int))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<ObjektPflichtenController>(objPflCon =>
                    objPflCon.SavePflichten()).RequireAnyRole(Role.ObjectGuru);

                #endregion

                #region PflichtenController

                conf.For<PflichtenController>().RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<PflichtenController>(pflCon =>
                    pflCon.Index(
                        default(int))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<PflichtenController>(pflCon =>
                    pflCon.PflichtUebersetzungenGridView(
                        default(int))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<PflichtenController>(pflCon =>
                    pflCon.PflichtenGridView(
                        default(int))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<PflichtenController>(pflCon =>
                    pflCon.PflichtenGridViewBatchEditUpdate(
                        default(MVCxGridViewBatchUpdateValues<PflichtViewModel, int>), default(int))).RequireAnyRole(Role.ProjectManager);
                conf.For<PflichtenController>(pflCon =>
                    pflCon.PflichtenObjektPartial(
                        default(int))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<PflichtenController>(pflCon =>
                    pflCon.RowSelectionPartial(
                        default(int))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<PflichtenController>(pflCon =>
                    pflCon.SaveObjects()).RequireAnyRole(Role.ObjectGuru);

                #endregion

                #region RechtsbereicheController

                conf.For<RechtsbereicheController>().RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<RechtsbereicheController>(recBerCont =>
                    recBerCont.Index()).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<RechtsbereicheController>(recBerCont =>
                    recBerCont.RechtsbereichUebersetzungenGridView(
                        default(int))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<RechtsbereicheController>(recBerCont =>
                    recBerCont.RechtsbereichUebersetzungenGridViewBatchEditUpdate(
                        default(MVCxGridViewBatchUpdateValues<RechtsbereichViewModel, int>), default(int))).RequireAnyRole(Role.ProjectManager);
                conf.For<RechtsbereicheController>(recBerCont =>
                    recBerCont.RechtsbereicheGridView()).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<RechtsbereicheController>(recBerCont =>
                    recBerCont.RechtsbereicheGridViewBatchEditUpdate(
                        default(MVCxGridViewBatchUpdateValues<RechtsbereichViewModel, int>))).RequireAnyRole(Role.ObjectGuru);

                #endregion

                #region RolesAdminController

                conf.For<RolesAdminController>().RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<RolesAdminController>(rolAdmCon =>
                    rolAdmCon.Index()).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<RolesAdminController>(rolAdmCon =>
                    rolAdmCon.RolesAdminViewPartial()).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);

                #endregion

                #region StandortObjektController

                conf.For<StandortObjektController>().RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<StandortObjektController>(staObjCon =>
                    staObjCon.Index()).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<StandortObjektController>(staObjCon =>
                    staObjCon.StandortObjektGridView(
                        default(int))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<StandortObjektController>(staObjCon =>
                    staObjCon.NewStandortObjekteGridView(default(int)))
                    .RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru);
                conf.For<StandortObjektController>(staObjCon =>
                    staObjCon.StandortObjektGridViewBatchEditUpdate(
                        default(MVCxGridViewBatchUpdateValues<StandortObjektViewModel, int>), default(int))).RequireAnyRole(Role.ProjectManager);

                #endregion

                #region UsersAdminController TODO

                conf.For<UsersAdminController>().RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru, Role.SuperUser);
                conf.For<UsersAdminController>(usrAdmCon =>
                    usrAdmCon.Index(default(bool))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru, Role.SuperUser);
                conf.For<UsersAdminController>(usrAdmCon =>
                    usrAdmCon.SendEmailConfirmation(
                        default(UserViewModel))).RequireAnyRole(Role.Admin, Role.SuperUser);
                conf.For<UsersAdminController>(usrAdmCon =>
                    usrAdmCon.UserAdminViewPartial(default(bool))).RequireAnyRole(Role.Admin, Role.ProjectManager, Role.ObjectGuru, Role.SuperUser);
                conf.For<UsersAdminController>(usrAdmCon =>
                    usrAdmCon.UserAdminViewPartialAddNew(
                        default(UserViewModel), default(bool))).RequireAnyRole(Role.Admin, Role.SuperUser);
                conf.For<UsersAdminController>(usrAdmCon =>
                    usrAdmCon.UserAdminViewPartialDelete(
                        default(string))).RequireAnyRole(Role.Admin, Role.SuperUser);
                conf.For<UsersAdminController>(usrAdmCon =>
                    usrAdmCon.UserAdminViewPartialUpdate(
                        default(UserViewModel), default(bool))).RequireAnyRole(Role.Admin, Role.SuperUser);
                conf.For<UsersAdminController>(usrAdmCon =>
                    usrAdmCon.ChangePasswordByAdminManual(default(ChangePasswordViewModel))).RequireAnyRole(Role.Admin, Role.SuperUser);
                //conf.For<UsersAdminController>(usrAdmCon =>
                //    usrAdmCon.ResetPasswordPopup(default(string))).AddPolicy(new AuthFlagPolicy());

                #endregion

                #endregion AdminArea

                //SecurityConfig for Controller/ Views within Portal Area
                #region PortalArea

                #region DashboardController

                conf.For<Areas.Portal.Controllers.DashboardController>()
                    .RequireAnyRole(Role.ProjectManager, Role.SuperUser, Role.EndUser, Role.ReadOnly, Role.Auditor, Role.Accountable, Role.Newsletter);
                conf.For<Areas.Portal.Controllers.DashboardController>(dasCon =>
                    dasCon.Index(default(int))).RequireAnyRole(Role.ProjectManager, Role.SuperUser, Role.EndUser, Role.ReadOnly, Role.Auditor, Role.Accountable, Role.Newsletter);

                #endregion

                #region IndividuelleForderungenController

                conf.For<IndividuelleForderungenController>()
                    .RequireAnyRole(Role.SuperUser, Role.EndUser, Role.ReadOnly, Role.Auditor, Role.Accountable);
                conf.For<IndividuelleForderungenController>(indForCon =>
                    indForCon.Index(
                        default(int?), default(int?))).RequireAnyRole(Role.SuperUser, Role.EndUser, Role.ReadOnly, Role.Auditor, Role.Accountable);
                conf.For<IndividuelleForderungenController>(indForCon =>
                    indForCon.IndividuelleForderungenGridView(
                        default(int?), default(int?), default(bool))).RequireAnyRole(Role.SuperUser, Role.EndUser, Role.ReadOnly, Role.Auditor, Role.Accountable);
                conf.For<IndividuelleForderungenController>(indForCon =>
                    indForCon.IndividuelleForderungenGridViewBatchEditUpdate(
                        default(MVCxGridViewBatchUpdateValues<IndividuelleForderungViewModel, int>), default(int?), default(bool), default(int)))
                    .RequireAnyRole(Role.SuperUser, Role.EndUser, Role.ReadOnly, Role.Auditor, Role.Accountable);

                #endregion

                #region KundendokumentController

                conf.For<KundendokumentController>()
                    .RequireAnyRole(Role.SuperUser, Role.EndUser, Role.ReadOnly, Role.Auditor, Role.Accountable);
                conf.For<KundendokumentController>(kunDokCon =>
                    kunDokCon.ErlassDetailGridView(
                        default(int), default(int?), default(int?), default(int)))
                    .RequireAnyRole(Role.SuperUser, Role.EndUser, Role.ReadOnly, Role.Auditor, Role.Accountable);
                conf.For<KundendokumentController>(kunDokCon =>
                    kunDokCon.ErlassfassungDetailGridView(
                        default(int), default(int?), default(int?), default(int)))
                    .RequireAnyRole(Role.SuperUser, Role.EndUser, Role.ReadOnly, Role.Auditor, Role.Accountable);
                conf.For<KundendokumentController>(kunDokCon =>
                    kunDokCon.ForderungsversionDetailGridView(
                        default(int), default(int?), default(int?), default(int)))
                    .RequireAnyRole(Role.SuperUser, Role.EndUser, Role.ReadOnly, Role.Auditor, Role.Accountable);
                conf.For<KundendokumentController>(kunDokCon =>
                    kunDokCon.Index(
                        default(int), default(int?)))
                    .RequireAnyRole(Role.SuperUser, Role.EndUser, Role.ReadOnly, Role.Auditor, Role.Accountable);
                conf.For<KundendokumentController>(kunDokCon =>
                    kunDokCon.KundendokumentArchivGridView(
                        default(int))).RequireAnyRole(Role.SuperUser, Role.EndUser, Role.ReadOnly, Role.Auditor, Role.Accountable);
                conf.For<KundendokumentController>(kunDokCon =>
                    kunDokCon.KundendokumentForderungenGridView(
                        default(int), default(int), default(bool), default(int)))
                    .RequireAnyRole(Role.SuperUser, Role.EndUser, Role.ReadOnly, Role.Auditor, Role.Accountable);
                conf.For<KundendokumentController>(kunDokCon =>
                    kunDokCon.KundendokumentForderungenGridViewBatchEditUpdate(
                        default(MVCxGridViewBatchUpdateValues<KundendokumentForderungsversionViewModel, int>), default(string[]),
                        default(int), default(int?), default(int?)))
                    .RequireAnyRole(Role.SuperUser, Role.EndUser, Role.ReadOnly, Role.Auditor, Role.Accountable);
                conf.For<KundendokumentController>(kunDokCon =>
                    kunDokCon.KundendokumentPflichtenGridView(
                        default(int?), default(int?), default(bool)))
                    .RequireAnyRole(Role.SuperUser, Role.EndUser, Role.ReadOnly, Role.Auditor, Role.Accountable);
                conf.For<KundendokumentController>(kunDokCon =>
                    kunDokCon.KundendokumentPflichtenGridViewBatchEditUpdate(
                        default(MVCxGridViewBatchUpdateValues<KundendokumentPflichtViewModel, int>), default(int?),
                        default(int?))).RequireAnyRole(Role.SuperUser, Role.EndUser, Role.ReadOnly);
                conf.For<KundendokumentController>(kunDokCon =>
                    kunDokCon.ForderungsversionAdditionalDataGridView(default(int), default(int?), default(int?)))
                    .RequireAnyRole(Role.SuperUser, Role.EndUser, Role.ReadOnly, Role.Auditor, Role.Accountable);
                conf.For<KundendokumentController>(kunDokCon =>
                    kunDokCon.ForderungsversionAdditionalDataGridViewBatchEdit(
                    default(MVCxGridViewBatchUpdateValues<KundendokumentForderungsversionViewModel, int>), default(int), default(int?), default(int?)))
                    .RequireAnyRole(Role.SuperUser, Role.EndUser, Role.ReadOnly, Role.Auditor, Role.Accountable);
                conf.For<KundendokumentController>(kunDokCon =>
                    kunDokCon.ForderungsversionCompareDetailGridView(default(int), default(int?), default(int?)))
                    .RequireAnyRole(Role.SuperUser, Role.EndUser, Role.ReadOnly, Role.Auditor, Role.Accountable);


                // master MasterKundendokumentController
                conf.For<MasterKundendokumentController>(kunDokCon =>
                   kunDokCon.Index())
                   .RequireAnyRole(Role.SuperUser, Role.EndUser, Role.ReadOnly, Role.Auditor, Role.Accountable);
                conf.For<MasterKundendokumentController>(kunDokCon =>
                    kunDokCon.KundendokumentForderungenGridView())
                    .RequireAnyRole(Role.SuperUser, Role.EndUser, Role.ReadOnly, Role.Auditor, Role.Accountable);
                conf.For<MasterKundendokumentController>(kunDokCon =>
                    kunDokCon.KundendokumentForderungenGridViewBatchEditUpdate(
                        default(MVCxGridViewBatchUpdateValues<MasterKundendokumentForderungsversionViewModel, int>), default(string[])))
                    .RequireAnyRole(Role.SuperUser, Role.EndUser, Role.ReadOnly, Role.Auditor, Role.Accountable);
                conf.For<MasterKundendokumentController>(kunDokCon =>
                    kunDokCon.ForderungsversionAdditionalDataGridView(default(int), default(int?), default(int?)))
                    .RequireAnyRole(Role.SuperUser, Role.EndUser, Role.ReadOnly, Role.Auditor, Role.Accountable);
                conf.For<MasterKundendokumentController>(kunDokCon =>
                    kunDokCon.ForderungsversionAdditionalDataGridViewBatchEdit(
                    default(MVCxGridViewBatchUpdateValues<KundendokumentForderungsversionViewModel, int>), default(int), default(int?), default(int?)))
                    .RequireAnyRole(Role.SuperUser, Role.EndUser, Role.ReadOnly, Role.Auditor, Role.Accountable);
                conf.For<MasterKundendokumentController>(kunDokCon =>
                    kunDokCon.ForderungsversionCompareDetailGridView(default(int), default(int?), default(int?)))
                    .RequireAnyRole(Role.SuperUser, Role.EndUser, Role.ReadOnly, Role.Auditor, Role.Accountable);


                #endregion

                #region StandorteController

                conf.For<StandorteController>()
                    .RequireAnyRole(Role.SuperUser, Role.EndUser, Role.ReadOnly, Role.Auditor, Role.Accountable);
                conf.For<StandorteController>(staCon =>
                    staCon.Index(
                        default(int))).RequireAnyRole(Role.SuperUser, Role.EndUser, Role.ReadOnly, Role.Auditor, Role.Accountable);
                conf.For<StandorteController>(staCon =>
                    staCon.StandorteGridView(
                        default(int))).RequireAnyRole(Role.SuperUser, Role.EndUser, Role.ReadOnly, Role.Auditor, Role.Accountable);

                #endregion

                #region KundendokumentStandortObjektController

                conf.For<KundendokumentStandortObjektController>().RequireAnyRole(Role.SuperUser, Role.EndUser, Role.ReadOnly);

                #endregion

                #endregion PortalArea

            });
            #endregion PageSecurityConf

            filters.Add(new HandleSecurityAttribute(), 0);

        }

        //todo save user roles into session and read it here from session
        private static IList<string> getRoles(string userid)
        {
            var userManager = sh.GetUserManager();
            return userManager.GetRoles(userid);
        }
    }


}
