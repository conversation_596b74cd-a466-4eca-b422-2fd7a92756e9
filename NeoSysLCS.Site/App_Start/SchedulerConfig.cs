using System;
using Hangfire;
using Hangfire.SqlServer;
using NeoSysLCS.Site.Tasks;
using Owin;
using System.Configuration;
using NeoSysLCS.Repositories;
using NeoSysLCS.DomainModel.Models;
using System.Diagnostics;

namespace NeoSysLCS.Site
{

    public partial class Startup
    {
        private readonly IUnitOfWork _unitOfWork;

        public Startup()
        {
            _unitOfWork = new UnitOfWork();
        }

        public void ConfigureScheduler(IAppBuilder app)
        {

            log.Info("Starting hangfire server");
            Debug.WriteLine("Starting hangfire server");

            GlobalConfiguration.Configuration
                .UseSqlServerStorage(
                    NeoSysLCS_Dev.GetConnectionString(),
                    new SqlServerStorageOptions { QueuePollInterval = TimeSpan.FromSeconds(1) });

            BackgroundJobServerOptions options = new BackgroundJobServerOptions();
            options.Activator = new JobActivator();

            app.UseHangfireDashboard("/hangfire", new DashboardOptions
            {
                Authorization = new[] { new HangfireDashboardFilter() }
            });
            app.UseHangfireServer();

            if (isProdEnv())
            {
                RecurringJob.AddOrUpdate<NewsletterTask>("newsletter", x => x.SendNewsletters(), "0 1 1 * *"); // At 01:00 on day-of-month 1
                log.Info("Hangfire(AddOrUpdate): newsletter");
                RecurringJob.AddOrUpdate<CortecTask>("cortec", x => x.SyncCustomers(), "0 22 * * 1-5"); // At 22:00 on every day-of-week from Monday through Friday
                log.Info("Hangfire(AddOrUpdate): cortec");
                RecurringJob.AddOrUpdate<CortecTask>("cortecTasks", x => x.CortecTaskCreation(), "*/3 * * * *"); // At every 3nd minute.
                log.Info("Hangfire(AddOrUpdate): cortecTasks");
                RecurringJob.AddOrUpdate<MassnahmeTask>("massnahme", x => x.SendReminderMail(), "0 5 * * *"); // At 05:00 on every day-of-week including Saturday and Sunday
                log.Info("Hangfire(AddOrUpdate): massnahme");
            }

            RecurringJob.AddOrUpdate<CustomerStatusTask>("customerStatus", x => x.HandleCustomerStatus(), "*/5 * * * *");
            log.Info("Hangfire(AddOrUpdate): customerStatus");

            RecurringJob.AddOrUpdate<EvaluationTask>("evaluation", x => x.Evaluate(), "0 19 * * 0"); // At 19:00 on Sunday.
            log.Info("Hangfire(AddOrUpdate): evaluation");

            RecurringJob.AddOrUpdate<ErlassImportTask>("erlassImport", x => x.ImportDocuments(), "0 20 * * 1-5"); // At 22:00 on every day-of-week from Monday through Friday
            log.Info("Hangfire(AddOrUpdate): erlassImport");

            RecurringJob.AddOrUpdate<SuvaChecklistTask>("suva", x => x.LoadChecklists(), "0 23 * * 1-5"); // At 23:00 on every day-of-week from Monday through Friday
            log.Info("Hangfire(AddOrUpdate): suva");

            RecurringJob.AddOrUpdate<UserPasswordResetTask>("userPasswordReset", x => x.SendPasswordResetEmails(), Cron.Never()); // MANUAL Trigger
            log.Info("Hangfire(AddOrUpdate): userPasswordReset");

            this.dbInitializations();
        }

        private void dbInitializations()
        {

            if (this._unitOfWork.KundeSummaryRepository.IsEmpty())
            {
                RecurringJob.Trigger("summarize");
                log.Info("Hangfire(Trigger): summarize");
            }
        }

        private bool isProdEnv()
        {
            return ConfigurationManager.AppSettings["Environment"].Equals("Prod");
        }

    }
}