using System;
using System.Configuration;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Text.RegularExpressions;
using System.Xml.Linq;
using log4net;
using MigraDoc.DocumentObjectModel;
using MigraDoc.DocumentObjectModel.Shapes;
using MigraDoc.DocumentObjectModel.Tables;
using MigraDoc.Rendering;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.Helper;
using NeoSysLCS.Repositories.ViewModels;
using NeoSysLCS.Site.Helpers;
using PdfSharp.Pdf;
using Border = MigraDoc.DocumentObjectModel.Border;

namespace NeoSysLCS.Site.Services.Newsletter
{
    /// <summary>
    /// Implementation of IPdfGenerationService that generates newsletter PDFs using MigraDoc
    /// </summary>
    public class NewsletterPdfGenerationService : IPdfGenerationService
    {
        private static readonly ILog log = LogManager.GetLogger(typeof(NewsletterPdfGenerationService));
        
        private readonly IUnitOfWork _unitOfWork;
        private System.Resources.ResourceManager resourceManager;
        private System.Globalization.CultureInfo languageInfo;

        /// <summary>
        /// Constructor with dependency injection
        /// </summary>
        /// <param name="unitOfWork">Unit of work for data access</param>
        public NewsletterPdfGenerationService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            resourceManager = new global::System.Resources.ResourceManager("NeoSysLCS.Resources.Properties.Resources", typeof(NeoSysLCS.Resources.Properties.Resources).Assembly);
        }

        /// <summary>
        /// Generates a newsletter PDF for a user with the provided content
        /// </summary>
        public Stream GenerateNewsletterPdf(
            ApplicationUser user,
            IQueryable<KommentarNewsletterViewModel> kommentare,
            IQueryable<GesetzaenderungViewModel> gesetzaenderungen,
            IQueryable<ConsultationViewModel> consultations,
            ApplicationUserNewsletterHistory lastNewsletter = null)
        {
            if (user?.Kunde == null)
            {
                return null;
            }

            int spracheID = user.SpracheID.Value;

            // Create a MigraDoc document
            Document document = CreateDocument(kommentare, gesetzaenderungen, consultations, user, spracheID);
            document.UseCmykColor = true;

            // Unicode encoding and font program embedding settings
            const bool unicode = false;
            const PdfFontEmbedding embedding = PdfFontEmbedding.Always;

            // Create a renderer for the MigraDoc document
            PdfDocumentRenderer pdfRenderer = new PdfDocumentRenderer(unicode, embedding);

            // Associate the MigraDoc document with a renderer
            pdfRenderer.Document = document;

            // Layout and render document to PDF
            pdfRenderer.RenderDocument();
            
            // Save the document to memory stream
            MemoryStream stream = new MemoryStream();
            pdfRenderer.PdfDocument.Save(stream, false);

            return stream;
        }

        /// <summary>
        /// Creates the MigraDoc document with all newsletter content
        /// </summary>
        private Document CreateDocument(
            IQueryable<KommentarNewsletterViewModel> kommentare,
            IQueryable<GesetzaenderungViewModel> gesetzaenderungen,
            IQueryable<ConsultationViewModel> consultations,
            ApplicationUser user,
            int spracheID)
        {
            var document = new Document();

            // Set document info based on language
            SetDocumentInfo(document, spracheID);

            DefineStyles(document);
            DefineCover(document);
            DefineContentSection(document);

            // Create content sections
            CreateKommentareParagraph(document, kommentare, user);
            CreateGesetzeAnderungParagraph(document, gesetzaenderungen, user);
            CreateConsultationParagraph(document, consultations, spracheID);

            return document;
        }

        /// <summary>
        /// Sets document information based on language
        /// </summary>
        private void SetDocumentInfo(Document document, int spracheID)
        {
            string monthName;
            string yearString = DateTime.Now.ToString("yyyy");

            switch (spracheID)
            {
                case 1: // German
                    monthName = new CultureInfo("de-DE", false).DateTimeFormat.GetMonthName(DateTime.Now.Month);
                    languageInfo = new CultureInfo("de");
                    break;
                case 2: // French
                    monthName = new CultureInfo("fr-FR", false).DateTimeFormat.GetMonthName(DateTime.Now.Month);
                    languageInfo = new CultureInfo("fr");
                    break;
                case 3: // Italian
                    monthName = new CultureInfo("it-IT", false).DateTimeFormat.GetMonthName(DateTime.Now.Month);
                    languageInfo = new CultureInfo("it");
                    break;
                case 4: // English
                    monthName = new CultureInfo("en-GB", false).DateTimeFormat.GetMonthName(DateTime.Now.Month);
                    languageInfo = new CultureInfo("en");
                    break;
                default:
                    monthName = new CultureInfo("de-DE", false).DateTimeFormat.GetMonthName(DateTime.Now.Month);
                    languageInfo = new CultureInfo("de");
                    break;
            }

            document.Info.Title = $"Newsletter {monthName} {yearString}";
            document.Info.Subject = $"Newsletter for {monthName} {yearString}";
            document.Info.Author = "NeoSys";
        }

        /// <summary>
        /// Defines document styles
        /// </summary>
        private void DefineStyles(Document document)
        {
            // Get the predefined style Normal
            Style style = document.Styles["Normal"];
            style.Font.Name = "Verdana";

            // Heading1 style
            style = document.Styles["Heading1"];
            style.Font.Name = "Tahoma";
            style.Font.Size = 14;
            style.Font.Bold = true;
            style.ParagraphFormat.PageBreakBefore = true;
            style.ParagraphFormat.SpaceBefore = "2cm";
            style.ParagraphFormat.SpaceAfter = 6;

            // Heading2 style
            style = document.Styles["Heading2"];
            style.Font.Size = 12;
            style.Font.Bold = true;
            style.ParagraphFormat.PageBreakBefore = false;
            style.ParagraphFormat.SpaceBefore = 6;
            style.ParagraphFormat.SpaceAfter = 6;

            // Heading3 style
            style = document.Styles["Heading3"];
            style.Font.Size = 10;
            style.Font.Bold = true;
            style.Font.Italic = true;
            style.ParagraphFormat.SpaceBefore = 6;
            style.ParagraphFormat.SpaceAfter = 3;

            // Header and Footer styles
            style = document.Styles[StyleNames.Header];
            style.ParagraphFormat.AddTabStop("16cm", TabAlignment.Right);

            style = document.Styles[StyleNames.Footer];
            style.ParagraphFormat.AddTabStop("8cm", TabAlignment.Center);

            // Create TextBox style
            style = document.Styles.AddStyle("TextBox", "Normal");
            style.ParagraphFormat.SpaceAfter = "1cm";
            style.ParagraphFormat.Borders.Distance = "3pt";
            style.ParagraphFormat.Shading.Color = Color.Parse("#F3F5F7");
        }

        /// <summary>
        /// Gets the path to a resource file
        /// </summary>
        private string GetResourcePath(string resource)
        {
            var outPutDirectory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().CodeBase);
            var logoImage = Path.Combine(outPutDirectory, "Views\\Emails\\" + resource);
            return new Uri(logoImage).LocalPath;
        }

        /// <summary>
        /// Downloads an image from a URL
        /// </summary>
        private byte[] DownloadImage(string url)
        {
            using (var webClient = new WebClient())
            {
                return webClient.DownloadData(url);
            }
        }

        /// <summary>
        /// Converts byte array to MigraDoc filename format
        /// </summary>
        private static string MigraDocFilenameFromByteArray(byte[] image)
        {
            return "base64:" + Convert.ToBase64String(image);
        }

        /// <summary>
        /// Defines the cover page
        /// </summary>
        private void DefineCover(Document document)
        {
            Section section = document.AddSection();

            // Create header
            var header = section.Headers.Primary;
            var image = header.AddImage(GetResourcePath("newsletter-logo.jpg"));
            image.Height = "2cm";
            image.LockAspectRatio = true;
            image.RelativeVertical = RelativeVertical.Line;
            image.RelativeHorizontal = RelativeHorizontal.Margin;
            image.Top = ShapePosition.Top;
            image.Left = ShapePosition.Left;
            image.WrapFormat.Style = WrapStyle.Through;

            var headerTitleParagraph = header.AddParagraph();
            headerTitleParagraph.Format.SpaceBefore = "1cm";
            headerTitleParagraph.Format.LeftIndent = "3cm";
            var headerTitle = headerTitleParagraph.AddFormattedText(resourceManager.GetString("Newsletter_Header", languageInfo));
            headerTitle.Font.Size = 10;

            var line = header.AddParagraph();
            line.Format.LeftIndent = "3cm";
            var hrBorder = new Border
            {
                Width = "2pt",
                Color = Colors.Red
            };
            line.Format.Borders.Bottom = hrBorder;
            line.Format.LineSpacing = 0;

            // Create footer
            Paragraph paragraph = section.Footers.Primary.AddParagraph();
            AddLocalizedMonthYear(paragraph);
            paragraph.Format.Font.Size = 9;
            paragraph.Format.Alignment = ParagraphAlignment.Center;

            // Cover title and subtitle
            paragraph = section.AddParagraph();
            paragraph.Format.SpaceBefore = "3cm";
            var coverTitle = paragraph.AddFormattedText(resourceManager.GetString("Newsletter_Title", languageInfo));
            coverTitle.Font.Size = 14;

            paragraph = section.AddParagraph();
            AddLocalizedMonthYear(paragraph);
            paragraph.Format.SpaceAfter = "5cm";

            // Cover image
            paragraph = section.AddParagraph();
            paragraph.Format.LeftIndent = Unit.Zero;
            paragraph.Format.RightIndent = Unit.Zero;
            paragraph.Format.Alignment = ParagraphAlignment.Center;

            image = paragraph.AddImage(GetResourcePath("newsletter-cover-image.jpg"));
            image.Width = "22cm";
        }

        /// <summary>
        /// Adds localized month and year to a paragraph
        /// </summary>
        private void AddLocalizedMonthYear(Paragraph paragraph)
        {
            string month;
            switch (languageInfo.Name)
            {
                case "de":
                    month = new CultureInfo("de-DE", false).DateTimeFormat.GetMonthName(DateTime.Now.Month);
                    break;
                case "fr":
                    month = new CultureInfo("fr-FR", false).DateTimeFormat.GetMonthName(DateTime.Now.Month);
                    break;
                case "it":
                    month = new CultureInfo("it-IT", false).DateTimeFormat.GetMonthName(DateTime.Now.Month);
                    break;
                case "en":
                    month = new CultureInfo("en-GB", false).DateTimeFormat.GetMonthName(DateTime.Now.Month);
                    break;
                default:
                    month = new CultureInfo("de-DE", false).DateTimeFormat.GetMonthName(DateTime.Now.Month);
                    break;
            }

            string monthFirstCharacter = char.ToUpper(month[0]).ToString();
            string monthRest = month.Substring(1);
            var formattedText = paragraph.AddFormattedText(monthFirstCharacter + monthRest + " " + DateTime.Now.ToString("yyyy"));
            formattedText.Font.Size = 10;
        }

        /// <summary>
        /// Defines the content section
        /// </summary>
        private void DefineContentSection(Document document)
        {
            Section section = document.AddSection();
            section.PageSetup.StartingNumber = 1;

            // Create header
            var header = section.Headers.Primary;
            var image = header.AddImage(GetResourcePath("newsletter-logo.jpg"));
            image.Height = "1cm";
            image.LockAspectRatio = true;
            image.RelativeVertical = RelativeVertical.Line;
            image.RelativeHorizontal = RelativeHorizontal.Margin;
            image.Top = ShapePosition.Top;
            image.Left = ShapePosition.Left;
            image.WrapFormat.Style = WrapStyle.Through;

            var headerTitleParagraph = header.AddParagraph();
            headerTitleParagraph.Format.LeftIndent = "3cm";
            var headerTitle = headerTitleParagraph.AddFormattedText(resourceManager.GetString("Newsletter_Header", languageInfo));
            headerTitle.Font.Size = 10;

            var line = header.AddParagraph();
            line.Format.LeftIndent = "3cm";
            var hrBorder = new Border
            {
                Width = "2pt",
                Color = Colors.Red
            };
            line.Format.Borders.Bottom = hrBorder;
            line.Format.LineSpacing = 0;

            // Create footer with page number and date
            var paragraphPageNumber = new Paragraph();
            paragraphPageNumber.AddTab();
            paragraphPageNumber.AddPageField();

            var paragraphDate = new Paragraph();
            paragraphDate.AddTab();
            AddLocalizedMonthYear(paragraphDate);

            section.Footers.Primary.Add(paragraphPageNumber);
            section.Footers.Primary.Add(paragraphDate);
        }

        /// <summary>
        /// Creates the Kommentare (comments) paragraph section
        /// </summary>
        private void CreateKommentareParagraph(Document document, IQueryable<KommentarNewsletterViewModel> kommentare, ApplicationUser user)
        {
            var section = document.LastSection;
            Paragraph paragraph = section.AddParagraph(resourceManager.GetString("Newsletter_Erlassaenderungen", languageInfo), "Heading1");
            paragraph.AddBookmark(resourceManager.GetString("Newsletter_Erlassaenderungen", languageInfo));

            foreach (var k in kommentare)
            {
                CreateKommentarTable(section, k);
                section.AddParagraph(); // Add space between entries
            }
        }

        /// <summary>
        /// Creates a table for a single Kommentar entry
        /// </summary>
        private void CreateKommentarTable(Section section, KommentarNewsletterViewModel k)
        {
            var table = section.AddTable();
            table.Borders.Visible = false;
            table.Format.KeepTogether = true;
            table.Format.Shading.Color = Color.Parse("#F3F5F7");
            table.Shading.Color = Color.Parse("#F3F5F7");
            table.TopPadding = 5;
            table.BottomPadding = 5;

            // Define columns
            var imgCol = table.AddColumn(Unit.FromCentimeter(3));
            imgCol.Format.Alignment = ParagraphAlignment.Left;
            var textCol = table.AddColumn(Unit.FromCentimeter(14));
            textCol.Format.Alignment = ParagraphAlignment.Justify;
            table.Rows.HeightRule = RowHeightRule.Auto;

            // Add title and subtitle row
            var row = table.AddRow();
            var titleParagraph = row.Cells[0].AddParagraph();
            row.Cells[0].MergeRight = 1;

            AddErlassLink(titleParagraph, k.ErlassQuelle, k.ErlassNummer, k.ErlassName);
            titleParagraph.AddLineBreak();
            AddKommentarLink(titleParagraph, k.KommentarQuelle, k.Beschluss, k.Inkrafttretung);

            // Add image and text row
            row = table.AddRow();
            row.VerticalAlignment = VerticalAlignment.Top;
            AddKommentarImage(row.Cells[0], k.KommentarImage);
            AddKommentarText(row.Cells[1], k.KommentarShortText, k.NeosysKommentar);
        }

        /// <summary>
        /// Adds Erlass (law) link to paragraph
        /// </summary>
        private void AddErlassLink(Paragraph paragraph, string erlassQuelle, string erlassNummer, string erlassName)
        {
            if (!string.IsNullOrEmpty(erlassQuelle) && IsValidUrl(erlassQuelle))
            {
                var normalizedUrl = NormalizeUrl(erlassQuelle);
                var hyperlink = paragraph.AddHyperlink(normalizedUrl, HyperlinkType.Web);

                var linkText = string.IsNullOrEmpty(erlassNummer) ? erlassName : erlassNummer;
                var hyperlinkText = hyperlink.AddFormattedText(linkText);
                hyperlinkText.Underline = Underline.Single;
                hyperlinkText.Font.Color = Colors.Blue;

                if (!string.IsNullOrEmpty(erlassNummer))
                {
                    paragraph.AddText(" " + erlassName);
                }
            }
            else
            {
                paragraph.AddText(erlassNummer + " " + erlassName);
            }
        }

        /// <summary>
        /// Adds Kommentar link to paragraph
        /// </summary>
        private void AddKommentarLink(Paragraph paragraph, string kommentarQuelle, DateTime beschluss, DateTime inkrafttretung)
        {
            if (!string.IsNullOrEmpty(kommentarQuelle) && IsValidUrl(kommentarQuelle))
            {
                var normalizedUrl = NormalizeUrl(kommentarQuelle);
                var hyperlink = paragraph.AddHyperlink(normalizedUrl, HyperlinkType.Web);
                var hyperlinkText = hyperlink.AddFormattedText(
                    resourceManager.GetString("Entitaet_Erlassfassung_Beschluss", languageInfo) + " " + beschluss.ToString("dd.MM.yyyy"));
                hyperlinkText.Underline = Underline.Single;
                hyperlinkText.Font.Color = Colors.Blue;
                hyperlinkText.Font.Size = 8;

                var subtitleText = paragraph.AddFormattedText(
                    ", " + resourceManager.GetString("Entitaet_Forderungsversion_Inkrafttretung", languageInfo) + ": " + inkrafttretung.ToString("dd.MM.yyyy"));
                subtitleText.Font.Size = 8;
            }
            else
            {
                var subtitleText = paragraph.AddFormattedText(
                    resourceManager.GetString("Entitaet_Erlassfassung_Beschluss", languageInfo) + " " + beschluss.ToString("dd.MM.yyyy") +
                    ", " + resourceManager.GetString("Entitaet_Forderungsversion_Inkrafttretung", languageInfo) + ": " + inkrafttretung.ToString("dd.MM.yyyy"));
                subtitleText.Font.Size = 8;
            }
        }

        /// <summary>
        /// Adds image to the Kommentar table cell
        /// </summary>
        private void AddKommentarImage(Cell cell, string kommentarImage)
        {
            try
            {
                if (!string.IsNullOrEmpty(kommentarImage))
                {
                    string env = ConfigurationManager.AppSettings["Environment"];
                    if (env == "SxTest" || env == "Prod")
                    {
                        try
                        {
                            // Load image from local directory
                            //TODO remove hardcoded urls and read this from config
                            var imageName = kommentarImage
                                .Replace("http://198.168.111.79/Upload/Kommentar/", "")
                                .Replace("https://lexplus.ch/Upload/Kommentar/", "");
                            string localPath = "C:\\inetpub\\neosys\\Upload\\Kommentar\\" + imageName;
                            
                            // Check if file exists before trying to load it
                            if (System.IO.File.Exists(localPath))
                            {
                                cell.AddImage(localPath);
                            }
                            else
                            {
                                // File not found, log error and use default image
                                log.Error($"Kommentar image file not found at path: {localPath}. Using default newsletter logo instead.");
                                cell.AddImage(GetResourcePath("newsletter-logo.jpg"));
                            }
                        }
                        catch (Exception ex)
                        {
                            // Error loading local image, log error and use default image
                            log.Error($"Error loading kommentar image from local path: {ex.Message}. Using default newsletter logo instead.");
                            cell.AddImage(GetResourcePath("newsletter-logo.jpg"));
                        }
                    }
                    else
                    {
                        try
                        {
                            var imageBytes = DownloadImage(kommentarImage);
                            cell.AddImage(MigraDocFilenameFromByteArray(imageBytes));
                        }
                        catch (Exception ex)
                        {
                            // Error downloading or processing image, log error and use default image
                            log.Error($"Error downloading or processing kommentar image from URL {kommentarImage}: {ex.Message}. Using default newsletter logo instead.");
                            cell.AddImage(GetResourcePath("newsletter-logo.jpg"));
                        }
                    }
                }
                else
                {
                    cell.AddImage(GetResourcePath("newsletter-logo.jpg"));
                }
            }
            catch (Exception ex)
            {
                // Catch-all for any unexpected errors, log error and use default image
                log.Error($"Unexpected error handling kommentar image: {ex.Message}. Using default newsletter logo.");
                cell.AddImage(GetResourcePath("newsletter-logo.jpg"));
            }

            // Configure the last added image (which will be our kommentar image)
            if (cell.Elements.Count > 0)
            {
                // Get the last element from the collection
                var lastElement = cell.Elements[cell.Elements.Count - 1];
                var image = lastElement as Image;
                if (image != null)
                {
                    image.Height = "2.5cm";
                    image.LockAspectRatio = true;
                    image.RelativeVertical = RelativeVertical.Line;
                    image.RelativeHorizontal = RelativeHorizontal.Margin;
                    image.Top = ShapePosition.Top;
                    image.Left = ShapePosition.Center;
                    image.WrapFormat.Style = WrapStyle.Through;
                }
            }
        }

        /// <summary>
        /// Adds text content to the Kommentar table cell
        /// </summary>
        private void AddKommentarText(Cell cell, string shortText, string neosysKommentar)
        {
            var textParagraph = cell.AddParagraph();
            textParagraph.Format.Alignment = ParagraphAlignment.Left;
            NewsletterTextHelper.CreateFormattedTextFromHtml(textParagraph, shortText);

            // Add "read more" link if available
            if (!string.IsNullOrEmpty(neosysKommentar) && IsValidUrl(neosysKommentar))
            {
                var normalizedUrl = NormalizeUrl(neosysKommentar);
                var hyperlink = textParagraph.AddHyperlink(normalizedUrl, HyperlinkType.Web);
                var hyperlinkText = hyperlink.AddFormattedText(" " + resourceManager.GetString("Newsletter_Weiterlesen", languageInfo));
                hyperlinkText.Font.Size = 9;
                hyperlinkText.Bold = true;
            }
        }

        /// <summary>
        /// Checks if a string is a valid URL
        /// </summary>
        private bool IsValidUrl(string url)
        {
            return !string.IsNullOrEmpty(url) &&
                   (url.StartsWith("https://") || url.StartsWith("http://") || url.StartsWith("www."));
        }

        /// <summary>
        /// Normalizes URL format
        /// </summary>
        private string NormalizeUrl(string url)
        {
            if (url.StartsWith("http://"))
            {
                return url.Replace("http://", "https://");
            }
            if (url.StartsWith("www."))
            {
                return url.Replace("www.", "https://");
            }
            return url;
        }

        /// <summary>
        /// Creates the Gesetzaenderung (law changes) paragraph section
        /// </summary>
        private void CreateGesetzeAnderungParagraph(Document document, IQueryable<GesetzaenderungViewModel> gesetzaenderungen, ApplicationUser user)
        {
            var section = document.LastSection;
            Paragraph paragraph = section.AddParagraph(resourceManager.GetString("Newsletter_Additional_Erlassaenderungen", languageInfo), "Heading1");
            paragraph.AddBookmark(resourceManager.GetString("Newsletter_Additional_Erlassaenderungen", languageInfo));

            var grouped = gesetzaenderungen
                .OrderBy(g => g.Herausgeber)
                .GroupBy(g => g.Herausgeber).ToList();

            foreach (var group in grouped)
            {
                var herausgeberParagraph = section.AddParagraph(group.Key, "Heading3");
                herausgeberParagraph.AddBookmark(group.Key);

                foreach (var g in group)
                {
                    CreateGesetzaenderungTable(section, g);
                    section.AddParagraph(); // Add space between entries
                }
            }
        }

        /// <summary>
        /// Creates a table for a single Gesetzaenderung entry
        /// </summary>
        private void CreateGesetzaenderungTable(Section section, GesetzaenderungViewModel g)
        {
            var table = section.AddTable();
            table.Borders.Visible = false;
            table.Format.KeepTogether = true;
            table.Format.Shading.Color = Color.Parse("#F3F5F7");
            table.Shading.Color = Color.Parse("#F3F5F7");
            table.TopPadding = 5;
            table.BottomPadding = 5;

            // Define columns
            var imgCol = table.AddColumn(Unit.FromCentimeter(3));
            imgCol.Format.Alignment = ParagraphAlignment.Left;
            var textCol = table.AddColumn(Unit.FromCentimeter(14));
            textCol.Format.Alignment = ParagraphAlignment.Justify;
            table.Rows.HeightRule = RowHeightRule.Auto;

            // Add title and subtitle row
            var row = table.AddRow();
            var titleParagraph = row.Cells[0].AddParagraph();
            row.Cells[0].MergeRight = 1;

            AddErlassLink(titleParagraph, g.ErlassQuelle, g.ErlassNummer, g.ErlassName);
            titleParagraph.AddLineBreak();
            AddKommentarLink(titleParagraph, g.KommentarQuelle, g.Beschluss, g.Inkrafttretung);
        }

        /// <summary>
        /// Creates the Consultation paragraph section
        /// </summary>
        private void CreateConsultationParagraph(Document document, IQueryable<ConsultationViewModel> consultations, int spracheID)
        {
            var section = document.LastSection;
            Paragraph paragraph = section.AddParagraph(resourceManager.GetString("View_Dashboard_Tab_Vernehmlassungen", languageInfo), "Heading1");
            paragraph.AddBookmark(resourceManager.GetString("View_Dashboard_Tab_Vernehmlassungen", languageInfo));

            foreach (var c in consultations)
            {
                CreateConsultationTable(section, c, spracheID);
                section.AddParagraph(); // Add space between entries
            }
        }

        /// <summary>
        /// Creates a table for a single Consultation entry
        /// </summary>
        private void CreateConsultationTable(Section section, ConsultationViewModel c, int spracheID)
        {
            var table = section.AddTable();
            table.Borders.Visible = false;
            table.Format.KeepTogether = true;
            table.Format.Shading.Color = Color.Parse("#F3F5F7");
            table.Shading.Color = Color.Parse("#F3F5F7");
            table.TopPadding = 1;
            table.BottomPadding = 1;

            var firstCol = table.AddColumn(Unit.FromCentimeter(3));
            firstCol.Format.Alignment = ParagraphAlignment.Left;
            var secondCol = table.AddColumn(Unit.FromCentimeter(14));
            secondCol.Format.Alignment = ParagraphAlignment.Justify;
            table.Rows.HeightRule = RowHeightRule.Auto;

            // Title row
            Row row = table.AddRow();
            Cell cell = row.Cells[0];
            var titleParagraph = cell.AddParagraph(c.Title);
            titleParagraph.Format.Font.Bold = true;
            cell.MergeRight = 1;

            // Erlasse row
            AddConsultationErlasseRow(table, c, spracheID);

            // Status row
            AddConsultationStatusRow(table, c);

            // Date rows
            AddConsultationDateRow(table, resourceManager.GetString("Entitaet_Consultation_EntryDate", languageInfo),
                c.OpenedDate?.ToString("dd.MM.yyyy") ?? "");
            AddConsultationDateRow(table, resourceManager.GetString("Entitaet_Consultation_Deadline", languageInfo),
                c.Deadline?.ToString("dd.MM.yyyy") ?? "");
            AddConsultationDateRow(table, resourceManager.GetString("Entitaet_Consultation_CompletedDate", languageInfo),
                c.CompletedDate?.ToString("dd.MM.yyyy") ?? "");

            // Source link row
            AddConsultationSourceRow(table, c);
        }

        /// <summary>
        /// Adds Erlasse row to consultation table
        /// </summary>
        private void AddConsultationErlasseRow(Table table, ConsultationViewModel c, int spracheID)
        {
            var row = table.AddRow();
            var cell = row.Cells[0];
            cell.AddParagraph(resourceManager.GetString("Entitaet_Erlass_Plural", languageInfo));
            cell = row.Cells[1];

            var listErlasse = c.ErlasseCollection.ToList();
            foreach (var erlass in c.ErlasseCollection)
            {
                XDocument uebersetzung = XDocument.Parse(erlass.Uebersetzung);
                var title = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Titel", spracheID);
                var quelle = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Quelle", spracheID);
                var srNummer = erlass.SrNummer;

                var newParagraph = cell.AddParagraph();
                newParagraph.Format.Alignment = ParagraphAlignment.Left;

                if (!string.IsNullOrEmpty(quelle) && IsValidUrl(quelle))
                {
                    var normalizedUrl = NormalizeUrl(quelle);
                    var hyperlink = newParagraph.AddHyperlink(normalizedUrl, HyperlinkType.Web);

                    var linkText = string.IsNullOrEmpty(srNummer) ? title : $"{srNummer}; {title}";
                    var hyperlinkText = hyperlink.AddFormattedText(linkText);
                    hyperlinkText.Underline = Underline.Single;
                    hyperlinkText.Font.Color = Colors.Blue;

                    if (!string.IsNullOrEmpty(srNummer))
                    {
                        newParagraph.AddText(" " + title);
                    }
                }
                else
                {
                    var text = string.IsNullOrEmpty(srNummer) ? title : $"{srNummer}; {title}";
                    newParagraph.AddFormattedText(text);
                }

                if (listErlasse.Last().ErlassID != erlass.ErlassID)
                {
                    newParagraph.AddLineBreak();
                }
            }
        }

        /// <summary>
        /// Adds status row to consultation table
        /// </summary>
        private void AddConsultationStatusRow(Table table, ConsultationViewModel c)
        {
            var row = table.AddRow();
            var cell = row.Cells[0];
            cell.AddParagraph(resourceManager.GetString("Entitaet_Consultation_Phase", languageInfo));
            cell = row.Cells[1];

            string statusText = "";
            switch (c.Status)
            {
                case ConsultationStatus.Completed:
                    statusText = resourceManager.GetString("Enum_ConsultationStatus_Completed", languageInfo);
                    break;
                case ConsultationStatus.Planned:
                    statusText = resourceManager.GetString("Enum_ConsultationStatus_Planned", languageInfo);
                    break;
                case ConsultationStatus.Ongoing:
                    statusText = resourceManager.GetString("Enum_ConsultationStatus_Ongoing", languageInfo);
                    break;
            }
            cell.AddParagraph(statusText);
        }

        /// <summary>
        /// Adds a date row to consultation table
        /// </summary>
        private void AddConsultationDateRow(Table table, string label, string dateValue)
        {
            var row = table.AddRow();
            var cell = row.Cells[0];
            cell.AddParagraph(label);
            cell = row.Cells[1];
            cell.AddParagraph(dateValue);
        }

        /// <summary>
        /// Adds source link row to consultation table
        /// </summary>
        private void AddConsultationSourceRow(Table table, ConsultationViewModel c)
        {
            var row = table.AddRow();
            var cell = row.Cells[0];
            cell.MergeRight = 1;

            if (!string.IsNullOrEmpty(c.Quelle) && IsValidUrl(c.Quelle))
            {
                var normalizedUrl = NormalizeUrl(c.Quelle);
                var hyperlink = cell.AddParagraph().AddHyperlink(normalizedUrl, HyperlinkType.Web);
                var hyperlinkText = hyperlink.AddFormattedText(" " + resourceManager.GetString("Newsletter_Weiterlesen", languageInfo));
                hyperlinkText.Font.Bold = true;
            }
        }

        /// <summary>
        /// Strips HTML tags from input string
        /// </summary>
        private static string StripHTML(string input)
        {
            return Regex.Replace(input, "<.*?>", String.Empty);
        }
    }
}
