using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;
using log4net;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.Helper;
using NeoSysLCS.Repositories.ViewModels;
using NeoSysLCS.Site.Tasks;

namespace NeoSysLCS.Site.Services.Newsletter
{
    /// <summary>
    /// Implementation of newsletter management service for user selection and batch sending
    /// </summary>
    public class NewsletterManagementService : INewsletterManagementService
    {
        private static readonly ILog log = LogManager.GetLogger(typeof(NewsletterManagementService));
        private static readonly ConcurrentDictionary<string, NewsletterSendStatusViewModel> _sendOperations = 
            new ConcurrentDictionary<string, NewsletterSendStatusViewModel>();

        private readonly IUnitOfWork _unitOfWork;
        private readonly INewsletterEmailService _emailService;
        private readonly IPdfGenerationService _pdfGenerationService;
        private readonly IFileUploadService _fileUploadService;

        /// <summary>
        /// Constructor with dependency injection
        /// </summary>
        public NewsletterManagementService(
            IUnitOfWork unitOfWork,
            INewsletterEmailService emailService,
            IPdfGenerationService pdfGenerationService,
            IFileUploadService fileUploadService)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _emailService = emailService ?? throw new ArgumentNullException(nameof(emailService));
            _pdfGenerationService = pdfGenerationService ?? throw new ArgumentNullException(nameof(pdfGenerationService));
            _fileUploadService = fileUploadService ?? throw new ArgumentNullException(nameof(fileUploadService));
        }

        /// <summary>
        /// Searches for users based on the provided criteria for newsletter selection
        /// </summary>
        public IQueryable<NewsletterUserSelectionViewModel> SearchUsers(NewsletterUserSearchViewModel criteria)
        {
            try
            {
                return _unitOfWork.UserRepository.GetUsersForNewsletterSelection(criteria);
            }
            catch (Exception ex)
            {
                log.Error($"Error searching users for newsletter selection: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// Sends newsletters to the specified users asynchronously
        /// </summary>
        public async Task<NewsletterSendStatusViewModel> SendNewslettersToUsersAsync(string[] userIds, bool forceResend = false,
            bool isTestMode = false, string testRecipientEmail = null, DateTime? testLastNewsletterDate = null)
        {
            var operationId = Guid.NewGuid().ToString();
            var status = new NewsletterSendStatusViewModel
            {
                TotalUsers = userIds.Length,
                StartTime = DateTime.Now,
                IsCompleted = false
            };

            _sendOperations[operationId] = status;

            try
            {
                log.Info($"Starting newsletter batch send operation {operationId} for {userIds.Length} users" +
                    (isTestMode ? " in TEST MODE" : ""));

                // Process users in batches to avoid overwhelming the system
                const int batchSize = 5;
                var batches = userIds.Select((id, index) => new { id, index })
                                   .GroupBy(x => x.index / batchSize)
                                   .Select(g => g.Select(x => x.id).ToArray());

                await Task.Run(() =>
                {
                    // Create test parameters container
                    var testParams = isTestMode ? new NewsletterTestModeParameters
                    {
                        IsTestMode = isTestMode,
                        TestRecipientEmail = testRecipientEmail,
                        TestLastNewsletterDate = testLastNewsletterDate
                    } : null;

                    foreach (var batch in batches)
                    {
                        ProcessUserBatch(batch, status, forceResend, testParams);
                    }

                    status.IsCompleted = true;
                    status.EndTime = DateTime.Now;
                    log.Info($"Completed newsletter batch send operation {operationId}. " +
                        $"Sent: {status.Sent}, Failed: {status.Failed}, Skipped: {status.Skipped}" +
                        (isTestMode ? " (TEST MODE)" : ""));
                });

                return status;
            }
            catch (Exception ex)
            {
                log.Error($"Error in newsletter batch send operation {operationId}: {ex.Message}", ex);
                status.IsCompleted = true;
                status.EndTime = DateTime.Now;
                throw;
            }
        }

        /// <summary>
        /// Processes a batch of users for newsletter sending
        /// </summary>
        private void ProcessUserBatch(string[] userIds, NewsletterSendStatusViewModel status, bool forceResend,
            NewsletterTestModeParameters testParams = null)
        {
            var context = _unitOfWork.Context;
            var newsletterRole = context.ApplicationRoles.FirstOrDefault(r => r.Name == Role.Newsletter);

            foreach (var userId in userIds)
            {
                var result = new NewsletterSendResultViewModel
                {
                    UserId = userId,
                    Status = NewsletterSendStatus.InProgress,
                    ProcessedAt = DateTime.Now
                };

                try
                {
                    var user = context.ApplicationUsers.Include("Kunde").FirstOrDefault(u => u.Id == userId);
                    if (user == null)
                    {
                        result.Status = NewsletterSendStatus.Failed;
                        result.ErrorMessage = "User not found";
                        status.Failed++;
                    }
                    else
                    {
                        result.UserName = user.FullName;
                        result.Email = user.Email;
                        
                        // For test mode, we might want to track the test email
                        if (testParams?.IsTestMode == true && !string.IsNullOrEmpty(testParams.TestRecipientEmail))
                        {
                            result.TestRecipientEmail = testParams.TestRecipientEmail;
                        }

                        // Check if user should receive newsletter
                        var lastNewsletter = FindLastSentNewsletter(user);
                        if (!forceResend && !ShouldSendNewsletter(user, newsletterRole, lastNewsletter))
                        {
                            result.Status = NewsletterSendStatus.Skipped;
                            result.ErrorMessage = "Newsletter not due for this user";
                            status.Skipped++;
                        }
                        else
                        {
                            // Process newsletter for user using existing logic
                            if (ProcessNewsletterForUser(user, lastNewsletter, result, testParams))
                            {
                                result.Status = NewsletterSendStatus.Success;
                                status.Sent++;
                                log.Info($"Successfully sent newsletter to {user.FullName} " +
                                    (testParams?.IsTestMode == true && !string.IsNullOrEmpty(testParams.TestRecipientEmail) 
                                     ? $"(TEST MODE, sent to {testParams.TestRecipientEmail})"
                                     : $"({user.Email})"));
                            }
                            else
                            {
                                result.Status = NewsletterSendStatus.Failed;
                                result.ErrorMessage = "Failed to generate or send newsletter";
                                status.Failed++;
                                log.Warn($"Failed to send newsletter to {user.FullName} ({user.Email})");
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    result.Status = NewsletterSendStatus.Failed;
                    result.ErrorMessage = ex.Message;
                    status.Failed++;
                    log.Error($"Error processing newsletter for user {userId}: {ex.Message}", ex);
                }

                status.Results.Add(result);
                status.InProgress = status.TotalUsers - (status.Sent + status.Failed + status.Skipped);
            }
        }

        /// <summary>
        /// Processes newsletter generation and sending for a single user (reused from NewsletterTask2)
        /// </summary>
        private bool ProcessNewsletterForUser(ApplicationUser user, ApplicationUserNewsletterHistory lastNewsletter, 
            NewsletterSendResultViewModel result, NewsletterTestModeParameters testParams = null)
        {
            try
            {
                // Override last newsletter date if in test mode
                var effectiveLastNewsletter = lastNewsletter;
                if (testParams?.IsTestMode == true && testParams.TestLastNewsletterDate.HasValue)
                {
                    // Create temporary last newsletter object with the test date
                    if (lastNewsletter != null)
                    {
                        effectiveLastNewsletter = new ApplicationUserNewsletterHistory
                        {
                            ID = lastNewsletter.ID,
                            UserID = lastNewsletter.UserID,
                            DateSent = testParams.TestLastNewsletterDate.Value,
                            NewsletterFileLink = lastNewsletter.NewsletterFileLink
                        };
                    }
                    else
                    {
                        effectiveLastNewsletter = new ApplicationUserNewsletterHistory
                        {
                            ID = Guid.NewGuid().ToString(),
                            UserID = user.Id,
                            DateSent = testParams.TestLastNewsletterDate.Value,
                            NewsletterFileLink = null
                        };
                    }
                    
                    result.TestLastNewsletterDate = testParams.TestLastNewsletterDate;
                }

                // Get newsletter content data with potentially overridden last newsletter date
                var newsletterData = GetNewsletterData(user, effectiveLastNewsletter);

                // Generate PDF
                using (var pdfStream = _pdfGenerationService.GenerateNewsletterPdf(
                    user,
                    newsletterData.Kommentare,
                    newsletterData.Gesetzaenderungen,
                    newsletterData.Consultations,
                    effectiveLastNewsletter))
                {
                    if (pdfStream == null)
                    {
                        log.Warn($"Null PdfStream newsletter for: {user.FullName}, TimeSpan {user.NewsletterPeriod}");
                        return false;
                    }

                    string fileUrl = null;
                    
                    // Skip PDF upload and history saving if in test mode
                    if (testParams?.IsTestMode != true)
                    {
                        // Upload PDF only if not in test mode
                        fileUrl = _fileUploadService.UploadNewsletterPdf(user, pdfStream);
                        result.PdfUrl = fileUrl;
                        
                        // Save newsletter history only if not in test mode
                        SaveNewsletterHistory(user, fileUrl, isManualSend: true);
                    }
                    else
                    {
                        log.Info($"Test mode enabled - skipping PDF upload and history saving for {user.FullName}");
                    }

                    // Send email (with override recipient if specified)
                    if (testParams?.IsTestMode == true && !string.IsNullOrEmpty(testParams.TestRecipientEmail))
                    {
                        // Clone the user to avoid modifying the actual user entity
                        var testUser = new ApplicationUser
                        {
                            Id = user.Id,
                            Email = testParams.TestRecipientEmail,
                            Vorname = user.Vorname,
                            Nachname = user.Nachname + " (TEST)",
                            SpracheID = user.SpracheID,
                            NewsletterPeriod = user.NewsletterPeriod,
                            KundeID = user.KundeID,
                            Kunde = user.Kunde
                        };
                        
                        // Cannot directly assign Roles as it's a read-only property
                        
                        _emailService.SendNewsletterEmail(testUser, pdfStream, user.SpracheID);
                    }
                    else
                    {
                        _emailService.SendNewsletterEmail(user, pdfStream, user.SpracheID);
                    }

                    return true;
                }
            }
            catch (Exception ex)
            {
                log.Error($"Error processing newsletter for user {user.FullName}: {ex.Message}", ex);
                result.ErrorMessage = ex.Message;
                return false;
            }
        }

        /// <summary>
        /// Gets newsletter data for a user (reused from NewsletterTask2)
        /// </summary>
        private (IQueryable<KommentarNewsletterViewModel> Kommentare, 
                IQueryable<GesetzaenderungViewModel> Gesetzaenderungen, 
                IQueryable<ConsultationViewModel> Consultations) GetNewsletterData(ApplicationUser user, ApplicationUserNewsletterHistory lastNewsletter)
        {
            int spracheID = user.SpracheID.Value;
            var sendingPeriod = user.NewsletterPeriod.GetValueOrDefault(6);
            var lastNewsletterSentTime = lastNewsletter?.DateSent ?? DateTime.Today.AddMonths(-sendingPeriod);

            var kommentare = _unitOfWork.DashboardRepository
                .GetAllKommentarNewsletterViewModelsByKunde(user.Kunde.KundeID, spracheID, lastNewsletterSentTime);

            var gesetzaenderungen = _unitOfWork.DashboardRepository
                .GetAllGesetzaenderungViewModelsByKunde(user.Kunde.KundeID, spracheID, lastNewsletterSentTime);

            var consultations = _unitOfWork.ConsultationRepository
                .GetConsultationViewModelsNewsletter(user.Kunde.KundeID, spracheID, lastNewsletterSentTime);

            return (kommentare, gesetzaenderungen, consultations);
        }

        /// <summary>
        /// Finds the last sent newsletter for a user (reused from NewsletterTask2)
        /// </summary>
        private ApplicationUserNewsletterHistory FindLastSentNewsletter(ApplicationUser user)
        {
            return _unitOfWork.Context.ApplicationUserNewsletterHistories
                .Where(h => h.UserID == user.Id)
                .OrderByDescending(h => h.DateSent)
                .FirstOrDefault();
        }

        /// <summary>
        /// Determines if a newsletter should be sent to a user (reused from NewsletterTask2)
        /// </summary>
        private bool ShouldSendNewsletter(ApplicationUser user, ApplicationRole role, ApplicationUserNewsletterHistory lastNewsletter)
        {
            if (!UserHasRole(user, role))
            {
                return false;
            }

            var sendingPeriod = user.NewsletterPeriod.GetValueOrDefault(6);
            return lastNewsletter == null || lastNewsletter.DateSent.AddMonths(sendingPeriod).Date <= DateTime.Now.Date;
        }

        /// <summary>
        /// Checks if a user has a specific role (reused from NewsletterTask2)
        /// </summary>
        private bool UserHasRole(ApplicationUser user, ApplicationRole role)
        {
            if (role == null) return false;
            var foundRole = user.Roles.FirstOrDefault(ur => ur.RoleId == role.Id);
            return foundRole != null;
        }

        /// <summary>
        /// Saves newsletter history (reused from NewsletterTask2 with manual send flag)
        /// </summary>
        private void SaveNewsletterHistory(ApplicationUser user, string fileUrl, bool isManualSend = false)
        {
            var newsletterHistory = new ApplicationUserNewsletterHistory
            {
                ID = Guid.NewGuid().ToString(),
                UserID = user.Id,
                DateSent = DateTime.Now,
                NewsletterFileLink = fileUrl
            };

            _unitOfWork.Context.ApplicationUserNewsletterHistories.Add(newsletterHistory);
            _unitOfWork.Context.SaveChanges();
        }

        /// <summary>
        /// Gets the current status of a newsletter sending operation
        /// </summary>
        public NewsletterSendStatusViewModel GetSendStatus(string operationId)
        {
            return _sendOperations.TryGetValue(operationId, out var status) ? status : null;
        }

        /// <summary>
        /// Gets newsletter history for a specific user or all users
        /// </summary>
        public IQueryable<NewsletterHistoryViewModel> GetNewsletterHistory(string userId = null)
        {
            var query = _unitOfWork.Context.ApplicationUserNewsletterHistories
                .Include("User")
                .Include("User.Kunde")
                .AsQueryable();

            if (!string.IsNullOrEmpty(userId))
            {
                query = query.Where(h => h.UserID == userId);
            }

            return query.Select(h => new NewsletterHistoryViewModel
            {
                Id = h.ID,
                UserId = h.UserID,
                UserName = h.User.FullName,
                Email = h.User.Email,
                DateSent = h.DateSent,
                NewsletterFileLink = h.NewsletterFileLink,
                CustomerName = h.User.Kunde.Name,
                IsManualSend = true // We'll assume manual sends for now
            }).OrderByDescending(h => h.DateSent);
        }

        /// <summary>
        /// Validates if newsletters can be sent to the specified users
        /// </summary>
        public NewsletterValidationResult ValidateUsersForSending(string[] userIds)
        {
            var result = new NewsletterValidationResult();
            var context = _unitOfWork.Context;
            var newsletterRole = context.ApplicationRoles.FirstOrDefault(r => r.Name == Role.Newsletter);

            foreach (var userId in userIds)
            {
                var user = context.ApplicationUsers.Include("Kunde").FirstOrDefault(u => u.Id == userId);
                if (user == null)
                {
                    result.Errors.Add($"User with ID {userId} not found");
                    result.InvalidUserCount++;
                    continue;
                }

                if (user.LockoutEndDateUtc.HasValue)
                {
                    result.Warnings.Add($"User {user.FullName} is inactive");
                }

                if (!UserHasRole(user, newsletterRole))
                {
                    result.Errors.Add($"User {user.FullName} does not have Newsletter role");
                    result.InvalidUserCount++;
                    continue;
                }

                if (user.Kunde == null)
                {
                    result.Errors.Add($"User {user.FullName} has no associated customer");
                    result.InvalidUserCount++;
                    continue;
                }

                if (string.IsNullOrEmpty(user.Email))
                {
                    result.Errors.Add($"User {user.FullName} has no email address");
                    result.InvalidUserCount++;
                    continue;
                }

                result.ValidUserCount++;
            }

            result.IsValid = result.Errors.Count == 0;
            return result;
        }
    }

    public class NewsletterTestModeParameters
    {
        public bool IsTestMode { get; set; }
        public string TestRecipientEmail { get; set; }
        public DateTime? TestLastNewsletterDate { get; set; }
    }
}
