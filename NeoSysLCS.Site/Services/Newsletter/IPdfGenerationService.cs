using System.IO;
using System.Linq;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories.ViewModels;

namespace NeoSysLCS.Site.Services.Newsletter
{
    /// <summary>
    /// Interface for PDF generation service for newsletters
    /// </summary>
    public interface IPdfGenerationService
    {
        /// <summary>
        /// Generates a newsletter PDF for a user with the provided content
        /// </summary>
        /// <param name="user">The user to generate the newsletter for</param>
        /// <param name="kommentare">Comments to include in the newsletter</param>
        /// <param name="gesetzaenderungen">Law changes to include in the newsletter</param>
        /// <param name="consultations">Consultations to include in the newsletter</param>
        /// <param name="lastNewsletter">The last newsletter sent to the user (optional)</param>
        /// <returns>A stream containing the generated PDF, or null if generation failed</returns>
        Stream GenerateNewsletterPdf(
            ApplicationUser user,
            IQueryable<KommentarNewsletterViewModel> kommentare,
            IQueryable<GesetzaenderungViewModel> gesetzaenderungen,
            IQueryable<ConsultationViewModel> consultations,
            ApplicationUserNewsletterHistory lastNewsletter = null);
    }
}
