using System;
using System.Collections.Generic;
using System.Web;
using log4net;
using Microsoft.AspNet.Identity;
using Microsoft.AspNet.Identity.EntityFramework;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories.ViewModels;


namespace NeoSysLCS.Site.Helpers
{
    public sealed class SessionHelper
    {
        private ApplicationUserManager _usrManager;

        public ApplicationUser GetCurrentUser()
        {
            _usrManager = new ApplicationUserManager(new UserStore<ApplicationUser>(new NeoSysLCS_Dev()));
            ApplicationUser usr = _usrManager.FindById(HttpContext.Current.User.Identity.GetUserId());

            return usr;
        }

        public void SetCurrentUser(UserViewModel user)
        {
            HttpContext.Current.Session["user"] = user;
        }

        public void SetPrivacyPolicyAccepted(bool accepted)
        {
            HttpContext.Current.Session["PrivacyPolicyAccepted"] = accepted;
        }

        public bool IsPrivacyPolicyAccepted()
        {
            if (HttpContext.Current.Session["PrivacyPolicyAccepted"] == null)
            {
                return false;
            }

            return (bool)HttpContext.Current.Session["PrivacyPolicyAccepted"];
        }

        public string GetCurrentUserID()
        {
            _usrManager = new ApplicationUserManager(new UserStore<ApplicationUser>(new NeoSysLCS_Dev()));
            ApplicationUser usr = _usrManager.FindById(System.Web.HttpContext.Current.User.Identity.GetUserId());
            return usr.Id;
        }

        public IList<string> GetCurrentUserRoles()
        {
            var identityId = HttpContext.Current.User.Identity.GetUserId();
            _usrManager = _usrManager ?? new ApplicationUserManager(new UserStore<ApplicationUser>(new NeoSysLCS_Dev()));
            var currentUserRoles = _usrManager.GetRoles(identityId);
            return currentUserRoles;

        }

        public ApplicationUserManager GetUserManager()
        {
            return new ApplicationUserManager(new UserStore<ApplicationUser>(new NeoSysLCS_Dev()));
        }

        public RoleManager<IdentityRole> GetRoleManager()
        {
            return new RoleManager<IdentityRole>(new RoleStore<IdentityRole>(new NeoSysLCS_Dev()));
        }

        /// <summary>
        /// Gets the Kundendokument Data from the Session 
        /// </summary>
        /// <param name="standortID"></param>
        /// <returns></returns>
        public static KundendokumentData GetKundendokumentData(int standortID)
        {
            //try
            //{
            //    var dictionary =
            //        HttpContext.Current.Session["kundendokumentData"] as Dictionary<int, KundendokumentData>;
            //    if (dictionary != null && dictionary.ContainsKey(standortID))
            //    {
            //        return dictionary[standortID];
            //    }
            //}
            //catch(Exception e)
            //{
            //    LogManager.GetLogger(typeof(SessionHelper)).Error(e.Message, e);
            //    HttpContext.Current.Session["kundendokumentData"] = new Dictionary<int, KundendokumentData>();
            //    return null;
            //}
            //return null;
            if (HttpContext.Current.Session["kundendokumentData"] == null)
            {
                HttpContext.Current.Session["kundendokumentData"] = new Dictionary<int, KundendokumentData>();
                return null;
            }

            var dictionary = HttpContext.Current.Session["kundendokumentData"] as Dictionary<int, KundendokumentData>;
            if (dictionary != null && dictionary.ContainsKey(standortID))
            {
                return dictionary[standortID];
            }
            return null;
        }

        /// <summary>
        /// Sets the KundendokumentData to the session
        /// </summary>
        /// <param name="data"></param>
        public static void SetKundendokumentData(KundendokumentData data)
        {
            //try
            //{
            //    var dictionary = HttpContext.Current.Session["kundendokumentData"] as Dictionary<int, KundendokumentData>;

            //    dictionary[data.StandortID] = data;
            //}
            //catch (Exception e)
            //{
            //    LogManager.GetLogger(typeof(SessionHelper)).Error(e.Message, e);
            //    HttpContext.Current.Session["kundendokumentData"] = new Dictionary<int, KundendokumentData>();
            //}
            if (HttpContext.Current.Session["kundendokumentData"] == null)
            {
                HttpContext.Current.Session["kundendokumentData"] = new Dictionary<int, KundendokumentData>();
            }

            var dictionary = HttpContext.Current.Session["kundendokumentData"] as Dictionary<int, KundendokumentData>;

            dictionary[data.StandortID] = data;
        }



        /// <summary>
        /// Gets the KundeID of the current user if he has the role form the parameter
        /// </summary>
        /// <returns></returns>
        public int? GetKundeIdOfCurrentUser()
        {
            //try
            //{
            //    var userId = HttpContext.Current.User.Identity.GetUserId();
            //    if (userId != null)
            //    {
            //        _usrManager = new ApplicationUserManager(new UserStore<ApplicationUser>(new NeoSysLCS_Dev()));
            //        ApplicationUser usr = _usrManager.FindById(userId);

            //        return usr.KundeID;
            //    }
            //}
            //catch (Exception e)
            //{
            //    LogManager.GetLogger(typeof(SessionHelper)).Error(e.Message, e);
            //    return null;
            //}
            //return null;
            var userId = HttpContext.Current.User.Identity.GetUserId();
            if (userId != null)
            {
                _usrManager = new ApplicationUserManager(new UserStore<ApplicationUser>(new NeoSysLCS_Dev()));
                ApplicationUser usr = _usrManager.FindById(userId);

                return usr.KundeID;
            }


            return null;
        }

        /// <summary>
        /// Clears the user cookies if needed
        /// </summary>
        /// <param name="user">the user</param>
        /// <param name="request">the current request</param>
        /// <param name="response">the current response</param>
        public void ClearAllUserCookiesIfNeeded(ApplicationUser user, HttpRequestBase request, HttpResponseBase response)
        {
            var app = HttpContext.Current.ApplicationInstance as MvcApplication;
            if (app != null)
            {
                var lastLogin = DateTime.Now;
                var lastLogincookie = request.Cookies["_lastLogin"];
                if (lastLogincookie != null)
                {
                    DateTime.TryParse(lastLogincookie.Value, out lastLogin); 
                }

                if (user.ForceClearCookies == true ||((DateTime)app.Application["lastModified"] > lastLogin || user.BearbeitetAm > lastLogin))
                {
                    var cookies = new List<HttpCookie>();

                    //lets delete all cookies
                    foreach (string cookieName in request.Cookies)
                    {
                        var cookie = new HttpCookie(cookieName)
                        {
                            Expires = DateTime.Now.AddDays(-1D)
                        };
                        cookies.Add(cookie);
                    }

                    foreach (var cookie in cookies)
                    {
                        response.Cookies.Add(cookie);
                    }

                    user.ForceClearCookies = false;
                }
            }
        }
    }
}