using System;
using System.ComponentModel.DataAnnotations;
using System.Reflection;


namespace NeoSysLCS.Site.Helpers
{
    /// <summary>
    /// Extension for enums
    /// </summary>
    public static class EnumExtensions
    {
        /// <summary>
        /// Gets the translation of an enum which is defined in the display annotation
        /// </summary>
        /// <param name="enumValue">The enum value.</param>
        /// <returns></returns>
        public static string GetTranslation(this Enum enumValue)
        {
            FieldInfo fi = enumValue.GetType().GetField(enumValue.ToString());
            if (fi == null)
            {
                return enumValue.ToString();
            }

            DisplayAttribute[] attributes = (DisplayAttribute[])fi.GetCustomAttributes(typeof(DisplayAttribute), false);

            if (attributes != null && attributes.Length > 0)
            {
                var resourceType = attributes[0].ResourceType;
                return resourceType.GetProperty(attributes[0].Name).GetValue(null).ToString();
            }
             
               
            return enumValue.ToString();
        }
    }
}