using System.Collections.Generic;
using HtmlAgilityPack;
using MigraDoc.DocumentObjectModel;

namespace NeoSysLCS.Site.Helpers
{
    public static class NewsletterTextHelper
    {
        public static void CreateFormattedTextFromHtml(Paragraph paragraph, string html)
        {
            if (string.IsNullOrEmpty(html))
            {
                return;
            }

            var document = new HtmlDocument();
            html = html.Replace("&nbsp;", " ");
            document.LoadHtml(html);

            foreach (var node in document.DocumentNode.ChildNodes)
            {
               ProcessNode(node, paragraph.AddFormattedText());
            }
        }

        private static void ProcessNode(HtmlNode node, FormattedText formattedText)
        {


            if (node is HtmlTextNode)
            {
                formattedText.AddText(node.InnerText);
                return;
            }

            if (node.Name.Equals("br"))
            {
                formattedText.AddLineBreak();
                return;
            }

            if (node.Name.Equals("strong"))
            {
                formattedText.Font.Bold = true;
            }

            if (node.Name.Equals("em"))
            {
                formattedText.Font.Italic = true;
            }

            if (node.Name.Equals("span"))
            {
                if (node.HasAttributes && HasStyleAttribute(node))
                {
                    
                    var styleAttributes = GetStyleAttributeDictionary(node);
                    if (styleAttributes.ContainsKey("font-size"))
                    {
                        var fontSizeRaw = styleAttributes["font-size"];
                        if (!fontSizeRaw.EndsWith("px")) // fixed issues if there is px in the font size
                        {
                            formattedText.Font.Size = Unit.Parse(fontSizeRaw);
                        }
                    }

                    if (styleAttributes.ContainsKey("color"))
                    {
                        var fontColorRaw = styleAttributes["color"];
                        formattedText.Font.Color = Color.Parse(fontColorRaw);
                    }

                    if (styleAttributes.ContainsKey("text-decoration") && styleAttributes["text-decoration"].Equals("underline"))
                    {
                        formattedText.Underline = Underline.Single;
                    }
                }
            }

            if (!node.HasChildNodes) return;

            foreach (var childNode in node.ChildNodes)
            {
                ProcessNode(childNode, formattedText.AddFormattedText());
            }
        }

        private static bool HasStyleAttribute(HtmlNode node)
        {
            return node.GetAttributeValue("style", null) != null;
        }

        private static Dictionary<string,string> GetStyleAttributeDictionary(HtmlNode node)
        {
            var styleMap = new Dictionary<string, string>();
            
            var styleAttributeValueRaw = node.GetAttributeValue("style", "");
            
            var parts = styleAttributeValueRaw.Split(';');
            foreach (var part in parts)
            {
                var keyValue = part.Split(':');
                if (keyValue.Length == 2)
                {
                    styleMap[keyValue[0].Trim()] = keyValue[1].Trim();
                }
            }

            return styleMap;
        }
    }
}