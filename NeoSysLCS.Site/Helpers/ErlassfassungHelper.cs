
using System.Collections.Generic;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;
using System;

namespace NeoSysLCS.Site.Helpers
{

    public static class ErlassfassungHelper
    {

        /// <summary>
        /// Extracts the erlassfassung ids.
        /// </summary>
        /// <param name="unitOfWork">The unit of work.</param>
        /// <param name="standortId">The standort identifier.</param>
        /// <param name="forderungenIds">The forderungen ids.</param>
        /// <param name="pflichtenIds">The pflichten ids.</param>
        /// <returns></returns>
        public static ISet<int> ExtractErlassfassungIds(IUnitOfWork unitOfWork, int standortId, ISet<string> forderungenIds,
             ISet<int> pflichtenIds)
        {

            IEnumerable<Int32> erlassIds = unitOfWork.ForderungsversionRepository.GetErlassfassungsIds(forderungenIds);

            IEnumerable<Int32> pflichtIds = unitOfWork.PflichtRepository.GetPflichtenIds(pflichtenIds);

            ISet<int> erlassfassungIds = new HashSet<int>();
            foreach (Int32 id in erlassIds)
            {
                erlassfassungIds.Add(id);
            }
            foreach (Int32 id in pflichtIds)
            {
                erlassfassungIds.Add(id);
            }
            return erlassfassungIds;
        }

    }
}