using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using log4net;

namespace NeoSysLCS.Site.Helpers
{
    public static class CookieHelper
    {
        private static readonly IUnitOfWork _unitOfWork;
        private static readonly NeoSysLCS_Dev _context;
        private readonly static ILog Logger = LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        static CookieHelper()
        {
            _context = new NeoSysLCS_Dev();
            _unitOfWork = new UnitOfWork();
        }

        public static string GetCookie(string cookieID)
        {
            var id = cookieID.Split('_');
            var cookID = id[2] + "_" + id[1];
            if (id[0] == "PortalKundendokumentForderungenGridView")
            {
                try
                {
                    return (from x in _context.GridViewCookie
                            where x.GridViewCookieID == cookID
                            select x.ForderungenLayout).FirstOrDefault() ?? "";
                }
                catch (Exception e)
                {
                    string message = e.Message;
                    return "";
                }
            }
            else if (id[0] == "ErlassfassungenGridView")
            {
                try
                { 
                    return (from x in _context.GridViewCookie
                            where x.GridViewCookieID == cookID
                            select x.ErlassLayout).FirstOrDefault() ?? "";
                }
                    catch (Exception e)
                {
                    string message = e.Message;
                    return "";
                }
        }
            else if (id[0] == "PortalKundendokumentRemovedForderungenGridView")
            {
                try
                {
                    return (from x in _context.GridViewCookie
                            where x.GridViewCookieID == cookID
                            select x.RemovedForderungenLayout).FirstOrDefault() ?? "";
                }
                catch (Exception e)
                {
                    string message = e.Message;
                    return "";
                }
            }
            else if (id[0] == "KundendokumentStandortObjektGridView")
            {
                try
                {
                    return (from x in _context.GridViewCookie
                            where x.GridViewCookieID == cookID
                            select x.StandortObjektLayout).FirstOrDefault() ?? "";
                }
                catch (Exception e)
                {
                    string message = e.Message;
                    return "";
                }
            }
            else if (id[0] == "KommentareGridView")
            {
                cookID = id[0] + "_" + id[1];

                try
                {
                    return (from x in _context.GridViewCookie
                            where x.GridViewCookieID == cookID
                            select x.StandortObjektLayout).FirstOrDefault() ?? "";
                }
                catch (Exception e)
                {
                    string message = e.Message;
                    return "";
                }
            }
            else if (id[0] == "KonzernGridView")
            {
                cookID = id[2] + "_" + id[1];

                try
                {
                    return (from x in _context.GridViewCookie
                        where x.GridViewCookieID == cookID
                        select x.KonzernLayout).FirstOrDefault() ?? "";
                }
                catch (Exception e)
                {
                    string message = e.Message;
                    return "";
                }
            }
            else if (id[0] == "MasterPortalKundendokumentForderungenGridView")
            {
                cookID = id[2] + "_" + id[1];

                try
                {
                    return (from x in _context.GridViewCookie
                        where x.GridViewCookieID == cookID
                        select x.MasterForderungenLayout).FirstOrDefault() ?? "";
                }
                catch (Exception e)
                {
                    string message = e.Message;
                    return "";
                }
            }
            else if (id[0] == "MasterErlassfassungenGridView")
            {
                cookID = id[2] + "_" + id[1];

                try
                {
                    return (from x in _context.GridViewCookie
                        where x.GridViewCookieID == cookID
                        select x.MasterErlassLayout).FirstOrDefault() ?? "";
                }
                catch (Exception e)
                {
                    string message = e.Message;
                    return "";
                }
            }

            return "";
        }

        public static void SetCookie(string cookieID, string layout)
        {
            var id = cookieID.Split('_');
            var cookID = id[2] + "_" + id[1];
            var cookie = (from x in _context.GridViewCookie
                          where x.GridViewCookieID == cookID
                          select x.GridViewCookieID).FirstOrDefault() ?? "";

            if (cookie != "")
            {
                try
                {
                    _unitOfWork.GridViewCookieViewRepository.Update(cookID, id[0], layout);
                }

                catch (Exception e)
                {
                    Logger.Warn("GridViewCookie update failed:" + cookID + " , Exception" + e.InnerException);
                }
            }
            else
            {
                try
                {
                    _unitOfWork.GridViewCookieViewRepository.Insert(cookID, id[0], layout);
                }
                catch (Exception e)
                {
                    Logger.Warn("GridViewCookie insert failed" + cookID + " , Exception" + e.InnerException);
                }
            }
        }
    }
}