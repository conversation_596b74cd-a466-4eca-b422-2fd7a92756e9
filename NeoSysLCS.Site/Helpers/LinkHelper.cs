using System;
using System.Linq;
using System.Text.RegularExpressions;

namespace NeoSysLCS.Site.Helpers
{
    public static class LinkHelper
    {
        /// <summary>
        /// Generates the HTML link if possible.
        /// </summary>
        /// <param name="candidate">The candidate.</param>
        /// <param name="showIcon">if set to <c>true</c> [show icon].</param>
        /// <returns></returns>
        public static string GenerateHtmlLinkIfPossible(object candidate, object displayName, bool showIcon = false)
        {
            var s = candidate as string;
            var d = displayName as string;
            if (s != null)
            {
                string linkCandidate = s;
                Uri uriResult;
                bool valid = Uri.TryCreate(linkCandidate, UriKind.Absolute, out uriResult);
                if (!valid)
                {
                    //check if string is ip address
                    var match = Regex.Match(linkCandidate, @"\b(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\b");
                    //avoid that is "123" is converted to http://*********
                    if (linkCandidate.Contains("www.") || match.Success)
                    {
                        valid = Uri.TryCreate("http://" + linkCandidate, UriKind.Absolute, out uriResult);
                    }
                }

                if (valid && (uriResult.Scheme == Uri.UriSchemeHttp || uriResult.Scheme == Uri.UriSchemeHttps) && !(uriResult.Scheme == Uri.UriSchemeFile || uriResult.Scheme == Uri.UriSchemeFtp))
                {
                    if (showIcon)
                    {
                        return Url2HtmlLinkWithIcon(d, uriResult.AbsoluteUri);
                    }
                    return Url2HtmlLink(d, uriResult.AbsoluteUri);
                }

                if (valid && (uriResult.Scheme == Uri.UriSchemeFile || uriResult.Scheme == Uri.UriSchemeFtp))
                {
                    if (showIcon)
                    {
                        return Url2HtmlLinkWithIcon(linkCandidate, uriResult.AbsoluteUri);
                    }
                    return Url2HtmlLink(linkCandidate, uriResult.AbsoluteUri);
                }

                return linkCandidate;
            }
            return "";
        }

        private static string Url2HtmlLink(string displayName, string link)
        {
            return "<a href=\"" + link + "\" target=\"_blank\" title=\"" + link + "\" \">" + displayName + "</a>";
        }

        private static string Url2HtmlLinkWithIcon(string displayName, string link)
        {
            //return "<a title=\"" + displayName + "\" href=\"" + link + "\" target=\"_blank\" \"><i class=' fa fa-external-link'></a>";
            return "<a class=\"iconlink\" title=\"" + displayName + "\" href=\"" + link + "\" target=\"_blank\" \"><i class=' fa fa-link'></a>";
        }
    }
}