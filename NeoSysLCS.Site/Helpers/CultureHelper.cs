using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Web.Mvc;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;
using System.Web;

namespace NeoSysLCS.Site.Helpers
{
    public static class CultureHelper
    {
        private static readonly IUnitOfWork UnitOfWork;
        private static readonly SessionHelper SessionHelper;

        static CultureHelper()
        {
            UnitOfWork = new UnitOfWork();
            SessionHelper = new SessionHelper();
        }
        // Valid cultures
        private static readonly List<string> ValidCultures = new List<string> { "af", "af-ZA", "sq", "sq-AL", "gsw-FR", "am-ET", "ar", "ar-DZ", "ar-BH", "ar-EG", "ar-IQ", "ar-JO", "ar-KW", "ar-LB", "ar-L<PERSON>", "ar-MA", "ar-OM", "ar-QA", "ar-SA", "ar-SY", "ar-TN", "ar-AE", "ar-YE", "hy", "hy-AM", "as-IN", "az", "az-Cyrl-AZ", "az-Latn-AZ", "ba-RU", "eu", "eu-ES", "be", "be-BY", "bn-BD", "bn-IN", "bs-Cyrl-BA", "bs-Latn-BA", "br-FR", "bg", "bg-BG", "ca", "ca-ES", "zh-HK", "zh-MO", "zh-CN", "zh-Hans", "zh-SG", "zh-TW", "zh-Hant", "co-FR", "hr", "hr-HR", "hr-BA", "cs", "cs-CZ", "da", "da-DK", "prs-AF", "div", "div-MV", "nl", "nl-BE", "nl-NL", "en", "en-AU", "en-BZ", "en-CA", "en-029", "en-IN", "en-IE", "en-JM", "en-MY", "en-NZ", "en-PH", "en-SG", "en-ZA", "en-TT", "en-GB", "en-US", "en-ZW", "et", "et-EE", "fo", "fo-FO", "fil-PH", "fi", "fi-FI", "fr", "fr-BE", "fr-CA", "fr-FR", "fr-LU", "fr-MC", "fr-CH", "fy-NL", "gl", "gl-ES", "ka", "ka-GE", "de", "de-AT", "de-DE", "de-LI", "de-LU", "de-CH", "el", "el-GR", "kl-GL", "gu", "gu-IN", "ha-Latn-NG", "he", "he-IL", "hi", "hi-IN", "hu", "hu-HU", "is", "is-IS", "ig-NG", "id", "id-ID", "iu-Latn-CA", "iu-Cans-CA", "ga-IE", "xh-ZA", "zu-ZA", "it", "it-IT", "it-CH", "ja", "ja-JP", "kn", "kn-IN", "kk", "kk-KZ", "km-KH", "qut-GT", "rw-RW", "sw", "sw-KE", "kok", "kok-IN", "ko", "ko-KR", "ky", "ky-KG", "lo-LA", "lv", "lv-LV", "lt", "lt-LT", "wee-DE", "lb-LU", "mk", "mk-MK", "ms", "ms-BN", "ms-MY", "ml-IN", "mt-MT", "mi-NZ", "arn-CL", "mr", "mr-IN", "moh-CA", "mn", "mn-MN", "mn-Mong-CN", "ne-NP", "no", "nb-NO", "nn-NO", "oc-FR", "or-IN", "ps-AF", "fa", "fa-IR", "pl", "pl-PL", "pt", "pt-BR", "pt-PT", "pa", "pa-IN", "quz-BO", "quz-EC", "quz-PE", "ro", "ro-RO", "rm-CH", "ru", "ru-RU", "smn-FI", "smj-NO", "smj-SE", "se-FI", "se-NO", "se-SE", "sms-FI", "sma-NO", "sma-SE", "sa", "sa-IN", "sr", "sr-Cyrl-BA", "sr-Cyrl-SP", "sr-Latn-BA", "sr-Latn-SP", "nso-ZA", "tn-ZA", "si-LK", "sk", "sk-SK", "sl", "sl-SI", "es", "es-AR", "es-BO", "es-CL", "es-CO", "es-CR", "es-DO", "es-EC", "es-SV", "es-GT", "es-HN", "es-MX", "es-NI", "es-PA", "es-PY", "es-PE", "es-PR", "es-ES", "es-US", "es-UY", "es-VE", "sv", "sv-FI", "sv-SE", "syr", "syr-SY", "tg-Cyrl-TJ", "tzm-Latn-DZ", "ta", "ta-IN", "tt", "tt-RU", "te", "te-IN", "th", "th-TH", "bo-CN", "tr", "tr-TR", "tk-TM", "ug-CN", "uk", "uk-UA", "wen-DE", "ur", "ur-PK", "uz", "uz-Cyrl-UZ", "uz-Latn-UZ", "vi", "vi-VN", "cy-GB", "wo-SN", "sah-RU", "ii-CN", "yo-NG" };
        // Include ONLY cultures you are implementing
        private static readonly List<string> Cultures = new List<string> {
            "de",  // first culture is the DEFAULT
            "fr", 
            "it",
            "en"
        };



        /// <summary>
        /// Returns all language which are available to the user making the request.
        /// 
        /// Which user makes the request is determined the SessionHelper. 
        /// If the user is a neosys user all available languages are returned. 
        /// If the user is part of any other company the union of all languages 
        /// defined on a locations of the comapanies is returned.
        /// (i.e. location1 = D,F, location2 = D,F location3=I => D,F,I)
        /// </summary>
        /// <returns></returns>
        public static IEnumerable<SelectListItem> GetUsersLanguagesAsSelectListItem()
        {
            IList<SelectListItem> items = new List<SelectListItem>();
            var kundeid = SessionHelper.GetKundeIdOfCurrentUser();
            if (kundeid != null)
            {
                var sprachen = new List<Sprache>();

           
                    //show all languages to all users
                    sprachen = new UnitOfWork().SpracheRepository.Get().ToList();
  

                if (HttpContext.Current.Request.Cookies["_culture"] != null)
                {
                    CultureInfo.CurrentUICulture = new CultureInfo(HttpContext.Current.Request.Cookies["_culture"].Value);
                }
                foreach (Sprache sprache in sprachen.Distinct())
                {
                    bool selected = (sprache.Lokalisierung == CultureInfo.CurrentUICulture.TwoLetterISOLanguageName);
                    items.Add(new SelectListItem() { Text = sprache.Name, Value = sprache.Lokalisierung, Selected = selected });
                }
            }

            return items;
        }

        /// <summary>
        /// Returns true if the language is a right-to-left language. Otherwise, false.
        /// </summary>
        public static bool IsRighToLeft()
        {
            return Thread.CurrentThread.CurrentCulture.TextInfo.IsRightToLeft;

        }
        /// <summary>
        /// Returns a valid culture name based on "name" parameter. If "name" is not valid, it returns the default culture "en-US"
        /// </summary>
        /// <param name="name" />Culture's name (e.g. en-US)</param>
        public static string GetImplementedCulture(string name)
        {
            // make sure it's not null
            if (string.IsNullOrEmpty(name))
                return GetDefaultCulture(); // return Default culture
            // make sure it is a valid culture first
            if (ValidCultures.Where(c => c.Equals(name, StringComparison.InvariantCultureIgnoreCase)).Count() == 0)
                return GetDefaultCulture(); // return Default culture if it is invalid
            // if it is implemented, accept it
            if (Cultures.Where(c => c.Equals(name, StringComparison.InvariantCultureIgnoreCase)).Count() > 0)
                return name; // accept it
            // Find a close match. For example, if you have "en-US" defined and the user requests "en-GB",
            // the function will return closes match that is "en-US" because at least the language is the same (ie English) 
            var n = GetNeutralCulture(name);
            foreach (var c in Cultures)
                if (c.StartsWith(n))
                    return c;
            // else
            // It is not implemented
            return GetDefaultCulture(); // return Default culture as no match found
        }

        /// <summary>
        /// Returns default culture name which is the first name decalared (e.g. en-US)
        /// </summary>
        /// <returns></returns>
        public static string GetDefaultCulture()
        {
            return Cultures[0]; // return Default culture
        }
        public static string GetCurrentCulture()
        {
            return Thread.CurrentThread.CurrentCulture.Name;
        }
        public static string GetCurrentNeutralCulture()
        {
            return GetNeutralCulture(Thread.CurrentThread.CurrentCulture.Name);
        }
        public static string GetNeutralCulture(string name)
        {
            if (!name.Contains("-")) return name;

            return name.Split('-')[0]; // Read first part only. E.g. "en", "es"
        }

        public static string GetStandortMenuName(int kundeId = 0, int spracheId = 0, bool einzahl = true) // bool einzahl -> falls true, soll "Standort" zurückgegeben werden, ansonsten "Standorte"
        {
            if (kundeId == 0)
            {
                kundeId = (int)SessionHelper.GetKundeIdOfCurrentUser();
            }
            var viewModel = UnitOfWork.StandortMenuUebersetzungRepository.GetStandortMenuUebersetzungenByKunde(kundeId);
            var titel = System.String.Empty;

            if (spracheId == 0)
            {
                var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
                var sprachen = UnitOfWork.SpracheRepository.Get();
                Sprache sprache = sprachen.FirstOrDefault(x => x.Lokalisierung == language);
                int _currentLang = sprache.SpracheID;

                if (viewModel != null)
                {
                    if(einzahl)
                    {
                        titel = (from eu in viewModel
                                 join sp in sprachen on eu.SpracheID equals sp.SpracheID
                                 where (eu.SpracheID == _currentLang && !string.IsNullOrEmpty(eu.Titel))
                                                 ||
                                                 (!string.IsNullOrEmpty(eu.Titel) &&
                                                     !(from eu2 in viewModel
                                                       where eu2.SpracheID == _currentLang && !string.IsNullOrEmpty(eu2.Titel)
                                                       select eu2).Any())
                                 orderby sp.Reihenfolge
                                 select eu.Titel).FirstOrDefault() ?? Resources.Properties.Resources.View_Breadcrumb_Standorte;
                        return titel;
                    }
                    else
                    {
                        titel = (from eu in viewModel
                                 join sp in sprachen on eu.SpracheID equals sp.SpracheID
                                 where (eu.SpracheID == _currentLang && !string.IsNullOrEmpty(eu.Titel))
                                                 ||
                                                 (!string.IsNullOrEmpty(eu.Titel) &&
                                                     !(from eu2 in viewModel
                                                       where eu2.SpracheID == _currentLang && !string.IsNullOrEmpty(eu2.Titel)
                                                       select eu2).Any())
                                 orderby sp.Reihenfolge
                                 select eu.TitelPlural).FirstOrDefault() ?? Resources.Properties.Resources.View_Breadcrumb_Standorte;
                        return titel;
                    }
                }
            }
            else {
                var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
                var sprachen = UnitOfWork.SpracheRepository.Get();
                Sprache sprache = sprachen.FirstOrDefault(x => x.Lokalisierung == language);
                int _currentLang = sprache.SpracheID;

                if (viewModel != null)
                {
                    if(einzahl)
                    {
                        titel = (from eu in viewModel
                                 where eu.SpracheID == spracheId
                                 select eu.Titel).FirstOrDefault() ?? Resources.Properties.Resources.View_Breadcrumb_Standorte;
                        return titel;
                    }
                    else
                    {
                        titel = (from eu in viewModel
                                 where eu.SpracheID == spracheId
                                 select eu.TitelPlural).FirstOrDefault() ?? Resources.Properties.Resources.View_Breadcrumb_Standorte;
                        return titel;
                    }                    
                }
            }
            return Resources.Properties.Resources.View_Breadcrumb_Standorte;

        }

    }
}