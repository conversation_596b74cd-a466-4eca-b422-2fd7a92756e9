using System;
using System.Collections.Generic;
using NeoSysLCS.Repositories.ViewModels;

namespace NeoSysLCS.Site.Helpers
{
    /// <summary>
    /// Encapsulate the kundendokument data when creating a kundendokument
    /// </summary>

    [Serializable]
    public class KundendokumentData : BaseViewModel
    {
        public KundendokumentData()
        {
            ForderungKundenbezuege = new List<KundenbezugItem>();
            PflichtKundenbezuege = new List<KundenbezugItem>();
            SelectedErlasssfassungenIDs = new HashSet<int>();
            SelectedPflichtenIDs = new HashSet<int>();
            SelectedForderungenIDs = new HashSet<string>();
        }

        public int StandortID { get; set; }
        public int? VorgaengerKundendokumentID { get; set; }
        public int? KundendokumentID { get; set; }

        public ISet<string> SelectedForderungenIDs { get; set; }
        public ISet<int> SelectedPflichtenIDs { get; set; }
        public ISet<int> SelectedErlasssfassungenIDs { get; set; }
        public IList<KundenbezugItem> ForderungKundenbezuege { get; set; }
        public IList<KundenbezugItem> PflichtKundenbezuege { get; set; }

        [Serializable]
        public class KundenbezugItem
        {
            public string Key { get; set; }
            public string Value { get; set; }

        }

    }
}