using System;
using System.ComponentModel.DataAnnotations;
using System.Reflection;

namespace NeoSysLCS.Site.Helpers
{
    public static class DateHelper
    {

        /// <summary>
        /// Gets the translation of the name of the Month for a give numeric value
        /// </summary>
        /// <param name="monthNumber">The property.</param>
        /// <returns></returns>
        public static string GetMonthName(string monthNumber)
        {

            switch (monthNumber)
            {
                case "1":
                    return Resources.Properties.Resources.Date_Month_January;
                case "2":
                    return Resources.Properties.Resources.Date_Month_February;
                case "3":
                    return Resources.Properties.Resources.Date_Month_March;
                case "4":
                    return Resources.Properties.Resources.Date_Month_April;
                case "5":
                    return Resources.Properties.Resources.Date_Month_May;
                case "6":
                    return Resources.Properties.Resources.Date_Month_June;
                case "7":
                    return Resources.Properties.Resources.Date_Month_July;
                case "8":
                    return Resources.Properties.Resources.Date_Month_August;
                case "9":
                    return Resources.Properties.Resources.Date_Month_September;
                case "10":
                    return Resources.Properties.Resources.Date_Month_October;
                case "11":
                    return Resources.Properties.Resources.Date_Month_November;
                case "12":
                    return Resources.Properties.Resources.Date_Month_December;
                default:
                    return Resources.Properties.Resources.Date_Month_Unknown;
            }

        }
    }
}