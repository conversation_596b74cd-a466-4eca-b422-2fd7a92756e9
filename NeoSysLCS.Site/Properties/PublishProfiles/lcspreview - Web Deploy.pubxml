<?xml version="1.0" encoding="utf-8"?>
<!--
This file is used by the publish/package process of your Web project. You can customize the behavior of this process
by editing this MSBuild file. In order to learn more about this please visit https://go.microsoft.com/fwlink/?LinkID=208121. 
-->
<Project>
  <PropertyGroup>
    <WebPublishMethod>MSDeploy</WebPublishMethod>
    <LastUsedBuildConfiguration>Release</LastUsedBuildConfiguration>
    <LastUsedPlatform>Any CPU</LastUsedPlatform>
    <SiteUrlToLaunchAfterPublish>http://*************:80/</SiteUrlToLaunchAfterPublish>
    <LaunchSiteAfterPublish>true</LaunchSiteAfterPublish>
    <ExcludeApp_Data>true</ExcludeApp_Data>
    <MSDeployServiceURL>https://*************:8172/msdeploy.axd</MSDeployServiceURL>
    <DeployIisAppPath>NeoSys Test</DeployIisAppPath>
    <RemoteSitePhysicalPath />
    <SkipExtraFilesOnServer>false</SkipExtraFilesOnServer>
    <MSDeployPublishMethod>WMSVC</MSDeployPublishMethod>
    <EnableMSDeployBackup>true</EnableMSDeployBackup>
    <EnableMsDeployAppOffline>false</EnableMsDeployAppOffline>
    <UserName>TEST-APP-SRV\Administrator</UserName>
    <_SavePWD>true</_SavePWD>
	<AllowUntrustedCertificate>True</AllowUntrustedCertificate>
	  <PublishDatabaseSettings>
		  <Objects xmlns="">
			  <ObjectGroup Name="NeoSysLCS_Dev" Order="1" Enabled="True">
				  <Destination Path="Data Source=TEST-SQL-SRV;Initial Catalog=NeoSysLCS_Test;User ID=NeoSysLCS_Test;Password=*********************" Name="Server=TEST-SQL-SRV;Database=NeoSysLCS_Test;User ID=NeoSysLCS_Test;Password=*********************;MultipleActiveResultSets=True" />
				  <Object Type="DbCodeFirst">
					  <Source Path="DBMigration" DbContext="NeoSysLCS.DomainModel.Models.NeoSysLCS_Dev, NeoSysLCS.DomainModel" MigrationConfiguration="NeoSysLCS.DomainModel.Migrations.Configuration, NeoSysLCS.DomainModel" Origin="Configuration" />
				  </Object>
			  </ObjectGroup>
		  </Objects>
	  </PublishDatabaseSettings>  
  </PropertyGroup>
	<ItemGroup>
		<MSDeployParameterValue Include="NeoSysLCS_Dev-Web.config Connection String">
			<ParameterValue>Server=TEST-SQL-SRV;Database=NeoSysLCS_Test;User ID=NeoSysLCS_Test;Password=*********************;MultipleActiveResultSets=True</ParameterValue>
		</MSDeployParameterValue>
	</ItemGroup>

</Project>