<?xml version="1.0" encoding="utf-8"?>
<!--
This file is used by the publish/package process of your Web project. You can customize the behavior of this process
by editing this MSBuild file. In order to learn more about this please visit http://go.microsoft.com/fwlink/?LinkID=208121. 
-->
<Project ToolsVersion="12.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <EnableMSDeployAppOffline>true</EnableMSDeployAppOffline>
    <WebPublishMethod>MSDeploy</WebPublishMethod>
    <LastUsedBuildConfiguration>Debug</LastUsedBuildConfiguration>
    <LastUsedPlatform>Any CPU</LastUsedPlatform>
    <SiteUrlToLaunchAfterPublish>https://lcstest.azurewebsites.net</SiteUrlToLaunchAfterPublish>
    <LaunchSiteAfterPublish>True</LaunchSiteAfterPublish>
    <ExcludeApp_Data>True</ExcludeApp_Data>
    <MSDeployServiceURL>lcstest.scm.azurewebsites.net:443</MSDeployServiceURL>
    <DeployIisAppPath>lcstest</DeployIisAppPath>
    <RemoteSitePhysicalPath />
    <SkipExtraFilesOnServer>False</SkipExtraFilesOnServer>
    <MSDeployPublishMethod>WMSVC</MSDeployPublishMethod>
    <EnableMSDeployBackup>False</EnableMSDeployBackup>
    <UserName>$lcstest</UserName>
    <_SavePWD>True</_SavePWD>
    <PublishDatabaseSettings>
      <Objects>
        <ObjectGroup Name="NeoSysLCS_Dev" Order="1" Enabled="False">
          <Destination Path="Data Source=tcp:h8zhwhoehm.database.windows.net,1433;Initial Catalog=NeoSysLCS_Test3;User ID=Neosys@h8zhwhoehm;Password=********" />
          <Object Type="DbCodeFirst">
            <Source Path="DBMigration" DbContext="NeoSysLCS.DomainModel.Models.NeoSysLCS_Dev, NeoSysLCS.DomainModel" MigrationConfiguration="NeoSysLCS.DomainModel.Migrations.Configuration, NeoSysLCS.DomainModel" Origin="Configuration" />
          </Object>
        </ObjectGroup>
      </Objects>
    </PublishDatabaseSettings>
    <_DestinationType>AzureWebSite</_DestinationType>
    <ADUsesOwinOrOpenIdConnect>False</ADUsesOwinOrOpenIdConnect>
    <PublishProvider>AzureWebSite</PublishProvider>
  </PropertyGroup>
  <Target Name="CustomDLLs">
    <ItemGroup>
      <_CustomFiles Include="bin\devexpress\**\*" />
      <FilesForPackagingFromProject Include="%(_CustomFiles.Identity)">
        <DestinationRelativePath>bin\%(Filename)%(Extension)</DestinationRelativePath>
      </FilesForPackagingFromProject>
    </ItemGroup>
  </Target>
  <PropertyGroup>
    <CopyAllFilesToSingleFolderForPackageDependsOn>CustomDLLs;
      ;</CopyAllFilesToSingleFolderForPackageDependsOn>
    <CopyAllFilesToSingleFolderForMsdeployDependsOn>CustomDLLs;

      CustomDLLs;
      ;
      ;</CopyAllFilesToSingleFolderForMsdeployDependsOn>
  </PropertyGroup>
  <ItemGroup>
    <MSDeployParameterValue Include="$(DeployParameterPrefix)NeoSysLCS_Dev-Web.config Connection String">
      <ParameterValue>Data Source=tcp:h8zhwhoehm.database.windows.net,1433;Initial Catalog=NeoSysLCS_Test3;User ID=Neosys@h8zhwhoehm;Password=********</ParameterValue>
    </MSDeployParameterValue>
  </ItemGroup>
</Project>