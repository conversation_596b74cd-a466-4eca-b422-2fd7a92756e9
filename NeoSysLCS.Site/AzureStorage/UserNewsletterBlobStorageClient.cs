using Microsoft.Azure.Storage.Blob;

namespace NeoSysLCS.Site.AzureStorage
{

    public class UserNewsletterBlobStorageClient : BlobStorageClientBase
    {
        private readonly string _userId;

        public UserNewsletterBlobStorageClient(CloudBlobContainer container, string userId) : base(container)
        {
            _userId = userId;
        }

        protected override string GetFolderName()
        {
            return _userId.ToString();
        }
    }
}
