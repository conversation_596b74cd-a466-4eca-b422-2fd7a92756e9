using Microsoft.Azure.Storage.Blob;

namespace NeoSysLCS.Site.AzureStorage
{
    using System;
    using System.IO;


    public abstract class BlobStorageClientBase : IBlobStorageClient
    {
        private readonly CloudBlobContainer _container;

        protected BlobStorageClientBase(CloudBlobContainer container)
        {
            _container = container;
        }

        public UploadResult Upload(string fileName, string mimeType, Stream stream)
        {
            var blob = GetBlob(fileName);
            blob.Properties.ContentType = mimeType;
            blob.UploadFromStream(stream);
            var properties = blob.Properties;
            var length = properties.Length;

            return new UploadResult(blob.Uri.AbsoluteUri, (int)length);
        }

        public Stream Download(string fileName)
        {
            var blob = GetBlob(fileName);
            var exists = blob.Exists();

            return exists ? blob.OpenRead() : Stream.Null;
        }

        public bool Exists(string fileName)
        {
            return GetBlob(fileName).Exists();
        }

        private CloudBlockBlob GetBlob(string fileName, bool combineUrl = true)
        {
            return _container.GetBlockBlobReference(combineUrl ? CombineUrl(fileName) : fileName);
        }

        private string CombineUrl(string fileName)
        {
            return GetFolderName() + "/" + Uri.EscapeUriString(fileName);
        }

        protected abstract string GetFolderName();
    }
}
