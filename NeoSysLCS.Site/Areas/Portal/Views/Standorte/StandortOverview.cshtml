@using System.Web.UI.WebControls
@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories
@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers

@model IQueryable<KundendokumentErlassfassungViewModel>


@{
    var grid = Html.DevExpress().GridView(settings =>
    {
        UnitOfWork unitOfWork = new UnitOfWork();
        settings.Name = "StandorteGridView_" + ViewData["StandortID"];
        settings.KeyFieldName = "StandortID";

        GridViewHelper.ApplyDefaultSettings(settings);
        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;
        settings.SettingsContextMenu.Enabled = false;
        settings.Settings.ShowGroupPanel = false;
        settings.CallbackRouteValues = new { Controller = "Standorte", Action = "StandortOverview", id = ViewData["StandortID"] };

        settings.Columns.Add(column =>
        {
            column.FieldName = "ErlassTitel";
            column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), column.FieldName);
            column.HeaderStyle.Wrap = DefaultBoolean.True;
            column.ReadOnly = true;
            column.EditFormSettings.Visible = DefaultBoolean.False;

        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "ErlassAbkuerzung";
            column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), column.FieldName);
            column.HeaderStyle.Wrap = DefaultBoolean.True;
            column.ReadOnly = true;
            column.EditFormSettings.Visible = DefaultBoolean.False;

        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "ErlassSrNummer";
            column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), column.FieldName);
            column.HeaderStyle.Wrap = DefaultBoolean.True;
            column.ReadOnly = true;
            column.EditFormSettings.Visible = DefaultBoolean.False;

        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "Inkrafttretung";
            column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), column.FieldName);
            column.ColumnType = MVCxGridViewColumnType.DateEdit;
            column.ReadOnly = true;
            column.EditFormSettings.Visible = DefaultBoolean.False;
            column.SortDescending();

        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "RelevanterKommentar";
            column.Caption = Resources.View_Kundendokument_Erlassfassung_NeosysKommentar;
            column.HeaderStyle.Wrap = DefaultBoolean.True;

            column.ColumnType = MVCxGridViewColumnType.Memo;
            column.Width = Unit.Percentage(30);
            var memoProp = column.PropertiesEdit as MemoProperties;
            if (memoProp != null)
            {
                memoProp.EncodeHtml = false;
            }
            column.ReadOnly = true;
            column.EditFormSettings.Visible = DefaultBoolean.False;
            column.SetDataItemTemplateContent(
                container =>
                {
                    var kommentar = (string)DataBinder.Eval(container.DataItem, "RelevanterKommentar");
                    ViewContext.Writer.Write(StringHelper.TruncateHtml(kommentar, 50));
                });
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "Status";
            column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), column.FieldName);

            //define as combobox for filtern over the enum
            column.ColumnType = MVCxGridViewColumnType.ComboBox;
            column.Settings.FilterMode = ColumnFilterMode.DisplayText;
            var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;

            var test = (from x in Model select x.Status).Distinct().ToList();

            var list = from KundendokumentItemStatus value in Enum.GetValues(typeof(KundendokumentItemStatus))
                       where test.Contains(value)
                       select new
                       {
                           Id = (int)value,
                           Name = value.GetTranslation()
                       };

            comboBoxProperties.DataSource = list;
            comboBoxProperties.ValueField = "Id";
            comboBoxProperties.TextField = "Name";

            column.HeaderStyle.Wrap = DefaultBoolean.True;
            column.ReadOnly = true;
            column.EditFormSettings.Visible = DefaultBoolean.False;
            column.SetDataItemTemplateContent(container =>
            {
                var statusCandidate = DataBinder.Eval(container.DataItem, "Status");
                if (statusCandidate != null)
                {
                    var status = (KundendokumentItemStatus)statusCandidate;
                    ViewContext.Writer.Write("<span class=\"label label-" + status + "\">" + status.GetTranslation() + "</span>");
                }
            });
        });


        settings.HtmlDataCellPrepared += (sender, e) =>
        {
            if (e.DataColumn.FieldName == "RelevanterKommentar")
            {
                //readonly
                e.Cell.Attributes.Add(
                   "onclick",
                   "openPopupReadonly(" + settings.Name + ", '" + e.DataColumn.FieldName + "', " + e.VisibleIndex + ", '" + e.DataColumn.Caption + "')"
                   );
            }

        };
        settings.ClientSideEvents.EndCallback = "function(s,e){SetDataIndexOfRowsWithErrors(s);}";

    });
    if (ViewData["EditError"] != null)
    {
        grid.SetEditErrorText((string)ViewData["EditError"]);
    }
}
@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = "KundendokumentErlassfassungID";
}).GetHtml()

