@using System.Web.UI.WebControls
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers

@{
    var gridArchive = Html.DevExpress().GridView(settings =>
    {

        settings.Name = "StandorteArchiveGridView";
        settings.KeyFieldName = "StandortID";

        GridViewHelper.ApplyDefaultSettings(settings);
        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;

        settings.SettingsContextMenu.Enabled = false;
        settings.Settings.ShowGroupPanel = false;
        settings.CallbackRouteValues = new { Controller = "Standorte", Action = "StandorteArchiveGridView", id = ViewData["StandortID"] };

        //Columns


        settings.Columns.Add(column =>
        {

            column.FieldName = "Name";
            column.Caption = Resources.View_Standort_Name;
        });

        settings.SettingsDetail.ShowDetailRow = true;
        settings.SetDetailRowTemplateContent(c => Html.DevExpress().PageControl(pageControlSettings =>
        {
            pageControlSettings.Name = "StandortPageControl_" + DataBinder.Eval(c.DataItem, "StandortID");
            pageControlSettings.Width = Unit.Percentage(100);
            pageControlSettings.TabPages.Add(Resources.View_Tab_Kundendokument_Archiv).SetContent(() =>
            {
                ViewContext.Writer.Write("<h3>" + Resources.View_Title_Kundendokumente_Uebersicht + "</h3>");
                Html.RenderAction("KundendokumentArchivGridView", "Kundendokument", new { id = DataBinder.Eval(c.DataItem, "StandortID") });
            });
        }).Render());
        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);



    });


    if (ViewData["EditError"] != null)
    {
        
        gridArchive.SetEditErrorText((string)ViewData["EditError"]);
    }

}


@gridArchive.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = "StandortID";
}).GetHtml()
