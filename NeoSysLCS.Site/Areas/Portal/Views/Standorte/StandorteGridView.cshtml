@using System.Web.UI.WebControls
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers

@{
    var grid = Html.DevExpress().GridView(settings =>
    {

        settings.Name = "StandorteGridView";
        settings.KeyFieldName = "StandortID";

        GridViewHelper.ApplyDefaultSettings(settings);
        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;

        settings.SettingsContextMenu.Enabled = false;
        settings.Settings.ShowGroupPanel = false;
        settings.CallbackRouteValues = new { Controller = "Standorte", Action = "StandorteGridView", id = ViewData["StandortID"] };

        //Columns

        settings.Columns.Add(column =>
        {
            column.FieldName = "Name";
            column.Caption = Resources.View_Standort_Name;
            column.Width = Unit.Pixel(200);
            column.SetDataItemTemplateContent(container => Html.DevExpress().HyperLink(hyperlink =>
            {
                var keyValue = container.KeyValue;
                hyperlink.Name = "Kundendokument_" + keyValue;
                hyperlink.Properties.Text = DataBinder.Eval(container.DataItem, "Name").ToString();
                hyperlink.NavigateUrl = Url.Action("Index", "Kundendokument", new { id = DataBinder.Eval(container.DataItem, "StandortID") });
            }).Render());
        });

        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);



    });



    if (ViewData["EditError"] != null)
    {
        grid.SetEditErrorText((string)ViewData["EditError"]);
    }

}
@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = "StandortID";
}).GetHtml()
