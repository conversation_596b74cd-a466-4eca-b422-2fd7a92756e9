@using NeoSysLCS.DomainModel.Models;
@using NeoSysLCS.Repositories;
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Site.Helpers
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers

@Html.DevExpress().GridView(
    settings =>
    {
        UnitOfWork unitOfWork = new UnitOfWork();

        GridViewHelper.ApplyUebersetzungsSettings(settings);

        settings.Name = "MassnahmeUebersetzungenGridView_" + ViewData["MassnahmeID"];
        settings.KeyFieldName = "MassnahmeUebersetzungID";

        settings.CallbackRouteValues = new { Controller = "Massnahme", Action = "MassnahmeUebersetzungenGridView", massnahmeID = ViewData["MassnahmeID"] };

        settings.SettingsEditing.BatchUpdateRouteValues = new { Controller = "Massnahme", Action = "MassnahmeUebersetzungenGridViewBatchEditUpdate", massnahmeID = ViewData["MassnahmeID"] };
        /*
        if (User.IsInRole(Role.ProjectManager))
        {
            settings.SettingsEditing.Mode = GridViewEditingMode.Batch;
        }
        */
        settings.SettingsEditing.Mode = GridViewEditingMode.Batch;

        settings.CommandColumn.Visible = false;

        //Columns
        settings.Columns.Add("MassnahmeText");
        settings.Columns.Add("Bemerkung");

        settings.Columns.Add(column =>
        {
            column.FieldName = "Link";
            column.ReadOnly = true;
            column.Caption = Resources.View_News_Link;
            column.SetDataItemTemplateContent(container =>
            {

                var url = DataBinder.Eval(container.DataItem, "Link");
                string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, "Link");
                ViewContext.Writer.Write(StringHelper.TruncateHtml(htmlLink, 50));
            });
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "SpracheID";
            column.Caption = "Sprache";
            column.ReadOnly = true;
            column.ColumnType = MVCxGridViewColumnType.ComboBox;
            column.Settings.FilterMode = ColumnFilterMode.DisplayText;
            var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;

            comboBoxProperties.DataSource = unitOfWork.SpracheRepository.Get().ToList();
            comboBoxProperties.TextField = "Name";
            comboBoxProperties.ValueField = "SpracheID";
            comboBoxProperties.ValueType = typeof(int);
        });

        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);

        settings.ClientSideEvents.BatchEditStartEditing = @"function(s,e){
                currentGrid = s;
            }";


        settings.ClientSideEvents.ClipboardCellPasting = @"function(s,e){
                e.cancel = currentGrid != s;
            }";

    }).BindToLINQ(string.Empty, string.Empty, (s, e) =>
    {
        e.QueryableSource = Model;
        e.KeyExpression = "MassnahmeUebersetzungID";
    }).GetHtml()