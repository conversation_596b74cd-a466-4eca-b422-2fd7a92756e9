@using System.Web.UI.WebControls
@using NeoSysLCS.Repositories;
@using NeoSysLCS.Resources.Properties;


@Html.DevExpress().GridView(
    settings =>
    {
        UnitOfWork unitOfWork = new UnitOfWork();

        //GridViewHelper.ApplyDefaultSettings(settings);
        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;

        settings.Name = "gvRowSelection";
        settings.KeyFieldName = "Id";
        settings.Width = Unit.Percentage(100);
        settings.CallbackRouteValues = new { Controller = "StandortAdministration", Action = "UserRowSelectionPartial", shortcutID = ViewData["ShortcutID"], standortID = ViewData["StandortID"] };
        settings.ControlStyle.CssClass = "grid pull-right";
        settings.Styles.AlternatingRow.Enabled = DefaultBoolean.True;
        settings.Settings.AutoFilterCondition = AutoFilterCondition.Contains;

        MVCxGridViewColumn firstNameColumn = settings.Columns.Add("FullName");
        firstNameColumn.Caption = Resources.Colunm_Name;
        //needed for selectionhelper
        settings.SettingsCookies.StoreFiltering = false;

        ////setting preselected
        settings.PreRender = (sender, e) =>
        {
            MVCxGridView gridView = sender as MVCxGridView;
            if (gridView != null)
                foreach (var obj in unitOfWork.UserRepository.GetAllByShortcut(Convert.ToInt32(ViewData["ShortcutID"])))//GetAllByShortcut(Convert.ToInt32(ViewData["ShortcutID"]))/*unitOfWork.UserRepository.GetAllByStandort(Convert.ToInt32(ViewData["StandortID"]))*/)
                {
                    gridView.Selection.SelectRowByKey(obj.Id);
                }
        };


        settings.CommandColumn.Visible = true;
        settings.CommandColumn.ShowSelectCheckbox = true;
        settings.SettingsPager.Visible = true;
        settings.Settings.ShowFilterRow = true;
        settings.Settings.ShowFilterRowMenu = true;
        settings.Settings.ShowFilterBar = GridViewStatusBarMode.Auto;
        settings.SettingsBehavior.AllowSelectByRowClick = false;

        settings.SettingsPager.Visible = true;
        //settings.ClientSideEvents.SelectionChanged = "SelectionChanged";

        //register events for selectionhelper
        settings.ClientSideEvents.Init = @"function(s, e){
            SelectionInit(" +
                                      ViewData["ShortcutID"] + @",
                                   s,
                                   SelectedRows" + @",
                                   '#count" + @"',
                                   '#Productresult" + @"');
                                    }";
        settings.ClientSideEvents.SelectionChanged = @"function(s,e){
                SelectionChanged(" + ViewData["ShortcutID"] + @", e);
            }";


    }).BindToLINQ(string.Empty, string.Empty, (s, e) =>
    {
        var unitOfWork = new UnitOfWork();
        var query = unitOfWork.UserRepository.GetAllUserViewModelsCurrentUserByStandortId(Convert.ToInt32(ViewData["StandortID"]));
        e.QueryableSource = query;
        e.KeyExpression = "Id";
    }).GetHtml()


