@using System.Web.UI.WebControls
@using NeoSysLCS.Resources.Properties

@Html.DevExpress().PageControl(pageControlSettings =>
{
    pageControlSettings.Name = "KundendokumentPageControl";
    pageControlSettings.Width = Unit.Percentage(100);
    pageControlSettings.SaveStateToCookies = true;
    //Fixed NEOS-298 Speicherbutton nicht sichtbar bei Masteransicht
    pageControlSettings.ClientSideEvents.ActiveTabChanging = "OnActiveTabChanging";//@"function(s,e){e.reloadContentOnCallback = true}";

    pageControlSettings.CallbackRouteValues = new { Controller = "StandortAdministration", Action = "KundendokumentPageControlCallbacksPartial", spracheID = ViewData["SpracheID"] };

    pageControlSettings.TabPages.Add(Resources.View_Auswertungen_Tab_Verantwortlichkeiten).SetContent(() =>
    {
        ViewData["Ansicht"] = "Verantwortlichkeiten";
        ViewContext.Writer.Write("<div style='display:inline-block'>");
        ViewContext.Writer.Write("<h2 style='display:inline-block'>" + Resources.View_Auswertungen_Tab_Verantwortlichkeiten + "</h2>");
        ViewContext.Writer.Write("<button type='button' class='btn btn-default' style='border: none; background-color: transparent; outline: none; padding-left: 5px; padding-right: 0px; padding-bottom:20px; padding-top: 5px' onclick=" + "javascript:ShowDetailPopup('/Portal/FAQPortal/ShowFAQPopup?view=Verantwortlichkeiten','pcModalMode_FAQ_" + ViewData["Ansicht"] + "');>" + "<img src='/Content/images/Question_Mark.png' style='width: 20px; height: 20px' /></button>");
        ViewContext.Writer.Write("</div>");
        Html.RenderAction(
            "ShortcutAdministration",
            "StandortAdministration");
    });

    pageControlSettings.TabPages.Add(Resources.Menu_Masteransicht, "Masteransicht").SetContent(() =>
    {
        ViewData["Ansicht"] = "Masteransicht";
        ViewContext.Writer.Write("<div style='display:inline-block'>");
        ViewContext.Writer.Write("<h5 style='display:inline-block'>" + Resources.View_Masteransicht_Hint + "</h5>");
        ViewContext.Writer.Write("<button type='button' class='btn btn-default' style='border: none; background-color: transparent; outline: none; padding-left: 5px; padding-right: 0px; padding-bottom:20px; padding-top: 5px' onclick=" + "javascript:ShowDetailPopup('/Portal/FAQPortal/ShowFAQPopup?view=Masteransicht','pcModalMode_FAQ_" + ViewData["Ansicht"] + "');>" + "<img src='/Content/images/Question_Mark.png' style='width: 20px; height: 20px' /></button>");
        ViewContext.Writer.Write("</div>");
        ViewContext.Writer.Write("<br></br>");
        Html.RenderAction(
            "KundendokumentMasterGridView",
            "StandortAdministration");
    });

    pageControlSettings.TabPages.Add(Resources.View_Spalten_Beschriftungen).SetContent(() =>
    {
        ViewData["Ansicht"] = "Beschriftung";
        ViewContext.Writer.Write("<div style='display:inline-block'>");
        ViewContext.Writer.Write("<h2 style='display:inline-block'>" + Resources.View_Spalten_Beschriftungen + "</h2>");
        ViewContext.Writer.Write("<button type='button' class='btn btn-default' style='border: none; background-color: transparent; outline: none; padding-left: 5px; padding-right: 0px; padding-bottom:20px; padding-top: 5px' onclick=" + "javascript:ShowDetailPopup('/Portal/FAQPortal/ShowFAQPopup?view=SpaltenBeschriftung','pcModalMode_FAQ_" + ViewData["Ansicht"] + "');>" + "<img src='/Content/images/Question_Mark.png' style='width: 20px; height: 20px' /></button>");
        ViewContext.Writer.Write("</div>");

        Html.RenderAction("KundendokumentSpaltenlabelsGridView", "Kundendokument");
    });

    pageControlSettings.TabPages.Add(Resources.Entitaet_StandortObjekt_Plural, "Standortobjekte").SetContent(() =>
    {
        ViewData["Ansicht"] = "Standortobjekte";
        ViewContext.Writer.Write("<div style='display:inline-block'>");
        ViewContext.Writer.Write("<h5 style='display:inline-block'>" + Resources.View_Standortobjekte_Hint + "</h5>");
        ViewContext.Writer.Write("<button type='button' class='btn btn-default' style='border: none; background-color: transparent; outline: none; padding-left: 5px; padding-right: 0px; padding-bottom:20px; padding-top: 5px' onclick=" + "javascript:ShowDetailPopup('/Portal/FAQPortal/ShowFAQPopup?view=StandortObjekte','pcModalMode_FAQ_" + ViewData["Ansicht"] + "');>" + "<img src='/Content/images/Question_Mark.png' style='width: 20px; height: 20px' /></button>");
        ViewContext.Writer.Write("</div>");
        ViewContext.Writer.Write("<br></br>");
        Html.RenderAction("AllStandortObjekteGridView", "KundendokumentStandortObjekt");
    });
}).GetHtml()

<script type="text/javascript">
    //<![CDATA[

    function ShowDetailPopup(url, win) {
        modal = eval(win);
        if (window.height < 600) {
            modal.SetHeight(window.height - 50);
        } else {
            modal.SetHeight(600);
        }

        if (window.width < 800) {
            modal.SetWidth(window.width - 50);
        } else {
            modal.SetWidth(800);
        }

        modal.SetContentUrl(url);
        modal.Show();
    }

    function CloseDetailPopup(win) {
        modal = eval(win);
        modal.Hide();
    }

    function AdjustSize() {
        $('.dxgvStatusBar_ModernoNeosys a').each(function (index) {
            $(this).css("position", "absolute");
            $(this).css("left", window.width - 600 + (index * 55));
            $(this).parent().css("padding-top", "25px");
            $(this).parent().css("padding-bottom", "25px");

        });
        $(window).bind("scroll", function () {

            $('.dxgvStatusBar_ModernoNeosys a').each(function (index) {
                $(this).css("left", window.width - 600 + (index * 55) + $(document).scrollLeft());
            });
        });


        $(window).bind("resize", function () {
            $('.dxgvStatusBar_ModernoNeosys a').each(function (index) {
                $(this).css("left", window.width - 600 + (index * 55) + $(document).scrollLeft());
            });
        });
    }

    function OnActiveTabChanging(s, e) {
        e.reloadContentOnCallback = true;
    }
    // ]]>
</script>

@Html.DevExpress().PopupControl(settings =>
{

    settings.Name = "pcModalMode_FAQ_" + ViewData["Ansicht"];
    //settings.ScrollBars = ScrollBars.Auto;
    settings.ShowPageScrollbarWhenModal = true;
    settings.ResizingMode = ResizingMode.Live;
    settings.ShowRefreshButton = false;
    settings.AutoUpdatePosition = true;
    settings.AllowDragging = true;
    settings.CloseAction = CloseAction.CloseButton;
    settings.PopupAnimationType = AnimationType.None;
    settings.HeaderText = "FAQ";
    settings.Modal = true;
    settings.AllowResize = true;
    settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
    settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
    settings.ClientSideEvents.Init = "function(s, e) { s.SetWidth(400); }";
    settings.ClientSideEvents.Shown = "function(s, e) { s.SetWidth(400); }";
}).GetHtml()