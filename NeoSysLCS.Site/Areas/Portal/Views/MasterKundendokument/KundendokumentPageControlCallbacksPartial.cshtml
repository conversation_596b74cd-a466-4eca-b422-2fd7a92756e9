@using System.Web.UI.WebControls
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Utilities.Export
@using NeoSysLCS.Site.Areas.Portal.Controllers
@using System.Web.UI.WebControls
@using NeoSysLCS.Repositories
@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Resources.Properties

@model List<int>

@Html.DevExpress().PageControl(pageControlSettings =>
{
    UnitOfWork unitOfWork = new UnitOfWork();
    pageControlSettings.Name = "KundendokumentPageControl";
    pageControlSettings.Width = Unit.Percentage(100);
    pageControlSettings.SaveStateToCookies = true;
    pageControlSettings.CallbackRouteValues = new { Controller = "MasterKundendokument", Action = "KundendokumentPageControlCallbacksPartial" };
    pageControlSettings.ClientSideEvents.ActiveTabChanging = "OnActiveTabChanging";
    pageControlSettings.TabPages.Add(Resources.View_Kundendokument_TabForderungen).SetContent(() =>
    {
        ViewData["Ansicht"] = "Forderungen";

        ViewContext.Writer.Write("<div style='display:inline-block'>");
        ViewContext.Writer.Write("<h2 style='display:inline-block'>" + Resources.View_Kundendokument_TabForderungen + "</h2>");
        ViewContext.Writer.Write("<button type='button' class='btn btn-default' style='border: none; background-color: transparent; outline: none; padding-left: 5px; padding-right: 0px; padding-bottom:20px; padding-top: 5px' onclick=" + "javascript:ShowDetailPopup('/Portal/FAQPortal/ShowFAQPopup?view=Forderungen','pcModalMode_FAQ_" + ViewData["Ansicht"] + "');>" + "<img src='/Content/images/Question_Mark.png' style='width: 20px; height: 20px' /></button>");
        ViewContext.Writer.Write("</div>");

        ViewContext.Writer.Write("<div>");

        ViewContext.Writer.Write("<a class='btn btn-default' type='button' onclick='MasterPortalKundendokumentForderungenGridView.CollapseAll();return false;' >" +
                                 "<i class='fa fa-minus-square'></i>&nbsp;" +
                                 Resources.Allgemein_CollapseAllRows +
                                 "</a>&nbsp;");
        ViewContext.Writer.Write("<a class='btn btn-default' type='button' onclick='MasterPortalKundendokumentForderungenGridView.ExpandAll();return false;' >" +
                                 "<i class='fa fa-plus-square'></i>&nbsp;" +
                                 Resources.Allgemein_ExpandAllRows +
                                 "</a>&nbsp;");
        ViewContext.Writer.Write("<a class='btn btn-default' type='button' onclick='MasterPortalKundendokumentForderungenGridView.ShowCustomizationDialog();return false;' >" +
                                 "<i class='fa fas fa-cog'></i>&nbsp;" +
                                 Resources.View_KundendokumentForderungen_Dialog +
                                 "</a>&nbsp;");
        ViewContext.Writer.Write("<a class='btn btn-default' type='button' onclick='javascript:ShowNewPopup(this)'; >" +
                                 "<i class='fa fas fa-filter'></i>&nbsp;" +
                                 Resources.Filter_Standortobjekt +
                                 "</a>&nbsp;");
        ViewContext.Writer.Write("<a class='btn btn-default' type='button' onclick='javascript:ShowNewPopupExportForderungen(this)'; >" +
                                 "<i class='fa fas fa-filter'></i>&nbsp;" +
                                 Resources.XLSX_Export_Format +
                                 "</a>&nbsp;");

        ViewContext.Writer.Write("</div>");

        var kundendokumentController = new MasterKundendokumentController();
        var forderungenViewModels = kundendokumentController.InitializeKundendokumentForderungenGridView();

        Html.RenderPartial("KundendokumentForderungenGridView", forderungenViewModels);
    });
    pageControlSettings.TabPages.Add(Resources.View_Kundendokument_TabErlassfassungen).SetContent(() =>
    {
        ViewData["Ansicht"] = "Gesetzesliste";
        ViewContext.Writer.Write("<div style='display:inline-block'>");
        ViewContext.Writer.Write("<h2 style='display:inline-block'>" + Resources.View_Kundendokument_TabErlassfassungen + "</h2>");
        ViewContext.Writer.Write("<button type='button' class='btn btn-default' style='border: none; background-color: transparent; outline: none; padding-left: 5px; padding-right: 0px; padding-bottom:20px; padding-top: 5px' onclick=" + "javascript:ShowDetailPopup('/Portal/FAQPortal/ShowFAQPopup?view=Gesetzesliste','pcModalMode_FAQ_" + ViewData["Ansicht"] + "');>" + "<img src='/Content/images/Question_Mark.png' style='width: 20px; height: 20px' /></button>");
        ViewContext.Writer.Write("</div>");

        ViewContext.Writer.Write("<div>");
        ViewContext.Writer.Write("<a class='btn btn-default' type='button' onclick='MasterErlassfassungenGridView.CollapseAll();return false;' >" +
                                 "<i class='fa fa-minus-square'></i>&nbsp;" +
                                    Resources.Allgemein_CollapseAllRows +
                                 "</a>&nbsp;");
        ViewContext.Writer.Write("<a class='btn btn-default' type='button' onclick='MasterErlassfassungenGridView.ExpandAll();return false;' >" +
                               "<i class='fa fa-plus-square'></i>&nbsp;" +
                                  Resources.Allgemein_ExpandAllRows +
                               "</a>&nbsp;");
        ViewContext.Writer.Write("<a class='btn btn-default' type='button' onclick='MasterErlassfassungenGridView.ShowCustomizationDialog();return false;' >" +
                      "<i class='fa fas fa-cog'></i>&nbsp;" +
                         Resources.View_KundendokumentForderungen_Dialog +
                      "</a>&nbsp;");
        ViewContext.Writer.Write("<a class='btn btn-default' type='button' onclick='javascript:ShowNewPopupExportErlassfassung(this)'; >" +
              "<i class='fa fas fa-filter'></i>&nbsp;" +
                 Resources.XLSX_Export_Format +
              "</a>&nbsp;");

        ViewContext.Writer.Write("</div>");

        var kundendokumentController = new MasterKundendokumentController();
        var erlassfassungenViewModels = kundendokumentController.InitializeKundendokumentErlassfassungenGridView();

        Html.RenderPartial("KundendokumentErlassfassungenGridView", erlassfassungenViewModels);
    });

}).GetHtml()

<script type="text/javascript">
    //<![CDATA[

    function ShowDetailPopup(url, win) {
        modal = eval(win);

        if (window.height < 600) {
            modal.SetHeight(window.height - 50);
        } else {
            modal.SetHeight(600);
        }

        if (window.width < 800) {
            modal.SetWidth(window.width - 50);
        } else {
            modal.SetWidth(800);
        }

        modal.SetContentUrl(url);
        modal.Show();
    }
    // ]]>
</script>

@Html.DevExpress().PopupControl(settings =>
{

    settings.Name = "pcModalMode_FAQ_" + ViewData["Ansicht"];
    //settings.ScrollBars = ScrollBars.Auto;
    settings.ShowPageScrollbarWhenModal = true;
    settings.ResizingMode = ResizingMode.Live;
    settings.ShowRefreshButton = false;
    settings.AutoUpdatePosition = true;
    settings.AllowDragging = true;
    settings.CloseAction = CloseAction.CloseButton;
    settings.PopupAnimationType = AnimationType.None;
    settings.HeaderText = "FAQ";
    settings.Modal = true;
    settings.AllowResize = true;
    settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
    settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
    settings.ClientSideEvents.Init = "function(s, e) { s.SetWidth(400); }";
    settings.ClientSideEvents.Shown = "function(s, e) { s.SetWidth(400); }";
}).GetHtml()