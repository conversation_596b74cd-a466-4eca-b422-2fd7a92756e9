@using System.Web.UI.WebControls
@using NeoSysLCS.Repositories
@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Resources.Properties

@model List<int>

@{
    ViewBag.Title = Resources.View_Masterdokument_Title;

    ViewContext.Writer.Write("<div style='display:inline-block'>");
    ViewContext.Writer.Write("<h2 style='display:inline-block'>" + ViewBag.Title + "</h2>");
    ViewContext.Writer.Write("</div>");

}

@section breadcrumb{
    <a href="#" onclick="history.go(-1); return false;" title="@Resources.Navigation_Zurueck"><i class="fa fa-arrow-left fa-lg"></i></a> |
    <span class="breadcrumb-noesys-navigation">
        <a href="/">@Resources.View_Breadcrumb_Home</a>
        <span> > </span>
        @Html.ActionLink("Standorte", "Index", "Standorte", new { area = "Portal" }, new { @class = "" })
        <span> > </span>
        <span>@ViewBag.Title</span>
    </span>

}

<script type="text/javascript">


    //Sets the height of the grid according to the size of the page
    function AdjustSize(s, e) {
        //adjust the scroll position if the gridview is edited
        //if (scrollY != null)
        //{
        //    window.scrollTo(scrollX, ASPxClientUtils.GetDocumentScrollTop());
        //}
        //AppendStylesToHeader(s.cpClasses);
        Grid_Init(s, e);
        $('.dxgvStatusBar_ModernoNeosys a').each(function (index) {
            $(this).css("position", "absolute");
            $(this).css("left", window.width - 400 + (index * 55));
            $(this).parent().css("padding-top", "25px");
            $(this).parent().css("padding-bottom", "25px");

        });
        $(window).bind("scroll", function () {

            $('.dxgvStatusBar_ModernoNeosys a').each(function (index) {
                $(this).css("left", window.width - 400 + (index * 55) + $(document).scrollLeft());
            });
        });


        $(window).bind("resize", function () {
            $('.dxgvStatusBar_ModernoNeosys a').each(function (index) {
                $(this).css("left", window.width - 400 + (index * 55) + $(document).scrollLeft());
            });
        });

        //init column size
        SetHeaderColumnSize();

        //initial Header Position
        SetHeaderPosition();

        $(window).bind("scroll", function () {
            //Header Position on scroll
            SetHeaderPosition();
        });
    }

    function Grid_Init(s, e) {
        if (s != null) {
            var dialogHelper = s.GetCustDialogHelper();
            var orignalFunc = dialogHelper.GetColumnItemMap;

            dialogHelper.GetColumnItemMap = function (key, columnIndex) {
                var map = orignalFunc.call(dialogHelper, key, columnIndex);
                var isFieldChooserKey = key === dialogHelper.CreatePageAreaMapKey(3, true);
                //var targetColumnIndex = s.GetColumnByField('StandortObjektTitel').index;
                if (isFieldChooserKey && 0 === columnIndex) {
                    return map.slice(0, 2);
                }
                return map;
            };
        }
    }

    function SetHeaderColumnSize() {
        //set fixed header row

        //first row of the grid with cells containing the current sizes
        var sizeTds = $("#MasterPortalKundendokumentForderungenGridView_DXMainTable tbody tr:first").children();

        //column collection of Grid - containing the min sizes
        var columns = MasterPortalKundendokumentForderungenGridView.columns;

        //header row cells
        var headTds = $("#MasterPortalKundendokumentForderungenGridView_DXHeadersRow0").children();

        //set size of inner table in order to set the width of the header cells (gets size of size row or min size of column
        //and substracts padding of header cell (left: 10; right: 10; border: 1 = 21)

        for (var i = 0; i < headTds.length; i++) {

            //check if it is a cell of an indentColumn
            if (i >= MasterPortalKundendokumentForderungenGridView.indentColumnCount) {

                //from the current index we need to get the index of the column in the column array (needed if column order has been changed)
                var columnNr = MasterPortalKundendokumentForderungenGridView.headerMatrix.matrix[0][i - MasterPortalKundendokumentForderungenGridView.indentColumnCount];
                if ($(sizeTds[i]).width() < columns[columnNr].minWidth) {
                    //if current Width is smaller then minWidth -> we need to use the minWidth (in some cases this function is called
                    //earlier then setting the minWidth on columns (e.g. ungroup columns)
                    $(headTds[i]).find('table').width(columns[columnNr].minWidth - 21);
                } else {
                    //gets the width from the size cell
                    $(headTds[i]).find('table').width($(sizeTds[i]).width() - 21);
                }
            } else {
                //gets the width from the size cell
                $(headTds[i]).find('table').width($(sizeTds[i]).width() - 21);
            }

            $(headTds[i]).find('table').css("table-layout", "fixed");
            $(headTds[i]).find('table tr td:last-child').css("width", "40px");

        }
    }

    function SetHeaderPosition() {

        if ($("#MasterPortalKundendokumentForderungenGridView_DXHeadersRow0").is(':visible')) {
            //offset to top of filter row
            var tableOffset = $("#MasterPortalKundendokumentForderungenGridView_DXFilterRow").offset().top;
            //offset to top of headerrow
            var offset = $(this).scrollTop();

            if (offset > tableOffset) {
                //headerrow is displayed below filterrow: so we need to set the margin left
                if (!$("#MasterPortalKundendokumentForderungenGridView_DXHeadersRow0").hasClass('table_positionfixed')) {
                    $("#MasterPortalKundendokumentForderungenGridView_DXHeadersRow0").addClass('table_positionfixed');
                }

                var pagewrapperMargin = parseInt($("#page-wrapper").css("margin-left"));
                var pagewrapperBorder = parseInt($("#page-wrapper").css("border-left-width"));
                var pagewrapperPadding = parseInt($("#page-wrapper").css("padding-left"));
                //var pageControlPadding = parseInt($("#KundendokumentPageControl_CC").css("padding-left"));
                //var pageControlBorder = parseInt($("#KundendokumentPageControl_CC").css("border-left-width"));

                //var offsetLeft = pagewrapperMargin + pagewrapperPadding + pageControlPadding + pageControlBorder + pagewrapperBorder + 1;
                var offsetLeft = pagewrapperMargin + pagewrapperPadding + pagewrapperBorder + 17;

                $("#MasterPortalKundendokumentForderungenGridView_DXHeadersRow0").css("left", 0);
                $("#MasterPortalKundendokumentForderungenGridView_DXHeadersRow0").css("margin-left", offsetLeft - $(document).scrollLeft());

            }

            else if (offset <= tableOffset && $("#MasterPortalKundendokumentForderungenGridView_DXHeadersRow0").hasClass('table_positionfixed')) {
                //headerrow is not below filterrow but position is fixed: so we remove positon fixed
                $("#MasterPortalKundendokumentForderungenGridView_DXHeadersRow0").removeClass('table_positionfixed');

                $("#MasterPortalKundendokumentForderungenGridView_DXHeadersRow0").css("left", "");
                $("#MasterPortalKundendokumentForderungenGridView_DXHeadersRow0").css("margin-left", "");
            }
        }
    }

    var objId, name, beschreibung, filter;
    function ShowNewPopup(modal, nothing, kundendokumentID) {
        if (window.height < 800) {
            NewStandortobj.SetHeight(window.height - 50);
        } else {
            NewStandortobj.SetHeight(800);
        }

        if (window.width < 1250) {
            NewStandortobj.SetWidth(window.width - 50);
        } else {
            NewStandortobj.SetWidth(1250);
        }
        NewStandortobj.Show();
    }

    function ShowNewPopupExportForderungen(modal, nothing, kundendokumentID) {
        if (window.height < 50) {
            ExportForderungen.SetHeight(window.height - 50);
        } else {
            ExportForderungen.SetHeight(50);
        }

        if (window.width < 400) {
            ExportForderungen.SetWidth(window.width - 50);
        } else {
            ExportForderungen.SetWidth(400);
        }
        ExportForderungen.Show();
    }

    function ShowNewPopupExportErlassfassung(modal, nothing, kundendokumentID) {
        if (window.height < 50) {
            ExportErlassfassung.SetHeight(window.height - 50);
        } else {
            ExportErlassfassung.SetHeight(50);
        }

        if (window.width < 400) {
            ExportErlassfassung.SetWidth(window.width - 50);
        } else {
            ExportErlassfassung.SetWidth(400);
        }
        ExportErlassfassung.Show();
    }

    function GetSelectedFieldValuesCallback(values) {

        var fields = new Array();

        //check if objID not null
        for (var j = 0; j < values.length; j++) {
            if (values[j][0] != null) {
                fields.push(values[j]);
            }
        }
        var filter = new String();
        var standortObjekt = "StandortObjektTitel = ";
        for (var j = 0; j < fields.length; j++) {
            name = fields[j][1];
            if (j == fields.length - 1) {
                filter += standortObjekt + "'" + name.replace(/'/g, "''") + "'";
            } else {
                filter += standortObjekt + "'" + name.replace(/'/g, "''") + "'" + " OR ";
            }
        }
        MasterPortalKundendokumentForderungenGridView.ApplyFilter(filter);
    }

    function OnActiveTabChanging(s, e) {
        e.reloadContentOnCallback = (e.tab.name == "Standortobjekte") ? true : false;
    }

    //Fixed NEOS-305 reset timer if starting to edit new cell
    var timerHandle = -1;
    function OnBatchStartEditing(s, e) {
        clearTimeout(timerHandle);
    }

    function OnBatchEditEndEditing(s, e) {
        timerHandle = setTimeout(function () {
            if (s.batchEditApi.HasChanges()) {
                s.UpdateEdit();
            }
        }, 600000);
    }

    var customStyleElement = null;
    function AppendStylesToHeader(styleRules) {
        RemoveCustomStyleElement();
        var container = document.createElement("DIV");
        container.innerHTML = "<style type='text/css'>" + styleRules.join("") + "</style>";

        var head = document.getElementsByTagName("HEAD")[0];
        customStyleElement = container.getElementsByTagName("STYLE")[0];
        head.appendChild(customStyleElement);
    }

    function RemoveCustomStyleElement() {
        if (customStyleElement) {
            customStyleElement.parentNode.removeChild(customStyleElement);
            customStyleElement = null;
        }
    }

    function ShowDetailPopup(url, win) {
        modal = eval(win);

        if (window.height < 600) {
            modal.SetHeight(window.height - 50);
        } else {
            modal.SetHeight(600);
        }

        if (window.width < 800) {
            modal.SetWidth(window.width - 50);
        } else {
            modal.SetWidth(800);
        }

        modal.SetContentUrl(url);
        modal.Show();
    }

</script>

<style type="text/css">
    .customChooserClass .customItemClass div:first-child {
        display: none;
    }

    .customChooserClass > div {
        display: flex;
        flex-direction: column;
    }

    .addScroll > div {
        overflow-y: scroll;
        height: 750px;
    }
</style>

@Html.Partial("_HtmlEditPopup", false)
@Html.Partial("_NewStandortObjektGridViewPopup")
@Html.Partial("_ExportPartialPortalForderungen")
@Html.Partial("_ExportPartialPortalErlassfassung")
@Html.Partial("KundendokumentPageControlCallbacksPartial", Model)

@Html.DevExpress().PopupControl(settings =>
{

    settings.Name = "pcModalMode_Massnahme_Erfassen_Checklist";
    settings.ScrollBars = ScrollBars.Auto;
    settings.MaxHeight = 550;
    settings.ShowPageScrollbarWhenModal = true;
    settings.ResizingMode = ResizingMode.Live;
    settings.ShowRefreshButton = false;
    settings.AutoUpdatePosition = true;
    settings.AllowDragging = true;
    settings.CloseAction = CloseAction.CloseButton;
    settings.PopupAnimationType = AnimationType.None;
    settings.HeaderText = Resources.Allgemein_Button_Massnahme;
    settings.Modal = true;
    settings.AllowResize = true;
    settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
    settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
    settings.ClientSideEvents.Init = "function(s, e) { s.SetWidth(800) }";
    settings.ClientSideEvents.Shown = "function(s, e) { s.SetWidth(800) }";
}).GetHtml()

@Html.DevExpress().PopupControl(settings =>
{

    settings.Name = "pcModalMode_Massnahme_Erfassen_Gesetzesliste";
    settings.ScrollBars = ScrollBars.Auto;
    settings.ShowPageScrollbarWhenModal = true;
    settings.ResizingMode = ResizingMode.Live;
    settings.MaxHeight = 550;
    settings.ShowRefreshButton = false;
    settings.AutoUpdatePosition = true;
    settings.AllowDragging = true;
    settings.CloseAction = CloseAction.CloseButton;
    settings.PopupAnimationType = AnimationType.None;
    settings.HeaderText = Resources.Allgemein_Button_Massnahme;
    settings.Modal = true;
    settings.AllowResize = false;
    settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
    settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
    settings.ClientSideEvents.Init = "function(s, e) { s.SetWidth(800); }";
    settings.ClientSideEvents.Shown = "function(s, e) { s.SetWidth(800); }";

}).GetHtml()

@Html.DevExpress().PopupControl(settings =>
{

    settings.Name = "pcModalMode_Massnahme_Erfassen_Forderungen";
    settings.ScrollBars = ScrollBars.Auto;
    settings.ShowPageScrollbarWhenModal = true;
    settings.ResizingMode = ResizingMode.Live;
    settings.MaxHeight = 550;
    settings.ShowRefreshButton = false;
    settings.AutoUpdatePosition = true;
    settings.AllowDragging = true;
    settings.CloseAction = CloseAction.CloseButton;
    settings.PopupAnimationType = AnimationType.None;
    settings.HeaderText = Resources.Allgemein_Button_Massnahme;
    settings.Modal = true;
    settings.AllowResize = false;
    settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
    settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
    settings.ClientSideEvents.Init = "function(s, e) { s.SetWidth(800); }";
    settings.ClientSideEvents.Shown = "function(s, e) { s.SetWidth(800); }";

}).GetHtml()

@Html.DevExpress().PopupControl(settings =>
{

    settings.Name = "pcModalMode_Shortcut";
    settings.ScrollBars = ScrollBars.Auto;
    settings.ShowPageScrollbarWhenModal = true;
    settings.ResizingMode = ResizingMode.Live;
    settings.MaxHeight = 550;
    settings.ShowRefreshButton = false;
    settings.AutoUpdatePosition = true;
    settings.AllowDragging = true;
    settings.CloseAction = CloseAction.CloseButton;
    settings.PopupAnimationType = AnimationType.None;
    settings.HeaderText = "Verantwortlichkeit erfassen";
    settings.Modal = true;
    settings.AllowResize = false;
    settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
    settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
    settings.ClientSideEvents.Init = "function(s, e) { s.SetWidth(400); s.SetHeight(250); }";
    settings.ClientSideEvents.Shown = "function(s, e) { s.SetWidth(400); s.SetHeight(250); }";
    settings.ClientSideEvents.Closing = "function(s, e){ location.reload(); }";

}).GetHtml()

@Html.DevExpress().PopupControl(settings =>
{

    settings.Name = "pcModalMode_FAQ_" + ViewData["Ansicht"];
    //settings.ScrollBars = ScrollBars.Auto;
    settings.ShowPageScrollbarWhenModal = true;
    settings.ResizingMode = ResizingMode.Live;
    settings.ShowRefreshButton = false;
    settings.AutoUpdatePosition = true;
    settings.AllowDragging = true;
    settings.CloseAction = CloseAction.CloseButton;
    settings.PopupAnimationType = AnimationType.None;
    settings.HeaderText = "FAQ";
    settings.Modal = true;
    settings.AllowResize = true;
    settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
    settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
    settings.ClientSideEvents.Init = "function(s, e) { s.SetWidth(400); }";
    settings.ClientSideEvents.Shown = "function(s, e) { s.SetWidth(400); }";
}).GetHtml()