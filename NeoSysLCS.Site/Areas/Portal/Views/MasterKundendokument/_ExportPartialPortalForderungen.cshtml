@using NeoSysLCS.Resources.Properties

@{
    Html.DevExpress().PopupControl(settings =>
    {
        settings.Name = "ExportForderungen";
        settings.Width = 350;
        settings.Height = 100;
        settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
        settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
        settings.AllowDragging = true;
        settings.EnableHotTrack = true;
        settings.ShowFooter = true;
        settings.Modal = true;
        settings.HeaderText = "Kundendokument exportieren";
        settings.CloseAction = CloseAction.CloseButton;

        settings.SetContent(() =>
        {
            ViewContext.Writer.Write(Resources.Export_Popup_1 + "<br><br>" + Resources.Export_Popup_2);
            Html.DevExpress().HyperLink(hl =>
            {
                hl.Name = "MailLink_masterDokument";
                hl.Properties.Text = "<EMAIL>";
                hl.NavigateUrl = "mailto: <EMAIL>";
                hl.ControlStyle.Font.Underline = true;
            }).Render();
        });

        settings.SetFooterTemplateContent(c =>
        {
            ViewContext.Writer.Write("<div style=\"overflow: hidden\"><div style=\"padding: 3px; float: right;\">");
            Html.DevExpress().Button(
            buttonSettings =>
            {
                buttonSettings.Name = "btnUpdate_masterDokument";
                buttonSettings.Width = 50;
                buttonSettings.Text = Resources.XSL_Export_Export;
                buttonSettings.UseSubmitBehavior = true;
                buttonSettings.ControlStyle.CssClass = "btn btn-sm pull-right";
                buttonSettings.Style.Add(HtmlTextWriterStyle.MarginLeft, "10px");
                buttonSettings.ClientSideEvents.Click = string.Format("function(s, e) {{ window.open('{0}'); ExportForderungen.Hide(); }}", @Url.Action("ForderungenExportDataAware", "MasterKundendokument"));
            })
            .Render();
            ViewContext.Writer.Write(" ");
            Html.DevExpress().Button(
                buttonSettings =>
                {
                    buttonSettings.Name = "btnCancel_masterDokument";
                    buttonSettings.Width = 50;
                    buttonSettings.Text = Resources.Button_Abbrechen;
                    buttonSettings.ClientSideEvents.Click = "function(s,e){ ExportForderungen.Hide()}";
                    buttonSettings.ControlStyle.CssClass = "btn btn-sm pull-right";
                    buttonSettings.Style.Add(HtmlTextWriterStyle.MarginLeft, "10px");
                }
            )
            .Render();
            ViewContext.Writer.Write("</div></div>");
        });
    }).Render();
}