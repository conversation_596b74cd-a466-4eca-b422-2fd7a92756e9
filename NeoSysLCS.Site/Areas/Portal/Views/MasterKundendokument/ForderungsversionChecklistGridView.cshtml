@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories;
@using NeoSysLCS.Site.Helpers
@using System.Web.UI.WebControls
@using NeoSysLCS.Resources.Properties

@model IQueryable<NeoSysLCS.Repositories.ViewModels.KundendokumentChecklistQuestionViewModel>

@Html.DevExpress().GridView(
    settings =>
    {
        UnitOfWork unitOfWork = new UnitOfWork();

        GridViewHelper.ApplyUebersetzungsSettings(settings);

        settings.Name = "ForderungsversionChecklistGridView_" + ViewData["KundendokumentForderungsversionID"];
        settings.KeyFieldName = "KundendokumentChecklistQuestionID";

        settings.CallbackRouteValues = new
        {
            Controller = "Kundendokument",
            Action = "ForderungsversionChecklistGridView",
            kundendokumentChecklistID = ViewData["KundendokumentChecklistID"],
            kundendokumentForderungsversionsID = ViewData["KundendokumentForderungsversionID"],
            standortID = ViewData["StandortID"],
            kundendokumentID = ViewData["KundendokumentID"],
            spracheID = ViewData["SpracheID"]
        };
        settings.SettingsEditing.BatchUpdateRouteValues = new
        {
            Controller = "Kundendokument",
            Action = "ChecklistDetailBatchEditUpdate",
            kundendokumentChecklistID = ViewData["KundendokumentChecklistID"],
            kundendokumentForderungsversionsID = ViewData["KundendokumentForderungsversionID"],
            standortID = ViewData["StandortID"],
            kundendokumentID = ViewData["KundendokumentID"],
            spracheID = ViewData["SpracheID"]
        };
        settings.SettingsEditing.BatchEditSettings.StartEditAction = GridViewBatchStartEditAction.Click;

        settings.CommandColumn.Visible = false;
        settings.SettingsLoadingPanel.Mode = GridViewLoadingPanelMode.ShowOnStatusBar;
        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;
        settings.Width = 1750;
        settings.SettingsEditing.Mode = GridViewEditingMode.Batch;
        settings.Settings.ShowFilterRow = true;
        settings.Settings.ShowFilterRowMenu = true;
        settings.Settings.ShowFilterBar = GridViewStatusBarMode.Auto;
        settings.Settings.ShowFilterRowMenuLikeItem = true;
        settings.Settings.AutoFilterCondition = AutoFilterCondition.Contains;

        settings.Columns.Add(column =>
        {
            column.FieldName = "HeaderTitle";
            column.Caption = Resources.View_ChecklistQuestion_Abschnitt;
            column.PropertiesEdit.EncodeHtml = false;
            column.GroupIndex = 0;
            column.MaxWidth = 50;
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "Numeration";
            column.Caption = Resources.Enitaet_ChecklistQuestion_Nr;
            column.Width = Unit.Percentage(5);
            column.EditFormSettings.Visible = DefaultBoolean.False;
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "Title";
            column.Caption = Resources.Enitaet_ChecklistQuestion_Titel;
            column.HeaderStyle.Wrap = DefaultBoolean.True;
            column.ColumnType = MVCxGridViewColumnType.Memo;
            column.Width = Unit.Percentage(55);
            var memoProp = column.PropertiesEdit as MemoProperties;
            if (memoProp != null)
            {
                memoProp.EncodeHtml = false;
            }
            column.ReadOnly = true;
            column.EditFormSettings.Visible = DefaultBoolean.False;
            column.PropertiesEdit.EncodeHtml = false;
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "Answer";
            column.Caption = Resources.Enitaet_ChecklistQuestion_Antwort;
            column.HeaderStyle.Wrap = DefaultBoolean.True;
            column.ColumnType = MVCxGridViewColumnType.ComboBox;
            column.Settings.FilterMode = ColumnFilterMode.DisplayText;
            column.Settings.AllowHeaderFilter = DefaultBoolean.True;
            column.SettingsHeaderFilter.Mode = GridHeaderFilterMode.CheckedList;
            column.Width = Unit.Percentage(20);
            var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;

            var list = from KundendokumentQuestionAnswer value in Enum.GetValues(typeof(KundendokumentQuestionAnswer))
                       select new
                       {
                           Id = (int)value,
                           Name = value.GetTranslation()
                       };

            comboBoxProperties.DataSource = list;
            comboBoxProperties.ValueField = "Id";
            comboBoxProperties.TextField = "Name";
            comboBoxProperties.ValueType = typeof(Int32);
            comboBoxProperties.ValidationSettings.Display = Display.Dynamic;
            comboBoxProperties.ValidationSettings.RequiredField.IsRequired = true;
            comboBoxProperties.ValidationSettings.RequiredField.ErrorText = NeoSysLCS.Resources.Properties.Resources.Fehler_FehlendeWerte;
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "SharedImage";
            column.Caption = Resources.Enitaet_ChecklistQuestion_Image;
            column.ReadOnly = true;
            column.Width = Unit.Percentage(20);
            column.SetDataItemTemplateContent(c =>
            {
                ViewContext.Writer.Write(String.Format("<img width=\"150px\" src=\"{0}\" />", DataBinder.Eval(c.DataItem, "SharedImage")));
            });
        });

        settings.Columns.Add(column =>
        {
            column.Caption = Resources.Entitaet_Massnahme_Singular;
            column.MinWidth = 150;
            column.ReadOnly = true;
            column.HeaderStyle.Wrap = DefaultBoolean.True;
            column.Settings.AllowAutoFilter = DefaultBoolean.False;
            column.SetDataItemTemplateContent(container =>
            {
                var checklistID = ViewData["KundendokumentChecklistID"];

                Html.DevExpress().Button(btn =>
                {
                    btn.Text = Resources.Allgemein_Button_Massnahme;
                    btn.Name = "Massnahme_Checklist_" + container.KeyValue;
                    btn.Attributes.Add("onClick", "javascript:ShowDetailPopup('" + Url.Action("ShowMassnahmePopupForSuva", "Kundendokument", new { checklistID = checklistID }) + "','pcModalMode_Massnahme_Erfassen_Checklist');");
                }).Render();
            });
        });

        settings.ClientSideEvents.BatchEditStartEditing = @"function(s,e){
                currentGrid = s;
            }";

        settings.ClientSideEvents.ClipboardCellPasting = @"function(s,e){
                e.cancel = currentGrid != s;
            }";

        settings.HtmlDataCellPrepared = (s, e) =>
        {

            string name = "";
            if (e.VisibleIndex >= 0)
            {
                name = e.GetValue("ChecklistType").ToString();
            }
            if (name == "Checkpoint")
            {
                e.Cell.BackColor = System.Drawing.Color.DarkGray;
            }
        };
    }).BindToLINQ(string.Empty, string.Empty, (s, e) =>
    {
        e.QueryableSource = Model;
        e.KeyExpression = "KundendokumentChecklistQuestionID";
    }).GetHtml()