@using System.Web.Mvc.Html
@using NeoSysLCS.Resources.Properties
@model NeoSysLCS.Repositories.ViewModels.MassnahmeErfassenViewModel

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="initial-scale=1.0, maximum-scale=1.0, user-scalable=no, width=device-width">
    <meta content="IE=Edge,chrome=1" http-equiv="X-UA-Compatible">
    @Styles.Render("~/Content/css")
    @Scripts.Render("~/bundles/modernizr")
    @Scripts.Render("~/Scripts/helpers/SelectionHelper.js")

    @Html.DevExpress().GetStyleSheets(
        new StyleSheet { ExtensionSuite = ExtensionSuite.Editors }
        )

    @Scripts.Render("~/bundles/jquery")
    @Scripts.Render("~/bundles/bootstrap")
    <script src="@Url.Content("~/Scripts/jquery.unobtrusive-ajax.min.js")" type="text/javascript"></script>

    <!-- The DevExpress ASP.NET MVC Extensions' scripts -->
    <!-- The DevExpress ASP.NET MVC Extensions' scripts -->
    @Html.DevExpress().GetScripts(
        new Script { ExtensionSuite = ExtensionSuite.NavigationAndLayout },
        new Script { ExtensionSuite = ExtensionSuite.GridView },
        new Script { ExtensionSuite = ExtensionSuite.Editors }
        )

    <meta name="description" content="NeoSysLCS" />

</head>

<body>

    @using (Html.BeginForm("CreateMassnahme", "Dashboard", new { @class = "form-horizontal", role = "form", FormMethod.Post }))
    {
        @Html.HiddenFor(m => m.Betreff)
        @Html.HiddenFor(m => m.StandortId)
        @Html.HiddenFor(m => m.OriginID)
        @Html.HiddenFor(m => m.Betroffen)
        @Html.HiddenFor(m => m.ArtikelID)
        @Html.HiddenFor(m => m.ChecklistQuestionID)

        // @Html.ValidationSummary("", new { @class = "text-danger" })

        <div class="form-group">
            @Html.Label(Resources.Entitaet_Massnahme_Singular, new { @class = "col-md-2 control-label" })
            <div class="col-md-10">
                @Html.TextBoxFor(m => m.Massnahme, new { @class = "form-control", @style = "width:100%" })
                @Html.ValidationMessageFor(m => m.Massnahme, "", new { @class = "text-danger" })
            </div>
            @Html.Label(Resources.Entitaet_Massnahme_Bemerkung, new { @class = "col-md-2 control-label" })
            <div class="col-md-10">
                @Html.TextAreaFor(m => m.Bemerkung, new { @class = "form-control", @style = "width:100%" })
            </div>
            @Html.Label(Resources.Entitaet_Massnahme_Termin, new { @class = "col-md-2 control-label", @style = "width:100%" })
            <div class="col-md-10">
                @Html.DevExpress().DateEditFor(m => m.Termin).GetHtml()
                @Html.ValidationMessageFor(m => m.Termin, "", new { @class = "text-danger" })
            </div>
            @Html.Label(Resources.Entitaet_KundendokumentForderungsversion_Verantwortlich, new { @class = "col-md-2 control-label", @style = "width:100%" })
            <div class="col-md-10">
                @Html.DevExpress().ComboBoxFor(m => m.ShortcutId, settings =>
                {
                    settings.Properties.TextField = "Name";
                    settings.Properties.ValueField = "ShortcutID";
                    settings.Properties.ValueType = typeof(Int32);
                    settings.Properties.NullText = "Select";
                    settings.Properties.NullDisplayText = "Select";
                    settings.SelectedIndex = 1;
                }).BindList(Model.Shortcuts.ToList()).GetHtml()
                @Html.ValidationMessageFor(m => m.ShortcutId, "", new { @class = "text-danger" })
            </div>
            <div class="col-md-10" style="margin-top: 20px; margin-bottom: 20px;">
                @Html.DevExpress().CheckBoxFor(m => m.EmailNotification,settings =>
                {
                    settings.Text = Resources.Entitaet_Massnahme_EmailNotification;
                }).GetHtml()
            </div>
        </div>


        <div class="form-group">
            <div class="col-md-offset-2 col-md-10">
                <input type="submit" value="@Resources.Button_Speichern" class="btn btn-default" />
            </div>
        </div>
    }

</body>
</html>
