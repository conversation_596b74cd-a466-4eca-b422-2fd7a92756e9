@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories;
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Site.Helpers


@{
    var grid = Html.DevExpress().GridView(
        settings =>
        {
            UnitOfWork unitOfWork = new UnitOfWork();

            GridViewHelper.ApplyDefaultSettings(settings);
            settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;

            settings.Name = "gvRowSelection";
            settings.KeyFieldName = "ShortcutID";

            settings.CallbackRouteValues = new { Controller = "MassnahmeShortcut", Action = "ShortcutSelectionPartial", id = ViewData["MassnahmeID"] };
            settings.ControlStyle.CssClass = "grid";

            settings.SettingsCookies.StoreFiltering = false;
            settings.CommandColumn.Visible = true;
            settings.CommandColumn.ShowSelectCheckbox = true;
            settings.SettingsBehavior.AllowSelectByRowClick = false;
            settings.SettingsBehavior.AllowSelectSingleRowOnly = true;
            settings.Settings.ShowGroupPanel = false;

            MVCxGridViewColumn column = settings.Columns.Add("Name");
            column.Caption = "Name";


            //setting preselected
            settings.PreRender = (sender, e) =>
            {
                MVCxGridView gridView = sender as MVCxGridView;
                if (gridView != null)
                {
                    Massnahme massnahme = unitOfWork.MassnahmeRepository.GetByID(Convert.ToInt32(ViewData["MassnahmeID"]));
                    gridView.Selection.SelectRowByKey(massnahme.ShortcutID);
                }
            };

            settings.ClientSideEvents.Init = @"function(s, e){
            SelectionInit(" +
                                          ViewData["MassnahmeID"] + @",
                                   s,
                                   SelectedRows" + @",
                                   '#count" + @"',
                                   '#Productresult" + @"');
                                    }";
            settings.ClientSideEvents.SelectionChanged = @"function(s,e){
                SelectionChanged(" + ViewData["MassnahmeID"] + @", e);
            }";

        });
}

@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
    {
        e.QueryableSource = Model;
        e.KeyExpression = "ShortcutID";
    }).GetHtml()