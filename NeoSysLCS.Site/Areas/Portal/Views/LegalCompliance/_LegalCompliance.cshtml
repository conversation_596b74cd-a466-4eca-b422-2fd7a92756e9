@using System.Drawing
@using NeoSysLCS.Repositories
@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers
@using System.Web.UI.WebControls;


@section AdditionalResources
{
    <style type="text/css">
        div#KundendokumentPageControl_CC {
            padding-bottom: 50px;
        }

        div#chart {
            height: 100% !important;
        }
    </style>
}

@{

    UnitOfWork _unitOfWork = new UnitOfWork();

    SessionHelper helper = new SessionHelper();
    ViewData["KundeID"] = helper.GetKundeIdOfCurrentUser();

    ViewContext.Writer.Write("<p></p>");

    Html.DevExpress().Chart(settings =>
    {
        //ChartAreaFullStckedDemoOptions options = Model;
        settings.Name = "chart";
        settings.Width = 2000;
        settings.Height = 500;
        settings.PaletteName = "Office";
        settings.RenderFormat = DevExpress.XtraCharts.Web.RenderFormat.Png;
        settings.BorderOptions.Visibility = DefaultBoolean.False;
        settings.CrosshairEnabled = /*options.ShowLabels ? DefaultBoolean.False : */DefaultBoolean.False;

        settings.Legends.Default(l =>
        {
            l.AlignmentHorizontal = LegendAlignmentHorizontal.Center;
            l.AlignmentVertical = LegendAlignmentVertical.BottomOutside;
            l.Direction = LegendDirection.RightToLeft;
            l.Border.Visibility = DefaultBoolean.False;
        });

        settings.CallbackRouteValues = new { Controller = "LegalCompliance", Action = "LegalCompliance", standortID = Int32.Parse(ViewData["StandortID"].ToString()) };

        var test = ViewData["StandortID"];

        settings.Series.Template(t =>
        {
            t.Views().FullStackedAreaSeriesView(v =>
            {
                v.SeriesLabel(l =>
                {
                    l.TextPattern = true ? "{VP:P0}" : "${V}M";
                    //l.TextPattern = "{V}";
                    l.ResolveOverlappingMode = ResolveOverlappingMode.Default;
                });
            });
            t.LabelsVisibility = true ? DefaultBoolean.True : DefaultBoolean.False;
            t.SetDataMembers("Month", "Erfuellt");
        });

        settings.CustomDrawSeries = (sender, e) =>
        {
            if (e.LegendText == Resources.Chart_Label_Erfuellt)
            {
                e.SeriesDrawOptions.Color = Color.FromArgb(151, 184, 87);
                e.LegendDrawOptions.Color = Color.FromArgb(151, 184, 87);
                e.Series.Label.LineColor = Color.FromArgb(151, 184, 87);
                e.Series.Label.TextColor = Color.FromArgb(151, 184, 87);
            };
            if (e.LegendText == Resources.Chart_Label_NichtErfuellt)
            {
                e.SeriesDrawOptions.Color = Color.FromArgb(176, 71, 68);
                e.LegendDrawOptions.Color = Color.FromArgb(176, 71, 68);
                e.Series.Label.LineColor = Color.FromArgb(176, 71, 68);
                e.Series.Label.TextColor = Color.FromArgb(176, 71, 68);
            };
            if (e.LegendText == Resources.Chart_Label_InAbklaeurung)
            {
                e.SeriesDrawOptions.Color = Color.FromArgb(75, 123, 181);
                e.LegendDrawOptions.Color = Color.FromArgb(75, 123, 181);
                e.Series.Label.LineColor = Color.FromArgb(75, 123, 181);
                e.Series.Label.TextColor = Color.FromArgb(75, 123, 181);
            };
            if (e.LegendText == Resources.Chart_Label_NotEdited)
            {
                e.SeriesDrawOptions.Color = Color.FromArgb(255, 204, 0);
                e.LegendDrawOptions.Color = Color.FromArgb(255, 204, 0);
                e.Series.Label.LineColor = Color.FromArgb(255, 204, 0);
                e.Series.Label.TextColor = Color.FromArgb(0, 0, 0);
            };
        };

        settings.XYDiagram(d =>
        {
            d.AxisX.Title.Text = "Erfuellung";
            d.AxisX.WholeRange.AutoSideMargins = false;
            d.AxisX.WholeRange.SideMarginsValue = 0;
            d.AxisX.Tickmarks.MinorVisible = false;

            d.AxisY.Label.TextPattern = "{V:P0}";
            d.AxisY.Title.Text = "Percent";
            d.AxisY.VisualRange.Auto = false;
            d.AxisY.VisualRange.AutoSideMargins = false;
            d.AxisY.VisualRange.MaxValue = 1;
            d.AxisY.VisualRange.MinValue = 0;
            d.AxisY.VisualRange.SideMarginsValue = 0;
            d.AxisY.WholeRange.Auto = false;
            d.AxisY.WholeRange.AutoSideMargins = false;
            d.AxisY.WholeRange.MaxValue = 1;
            d.AxisY.WholeRange.MinValue = 0;
            d.AxisY.WholeRange.SideMarginsValue = 0;
            d.AxisY.Tickmarks.MinorVisible = false;
        });



    }).Bind(_unitOfWork.LegalComplianceRepository.GetByStandortKundendokument(ViewData["KundeID"], DateTime.Parse(ViewData["startDate"].ToString()), DateTime.Parse(ViewData["endDate"].ToString()) , Int16.Parse(ViewData["StandortID"].ToString())), seriesDataMember: "Erfuellung").GetHtml();

    //< script type = "text/javascript" >
    // function UpdateLabelOptionState() {
    //        ValueAsPercent.SetEnabled(ShowLabels.GetChecked());
    //    }
    //</ script >


}