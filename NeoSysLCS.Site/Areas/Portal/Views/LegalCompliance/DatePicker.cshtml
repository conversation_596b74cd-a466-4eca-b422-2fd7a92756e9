@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Areas.Admin.Controllers
@using NeoSysLCS.Site.Utilities.Export
@using System.Web.UI.WebControls
@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Repositories
@using System.Globalization
@using NeoSysLCS.DomainModel.Models

@model NeoSysLCS.Repositories.ViewModels.DateRangePickerModel

@section AdditionalResources
{
    <script src="@Url.Content("~/Scripts/jquery.validate.min.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/Scripts/jquery.validate.unobtrusive.min.js")" type="text/javascript"></script>
    <style type="text/css">
        .pickerGroupContainer,
        .pickerFormLayout > .dxflGroupSys {
            padding-left: 0 !important;
        }
        div#KundendokumentPageControl_CC {
            padding-bottom: 50px;
        }

        div#chart {
            height: 100% !important;
        }
    </style>
}

@Html.DevExpress().GetStyleSheets(
    new StyleSheet { ExtensionSuite = ExtensionSuite.Editors }
    )

<!-- The DevExpress ASP.NET MVC Extensions' scripts -->
@Html.DevExpress().GetScripts(
    new Script { ExtensionSuite = ExtensionSuite.Editors }
    )

<script type="text/javascript">
    var currentDate = new Date();
    function OnCustomDisabledDate(s, e, fromMonth, fromYear, toMonth, toYear) {

        if (e.date.getFullYear() < fromYear) {
            e.isDisabled = true;
        }
        else if (e.date.getFullYear() === fromYear && e.date.getMonth() < fromMonth-1) {
            e.isDisabled = true;
        }
        else if (e.date.getFullYear() > toYear) {
            e.isDisabled = true;
        }
        else if (e.date.getFullYear() === toYear && e.date.getMonth() > toMonth-1)
        {
            e.isDisabled = true;
        }
    }  
    function ShowDetailPopup(url, win) {
        modal = eval(win);

        if (window.height < 600) {
            modal.SetHeight(window.height - 50);
        } else {
            modal.SetHeight(600);
        }

        if (window.width < 800) {
            modal.SetWidth(window.width - 50);
        } else {
            modal.SetWidth(800);
        }

        modal.SetContentUrl(url);
        modal.Show();
    }

    function OnClick(s, e) {
    //    var selectedItems = comboBox5.GetSelectedValues();
    }
</script>

@{
    IUnitOfWork _unitOfWork = new UnitOfWork();

    <div class="page-header" style="display:inline-block">
        <h1 style="display:inline-block">Legal Compliance</h1>
        <button type="button" class="btn btn-default" style="border:none; background-color: transparent; outline:none; padding-left: 5px; padding-right: 0px; padding-bottom:20px; padding-top: 5px"
                onclick="javascript: ShowDetailPopup('/Portal/FAQPortal/ShowFAQPopup?view=LegalCompliance', 'pcModalMode_FAQ_LegalCompliance');">
            <img src="~/Content/images/Question_Mark.png" style="width:20px;height:20px" />
        </button>
    </div>

    var standortMenuName = NeoSysLCS.Site.Helpers.CultureHelper.GetStandortMenuName();
    if (CultureInfo.CurrentUICulture.TwoLetterISOLanguageName != "de")
    {
        standortMenuName = standortMenuName.ToLower();
    }

    List<StandortViewModel> standorte = _unitOfWork.KundeRepository.LegalComplianceGetStandorteByKundeViewModel(Int16.Parse(ViewData["KundeID"].ToString())).ToList();
    var selected = new StandortViewModel();
    if (Int32.Parse(ViewData["StandortID"].ToString()) == 0)
    {
        selected = standorte.First();
        ViewData["StandortID"] = standorte.First().StandortID;
    }
    else
    {
        selected = standorte.First(e => e.StandortID == Int32.Parse(ViewData["StandortID"].ToString()));
        ViewData["StandortID"] = selected.StandortID;
    }
}


@using (Html.BeginForm("DateRangePicker", "LegalCompliance", FormMethod.Post))
{

    @*<p> @Html.Label("Wählen Sie die gewünschten Standorte aus" + "(" + "max. 10" + ")") </p>*@

    <div class="edit_form">
        <div class="form-group">
            <p>@Html.Label("Test", Resources.View_LegalCompliance_ChooseStandort + " " + standortMenuName, new { @class = "col-md-12 control-label", @style = "width:100%" })</p>

            <div class="col-md-12">
                @Html.DevExpress().ComboBox(
                 settings =>
                 {
                     settings.Name = "comboBox5";
                     settings.Width = 750;
                     settings.Properties.TextField = "Name";
                     settings.Properties.ValueField = "StandortID";
                     settings.Properties.ClearButton.DisplayMode = ClearButtonDisplayMode.OnHover;
                     settings.Properties.ClientSideEvents.SelectedIndexChanged = @"function(s,e){
                               window.location = '" + @Url.Action("StanodrtIndex", "LegalCompliance", new { Area = "Portal" }) + @"?standortID=' + s.GetValue()
                           }";
                 }).BindList(standorte).Bind(selected.Name).GetHtml()
            </div>
        </div>

            @Html.ValidationSummary("", new { @class = "text-danger" })

            <div class="form-group">
                @Html.Label(Resources.Label_From, new { @class = "col-md-12 control-label", @style = "width:100%" })
                <div class="col-md-12">
                    @Html.DevExpress().DateEditFor(m => m.Start, settings => { }).GetHtml()
                    @Html.ValidationMessageFor(m => m.Start, "", new { @class = "text-danger" })
                </div>
                @Html.Label(Resources.Label_To, new { @class = "col-md-12 control-label", @style = "width:100%" })
                <div class="col-md-12">
                    @Html.DevExpress().DateEditFor(m => m.End, settings => { }).GetHtml()
                    @Html.ValidationMessageFor(m => m.End, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                <div class="col-md-12">
                    @Html.DevExpress().Button(
                        buttonSettings =>
                        {
                            buttonSettings.Name = "SaveDatePick";
                            buttonSettings.ControlStyle.CssClass = "button";
                            buttonSettings.Width = Unit.Pixel(150);
                            buttonSettings.Text = Resources.View_LegalCompliance_SaveSelection;
                            buttonSettings.ClientSideEvents.Click = "OnClick";
                            buttonSettings.UseSubmitBehavior = true;
                        }).GetHtml()
                </div>
            </div>
        </div>
}

@Html.DevExpress().PopupControl(settings =>
{

    settings.Name = "pcModalMode_FAQ_LegalCompliance";
    settings.ShowPageScrollbarWhenModal = true;
    settings.ResizingMode = ResizingMode.Live;
    settings.ShowRefreshButton = false;
    settings.AutoUpdatePosition = true;
    settings.AllowDragging = true;
    settings.CloseAction = CloseAction.CloseButton;
    settings.PopupAnimationType = AnimationType.None;
    settings.HeaderText = "FAQ";
    settings.Modal = true;
    settings.AllowResize = true;
    settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
    settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
    settings.ClientSideEvents.Init = "function(s, e) { s.SetWidth(400); }";
    settings.ClientSideEvents.Shown = "function(s, e) { s.SetWidth(400); }";
}).GetHtml()

@Html.Partial("_LegalCompliance")