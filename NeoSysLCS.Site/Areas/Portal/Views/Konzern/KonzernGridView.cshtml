@using System.Data.Entity
@using System.Web.UI.WebControls
@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Site.Areas.Portal.Controllers
@using NeoSysLCS.Site.Helpers
@using NeoSysLCS.Resources.Properties
@using DevExpress.Data
@using DevExpress.Data.Filtering
@using NeoSysLCS.Repositories
@using NeoSysLCS.Site.Helpers
@using NeoSysLCS.Site.Utilities.Export

@model IQueryable<NeoSysLCS.Repositories.ViewModels.KonzernViewModel>

@{
    @functions{
        public static string HighlightSearchText(string source, string searchText)
        {
            if (string.IsNullOrWhiteSpace(searchText))
                return source;
            var regex = new System.Text.RegularExpressions.Regex(System.Text.RegularExpressions.Regex.Escape(searchText), System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            if (regex.IsMatch(source))
                return string.Format("<span>{0}</span>", regex.Replace(source, "<span class='dxgvHL'>$0</span>"));
            return source;
        }
    }

    var grid = Html.DevExpress().GridView(settings =>
    {
        IUnitOfWork unitOfWork = new UnitOfWork();
        settings.Name = "KonzernGridView";

        GridViewHelper.ApplyDefaultSettings(settings);
        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;
        settings.KeyFieldName = "KonzernID";
        var kundeId = ViewData["KundeId"];

        settings.CallbackRouteValues = new { Controller = "Konzern", Action = "KonzernGridView" };

        settings.SettingsPopup.CustomizationWindow.Width = 300;
        settings.SettingsCustomizationDialog.Enabled = true;
        Session["Model"] = Model;
        settings.SettingsExport.ExcelExportMode = DevExpress.Export.ExportType.DataAware;
        settings.SettingsExport.EnableClientSideExportAPI = true;
        settings.Styles.CustomizationDialog.CssClass = "addScroll";

        settings.CommandColumn.Visible = true;
        settings.CommandColumn.MinWidth = 85;
        settings.CommandColumn.AllowDragDrop = DefaultBoolean.False;
        settings.Styles.GroupPanel.CssClass = "noWrapClass";


        settings.SettingsContextMenu.Enabled = false;

        settings.Columns.Add(column =>
        {
            column.FieldName = "StandortObjektID";
            column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
            column.MinWidth = 200;
            column.HeaderStyle.Wrap = DefaultBoolean.True;
            column.ReadOnly = true;
            column.GroupIndex = 1;
            column.EditFormSettings.Visible = DefaultBoolean.False;
            if (Model != null)
            {
                column.ColumnType = MVCxGridViewColumnType.ComboBox;
                column.Settings.FilterMode = ColumnFilterMode.DisplayText;
                var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;
                comboBoxProperties.DataSource = (Model.Select(x => new { x.StandortObjektID, x.StandortObjektTitel }).OrderBy(x => x.StandortObjektTitel)).Distinct().ToList();
                comboBoxProperties.TextField = "StandortObjektTitel";
                comboBoxProperties.ValueField = "StandortObjektID";
                comboBoxProperties.ValueType = typeof(int);
            }
            column.SetDataItemTemplateContent(container => Html.DevExpress().HyperLink(hyperlink =>
            {
                var keyValue = container.KeyValue;
                hyperlink.Name = "Kundendokument_" + keyValue;
                var standortTitel = DataBinder.Eval(container.DataItem, "StandortObjektTitel").ToString();
                var KundendokumentForderungsversionID = DataBinder.Eval(container.DataItem, "KundendokumentForderungsversionID");
                hyperlink.Properties.Text = standortTitel;
                hyperlink.NavigateUrl = Url.Action("IndexDirectLink", "Kundendokument", new { id = DataBinder.Eval(container.DataItem, "StandortID"), standortTitel = standortTitel });
                
                // Style the hyperlink to be more visible
                hyperlink.ControlStyle.Font.Underline = true;
            }).Render());
        });

        settings.CustomUnboundColumnData = (s, e) =>
        {
            if (e.Column.FieldName == "RechtsbereicheUnbound")
            {
                var dataItem = e.GetListSourceFieldValue("Rechtsbereiche");
                IQueryable<RechtsbereichViewModel> rechtsbereiche = dataItem as IQueryable<RechtsbereichViewModel>;
                List<string> tbl = new List<string>();

                foreach (RechtsbereichViewModel t in rechtsbereiche)
                {
                    tbl.Add(t.Name);
                }
                e.Value = String.Join(", ", tbl);
            }
        };

        settings.Columns.Add(column =>
        {
            column.FieldName = "RechtsbereicheUnbound";
            column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), "Rechtsbereiche");
            column.MinWidth = 140;
            column.ReadOnly = true;
            column.HeaderStyle.Wrap = DefaultBoolean.True;
            column.EditFormSettings.Visible = DefaultBoolean.False;

            column.Settings.ShowFilterRowMenu = DefaultBoolean.False;
            column.UnboundType = UnboundColumnType.String;
            column.Settings.AllowSort = DefaultBoolean.False;
            column.SetDataItemTemplateContent(c =>
            {
                var test = c.Grid.SearchPanelFilter;
                var dataItem = DataBinder.Eval(c.DataItem, "Rechtsbereiche");
                if (dataItem != null)
                {
                    IQueryable<RechtsbereichViewModel> rechtsbereiche = dataItem as IQueryable<RechtsbereichViewModel>;
                    List<string> tbl = new List<string>();

                    foreach (RechtsbereichViewModel t in rechtsbereiche)
                    {
                        tbl.Add(t.Name);
                    }

                    Html.DevExpress().Label(labelSettings =>
                    {
                        labelSettings.Name = "RechtsbereicheUnbound" + c.ClientID;
                        labelSettings.Text = String.Join(", ", tbl);
                        labelSettings.EncodeHtml = false;
                        labelSettings.PreRender += (s, e) =>
                        {
                            MVCxLabel lbl = (MVCxLabel)s;
                            lbl.Text = HighlightSearchText(lbl.Text, c.Grid.SearchPanelFilter);
                        };
                    }).Render();
                }
            });
        });

        settings.AutoFilterCellEditorCreate = (s, e) =>
        {
            if (e.Column.FieldName == "RechtsbereicheUnbound")
            {
                ComboBoxProperties combo = new ComboBoxProperties();
                e.EditorProperties = combo;
            }
        };

        settings.AutoFilterCellEditorInitialize = (s, e) =>
        {
            var gv = s as MVCxGridView;
            var expr = gv.FilterExpression;
            if (e.Column.FieldName == "RechtsbereicheUnbound")
            {
                MVCxComboBox cb = ((MVCxComboBox)e.Editor);
                cb.DataSource = KonzernController.GetRechtsbereichKonzernComboBox().ToList().Select(i => i.Name);
                cb.DataBindItems();
                if (Session["RechtsbereicheFilter"] != null && expr.Contains("Rechtsbereiche"))
                {
                    e.Editor.Value = Session["RechtsbereicheFilter"];
                }
            }
        };

        settings.ProcessColumnAutoFilter = (sender, e) =>
        {
            var gv = sender as MVCxGridView;
            if (e.Column.FieldName == "RechtsbereicheUnbound")
            {
                string value = e.Value;
                if (gv.FilterExpression != "")
                {
                    e.Criteria = CriteriaOperator.Parse(gv.FilterExpression + "OR Rechtsbereiche[contains(Name, ?)]", value);
                    gv.FilterExpression = null;
                    Session["RechtsbereicheFilter"] = "";
                }
                else
                {
                    e.Criteria = DevExpress.Data.Filtering.CriteriaOperator.Parse("Rechtsbereiche[contains(Name, ?)]", value);
                    Session["RechtsbereicheFilter"] = e.Value;
                }

            }
        };

        settings.Columns.Add(column =>
        {
            column.FieldName = "ErlassID";
            column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
            column.MinWidth = 200;
            column.HeaderStyle.Wrap = DefaultBoolean.True;
            column.ReadOnly = true;
            column.EditFormSettings.Visible = DefaultBoolean.False;
            if (Model != null)
            {
                column.ColumnType = MVCxGridViewColumnType.ComboBox;
                column.Settings.FilterMode = ColumnFilterMode.DisplayText;
                var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;
                comboBoxProperties.DataSource = (Model.Select(x => new { x.ErlassID, ErlassDisplay = x.ErlassTitel + " ( " + (string.IsNullOrWhiteSpace(x.Abkuerzung) ? "-" : x.Abkuerzung) + " )" }).OrderBy(x => x.ErlassDisplay).Distinct().ToList());
                comboBoxProperties.TextField = "ErlassDisplay";
                comboBoxProperties.ValueField = "ErlassID";
                comboBoxProperties.ValueType = typeof(int);
            }
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "ArtikelNummer";
            column.Caption = Resources.Entitaet_Artikel_Singular;
            column.MinWidth = 110;
            column.HeaderStyle.Wrap = DefaultBoolean.True;
            column.EditFormSettings.Visible = DefaultBoolean.False;
            column.CellStyle.HorizontalAlign = HorizontalAlign.Center;
            column.Settings.FilterMode = ColumnFilterMode.DisplayText;
            column.SetDataItemTemplateContent(
                container =>
                {

                    var url = DataBinder.Eval(container.DataItem, "ArtikelQuelle");
                    var nr = DataBinder.Eval(container.DataItem, "ArtikelNummer");
                    string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, nr, false);
                    ViewContext.Writer.Write(htmlLink);
                });
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "KonzernStatus";
            column.Caption = Resources.Entitaet_Erlassfassung_Status;
            column.ColumnType = MVCxGridViewColumnType.ComboBox;
            column.Settings.FilterMode = ColumnFilterMode.DisplayText;
            var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;

            var test = (from x in Model select x.KonzernStatus).Distinct().ToList();

            var list = from KundendokumentItemStatusForKonzern value in Enum.GetValues(typeof(KundendokumentItemStatusForKonzern))
                       where test.Contains(value)
                       select new
                       {
                           Id = (int)value,
                           Name = value.GetTranslation()
                       };

            comboBoxProperties.DataSource = list;
            comboBoxProperties.ValueField = "Id";
            comboBoxProperties.TextField = "Name";


            column.MinWidth = 100;
            column.HeaderStyle.Wrap = DefaultBoolean.True;
            column.ReadOnly = true;
            column.EditFormSettings.Visible = DefaultBoolean.False;
            column.HeaderStyle.Wrap = DefaultBoolean.True;
            column.SetDataItemTemplateContent(container =>
            {
                var statusCandidate = DataBinder.Eval(container.DataItem, "KonzernStatus");
                if (statusCandidate != null)
                {
                    var status = (KundendokumentItemStatusForKonzern)statusCandidate;
                    ViewContext.Writer.Write("<span class=\"label label-" + status + "\">" + status.GetTranslation() + "</span>");
                }
            });
        });

        foreach (MVCxGridViewColumn col in KonzernController.GetStandortColumns(ViewContext))
        {
            settings.Columns.Add(col);
            settings.FormatConditions.AddHighlight(h =>
            {
                h.ApplyToRow = false;
                h.Format = GridConditionHighlightFormat.Custom;
                h.CellStyle.BackColor = System.Drawing.Color.FromArgb(176, 71, 68);
                h.Rule = GridConditionRule.Expression;
                h.Expression = col.FieldName + " == 0";
                h.FieldName = col.FieldName;
            });
            settings.FormatConditions.AddHighlight(h =>
            {
                h.ApplyToRow = false;
                h.Format = GridConditionHighlightFormat.Custom;
                h.CellStyle.BackColor = System.Drawing.Color.FromArgb(151, 184, 87);
                h.Rule = GridConditionRule.Expression;
                h.Expression = col.FieldName + " == 1";
                h.FieldName = col.FieldName;
            });
            settings.FormatConditions.AddHighlight(h =>
            {
                h.ApplyToRow = false;
                h.Format = GridConditionHighlightFormat.Custom;
                h.CellStyle.BackColor = System.Drawing.Color.FromArgb(75, 123, 181);
                h.Rule = GridConditionRule.Expression;
                h.Expression = col.FieldName + " == 2";
                h.FieldName = col.FieldName;
            });
            settings.FormatConditions.AddHighlight(h =>
            {
                h.ApplyToRow = false;
                h.Format = GridConditionHighlightFormat.Custom;
                h.CellStyle.BackColor = System.Drawing.Color.FromArgb(255, 255, 255);
                h.Rule = GridConditionRule.Expression;
                h.Expression = col.FieldName + " == 3";
                h.FieldName = col.FieldName;
            });
            settings.FormatConditions.AddHighlight(h =>
            {
                h.ApplyToRow = false;
                h.Format = GridConditionHighlightFormat.Custom;
                h.CellStyle.BackColor = System.Drawing.Color.FromArgb(214, 214, 214);
                h.Rule = GridConditionRule.Expression;
                h.Expression = col.FieldName + " == 4";
                h.FieldName = col.FieldName;
            });
            settings.FormatConditions.AddHighlight(h =>
            {
                h.ApplyToRow = false;
                h.Format = GridConditionHighlightFormat.Custom;
                h.CellStyle.BackColor = System.Drawing.Color.FromArgb(255, 204, 0);
                h.Rule = GridConditionRule.Expression;
                h.Expression = col.FieldName + " == 5";
                h.FieldName = col.FieldName;
            });
        }
        settings.ClientLayout = (sender, e) =>
        {
            MVCxGridView gridView = sender as MVCxGridView;
            var cookieID = gridView.SettingsCookies.CookiesID + "_" + kundeId;
            if (e.LayoutMode == ClientLayoutMode.Loading)
            {
                var layout = CookieHelper.GetCookie(cookieID);
                if (layout != "")
                {
                    e.LayoutData = layout;
                }
            }
            else if (e.LayoutMode == ClientLayoutMode.Saving)
            {
                CookieHelper.SetCookie(cookieID, e.LayoutData);
            }
        };
    });

    if (ViewData["EditError"] != null)
    {
        grid.SetEditErrorText((string)ViewData["EditError"]);
    }
}


@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;//KonzernController.GetAllViewModels();
    e.KeyExpression = "KonzernID";
}).GetHtml()
