@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Areas.Admin.Controllers
@using NeoSysLCS.Site.Utilities.Export
@using System.Web.UI.WebControls
@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Repositories;

@model IQueryable<NeoSysLCS.Repositories.ViewModels.KonzernViewModel>

@{
    ViewBag.Title = Resources.View_Konzern_Title;
    //IUnitOfWork _unitOfWork = new UnitOfWork();
    //var test = ViewData["KundeId"];
    //var standorte = _unitOfWork.StandortRepository.GetAllStandortViewModels((int)ViewData["kundeId"]).ToList();
    ViewContext.Writer.Write("<h1 class='page-header'>" + ViewBag.Title + "</h1>");
}

@Html.Partial("KonzernGridView", Model)