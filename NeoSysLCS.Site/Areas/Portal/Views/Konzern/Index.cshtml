@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Areas.Admin.Controllers
@using NeoSysLCS.Site.Utilities.Export
@using System.Web.UI.WebControls
@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Repositories
@using System.Globalization

@model IQueryable<NeoSysLCS.Repositories.ViewModels.KonzernViewModel>

@{
    IUnitOfWork _unitOfWork = new UnitOfWork();
    var kundeId = ViewData["KundeId"];
    var standorte = _unitOfWork.StandortRepository.GetAllStandortViewModels((int)ViewData["kundeId"]).ToList();

    var standortMenuName = NeoSysLCS.Site.Helpers.CultureHelper.GetStandortMenuName(0,0,false);
    if (CultureInfo.CurrentUICulture.TwoLetterISOLanguageName != "de")
    {
        standortMenuName = standortMenuName.ToLower();
    }

    <div class="page-header" style="display:inline-block">
        <h1 style="display:inline-block">@Resources.View_Konzern_Title</h1>
        <button type="button" class="btn btn-default" style="border:none; background-color: transparent; outline:none; padding-left: 5px; padding-right: 0px; padding-bottom:20px; padding-top: 5px"
                onclick="javascript: ShowDetailPopup('/Portal/FAQPortal/ShowFAQPopup?view=Uebersicht', 'pcModalMode_FAQ_Uebersicht');">
            <img src="~/Content/images/Question_Mark.png" style="width:20px;height:20px" />
        </button>
    </div>

    ViewBag.Title = Resources.View_Konzern_Title;
}

<style type="text/css">
    .customChooserClass .customItemClass div:first-child {
        display: none;
    }

    .customChooserClass > div {
        display: flex;
        flex-direction: column;
    }

    .addScroll > div {
        overflow-y: scroll;
        height: 750px;
    }
</style>

<script type="text/javascript">
    function OnClick(s, e) {
        var selectedItems = Standort.GetSelectedValues();
    }
    function ShowDetailPopup(url, win) {
        modal = eval(win);

        if (window.height < 600) {
            modal.SetHeight(window.height - 50);
        } else {
            modal.SetHeight(600);
        }

        if (window.width < 800) {
            modal.SetWidth(window.width - 50);
        } else {
            modal.SetWidth(800);
        }

        modal.SetContentUrl(url);
        modal.Show();
    }

    function ShowNewPopupExportKonzern(modal, nothing, kundendokumentID) {
        if (window.height < 50) {
            ExportKonzern.SetHeight(window.height - 50);
        } else {
            ExportKonzern.SetHeight(50);
        }

        if (window.width < 400) {
            ExportKonzern.SetWidth(window.width - 50);
        } else {
            ExportKonzern.SetWidth(400);
        }
        ExportKonzern.Show();
    }
</script>

@using (Html.BeginForm())
{
    @*<p> @Html.Label("Wählen Sie die gewünschten Standorte aus" + "(" + "max. 10" + ")") </p>*@

    <div class="edit_form" style="display:inline-block">

        <div class="form-group">
            <p>@Html.Label("Test", Resources.View_Konzern_Choose_Standort + " " + standortMenuName + " " + Resources.View_Konzern_Choose_StandortMax)</p>

            @Html.DevExpress().CheckBoxList(
            chkSettings =>
            {
                chkSettings.Name = "Standort";
                chkSettings.Properties.ValueField = "StandortID";
                chkSettings.Properties.TextField = "Name";
                chkSettings.ShowModelErrors = true;
                chkSettings.Properties.RepeatColumns = 3;
                chkSettings.Properties.RepeatDirection = RepeatDirection.Vertical;
                chkSettings.Width = 750;

                chkSettings.PreRender = (sender, e) =>
                {
                    var currentStandorte = ViewData["selectedStandorte"] as string[]; //as HashSet<StandortViewModel>;

                    if (currentStandorte != null)
                    {
                        var cbl = (MVCxCheckBoxList)sender;
                        foreach (ListEditItem item in cbl.Items)
                        {
                            //var name = item.Value.ToString();
                            if (currentStandorte.Count(s => s == (string)item.Value) == 1)
                                item.Selected = true;
                        }
                    }
                };
            }).BindList(standorte).GetHtml()
        </div>
        <div class="form-group">

            @Html.DevExpress().Button(
            buttonSettings =>
            {
                buttonSettings.Name = "Open";
                buttonSettings.ControlStyle.CssClass = "button";
                buttonSettings.Width = Unit.Pixel(150);
                buttonSettings.Text = Resources.View_Konzern_Save;
                buttonSettings.UseSubmitBehavior = true;
                buttonSettings.ClientSideEvents.Click = "OnClick";

            }).GetHtml()
        </div>
    </div>


}

@using (Html.BeginForm())
{
    ViewContext.Writer.Write("<div style='display:flex;margin-top:15px;width:1000px'>");

    ViewContext.Writer.Write("<a class='btn btn-default' type='button' onclick='KonzernGridView.CollapseAll();return false;' >" +
    "<i class='fa fa-minus-square'></i>&nbsp;" +
        Resources.Allgemein_CollapseAllRows +
    "</a>&nbsp;");
    ViewContext.Writer.Write("<a class='btn btn-default' type='button' onclick='KonzernGridView.ExpandAll();return false;' >" +
                           "<i class='fa fa-plus-square'></i>&nbsp;" +
                              Resources.Allgemein_ExpandAllRows +
                           "</a>&nbsp;");

    ViewContext.Writer.Write("<a class='btn btn-default' type='button' onclick='KonzernGridView.ShowCustomizationDialog();return false;' >" +
        "<i class='fa fas fa-cog'></i>&nbsp;" +
        Resources.View_KundendokumentForderungen_Dialog +
        "</a>&nbsp;");
    //ViewContext.Writer.Write("<a class='btn btn-default' type='button' onclick='javascript:ShowNewPopupExportKonzern(this)'; >" +
    //          "<i class='fa fas fa-filter'></i>&nbsp;" +
    //             Resources.XLSX_Export_Format +
    //          "</a>&nbsp;");
}

@using (Html.BeginForm())
{
    ViewContext.Writer.Write("<a class='btn btn-default' type='button' onclick='javascript:ShowNewPopupExportKonzern(this)'; >" +
              "<i class='fa fas fa-filter'></i>&nbsp;" +
                 Resources.XLSX_Export_Format +
    "</a>&nbsp;");
    ViewContext.Writer.Write("</div>");

}


@Html.Partial("KonzernGridView", Model)
@Html.Partial("_ExportPartialPortalKonzern", Model)

@Html.DevExpress().PopupControl(settings =>
{

    settings.Name = "pcModalMode_FAQ_Uebersicht";
    //settings.ScrollBars = ScrollBars.Auto;
    settings.ShowPageScrollbarWhenModal = true;
    settings.ResizingMode = ResizingMode.Live;
    settings.ShowRefreshButton = false;
    settings.AutoUpdatePosition = true;
    settings.AllowDragging = true;
    settings.CloseAction = CloseAction.CloseButton;
    settings.PopupAnimationType = AnimationType.None;
    settings.HeaderText = "FAQ";
    settings.Modal = true;
    settings.AllowResize = true;
    settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
    settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
    settings.ClientSideEvents.Init = "function(s, e) { s.SetWidth(400); }";
    settings.ClientSideEvents.Shown = "function(s, e) { s.SetWidth(400); }";
}).GetHtml()
