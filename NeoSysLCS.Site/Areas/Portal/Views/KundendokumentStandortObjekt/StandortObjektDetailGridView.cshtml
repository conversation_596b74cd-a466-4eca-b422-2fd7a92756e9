@using NeoSysLCS.Repositories;
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Site.Helpers
@using NeoSysLCS.Resources.Properties

@Html.DevExpress().GridView(
    settings =>
    {
        UnitOfWork unitOfWork = new UnitOfWork();

        GridViewHelper.ApplyUebersetzungsSettings(settings);

        settings.Name = "StandortObjektDetailGridView" + ViewData["StandortObjektID"];
        settings.KeyFieldName = "StandortObjektUebersetzungID";
        settings.CallbackRouteValues = new { Controller = "KundendokumentStandortObjekt", Action = "StandortObjektDetailGridView", StandortObjektID = ViewData["StandortObjektID"], standortId = ViewData["StandortID"] };

        settings.SettingsEditing.BatchUpdateRouteValues = new { Controller = "KundendokumentStandortObjekt", Action = "StandortObjektDetailGridViewBatchEditUpdate", StandortObjektID = ViewData["StandortObjektID"] };
        settings.CommandColumn.Visible = false;

        settings.SettingsEditing.Mode = GridViewEditingMode.Batch;
        //Columns
        settings.Columns.Add("Name", Resources.Entitaet_StandortObjekt_Singular);

        settings.Columns.Add(column =>
        {
            column.FieldName = "SpracheID";
            column.Caption = "Sprache";
            column.ReadOnly = true;

            column.ColumnType = MVCxGridViewColumnType.ComboBox;
            column.Settings.FilterMode = ColumnFilterMode.DisplayText;
            var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;

            comboBoxProperties.DataSource = unitOfWork.SpracheRepository.Get().ToList();
            comboBoxProperties.TextField = "Name";
            comboBoxProperties.ValueField = "SpracheID";
            comboBoxProperties.ValueType = typeof(int);
        });
        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);

    }).Bind(Model).GetHtml()
