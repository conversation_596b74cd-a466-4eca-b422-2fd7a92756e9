@using System.Globalization
@using System.Web.UI.WebControls
@using System.Xml.Linq
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers
@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Repositories.ViewModels
@using DevExpress.Data.Filtering

@model IQueryable<NeoSysLCS.Repositories.ViewModels.ConsultationViewModel>

@functions{
    public static string HighlightSearchText(string source, string searchText)
    {
        if (string.IsNullOrWhiteSpace(searchText))
            return source;
        var regex = new System.Text.RegularExpressions.Regex(System.Text.RegularExpressions.Regex.Escape(searchText), System.Text.RegularExpressions.RegexOptions.IgnoreCase);
        if (regex.IsMatch(source))
            return string.Format("<span>{0}</span>", regex.Replace(source, "<span class='dxgvHL'>$0</span>"));
        return source;
    }
}

@{

    var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
    var unitOfWork = new UnitOfWork();
    Sprache sprache = unitOfWork.SpracheRepository.Get().FirstOrDefault(s => s.Lokalisierung == language);
    var _currentLang = sprache.SpracheID;

    var grid = Html.DevExpress().GridView(settings =>
    {
        settings.Name = "ConsultationGridView";
        GridViewHelper.ApplyDefaultSettings(settings);
        settings.SettingsResizing.ColumnResizeMode = ColumnResizeMode.NextColumn;
        settings.Settings.ShowFilterRow = true;

        settings.KeyFieldName = "ConsultationId";

        settings.CallbackRouteValues = new { Controller = "Dashboard", Action = "ConsultationPartialView", kundeId = ViewData["KundeID"] };

        GridViewHelper.ApplyDefaultSettings(settings);
        settings.Settings.ShowGroupPanel = false;
        settings.CommandColumn.Visible = true;
        settings.CommandColumn.Width = Unit.Percentage(3);
        settings.CommandColumn.AllowDragDrop = DefaultBoolean.False;

        settings.Columns.Add(column =>
        {
            column.FieldName = "Title";
            column.Caption = Resources.Entitaet_Consultation_Titel;
            column.ReadOnly = true;
            column.Width = Unit.Percentage(15);
            column.HeaderStyle.Wrap = DefaultBoolean.True;
            column.Settings.AllowHeaderFilter = DefaultBoolean.True;
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "Quelle";
            column.Caption = Resources.Entitaet_Erlass_Quelle;
            column.Width = Unit.Percentage(5);
            column.SetDataItemTemplateContent(
                container =>
                {
                    var url = DataBinder.Eval(container.DataItem, "Quelle");
                    string htmlLink = htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, "Link", false);
                    ViewContext.Writer.Write(htmlLink);
                });
        });

        settings.Columns.Add(column =>
        {
            column.Caption = Resources.Entitaet_Erlass_Plural;
            column.FieldName = "ErlasseUnbound";
            column.Settings.AllowHeaderFilter = DefaultBoolean.False;
            column.Width = Unit.Percentage(15);
            column.ReadOnly = true;
            column.Settings.ShowFilterRowMenu = DefaultBoolean.False;
            column.Settings.AllowSort = DefaultBoolean.False;
            column.HeaderStyle.Wrap = DefaultBoolean.True;
            column.ColumnType = MVCxGridViewColumnType.Memo;
            var memoProp = column.PropertiesEdit as MemoProperties;
            if (memoProp != null)
            {
                memoProp.EncodeHtml = false;
            }

            column.SetDataItemTemplateContent(c =>
            {
                var dataItem = DataBinder.Eval(c.DataItem, "Erlasse");
                if (dataItem != null)
                {
                    IQueryable<ErlassViewModel> erlasse = dataItem as IQueryable<ErlassViewModel>;
                    List<string> tbl = new List<string>();


                    foreach (ErlassViewModel e in erlasse)
                    {
                        var Abkuerzung = string.IsNullOrEmpty(Convert.ToString(DataBinder.Eval(c.DataItem, "Abkuerzung"))) ? "-" : Convert.ToString(DataBinder.Eval(c.DataItem, "Abkuerzung"));
                        XDocument uebersetzung = XDocument.Parse(e.Uebersetzung);
                        string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(e.Quelle, "<u>" + e.SrNummer + "; " + e.Titel + " ( "+  Abkuerzung + " )"+"</u>", false);
                        tbl.Add(htmlLink);
                    }

                    Html.DevExpress().Label(labelSettings =>
                    {
                        labelSettings.Name = "Erlasse" + c.ClientID;
                        labelSettings.Text = String.Join("<br />", tbl.Distinct());
                        labelSettings.EncodeHtml = false;
                        labelSettings.PreRender += (s, e) =>
                        {
                            MVCxLabel lbl = (MVCxLabel)s;
                            lbl.Text = HighlightSearchText(lbl.Text, c.Grid.SearchPanelFilter);
                        };
                    }).Render();
                }
            });
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "Status";
            column.Caption = Resources.Entitaet_Consultation_Phase;
            column.HeaderStyle.Wrap = DefaultBoolean.True;
            column.ColumnType = MVCxGridViewColumnType.ComboBox;
            column.Settings.FilterMode = ColumnFilterMode.DisplayText;
            column.Settings.AllowHeaderFilter = DefaultBoolean.True;
            column.SettingsHeaderFilter.Mode = GridHeaderFilterMode.CheckedList;
            column.ReadOnly = true;
            column.Width = Unit.Percentage(5);
            var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;

            var list = from ConsultationStatus value in Enum.GetValues(typeof(ConsultationStatus))
                       select new
                       {
                           Id = (int)value,
                           Name = value.GetTranslation()
                       };

            comboBoxProperties.DataSource = list;
            comboBoxProperties.ValueField = "Id";
            comboBoxProperties.TextField = "Name";
            comboBoxProperties.ValueType = typeof(Int32);
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "EntryDate";
            column.Caption = Resources.Entitaet_Consultation_EntryDate;
            column.Width = Unit.Percentage(5);
            column.ColumnType = MVCxGridViewColumnType.DateEdit;
            column.PropertiesEdit.DisplayFormatString = "d";
            column.SortDescending();
            column.ReadOnly = true;
            column.Settings.AllowHeaderFilter = DefaultBoolean.True;
            column.SettingsHeaderFilter.Mode = GridHeaderFilterMode.DateRangePicker;
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "OpenedDate";
            column.Caption = Resources.Entitaet_Consultation_OpenedDate;
            column.Width = Unit.Percentage(5);
            column.ColumnType = MVCxGridViewColumnType.DateEdit;
            column.PropertiesEdit.DisplayFormatString = "d";
            column.ReadOnly = true;
            column.Settings.AllowHeaderFilter = DefaultBoolean.True;
            column.SettingsHeaderFilter.Mode = GridHeaderFilterMode.DateRangePicker;
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "Deadline";
            column.Caption = Resources.Entitaet_Consultation_Deadline;
            column.Width = Unit.Percentage(5);
            column.ColumnType = MVCxGridViewColumnType.DateEdit;
            column.PropertiesEdit.DisplayFormatString = "d";
            column.ReadOnly = true;
            column.Settings.AllowHeaderFilter = DefaultBoolean.True;
            column.SettingsHeaderFilter.Mode = GridHeaderFilterMode.DateRangePicker;
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "CompletedDate";
            column.Caption = Resources.Entitaet_Consultation_CompletedDate;
            column.Width = Unit.Percentage(8);
            column.ColumnType = MVCxGridViewColumnType.DateEdit;
            column.PropertiesEdit.DisplayFormatString = "d";
            column.ReadOnly = true;
            column.Settings.AllowHeaderFilter = DefaultBoolean.True;
            column.SettingsHeaderFilter.Mode = GridHeaderFilterMode.DateRangePicker;
        });

        MVCxGridViewColumn massnahmeColumn = settings.Columns.Add("Massnahme");
        massnahmeColumn.Caption = Resources.Entitaet_Massnahme_Singular;
        massnahmeColumn.ReadOnly = true;
        massnahmeColumn.Settings.AllowHeaderFilter = DefaultBoolean.True;
        massnahmeColumn.SettingsHeaderFilter.Mode = GridHeaderFilterMode.CheckedList;
        massnahmeColumn.Settings.AllowAutoFilter = DefaultBoolean.False;
        massnahmeColumn.Settings.ShowFilterRowMenu = DefaultBoolean.False;
        massnahmeColumn.Width = Unit.Percentage(10);
        massnahmeColumn.SetDataItemTemplateContent(container =>
        {

            var consultationId = container.KeyValue;
            var title = (string)DataBinder.Eval(container.DataItem, "Title");

            Html.DevExpress().Button(btn =>
            {
                btn.Text = Resources.Allgemein_Button_Massnahme;
                btn.Name = "Massnahme_" + container.KeyValue;
                btn.Attributes.Add("onClick", "javascript:ShowDetailPopup('" + Url.Action("ShowMassnahmePopupForVernehmlassung", "Dashboard", new { consultationId = consultationId, title = title }) + "','pcModalMode_Massnahme_Erfassen_Vernehmlassung');");
            }).Render();

            var MassnahmeNewCount = (int)DataBinder.Eval(container.DataItem, "MassnahmeNewCount");
            var MassnahmeInProgressCount = (int)DataBinder.Eval(container.DataItem, "MassnahmeInProgressCount");
            var MassnahmeFinishedCount = (int)DataBinder.Eval(container.DataItem, "MassnahmeFinishedCount");

            ViewContext.Writer.Write("<br></br>");
            ViewContext.Writer.Write("<div style='display:inline-block'>" + Resources.Enum_MassnahmeStatus_New + " (" + MassnahmeNewCount + ")" + "</div>");
            ViewContext.Writer.Write("<br>");
            ViewContext.Writer.Write("<div style='display:inline-block'>" + Resources.Enum_MassnahmeStatus_InProgress + " (" + MassnahmeInProgressCount + ")" + "</div>");
            ViewContext.Writer.Write("<br>");
            ViewContext.Writer.Write("<div style='display:inline-block'>" + Resources.Enum_MassnahmeStatus_Finished + " (" + MassnahmeFinishedCount + ")" + "</div>");
        });

        settings.CustomUnboundColumnData = (s, e) =>
        {
            if (e.Column.FieldName == "ErlasseUnbound")
            {
                var dataItem = e.GetListSourceFieldValue("Erlasse");
                if (dataItem != null)
                {
                    IQueryable<ErlassViewModel> erlasse = dataItem as IQueryable<ErlassViewModel>;
                    List<string> tbl = new List<string>();

                    foreach (ErlassViewModel es in erlasse)
                    {
                        XDocument uebersetzung = XDocument.Parse(es.Uebersetzung);
                        string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(es.Quelle, "<u>" + es.SrNummer + "; " + es.Titel + "</u>", false);
                        e.Value = String.Join("<br />", htmlLink);
                    }
                }
            }
        };

        settings.AutoFilterCellEditorCreate = (s, e) =>
        {
            if (e.Column.FieldName == "ErlasseUnbound")
            {
                ComboBoxProperties combo = new ComboBoxProperties();
                e.EditorProperties = combo;
            }
        };

        settings.AutoFilterCellEditorInitialize = (s, e) =>
        {
            var gv = s as MVCxGridView;
            var expr = gv.FilterExpression;
            if (e.Column.FieldName == "ErlasseUnbound")
            {
                MVCxComboBox cb = ((MVCxComboBox)e.Editor);

                List<int> erlassIds = new List<int>();
                var test = Model.Select(x => x.ErlasseCollection).ToList();
                foreach (var test2 in test)
                {
                    foreach (var test3 in test2)
                    {
                        if (!erlassIds.Contains(test3.ErlassID))
                        {
                            erlassIds.Add(test3.ErlassID);
                        }
                    }
                }
                cb.DataSource = unitOfWork.ErlassRepository.GetErlassComboBoxByIds(erlassIds).Select(x => x.Titel);
                cb.DataBindItems();
                if (Session["ErlasseFilter"] != null && expr.Contains("Erlasse"))
                {
                    e.Editor.Value = Session["ErlasseFilter"];
                }
            }
        };

        settings.ProcessColumnAutoFilter = (sender, e) =>
        {
            var gv = sender as MVCxGridView;
            if (e.Column.FieldName == "ErlasseUnbound")
            {
                string value = e.Value;
                if (gv.FilterExpression != "")
                {
                    e.Criteria = CriteriaOperator.Parse(gv.FilterExpression + "OR Erlasse[contains(Titel, ?)]", value);
                    gv.FilterExpression = null;
                    Session["ErlasseFilter"] = "";
                }
                else
                {
                    e.Criteria = CriteriaOperator.Parse("Erlasse[contains(Titel, ?)]", value);
                    Session["ErlasseFilter"] = e.Value;
                }

            }
        };

        settings.BeforeHeaderFilterFillItems = (sender, e) =>
        {
            if (e.Column.FieldName == "Massnahme")
            {
                e.AddValue("Neu", new BinaryOperator("MassnahmeNewCount", 1, BinaryOperatorType.GreaterOrEqual));
                e.AddValue("In Arbeit", new BinaryOperator("MassnahmeInProgressCount", 1, BinaryOperatorType.GreaterOrEqual));
                e.AddValue("Erledigt", new BinaryOperator("MassnahmeFinishedCount", 1, BinaryOperatorType.GreaterOrEqual));
                e.Handled = true;
            }
        };

        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);
    });



    if (ViewData["EditError"] != null)
    {
        grid.SetEditErrorText((string)ViewData["EditError"]);
    }

    grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
    {
        e.QueryableSource = Model;
        e.KeyExpression = "ConsultationId";
    }).GetHtml();

}