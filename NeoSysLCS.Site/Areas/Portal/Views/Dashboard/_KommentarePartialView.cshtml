@using System.Web.UI.WebControls
@using DevExpress.Data
@using DevExpress.Data.Filtering
@using NeoSysLCS.Repositories
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Site.Helpers
@using NeoSysLCS.Repositories.ViewModels
@using System.Globalization
@using System.Xml.Linq
@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Resources.Properties

@model IQueryable<NeoSysLCS.Repositories.ViewModels.KommentarNewsletterViewModel>

@functions{
    public static string HighlightSearchText(string source, string searchText)
    {
        if (string.IsNullOrWhiteSpace(searchText))
            return source;
        var regex = new System.Text.RegularExpressions.Regex(System.Text.RegularExpressions.Regex.Escape(searchText), System.Text.RegularExpressions.RegexOptions.IgnoreCase);
        if (regex.IsMatch(source))
            return string.Format("<span>{0}</span>", regex.Replace(source, "<span class='dxgvHL'>$0</span>"));
        return source;
    }
}

@{
    if (Model.Any())
    {

        var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
        var unitOfWork = new UnitOfWork();
        NeoSysLCS_Dev context = new NeoSysLCS_Dev();
        Sprache sprache = unitOfWork.SpracheRepository.Get().FirstOrDefault(s => s.Lokalisierung == language);
        var _currentLang = sprache.SpracheID;

        List<int> standortIds = new List<int>();
        List<IQueryable<StandortViewModel>> standortViewModels = Model.Select(x => x.Standorte).ToList();
        foreach (IQueryable<StandortViewModel> standortViewModelList in standortViewModels)
        {
            foreach (StandortViewModel standortViewModel in standortViewModelList)
            {
                if (!standortIds.Contains(standortViewModel.StandortID))
                {
                    standortIds.Add(standortViewModel.StandortID);
                }
            }
        }


        var grid = Html.DevExpress().GridView(settings =>
        {


            settings.Name = "KommentarNewsletterGridView";
            settings.KeyFieldName = "KommentarNewsletterID";

            settings.CallbackRouteValues = new { Controller = "Dashboard", Action = "KommentarePartialView", standortID = ViewData["StandortID"], kundeId = ViewData["KundeID"] };

            GridViewHelper.ApplyDefaultSettings(settings);
            settings.Settings.ShowGroupPanel = false;
            //settings.CommandColumn.Width = Unit.Percentage(1);
            //settings.CommandColumn.AllowDragDrop = DefaultBoolean.False;
            settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;
            settings.CommandColumn.Visible = true;
            settings.CommandColumn.Width = Unit.Percentage(1);
            settings.CommandColumn.ShowNewButtonInHeader = false;
            settings.CommandColumn.ShowEditButton = false;

            settings.Columns.Add(column =>
            {
                column.FieldName = "ErlassNummer";
                column.Caption = Resources.Erlassnummer;
                column.ColumnType = MVCxGridViewColumnType.Default;
                column.Settings.AllowHeaderFilter = DefaultBoolean.True;
                column.SettingsHeaderFilter.Mode = GridHeaderFilterMode.CheckedList;
                column.Width = Unit.Percentage(10);
                column.SetDataItemTemplateContent(
                container =>
                {
                    var url = DataBinder.Eval(container.DataItem, "ErlassQuelle");
                    var srNummer = DataBinder.Eval(container.DataItem, "ErlassNummer");
                    string htmlLink = "";
                    if (String.IsNullOrEmpty(srNummer.ToString()))
                    {
                        htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, "Link", false);
                    }
                    else
                    {
                        htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, srNummer, false);
                    }
                    ViewContext.Writer.Write(htmlLink);
                });
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "ErlassName";
                column.Caption = Resources.Entitaet_Erlass_Titel;
                column.Width = Unit.Percentage(10);
                column.Settings.AllowHeaderFilter = DefaultBoolean.True;
                column.SettingsHeaderFilter.Mode = GridHeaderFilterMode.CheckedList;
                column.ColumnType = MVCxGridViewColumnType.Default;
                column.ReadOnly = true;
            });

            settings.Columns.Add(column =>
            {
                column.Caption = Resources.Allgemein_Mitgeandert;
                column.FieldName = "ErlasseUnbound";
                column.Width = Unit.Percentage(30);
                column.ReadOnly = true;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.Settings.ShowFilterRowMenu = DefaultBoolean.False;
                column.UnboundType = UnboundColumnType.String;
                column.Settings.AllowSort = DefaultBoolean.False;
                column.SetDataItemTemplateContent(c =>
                {
                    var dataItem = DataBinder.Eval(c.DataItem, "Erlasse");
                    if (dataItem != null)
                    {
                        IQueryable<ErlassViewModel> erlasse = dataItem as IQueryable<ErlassViewModel>;
                        List<string> tbl = new List<string>();

                        foreach (ErlassViewModel e in erlasse)
                        {
                            XDocument uebersetzung = XDocument.Parse(e.Uebersetzung);
                            string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(e.Quelle, "<u>" + e.SrNummer + "; " + e.Titel + "</u>", false);
                            tbl.Add(htmlLink);
                        }

                        Html.DevExpress().Label(labelSettings =>
                        {
                            labelSettings.Name = "Erlasse" + c.ClientID;
                            labelSettings.Text = String.Join("<br />", tbl.Distinct());
                            labelSettings.EncodeHtml = false;
                            labelSettings.PreRender += (s, e) =>
                            {
                                MVCxLabel lbl = (MVCxLabel)s;
                                lbl.Text = HighlightSearchText(lbl.Text, c.Grid.SearchPanelFilter);
                            };
                        }).Render();
                    }
                });
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "KommentarQuelle";
                column.Caption = Resources.Entitaet_Kommentar_Quelle;
                column.ColumnType = MVCxGridViewColumnType.Default;
                column.Settings.AllowHeaderFilter = DefaultBoolean.True;
                column.SettingsHeaderFilter.Mode = GridHeaderFilterMode.CheckedList;
                column.Width = Unit.Percentage(10);
                column.SetDataItemTemplateContent(
                    container =>
                    {

                        var url = DataBinder.Eval(container.DataItem, "KommentarQuelle");
                        string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, "Link", false);
                        ViewContext.Writer.Write(htmlLink);
                    });
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "Beschluss";
                column.Caption = Resources.Entitaet_Erlassfassung_Beschluss;
                column.ColumnType = MVCxGridViewColumnType.DateEdit;
                column.PropertiesEdit.DisplayFormatString = "d";
                column.Width = Unit.Percentage(5);
                column.Settings.AllowHeaderFilter = DefaultBoolean.True;
                column.SettingsHeaderFilter.Mode = GridHeaderFilterMode.DateRangePicker;
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "Inkrafttretung";
                column.Caption = Resources.Entitaet_Erlassfassung_Inkrafttretung;
                column.ColumnType = MVCxGridViewColumnType.DateEdit;
                column.PropertiesEdit.DisplayFormatString = "d";
                column.Width = Unit.Percentage(5);
                column.Settings.AllowHeaderFilter = DefaultBoolean.True;
                column.SettingsHeaderFilter.Mode = GridHeaderFilterMode.DateRangePicker;
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "StandorteUnbound";
                column.Caption = CultureHelper.GetStandortMenuName(0, 0, false);
                column.Width = Unit.Percentage(30);
                column.ReadOnly = true;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.Settings.ShowFilterRowMenu = DefaultBoolean.False;
                column.UnboundType = UnboundColumnType.String;
                column.Settings.AllowSort = DefaultBoolean.False;
                column.Settings.AllowHeaderFilter = DefaultBoolean.True;
                column.SettingsHeaderFilter.Mode = GridHeaderFilterMode.CheckedList;
                column.SetDataItemTemplateContent(c =>
                {
                    var dataItem = DataBinder.Eval(c.DataItem, "Standorte");
                    if (dataItem != null)
                    {
                        IQueryable<StandortViewModel> standorte = dataItem as IQueryable<StandortViewModel>;
                        List<string> tbl = new List<string>();

                        foreach (StandortViewModel st in standorte)
                        {
                            tbl.Add(st.Name);
                        }

                        Html.DevExpress().Label(labelSettings =>
                        {
                            labelSettings.Name = "Standorte" + c.ClientID;
                            labelSettings.Text = String.Join("<br />", tbl.Distinct());
                            labelSettings.EncodeHtml = false;
                            labelSettings.PreRender += (s, e) =>
                            {
                                MVCxLabel lbl = (MVCxLabel)s;
                                lbl.Text = HighlightSearchText(lbl.Text, c.Grid.SearchPanelFilter);
                            };
                        }).Render();
                    }
                });
            });



            settings.Columns.Add(column =>
            {
                column.FieldName = "NeosysKommentar";
                column.Caption = Resources.View_Kundendokument_Erlassfassung_NeosysKommentar;
                column.Settings.AllowHeaderFilter = DefaultBoolean.True;
                column.SettingsHeaderFilter.Mode = GridHeaderFilterMode.CheckedList;
                column.SetDataItemTemplateContent(container =>
                {

                    var url = DataBinder.Eval(container.DataItem, "NeosysKommentar");
                    string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, "Link");
                    ViewContext.Writer.Write(StringHelper.TruncateHtml(htmlLink, 50));
                });
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "KommentarShortText";
                column.Caption = Resources.Entitaet_Kommentar_ShortText;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.Settings.AllowHeaderFilter = DefaultBoolean.False;
                column.ColumnType = MVCxGridViewColumnType.Memo;
                var memoProp = column.PropertiesEdit as MemoProperties;
                if (memoProp != null)
                {
                    memoProp.EncodeHtml = false;
                }
                column.ReadOnly = true;
                column.Width = Unit.Percentage(10);

            });

            MVCxGridViewColumn massnahmeColumn = settings.Columns.Add("Massnahme");
            massnahmeColumn.Caption = Resources.Entitaet_Massnahme_Singular;
            massnahmeColumn.ReadOnly = true;
            massnahmeColumn.Settings.AllowHeaderFilter = DefaultBoolean.True;
            massnahmeColumn.SettingsHeaderFilter.Mode = GridHeaderFilterMode.CheckedList;
            massnahmeColumn.Settings.AllowAutoFilter = DefaultBoolean.False;
            massnahmeColumn.Settings.ShowFilterRowMenu = DefaultBoolean.False;

            massnahmeColumn.SetDataItemTemplateContent(container =>
            {

                var kommentarId = container.KeyValue;

                var standortId = (string) DataBinder.Eval(container.DataItem, "StandortIDs");
                var kundeId = (int)DataBinder.Eval(container.DataItem, "KundeID");
                var erlassNr = (string)DataBinder.Eval(container.DataItem, "ErlassNummer");
                var erlassTitel = (string)DataBinder.Eval(container.DataItem, "ErlassName");
                var betroffen = (bool)DataBinder.Eval(container.DataItem, "Betroffen");

                Html.DevExpress().Button(btn =>
                {
                    btn.Text = Resources.Allgemein_Button_Massnahme;
                    btn.Name = "Massnahme_" + container.KeyValue;
                    btn.Attributes.Add("onClick", "javascript:ShowDetailPopup('" + Url.Action("ShowMassnahmePopupForKommentare", "Dashboard", new { kommentarID = kommentarId, standortIds = standortId, kundeID = kundeId, erlassNr = erlassNr, erlassTitel = erlassTitel, betroffen = betroffen }) + "','pcModalMode_Massnahme_Erfassen_Kommentare');");
                }).Render();

                var MassnahmeNewCount = (int)DataBinder.Eval(container.DataItem, "MassnahmeNewCount");
                var MassnahmeInProgressCount = (int)DataBinder.Eval(container.DataItem, "MassnahmeInProgressCount");
                var MassnahmeFinishedCount = (int)DataBinder.Eval(container.DataItem, "MassnahmeFinishedCount");

                ViewContext.Writer.Write("<br></br>");
                ViewContext.Writer.Write("<div style='display:inline-block'>" + Resources.Enum_MassnahmeStatus_New + " (" + MassnahmeNewCount + ")" + "</div>");
                ViewContext.Writer.Write("<br>");
                ViewContext.Writer.Write("<div style='display:inline-block'>" + Resources.Enum_MassnahmeStatus_InProgress + " (" + MassnahmeInProgressCount + ")" + "</div>");
                ViewContext.Writer.Write("<br>");
                ViewContext.Writer.Write("<div style='display:inline-block'>" + Resources.Enum_MassnahmeStatus_Finished + " (" + MassnahmeFinishedCount + ")" + "</div>");
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "NewsletterDate";
                column.Caption = "NewsletterDate";
                column.ColumnType = MVCxGridViewColumnType.DateEdit;
                column.SortIndex = 0;
                column.SortOrder = ColumnSortOrder.Descending;
                column.Visible = false;
            });


            settings.CustomUnboundColumnData = (s, e) =>
            {
                if (e.Column.FieldName == "ErlasseUnbound")
                {
                    var dataItem = e.GetListSourceFieldValue("Erlasse");
                    if (dataItem != null)
                    {
                        IQueryable<ErlassViewModel> erlasse = dataItem as IQueryable<ErlassViewModel>;
                        List<string> tbl = new List<string>();

                        foreach (ErlassViewModel es in erlasse)
                        {
                            XDocument uebersetzung = XDocument.Parse(es.Uebersetzung);
                            string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(es.Quelle, "<u>" + es.SrNummer + "; " + es.Titel + "</u>", false);
                            e.Value = String.Join("<br />", htmlLink);
                        }
                    }
                }
                if (e.Column.FieldName == "StandorteUnbound")
                {
                    var dataItem = e.GetListSourceFieldValue("Standorte");
                    if (dataItem != null)
                    {
                        IQueryable<StandortViewModel> standorte = dataItem as IQueryable<StandortViewModel>;
                        List<string> tbl = new List<string>();

                        foreach (StandortViewModel st in standorte)
                        {
                            tbl.Add(st.Name);
                        }
                    }
                }
            };

            settings.AutoFilterCellEditorCreate = (s, e) =>
            {
                if (e.Column.FieldName == "ErlasseUnbound")
                {
                    ComboBoxProperties combo = new ComboBoxProperties();
                    e.EditorProperties = combo;
                }
                if (e.Column.FieldName == "StandorteUnbound")
                {
                    ComboBoxProperties combo = new ComboBoxProperties();
                    e.EditorProperties = combo;
                }
                if (e.Column.FieldName == "Massnahme")
                {
                    ComboBoxProperties combo = new ComboBoxProperties();
                    e.EditorProperties = combo;
                }
            };

            settings.AutoFilterCellEditorInitialize = (s, e) =>
            {
                var gv = s as MVCxGridView;
                var expr = gv.FilterExpression;
                if (e.Column.FieldName == "ErlasseUnbound")
                {
                    MVCxComboBox cb = ((MVCxComboBox)e.Editor);

                    List<int> erlassIds = new List<int>();
                    var test = Model.Select(x => x.ErlasseCollection).ToList();
                    foreach (var test2 in test)
                    {
                        foreach (var test3 in test2)
                        {
                            if (!erlassIds.Contains(test3.ErlassID))
                            {
                                erlassIds.Add(test3.ErlassID);
                            }
                        }
                    }
                    cb.DataSource = unitOfWork.ErlassRepository.GetErlassComboBoxByIds(erlassIds).Select(x => x.Titel);
                    cb.DataBindItems();
                    if (Session["ErlasseFilter"] != null && expr.Contains("Erlasse"))
                    {
                        e.Editor.Value = Session["ErlasseFilter"];
                    }
                }
                if (e.Column.FieldName == "StandorteUnbound")
                {
                    MVCxComboBox cb = ((MVCxComboBox)e.Editor);
                    cb.DataSource = unitOfWork.StandortRepository.GetStandortComboBoxByIds(standortIds).Select(x => x.Name);
                    cb.DataBindItems();
                    if (Session["StandorteFilter"] != null && expr.Contains("Standorte"))
                    {
                        var test5 = Session["StandorteFilter"];
                        e.Editor.Value = Session["StandorteFilter"];
                    }
                }
            };

            settings.ProcessColumnAutoFilter = (sender, e) =>
            {
                var gv = sender as MVCxGridView;
                if (e.Column.FieldName == "ErlasseUnbound")
                {
                    string value = e.Value;
                    if (gv.FilterExpression != "")
                    {
                        e.Criteria = CriteriaOperator.Parse(gv.FilterExpression + "OR Erlasse[contains(Titel, ?)]", value);
                        gv.FilterExpression = null;
                        Session["ErlasseFilter"] = "";
                    }
                    else
                    {
                        e.Criteria = CriteriaOperator.Parse("Erlasse[contains(Titel, ?)]", value);
                        Session["ErlasseFilter"] = e.Value;
                    }
                }
                if (e.Column.FieldName == "StandorteUnbound")
                {
                    string value = e.Value;
                    if (gv.FilterExpression != "")
                    {
                        e.Criteria = CriteriaOperator.Parse(gv.FilterExpression + "OR Standorte[contains(Name, ?)]", value);
                        gv.FilterExpression = null;
                        Session["StandorteFilter"] = "";
                    }
                    else
                    {
                        e.Criteria = CriteriaOperator.Parse("Standorte[contains(Name, ?)]", value);
                        Session["StandorteFilter"] = e.Value;
                    }
                }
            };

            settings.BeforeHeaderFilterFillItems = (sender, e) =>
            {
                if (e.Column.FieldName == "Massnahme")
                {
                    e.AddValue("Neu", new BinaryOperator("MassnahmeNewCount", 1, BinaryOperatorType.GreaterOrEqual));
                    e.AddValue("In Arbeit", new BinaryOperator("MassnahmeInProgressCount", 1, BinaryOperatorType.GreaterOrEqual));
                    e.AddValue("Erledigt", new BinaryOperator("MassnahmeFinishedCount", 1, BinaryOperatorType.GreaterOrEqual));
                    e.Handled = true;
                }

                if (e.Column.FieldName == "StandorteUnbound")
                {
                    List<StandortViewModel> viewModels = unitOfWork.StandortRepository.GetStandortComboBoxByIds(standortIds).ToList();
                    foreach (StandortViewModel viewModel in viewModels)
                    {
                        e.AddValue(viewModel.Name, CriteriaOperator.Parse("Standorte[contains(Name, ?)]", viewModel.Name));
                    }
                    Session["StandorteFilter"] = ""; // set StandorteFielder to "" if header filter is used, otherwise it could be that a wrong value is displayed in the combo box
                    e.Handled = true;
                }

            };

        });
        if (ViewData["EditError"] != null)
        {
            grid.SetEditErrorText((string)ViewData["EditError"]);
        }

        grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
        {
            e.QueryableSource = Model;
            e.KeyExpression = "KommentarNewsletterID";
        }).GetHtml();
    }

}

