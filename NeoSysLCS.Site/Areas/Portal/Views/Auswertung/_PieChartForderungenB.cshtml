@using System.Drawing
@using NeoSysLCS.Repositories
@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers

@{
    UnitOfWork _unitOfWork = new UnitOfWork();

    SessionHelper helper = new SessionHelper();
    ViewData["KundeID"] = helper.GetKundeIdOfCurrentUser();

    var selected = _unitOfWork.StandortRepository.GetStandortViewModelById(Int16.Parse(ViewData["StandortID"].ToString()));

    Html.DevExpress().Chart(settings =>
    {
        settings.Name = "ChartB";
        settings.Width = 525;
        settings.Height = 400;
        settings.Legend.AlignmentHorizontal = LegendAlignmentHorizontal.RightOutside;

        Series series = new Series("B",
            DevExpress.XtraCharts.ViewType.Pie);
        series.ArgumentDataMember = "name";

        series.ValueDataMembers[0] = "value";
        series.LabelsVisibility = DefaultBoolean.True;

        //Prevent overlapping
        ((PieSeriesLabel)series.Label).Position = PieSeriesLabelPosition.Outside;
        ((PieSeriesLabel)series.Label).ResolveOverlappingMode = ResolveOverlappingMode.Default;

        series.Label.TextPattern = "{A}: {V:G}";

        settings.CustomDrawSeriesPoint = (sender, e) =>
        {

            PieDrawOptions options = (PieDrawOptions)e.SeriesDrawOptions;
            // GradientFillOptionsBase gradientOptions = ((GradientFillOptionsBase)options.FillStyle.Options);
            PieDrawOptions legendDrawOptions = (PieDrawOptions)e.LegendDrawOptions;
            options.FillStyle.FillMode = FillMode.Gradient;
            if (e.SeriesPoint.Argument == Resources.Chart_Label_Erfuellt)
            {
                options.Color = Color.FromArgb(151, 184, 87);
                options.FillStyle.FillMode = FillMode.Solid;
                legendDrawOptions.Color = Color.FromArgb(151, 184, 87);
                legendDrawOptions.FillStyle.FillMode = FillMode.Solid;
            }
            else if (e.SeriesPoint.Argument == Resources.Chart_Label_InAbklaeurung)
            {
                options.Color = Color.FromArgb(75, 123, 181);
                options.FillStyle.FillMode = FillMode.Solid;
                legendDrawOptions.Color = Color.FromArgb(75, 123, 181);
                legendDrawOptions.FillStyle.FillMode = FillMode.Solid;
            }
            else if (e.SeriesPoint.Argument == Resources.Chart_Label_NichtErfuellt)
            {
                options.Color = Color.FromArgb(176, 71, 68);
                options.FillStyle.FillMode = FillMode.Solid;
                legendDrawOptions.Color = Color.FromArgb(176, 71, 68);
                legendDrawOptions.FillStyle.FillMode = FillMode.Solid;
            }
            else if (e.SeriesPoint.Argument == Resources.Chart_Label_NotEdited)
            {
                options.Color = Color.FromArgb(255, 204, 0);
                options.FillStyle.FillMode = FillMode.Solid;
                legendDrawOptions.Color = Color.FromArgb(255, 204, 0);
                legendDrawOptions.FillStyle.FillMode = FillMode.Solid;
            }
        };
        settings.CustomPaint = (sender, e) =>
        {
            MVCxChartControl chart = (MVCxChartControl)sender;
            if (!chart.Series.SelectMany(s => s.ActualPoints).Any())
            {
                e.Graphics.FillRectangle(Brushes.White, e.Bounds);
                e.Graphics.DrawString(chart.EmptyChartText.Text, chart.EmptyChartText.Font, Brushes.Black, e.Bounds,
                    new StringFormat() { Alignment = StringAlignment.Center, LineAlignment = StringAlignment.Center });
            }
        };


        settings.Titles.Add(new ChartTitle()
        {
            Alignment = StringAlignment.Far,
            Dock = ChartTitleDockStyle.Bottom,
            Font = new Font("Arial", 8),
            TextColor = Color.Gray,
            Text = "Lexplus - " + DateTime.Now.ToString("dd.MM.yy")
        });

        settings.BoundDataChanged = (s, e) =>
        {
            var chart = (MVCxChartControl)s;
            var view = (PieSeriesViewBase)chart.Series[0].View;

            var myText = chart.EmptyChartText;
            myText.EnableAntialiasing = DefaultBoolean.True;
            myText.Text = Resources.Chart_Keine_Datensaetze;
            myText.TextColor = Color.Beige;

            SeriesPoint explodedPoint = null;
            foreach (SeriesPoint point in chart.Series[0].Points)
            {
                if (point.Argument == Resources.Chart_Label_Erfuellt)
                {
                    explodedPoint = point;
                    break;
                }
            }
            if (explodedPoint != null)
            {
                view.ExplodedPoints.Add(explodedPoint);
            }

        };

        settings.Series.Add(series);

    }).Bind(_unitOfWork.KundendokumentForderungsversionRepository.GetAllRelevantKundendokumentForderungsversionViewModelsBCount(ViewData["KundeID"], Int16.Parse(ViewData["StandortID"].ToString()))).GetHtml();

}