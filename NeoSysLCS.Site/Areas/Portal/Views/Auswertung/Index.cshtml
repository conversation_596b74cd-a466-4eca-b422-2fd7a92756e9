@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Resources.Properties

@model IQueryable<StandortViewModel>

@section Scripts {
    @Scripts.Render("~/Scripts/helpers/BatchEditDeleteHelper.js")
}

@{
    ViewBag.Title = Resources.Menu_Auswertungen + " " + NeoSysLCS.Site.Helpers.CultureHelper.GetStandortMenuName();
    <div class="page-header" style="display:inline-block">
        <h1 style="display:inline-block">@Resources.Menu_Auswertung @NeoSysLCS.Site.Helpers.CultureHelper.GetStandortMenuName(0,0,false)</h1>
        <button type="button" class="btn btn-default" style="border:none; background-color: transparent; outline:none; padding-left: 5px; padding-right: 0px; padding-bottom:20px; padding-top: 5px"
                onclick="javascript: ShowDetailPopup('/Portal/FAQPortal/ShowFAQPopup?view=Diagramme', 'pcModalMode_FAQ_Auswertung');">
            <img src="~/Content/images/Question_Mark.png" style="width:20px;height:20px" />
        </button>
    </div>
}

<script type="text/javascript">
    function ShowDetailPopup(url, win) {
        modal = eval(win);

        if (window.height < 600) {
            modal.SetHeight(window.height - 50);
        } else {
            modal.SetHeight(600);
        }

        if (window.width < 800) {
            modal.SetWidth(window.width - 50);
        } else {
            modal.SetWidth(800);
        }

        modal.SetContentUrl(url);
        modal.Show();
    }
</script>

@Html.Partial("StandortAuswertung", Model)

@Html.DevExpress().PopupControl(settings =>
{

    settings.Name = "pcModalMode_FAQ_Auswertung";
    settings.ShowPageScrollbarWhenModal = true;
    settings.ResizingMode = ResizingMode.Live;
    settings.ShowRefreshButton = false;
    settings.AutoUpdatePosition = true;
    settings.AllowDragging = true;
    settings.CloseAction = CloseAction.CloseButton;
    settings.PopupAnimationType = AnimationType.None;
    settings.HeaderText = "FAQ";
    settings.Modal = true;
    settings.AllowResize = true;
    settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
    settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
    settings.ClientSideEvents.Init = "function(s, e) { s.SetWidth(400); }";
    settings.ClientSideEvents.Shown = "function(s, e) { s.SetWidth(400); }";
}).GetHtml()