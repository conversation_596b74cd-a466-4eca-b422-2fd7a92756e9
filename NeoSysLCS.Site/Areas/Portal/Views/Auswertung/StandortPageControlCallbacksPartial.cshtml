@using System.Web.UI.WebControls
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Repositories

@*<script type="text/javascript">
    function OnClick(s, e) {
        if (Months != null) {
            var selectedItems = Months.GetSelectedValues();
        }
    }
</script>*@

@Html.DevExpress().PageControl(pageControlSettings =>
{
    pageControlSettings.Name = "KundendokumentPageControl";
    pageControlSettings.Width = Unit.Percentage(100);
    pageControlSettings.Height = Unit.Percentage(100);
    pageControlSettings.SaveStateToCookies = true;
    pageControlSettings.CallbackRouteValues = new { Controller = "Auswertung", Action = "StandortPageControlCallbacksPartial", standortID = ViewData["StandortID"], spracheID = ViewData["SpracheID"] };

    pageControlSettings.TabPages.Add(@Resources.View_PortalDashboard_Erfüllungsgrad).SetContent(() =>
    {
        Html.RenderPartial("_PieChartForderungenFullfilment");
    });

    pageControlSettings.TabPages.Add(Resources.View_Auswertungen_Bewilligungspflichtig).SetContent(() =>
    {
        Html.RenderPartial("_PieChartForderungenB");
    });

    pageControlSettings.TabPages.Add(Resources.View_Auswertungen_Nachweispflichtig).SetContent(() =>
    {
        Html.RenderPartial("_PieChartForderungenN");
    });

    pageControlSettings.TabPages.Add(Resources.Entitaet_StandortObjekt_Plural).SetContent(() =>
    {
        Html.RenderPartial("_PieChartForderungenStandortobjekte");
    });

    pageControlSettings.TabPages.Add(Resources.Entitaet_Rechtsbereich_Plural).SetContent(() =>
    {
        Html.RenderPartial("_PieChartForderungenRechtsbereiche");
    });

    pageControlSettings.TabPages.Add(Resources.View_Auswertungen_Tab_Verantwortlichkeiten).SetContent(() =>
    {
        Html.RenderPartial("_PieChartForderungenShortcut");
    });
}).GetHtml()