@using System.Web.UI.WebControls
@using DevExpress.XtraBars.Docking
@using NeoSysLCS.Repositories
@using NeoSysLCS.Resources.Properties


@Html.DevExpress().PopupControl(settings =>
{
    IUnitOfWork _unitOfWork = new UnitOfWork();

    settings.Name = "NewStandortobj";
    settings.ScrollBars = ScrollBars.Auto;
    settings.ShowPageScrollbarWhenModal = true;
    settings.ResizingMode = ResizingMode.Live;
    settings.ScrollBars = ScrollBars.Auto;
    settings.AutoUpdatePosition = true;
    settings.AllowDragging = true;
    settings.CloseAction = CloseAction.CloseButton;
    settings.PopupAnimationType = AnimationType.None;
    settings.HeaderText = Resources.Filter_Standortobjekt;
    settings.Modal = true;
    settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
    settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
    settings.SetContent(() =>
    {
        Html.RenderPartial("_RowSelectionPartial", _unitOfWork.ObjektkategorieRepository.GetTreeObjectsByKundendokument((int)ViewData["KundendokumentID"], (int)ViewData["SpracheID"]));
        Html.DevExpress().Button(
            buttonSettings =>
            {
                buttonSettings.Name = "btnCancel";
                buttonSettings.ControlStyle.CssClass = "btn btn-default";
                buttonSettings.Width = 80;
                buttonSettings.Text = Resources.Button_Abbrechen;
                buttonSettings.ClientSideEvents.Click = "function(s, e){ NewStandortobj.Hide(); }";
            }
        )
        .Render();
        Html.DevExpress().Button(
            buttonSettings =>
            {
                buttonSettings.Name = "btnSave";
                buttonSettings.ControlStyle.CssClass = "btn btn-default pull-right";
                buttonSettings.Width = 80;
                buttonSettings.Text = Resources.Button_Speichern;
                buttonSettings.ClientSideEvents.Click = @"function(s, e){
                                                            NewStandortObjektGrid.GetSelectedNodeValues('ObjektID;Name;Beschreibung', GetSelectedFieldValuesCallback);
                                                            NewStandortobj.Hide();
                                                            }";
            }

        )
        .Render();
    }
    );
}).GetHtml()



