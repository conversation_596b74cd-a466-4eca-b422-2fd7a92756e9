@using NeoSysLCS.Repositories.ViewModels

@model KundendokumentErlassfassungViewModel

<div>
    <div class="row">
        <div class="col-md-2">@Html.LabelFor(model => model.Beschluss)</div>
        <div class="col-md-10">@Model.Beschluss.ToString("dd.MM.yyyy")</div>
    </div>
    <div class="row">
        <div class="col-md-2">@Html.LabelFor(model => model.Inkrafttretung)</div>
        <div class="col-md-10">@Model.Inkrafttretung.ToString("dd.MM.yyyy")</div>
    </div>
    <div class="row">
        <div class="col-md-2">@Html.LabelFor(model => model.Quelle)</div>
        <div class="col-md-10"><a href="@Model.Quelle" target="_blank">@Model.Quelle</a></div>
    </div>
    @if (Model.Betroffen)
    {
        <div class="row">
            <div class="col-md-2">@Html.LabelFor(model => model.BetroffenKommentar)</div>
            <div class="col-md-10">@Html.Raw(Model.LinkBetroffenKommentar != null && Model.LinkBetroffenKommentar != "---" ? (Model.LinkBetroffenKommentar.StartsWith("www.") ? "<a href=http://" + @Model.LinkBetroffenKommentar + " target=" + "_blank" + ">" + @Model.LinkBetroffenKommentar + "</a>"  
                                  : "<a href=" + @Model.LinkBetroffenKommentar + " target=" + "_blank" + ">" + @Model.LinkBetroffenKommentar + "</a>" ): Model.BetroffenKommentar)</div>
        </div>
    }
    else
    {
        <div class="row">
            <div class="col-md-2">@Html.Label("Kommentar")</div>
            <div class="col-md-10">@Html.Raw(Model.LinkNichtBetroffenKommentar != null && Model.LinkBetroffenKommentar != "---" ? (Model.LinkNichtBetroffenKommentar.StartsWith("www.") ? "<a href=http://" + @Model.LinkNichtBetroffenKommentar + " target=" + "_blank" + ">" + @Model.LinkNichtBetroffenKommentar + "</a>"
                                  : "<a href=" + @Model.LinkNichtBetroffenKommentar + " target=" + "_blank" + ">" + @Model.LinkNichtBetroffenKommentar + "</a>") : Model.NichtBetroffenKommentar)</div>
        </div>
    }
   
</div>


