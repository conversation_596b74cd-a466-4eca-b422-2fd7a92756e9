@using NeoSysLCS.Repositories.ViewModels

@model ForderungsversionViewModel


<div>
    <div class="row">
        <div class="col-md-2">@Html.LabelFor(model => model.Beschreibung)</div>
        <div class="col-md-10">@Html.Raw(Model.Beschreibung)</div>
    </div>
    <div class="row">
        <div class="col-md-2">@Html.LabelFor(model => model.ArtikelNummer)</div>
        <div class="col-md-10">@Model.ArtikelNummer</div>
    </div>
    <div class="row">
        <div class="col-md-2">@Html.LabelFor(model => model.Inkrafttretung)</div>
        <div class="col-md-10">@Model.Inkrafttretung.ToString("dd.MM.yyyy")</div>
    </div>
    <div class="row">
        <div class="col-md-2">@Html.LabelFor(model => model.Bewilligungspflicht)</div>
        <div class="col-md-10">@Html.CheckBoxFor(m => m.Bewilligungspflicht, new { @disabled = "disabled" })</div>
    </div>
    <div class="row">
        <div class="col-md-2">@Html.LabelFor(model => model.Nachweispflicht)</div>
        <div class="col-md-10">@Html.CheckBoxFor(m => m.Nachweispflicht, new { @disabled = "disabled" })</div>
    </div>
 
</div>


