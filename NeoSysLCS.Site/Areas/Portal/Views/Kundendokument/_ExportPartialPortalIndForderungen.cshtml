@using NeoSysLCS.Resources.Properties
@model  int

@{
    //Html.DevExpress().HyperLink(hl =>
    //{
    //    hl.Name = "KundendokumentExport_" + Model;
    //    hl.Properties.ClientSideEvents.Click = "function(s,e){ popupExport_" + Model + ".Show()}";
    //    hl.Properties.Text = Resources.XSL_Export_Export;
    //    hl.ToolTip = Resources.XSL_Export_Export + " Excel";
    //    hl.Properties.ImageUrl = "~/Content/images/file-excel-o_26.png";
    //}).Render();

    Html.DevExpress().PopupControl(settings =>
    {
        settings.Name = "ExportIndForderungen";
        settings.Width = 350;
        settings.Height = 100;
        settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
        settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
        settings.AllowDragging = true;
        settings.EnableHotTrack = true;
        settings.ShowFooter = true;
        settings.Modal = true;
        settings.HeaderText = "Kundendokument exportieren";
        settings.CloseAction = CloseAction.CloseButton;

        settings.SetContent(() =>
        {
            ViewContext.Writer.Write(Resources.Export_Popup_1 + "<br><br>" + Resources.Export_Popup_2);
            Html.DevExpress().HyperLink(hl =>
            {
                hl.Name = "MailLinkInd" + Model;
                hl.Properties.Text = "<EMAIL>";
                hl.NavigateUrl = "mailto: <EMAIL>";
                hl.ControlStyle.Font.Underline = true;
            }).Render();
        });

        settings.SetFooterTemplateContent(c =>
        {
            ViewContext.Writer.Write("<div style=\"overflow: hidden\"><div style=\"padding: 3px; float: right;\">");
            Html.DevExpress().Button(
            buttonSettings =>
            {
                buttonSettings.Name = "btnUpdateInd" + Model;
                buttonSettings.Width = 50;
                buttonSettings.Text = Resources.XSL_Export_Export;
                buttonSettings.UseSubmitBehavior = true;
                buttonSettings.ClientSideEvents.Click = string.Format("function(s, e) {{ window.open('{0}'); ExportIndForderungen.Hide(); }}", @Url.Action("IndForderungenExportDataAware", "Kundendokument", new { Area = "", standortId = ViewData["StandortID"], kundendokumentID = Model }));
                buttonSettings.ControlStyle.CssClass = "btn btn-sm pull-right";
                buttonSettings.Style.Add(HtmlTextWriterStyle.MarginLeft, "10px");
            })
            .Render();
            ViewContext.Writer.Write(" ");
            Html.DevExpress().Button(
                buttonSettings =>
                {
                    buttonSettings.Name = "btnCancelInd" + Model;
                    buttonSettings.Width = 50;
                    buttonSettings.Text = Resources.Button_Abbrechen;
                    buttonSettings.ClientSideEvents.Click = "function(s,e){ ExportIndForderungen.Hide()}";
                    buttonSettings.ControlStyle.CssClass = "btn btn-sm pull-right";
                    buttonSettings.Style.Add(HtmlTextWriterStyle.MarginLeft, "10px");
                }
            )
            .Render();
            ViewContext.Writer.Write("</div></div>");
        });
    }).Render();
}