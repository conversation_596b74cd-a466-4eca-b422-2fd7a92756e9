
@using NeoSysLCS.Repositories
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Site.Helpers
@using NeoSysLCS.DomainModel.Models

@model  IQueryable<KundendokumentForderungsversionViewModel>

@{
    var grid = Html.DevExpress().GridView(settings =>
    {
        IUnitOfWork unitOfWork = new UnitOfWork();
        settings.Name = "PortalForderungenAdditionalDataGridView_" + ViewData["KundendokumentForderungsversionID"];
        settings.KeyFieldName = "KundendokumentForderungsversionID";
        var sessionHelper = new SessionHelper();
        var user = unitOfWork.UserRepository.GetUserViewModelById(sessionHelper.GetCurrentUser().Id);

        GridViewHelper.ApplyDefaultSettings(settings);


        settings.SettingsContextMenu.Enabled = false;
        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;
        settings.Settings.ShowGroupPanel = false;
        settings.Settings.ShowFilterRow = false;
        settings.Settings.ShowFooter = false;
        settings.SettingsPager.AlwaysShowPager = false;
        settings.SettingsPager.FirstPageButton.Visible = false;
        settings.SettingsPager.LastPageButton.Visible = false;
        settings.SettingsPager.PageSizeItemSettings.Visible = false;
        settings.CallbackRouteValues = new
        {
            Controller = "Kundendokument",
            Action = "ForderungsversionAdditionalDataGridView",
            standortID = ViewData["StandortID"],
            kundendokumentID = ViewData["KundendokumentID"],
            kundendokumentForderungsversionsID = ViewData["KundendokumentForderungsversionID"]
        };

        if (unitOfWork.KundendokumentRepository.IsCurrentKundendokument((int?)ViewData["StandortID"], (int?)ViewData["KundendokumentID"]) &&
            (ViewData["roleId"].ToString() == "9536dddb-bfaa-47ee-9fba-c0077e38c9d3" || User.IsInRole(Role.SuperUser)))
        {
            //Kundendokument is editable
            settings.SettingsEditing.BatchUpdateRouteValues = new
            {
                Controller = "Kundendokument",
                Action = "ForderungsversionAdditionalDataGridViewBatchEdit",
                standortID = ViewData["StandortID"],
                kundendokumentID = ViewData["KundendokumentID"],
                kundendokumentForderungsversionsID = ViewData["KundendokumentForderungsversionID"]

            };
            settings.SettingsEditing.Mode = GridViewEditingMode.Batch;
            settings.CommandColumn.Visible = true;
            settings.CommandColumn.ShowNewButtonInHeader = false;
            settings.CommandColumn.ShowEditButton = false;
        }

        else if ((User.IsInRole(Role.ReadOnly) && user.WriteStandorte.Count(s => s.StandortID == (int)ViewData["StandortID"]) == 1) || User.IsInRole(Role.EndUser))
        {
            //Kundendokument is editable
            settings.SettingsEditing.BatchUpdateRouteValues = new
            {
                Controller = "Kundendokument",
                Action = "ForderungsversionAdditionalDataGridViewBatchEdit",
                standortID = ViewData["StandortID"],
                kundendokumentID = ViewData["KundendokumentID"],
                kundendokumentForderungsversionsID = ViewData["KundendokumentForderungsversionID"]

            };
            settings.SettingsEditing.Mode = GridViewEditingMode.Batch;
            settings.CommandColumn.Visible = true;
            settings.CommandColumn.ShowNewButtonInHeader = false;
            settings.CommandColumn.ShowEditButton = false;
        }

        //Columns
        settings.Columns.Add(column =>
        {
            column.FieldName = "Spalte1";
            column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
        });
        settings.Columns.Add(column =>
        {
            column.FieldName = "Spalte2";
            column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
        });
        settings.Columns.Add(column =>
        {
            column.FieldName = "Spalte3";
            column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
        });
        settings.Columns.Add(column =>
        {
            column.FieldName = "Spalte4";
            column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
        });
        settings.Columns.Add(column =>
        {
            column.FieldName = "Spalte5";
            column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
        });
        settings.Columns.Add(column =>
        {
            column.FieldName = "Spalte6";
            column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
        });
        settings.Columns.Add(column =>
        {
            column.FieldName = "Spalte7";
            column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
        });
        settings.Columns.Add(column =>
        {
            column.FieldName = "Spalte8";
            column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
        });
        settings.Columns.Add(column =>
        {
            column.FieldName = "Spalte9";
            column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
        });
        settings.Columns.Add(column =>
        {
            column.FieldName = "Spalte10";
            column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
        });
    });
    if (ViewData["EditError"] != null)
    {
        grid.SetEditErrorText((string)ViewData["EditError"]);
    }
}

@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = "KundendokumentForderungsversionID";
}).GetHtml()


 