@using NeoSysLCS.Repositories.ViewModels

@model ErlassViewModel

<div>
    <div class="row">
        <div class="col-md-2">@Html.LabelFor(model => model.SrNummer)</div>
        <div class="col-md-10">@Model.SrNummer</div>
    </div>
    <div class="row">
        <div class="col-md-2">@Html.LabelFor(model => model.Abkuerzung)</div>
        <div class="col-md-10">@Model.Abkuerzung</div>
    </div>
    <div class="row">
        <div class="col-md-2">@Html.LabelFor(model => model.Titel)</div>
        <div class="col-md-10">@Model.Titel</div>
    </div>
    <div class="row">
        <div class="col-md-2">@Html.LabelFor(model => model.Herausgeber)</div>
        <div class="col-md-10">@Model.Herausgeber</div>
    </div>
    <div class="row">
        <div class="col-md-2">@Html.LabelFor(model => model.Erlasstyp)</div>
        <div class="col-md-10">@Model.Erlasstyp</div>
    </div>
    <div class="row">
        <div class="col-md-2">@Html.LabelFor(model => model.Quelle)</div>
        <div class="col-md-10"><a href="@Model.Quelle" target="_blank">@Model.Quelle</a></div>
    </div>
</div>


