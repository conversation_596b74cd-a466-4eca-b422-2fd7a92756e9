@using System.Web.UI.WebControls
@using NeoSysLCS.Repositories
@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers

@model  IQueryable<KundendokumentViewModel>

@{
    var grid = Html.DevExpress().GridView(settings =>
    {
        var unitOfWork = new UnitOfWork();
        settings.Name = "AllDocumentsGridView";
        settings.KeyFieldName = "KundendokumentID";

        GridViewHelper.ApplyDefaultSettings(settings);
        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;

        settings.Settings.ShowGroupPanel = false;
        settings.SettingsContextMenu.Enabled = false;
        settings.Settings.ShowFilterRow = true;
        settings.CallbackRouteValues = new
        {
            Controller = "Downloadbereich",
            Action = "_AllDocumentsGridView",
            kundeID = ViewData["KundeID"]
        };

        //Columns

        settings.Columns.Add(column =>
        {
            column.Caption = "#";
            column.Width = Unit.Pixel(100);
            column.SetDataItemTemplateContent(container =>
            {
                ViewData["StandortID"] = DataBinder.Eval(container.DataItem, "StandortID");
                Html.RenderPartial("~/Views/KundendokumentImportExport/_ExportPartialPortal.cshtml", container.KeyValue);
            });


        });
        settings.Columns.Add(column =>
        {
            column.FieldName = "StandortID";
            column.Caption = NeoSysLCS.Site.Helpers.CultureHelper.GetStandortMenuName();
            column.ColumnType = MVCxGridViewColumnType.ComboBox;
            column.Settings.FilterMode = ColumnFilterMode.DisplayText;
            var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;
            var list = unitOfWork.StandortRepository.GetAllStandortViewModels((int)ViewData["KundeID"]).ToList();
            comboBoxProperties.DataSource = list;
            comboBoxProperties.ValueField = "StandortID";
            comboBoxProperties.TextField = "Name";
            column.ReadOnly = true;
            column.Settings.AllowHeaderFilter = DefaultBoolean.True;
            column.SettingsHeaderFilter.Mode = GridHeaderFilterMode.CheckedList;
        });
        settings.Columns.Add(column =>
        {
            column.FieldName = "PublizierenAm";
            column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentViewModel), column.FieldName);
            column.ReadOnly = true;
            column.ColumnType = MVCxGridViewColumnType.DateEdit;
            column.EditFormSettings.Visible = DefaultBoolean.False;
            column.SortDescending();
            column.Settings.AllowHeaderFilter = DefaultBoolean.True;
            column.SettingsHeaderFilter.Mode = GridHeaderFilterMode.DateRangePicker;
        });
        settings.Columns.Add(column =>
        {
            column.Caption = Resources.View_Kundendokument_Status;
            column.ReadOnly = true;
            column.ColumnType = MVCxGridViewColumnType.DateEdit;
            column.EditFormSettings.Visible = DefaultBoolean.False;
            column.SetDataItemTemplateContent(container =>
            {
                if (unitOfWork.KundendokumentRepository.IsCurrentKundendokument((int?)DataBinder.Eval(container.DataItem, "StandortID"), (int?)DataBinder.Eval(container.DataItem, "KundendokumentID")))
                {
                    ViewContext.Writer.Write(Resources.View_Kundendokument_Current);
                }
                else
                {
                    ViewContext.Writer.Write(Resources.View_Kundendokument_Old);
                }
            });
            column.SortDescending();
        });
    });

    if (ViewData["EditError"] != null)
    {
        grid.SetEditErrorText((string)ViewData["EditError"]);
    }
}


@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = "KundendokumentID";
}).GetHtml()
