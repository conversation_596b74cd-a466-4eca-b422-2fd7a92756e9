@using System.Web.UI.WebControls
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers
@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Repositories


@model IQueryable<ApplicationUserNewsletterHistoryViewModel>


@{
    var grid = Html.DevExpress().GridView(settings =>
    {
        IUnitOfWork unitOfWork = new UnitOfWork();

        settings.Name = "NewsletterHistoryGridView";
        settings.KeyFieldName = "ID";

        GridViewHelper.ApplyDefaultSettings(settings);
        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;

        settings.SettingsContextMenu.Enabled = false;
        settings.Settings.ShowGroupPanel = false;
        settings.Settings.ShowFilterRow = false;
        settings.CallbackRouteValues = new { Controller = "Downloadbereich", Action = "_NewsletterHistoryGridView" };

        settings.Columns.Add(column =>
        {
            column.Caption = "#";
            column.Width = Unit.Pixel(100);
            column.SetDataItemTemplateContent(container =>
            {

                var url = (string)DataBinder.Eval(container.DataItem, "NewsletterFileLink");
                if (!string.IsNullOrEmpty(url) && url != "---")
                {
                    url = !url.StartsWith("http") ? "http://" + url : url;
                    Html.DevExpress().HyperLink(hyperlink =>
                    {
                        hyperlink.EncodeHtml = false;
                        hyperlink.Properties.Text = "<i class=\" fa fa-link\"></i>";
                        hyperlink.NavigateUrl = url;
                        hyperlink.Properties.Target = "_blank";
                    }).Render();

                    ViewContext.Writer.Write("&nbsp;&nbsp;");
                }
            });


        });
        settings.Columns.Add(column =>
        {
            column.FieldName = "DateSent";
            column.Caption = Resources.View_Newsletter_DateSent;
            column.ReadOnly = true;
            column.ColumnType = MVCxGridViewColumnType.DateEdit;
            column.EditFormSettings.Visible = DefaultBoolean.False;
            column.SortDescending();
        });

        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);
    });

    if (ViewData["EditError"] != null)
    {
        grid.SetEditErrorText((string)ViewData["EditError"]);
    }
}
@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = "ID";
}).GetHtml()