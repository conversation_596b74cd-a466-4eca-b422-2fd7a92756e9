@using System.Web.UI.WebControls
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers
@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Repositories


@model IQueryable<NeoSysLCS.Repositories.ViewModels.StandortBerichtViewModel>


@{
    var grid = Html.DevExpress().GridView(settings =>
    {
        IUnitOfWork unitOfWork = new UnitOfWork();

        settings.Name = "StandorteMitBerichtGridView";
        settings.KeyFieldName = "StandortBerichtID";

        GridViewHelper.ApplyDefaultSettings(settings);
        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;

        settings.SettingsContextMenu.Enabled = false;
        settings.Settings.ShowGroupPanel = false;
        settings.Settings.ShowFilterRow = true;
        settings.CallbackRouteValues = new { Controller = "Downloadbereich", Action = "_StandorteMitBerichtGridView", id = ViewData["StandortID"] };

        settings.Columns.Add(column =>
        {
            column.Caption = "#";
            column.Width = Unit.Percentage(10);
            column.SetDataItemTemplateContent(container =>
            {
                var standortId = container.KeyValue;
                ViewData["standortId"] = standortId;

                var url = (string)DataBinder.Eval(container.DataItem, "Quelle");
                if (!string.IsNullOrEmpty(url) && url != "---")
                {
                    url = !url.StartsWith("http") ? "http://" + url : url;
                    Html.DevExpress().HyperLink(hyperlink =>
                    {
                        hyperlink.EncodeHtml = false;
                        hyperlink.Properties.Text = "<i class=\" fa fa-link\"></i>";
                        hyperlink.NavigateUrl = url;
                        hyperlink.Properties.Target = "_blank";
                    }).Render();

                    ViewContext.Writer.Write("&nbsp;&nbsp;");
                }
            });


        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "ReportDate";
            column.Caption = column.Caption = Resources.View_Bericht_ReportDate;
            column.Width = Unit.Percentage(20);
            column.ColumnType = MVCxGridViewColumnType.DateEdit;
            column.HeaderStyle.Wrap = DefaultBoolean.True;
            column.Settings.AllowHeaderFilter = DefaultBoolean.True;
            column.SettingsHeaderFilter.Mode = GridHeaderFilterMode.DateRangePicker;
        });


        settings.Columns.Add(column =>
        {
            column.FieldName = "StandortName";
            column.Caption = NeoSysLCS.Site.Helpers.CultureHelper.GetStandortMenuName();
            column.Width = Unit.Percentage(20);
            column.ReadOnly = true;
            column.ColumnType = MVCxGridViewColumnType.Memo;
            column.EditFormSettings.Visible = DefaultBoolean.False;
            column.SortDescending();
        });


        settings.Columns.Add(column =>
        {
            column.FieldName = "Name";
            column.Caption = "Name";
            column.Width = Unit.Percentage(60);
            column.ReadOnly = true;
            column.ColumnType = MVCxGridViewColumnType.Memo;
            column.EditFormSettings.Visible = DefaultBoolean.False;
            column.SortDescending();
        });

        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);
    });

    if (ViewData["EditError"] != null)
    {
        grid.SetEditErrorText((string)ViewData["EditError"]);
    }
}
@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = "StandortBerichtID";
}).GetHtml()