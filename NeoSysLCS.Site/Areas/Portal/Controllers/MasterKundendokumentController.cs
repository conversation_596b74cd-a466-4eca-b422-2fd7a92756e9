using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using DevExpress.Web.Mvc;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;
using NeoSysLCS.Site.Controllers;
using NeoSysLCS.Site.Helpers;
using System.Globalization;
using Microsoft.AspNet.Identity;
using NeoSysLCS.Repositories.Helper;
using DevExpress.Web;
using System.Web.UI.WebControls;
using DevExpress.Utils;
using DevExpress.Data;
using System.Web.UI;
using DevExpress.XtraPrinting;
using System.Net;
using DevExpress.Export;
using DocumentFormat.OpenXml.EMMA;
using static DevExpress.XtraPrinting.Native.ExportOptionsPropertiesNames;
using System.Web.UI.HtmlControls;
using DevExpress.Data.Filtering;
using System.Xml.Linq;
using System.Web.WebPages;

namespace NeoSysLCS.Site.Areas.Portal.Controllers
{
    public class MasterKundendokumentController : BaseController
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly SessionHelper _sh;
        public MasterKundendokumentController()
        {
            _unitOfWork = new UnitOfWork();
            _sh = new SessionHelper();
        }

        public MasterKundendokumentController(IUnitOfWork unitOfWorkCandidate)
        {
            _unitOfWork = unitOfWorkCandidate;
        }


        public ActionResult Index()
        {
            var kundendokumentIds = Init();

            return View("Index", kundendokumentIds);
        }

        private List<int> Init()
        {
            var kundeID = _sh.GetCurrentUser().KundeID;
            var standortIds = getStandortIdsForCustomer(kundeID, _sh);
            var currentKundendokumentIds =
                _unitOfWork.KundendokumentRepository.GetAllCurrentKundendokumentIDs(standortIds);
            ViewData["KundendokumentIDs"] = currentKundendokumentIds;

            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            Sprache sprache = _unitOfWork.SpracheRepository.Get(s => s.Lokalisierung == language).FirstOrDefault();
            var standortSprachen = _unitOfWork.StandortRepository.GetByID(standortIds.FirstOrDefault()).Sprachen.OrderBy(s => s.SpracheID);

            if (standortSprachen.Contains(sprache))
            {
                ViewData["SpracheID"] = sprache.SpracheID;
            }
            else
            {
                ViewData["SpracheID"] = standortSprachen.FirstOrDefault().SpracheID;
            }

            return currentKundendokumentIds;
        }

        public ActionResult KundendokumentPageControlCallbacksPartial()
        {
            return PartialView("KundendokumentPageControlCallbacksPartial");
        }



        /// <summary>
        /// Shows all kundendokument forderungen of the specified kundendokument
        /// </summary>
        /// <returns></returns>
        [ValidateInput(false)]
        public ActionResult KundendokumentForderungenGridView()
        {
            var viewModels = InitializeKundendokumentForderungenGridView();

            return PartialView("KundendokumentForderungenGridView", viewModels.AsQueryable());
        }

        /// <summary>
        /// Shows all kundendokument erlassfassungen
        /// </summary>
        /// <returns></returns>
        public ActionResult KundendokumentErlassfassungenGridView()
        {
            var viewModels = InitializeKundendokumentErlassfassungenGridView();

            return PartialView("KundendokumentErlassfassungenGridView", viewModels.AsQueryable());
        }

        public IQueryable<MasterKundendokumentForderungsversionViewModel> InitializeKundendokumentForderungenGridView()
        {
            var sh = new SessionHelper();
            var kundeID = sh.GetCurrentUser().KundeID;
            var standortIds = getStandortIdsForCustomer(kundeID, sh);
            var currentKundendokumentIds =
                _unitOfWork.KundendokumentRepository.GetAllCurrentKundendokumentIDs(standortIds);
            ViewData["KundendokumentIDs"] = currentKundendokumentIds;

            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            Sprache sprache = _unitOfWork.SpracheRepository.Get(s => s.Lokalisierung == language).FirstOrDefault();
            var standortSprachen = _unitOfWork.StandortRepository.GetByID(standortIds.FirstOrDefault()).Sprachen.OrderBy(s => s.SpracheID);

            if (standortSprachen.Contains(sprache))
            {
                ViewData["SpracheID"] = sprache.SpracheID;
            }
            else
            {
                ViewData["SpracheID"] = standortSprachen.FirstOrDefault().SpracheID;
            }

            return _unitOfWork.KundendokumentForderungsversionRepository.
                GetAllFreigegebeneForderungsversionViewModels(currentKundendokumentIds);
        }

        public IQueryable<MasterKundendokumentErlassfassungViewModel> InitializeKundendokumentErlassfassungenGridView()
        {
            var sh = new SessionHelper();
            var kundeID = sh.GetCurrentUser().KundeID;
            var standortIds = getStandortIdsForCustomer(kundeID, sh);
            var currentKundendokumentIds =
               _unitOfWork.KundendokumentRepository.GetAllCurrentKundendokumentIDs(standortIds);
            ViewData["KundendokumentIDs"] = currentKundendokumentIds;

            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            Sprache sprache = _unitOfWork.SpracheRepository.Get(s => s.Lokalisierung == language).FirstOrDefault();
            var standortSprachen = _unitOfWork.StandortRepository.GetByID(standortIds.FirstOrDefault()).Sprachen.OrderBy(s => s.SpracheID);

            if (standortSprachen.Contains(sprache))
            {
                ViewData["SpracheID"] = sprache.SpracheID;
            }
            else
            {
                ViewData["SpracheID"] = standortSprachen.FirstOrDefault().SpracheID;
            }

            return _unitOfWork.KundendokumentErlassfassungRepository.
                GetAllKundendokumentErlassfassungViewModels(currentKundendokumentIds, standortSprachen.Contains(sprache) ? sprache.SpracheID : standortSprachen.FirstOrDefault().SpracheID);
        }

        /// <summary>
        /// Saves the changes of kundendokument forderungen done by the enduser
        /// </summary>
        /// <param name="updateValues">The update values.</param>
        /// <param name="visibleColumsn">The field names of all visible columns</param>
        /// <param name="standortID">The standort identifier.</param>
        /// <param name="kundendokumentID">The kundendokument identifier.</param>
        /// <returns></returns>

        [ValidateInput(false)]
        public ActionResult KundendokumentForderungenGridViewBatchEditUpdate(MVCxGridViewBatchUpdateValues<MasterKundendokumentForderungsversionViewModel, int> updateValues, string[] visibleColumns)
        {
            if (ModelState.IsValid)
            {
                foreach (var viewModel in updateValues.Update)
                {
                    var kfv = _unitOfWork.KundendokumentForderungsversionRepository.GetByID(viewModel.KundendokumentForderungsversionID);

                    //check if column is visible in grid otherwise it will always get a null entry foreach invisible column
                    for (var i = 0; i < visibleColumns.Count(); i++)
                    {
                        if (visibleColumns[i] == "Erfuellung")
                        {
                            kfv.Erfuellung = viewModel.Erfuellung;
                        }
                        if (visibleColumns[i] == "LetzterPruefZeitpunkt")
                        {
                            kfv.LetztePruefungAm = viewModel.LetzterPruefZeitpunkt;
                        }
                        if (visibleColumns[i] == "NaechstePruefungAm")
                        {
                            kfv.NaechstePruefungAm = viewModel.NaechstePruefungAm;
                        }
                        if (visibleColumns[i] == "Pruefmethode")
                        {
                            kfv.Pruefmethode = viewModel.Pruefmethode;
                        }
                        if (visibleColumns[i] == "Verantwortlich")
                        {
                            kfv.Verantwortlich = viewModel.Verantwortlich;
                        }
                        if (visibleColumns[i] == "Kommentar")
                        {
                            kfv.Kommentar = viewModel.Kommentar;
                        }
                        if (visibleColumns[i] == "Ablageort")
                        {
                            kfv.Ablageort = viewModel.Ablageort;
                        }
                        if (visibleColumns[i] == "Spalte1")
                        {
                            kfv.Spalte1 = viewModel.Spalte1;
                        }
                        if (visibleColumns[i] == "Spalte2")
                        {
                            kfv.Spalte2 = viewModel.Spalte2;
                        }
                        if (visibleColumns[i] == "Spalte3")
                        {
                            kfv.Spalte3 = viewModel.Spalte3;
                        }
                        if (visibleColumns[i] == "Spalte4")
                        {
                            kfv.Spalte4 = viewModel.Spalte4;
                        }
                        if (visibleColumns[i] == "Spalte5")
                        {
                            kfv.Spalte5 = viewModel.Spalte5;
                        }
                        if (visibleColumns[i] == "Spalte6")
                        {
                            kfv.Spalte6 = viewModel.Spalte6;
                        }
                        if (visibleColumns[i] == "Spalte7")
                        {
                            kfv.Spalte7 = viewModel.Spalte7;
                        }
                        if (visibleColumns[i] == "Spalte8")
                        {
                            kfv.Spalte8 = viewModel.Spalte8;
                        }
                        if (visibleColumns[i] == "Spalte9")
                        {
                            kfv.Spalte9 = viewModel.Spalte9;
                        }
                        if (visibleColumns[i] == "Spalte10")
                        {
                            kfv.Spalte10 = viewModel.Spalte10;
                        }
                        if (visibleColumns[i] == "ShortcutID")
                        {
                            kfv.Shortcut = _unitOfWork.ShortcutRepository.GetByID(viewModel.ShortcutID);
                        }
                    }



                    kfv.BearbeitetAm = DateTime.Now;
                    kfv.BearbeitetVonID = System.Web.HttpContext.Current.User.Identity.GetUserId();
                    _unitOfWork.KundendokumentForderungsversionRepository.Update(kfv);
                }
            }
            var viewModels = InitializeKundendokumentForderungenGridView();
            return PartialView("KundendokumentForderungenGridView", viewModels.AsQueryable());
        }



        [ValidateInput(false)]
        public ActionResult KundendokumentErlassfassungGridViewBatchEditUpdate(MVCxGridViewBatchUpdateValues<KundendokumentErlassfassungViewModel, int> updateValues, string[] visibleColumns)
        {
            if (ModelState.IsValid)
            {
                foreach (var viewModel in updateValues.Update)
                {
                    var kfv = _unitOfWork.KundendokumentErlassfassungRepository.GetByID(viewModel.KundendokumentErlassfassungID);

                    //check if column is visible in grid otherwise it will always get a null entry foreach invisible column
                    for (var i = 0; i < visibleColumns.Count(); i++)
                    {
                        if (visibleColumns[i] == "Kommentar")
                        {
                            kfv.Kommentar = viewModel.Kommentar;
                        }
                    }

                    kfv.BearbeitetAm = DateTime.Now;
                    kfv.BearbeitetVonID = System.Web.HttpContext.Current.User.Identity.GetUserId();
                    _unitOfWork.KundendokumentErlassfassungRepository.Update(kfv);
                }
            }

            var viewModels = InitializeKundendokumentErlassfassungenGridView();
            return PartialView("KundendokumentErlassfassungenGridView", viewModels.AsQueryable());
        }

        public List<int> getStandortIdsForCustomer(int? kundeID, SessionHelper sessionHelper)
        {
            var currentUser = sessionHelper.GetCurrentUser();
            // reverting changes prior to commit: 17810741022d9b8fe34c7efb20d6ca4ee5ff3095
            List<int> standortIds = new List<int>();
            return _unitOfWork.StandortRepository.GetAllStandortViewModels(kundeID.Value).Select(x => x.StandortID).ToList();
        }

        /// <summary>
        /// Returs tree of object categories - used for standortobjekt filter in Forderungen tab.
        /// </summary>
        /// <param name="kundendokumentIDs"></param>
        /// <param name="spracheID"></param>
        /// <returns></returns>
        public ActionResult NewStandortObjekteGridView(String kundendokumentIDs, int spracheID)
        {
            if (kundendokumentIDs.IsEmpty())
            {
                // Handle the case where kundendokumentIDs is null or empty
                return PartialView("_RowSelectionPartial", Enumerable.Empty<NewStandortObjektViewModel>().AsQueryable());
            }

            var kundendokumentIDsList = kundendokumentIDs.Split(',').Select(int.Parse).ToList();

            ViewData["KundendokumentIDs"] = kundendokumentIDsList;
            ViewData["SpracheID"] = spracheID;

            var objektkategorien = _unitOfWork.ObjektkategorieRepository.GetTreeObjectsByKundendokumentIds(kundendokumentIDsList, spracheID);

            return PartialView("_RowSelectionPartial", objektkategorien);
        }

        /// <summary>
        /// Shows all kundendokumente of the specified standort
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns></returns>
        public ActionResult KundendokumentArchivGridView(int id)
        {
            ViewData["StandortID"] = id;
            var viewModels = _unitOfWork.KundendokumentRepository.GetAllFreigegebeneKundendokumentViewModel(id);
            return PartialView("KundendokumentArchivGridView", viewModels);
        }

        private List<int> usersStandorteIds(SessionHelper sh, int? standortId)
        {
            if (standortId != null)
            {
                return new List<int>() { (int)standortId };
            }

            var kundeID = sh.GetCurrentUser().KundeID;
            return _unitOfWork.StandortRepository.GetAllStandortViewModels(kundeID.HasValue ? kundeID.Value : 0).Select(s => s.StandortID).AsEnumerable().ToList();
        }

        public ActionResult KundendokumentSpaltenlabelsGridView(int? standortId)
        {
            var sh = new SessionHelper();
            var cols = InitializeKundendokumentSpaltenlabelsGridViewData(sh, standortId);
            ViewData["KundeID"] = sh.GetCurrentUser().KundeID;

            return PartialView("KundendokumentSpaltenlabelsGridView", cols);
        }

        private List<KundendokumentSpaltenlabelViewModel> InitializeKundendokumentSpaltenlabelsGridViewData(SessionHelper sh, int? standortId)
        {

            var standorteIds = usersStandorteIds(sh, standortId);
            var cols = new List<KundendokumentSpaltenlabelViewModel>();
            foreach (var standorteId in standorteIds)
            {
                var roleId = _unitOfWork.UserStandortRoleRepository.GetRoleIdById(sh.GetCurrentUserID() + "_" + standorteId);
                if (roleId != null)
                {
                    ViewData["roleId"] = roleId;
                }
                else
                {
                    ViewData["roleId"] = "NoRole";
                }

                var kd = _unitOfWork.KundendokumentRepository.GetCurrentKundendokumentViewModel(standorteId);
                var kdId = kd?.KundendokumentID ?? 0;

                var columns = _unitOfWork.KundendokumentRepository.GetByID(kdId, "KundendokumentSpaltenlabel")?.KundendokumentSpaltenlabel ?? null;
                var col = new KundendokumentSpaltenlabelViewModel()
                {

                    StandortId = standorteId,
                    KundendokumentId = kdId,
                    StandortName = kd?.StandortName,
                };

                if (columns != null)
                    col = new KundendokumentSpaltenlabelViewModel()
                    {
                        KundendokumentSpaltenlabelID = columns.KundendokumentSpaltenlabelID,
                        StandortId = standorteId,
                        KundendokumentId = kdId,
                        StandortName = kd?.StandortName,
                        LabelSpalte1 = columns.LabelSpalte1,
                        LabelSpalte2 = columns.LabelSpalte2,
                        LabelSpalte3 = columns.LabelSpalte3,
                        LabelSpalte4 = columns.LabelSpalte4,
                        LabelSpalte5 = columns.LabelSpalte5,
                        LabelSpalte6 = columns.LabelSpalte6,
                        LabelSpalte7 = columns.LabelSpalte7,
                        LabelSpalte8 = columns.LabelSpalte8,
                        LabelSpalte9 = columns.LabelSpalte9,
                        LabelSpalte10 = columns.LabelSpalte10
                    };

                if (kdId != 0)
                {
                    cols.Add(col);
                }
            }
            ViewData["KundeID"] = sh.GetCurrentUser().KundeID;
            return cols;
        }


        [ValidateInput(false)]
        public ActionResult KundendokumentSpaltenlabelsGridViewBatchEditUpdate(MVCxGridViewBatchUpdateValues<KundendokumentSpaltenlabelViewModel, int> updateValues)
        {
            var sh = new SessionHelper();

            NeoSysLCS_Dev _context = new NeoSysLCS_Dev();


            foreach (var row in updateValues.Update)
            {
                GridViewUpdateHelper<KundendokumentSpaltenlabel, KundendokumentSpaltenlabelViewModel>.DoUpdateInsert(() =>
                {
                    if (updateValues.IsValid(row))
                    {
                        var standortId = row.StandortId;
                        var kundendokumentID = row.KundendokumentId;
                        var roleId = _unitOfWork.UserStandortRoleRepository.GetRoleIdById(sh.GetCurrentUserID() + "_" + standortId);
                        if (roleId != null)
                        {
                            ViewData["roleId"] = roleId;
                        }
                        else
                        {
                            ViewData["roleId"] = "NoRole";
                        }
                        ViewData["KundendokumentID"] = kundendokumentID;
                        ViewData["StandortID"] = standortId;
                        var kundendokument = _unitOfWork.KundendokumentRepository.GetByID(kundendokumentID, "KundendokumentSpaltenLabel");

                        if (kundendokument.KundendokumentSpaltenlabel == null)
                            kundendokument.KundendokumentSpaltenlabel = new KundendokumentSpaltenlabel();

                        kundendokument.KundendokumentSpaltenlabel.LabelSpalte1 = row.LabelSpalte1;
                        kundendokument.KundendokumentSpaltenlabel.LabelSpalte2 = row.LabelSpalte2;
                        kundendokument.KundendokumentSpaltenlabel.LabelSpalte3 = row.LabelSpalte3;
                        kundendokument.KundendokumentSpaltenlabel.LabelSpalte4 = row.LabelSpalte4;
                        kundendokument.KundendokumentSpaltenlabel.LabelSpalte5 = row.LabelSpalte5;
                        kundendokument.KundendokumentSpaltenlabel.LabelSpalte6 = row.LabelSpalte6;
                        kundendokument.KundendokumentSpaltenlabel.LabelSpalte7 = row.LabelSpalte7;
                        kundendokument.KundendokumentSpaltenlabel.LabelSpalte8 = row.LabelSpalte8;
                        kundendokument.KundendokumentSpaltenlabel.LabelSpalte9 = row.LabelSpalte9;
                        kundendokument.KundendokumentSpaltenlabel.LabelSpalte10 = row.LabelSpalte10;
                        _unitOfWork.KundendokumentRepository.Update(kundendokument);
                        _unitOfWork.Save();
                    }
                }, row, updateValues);
            }

            foreach (var row in updateValues.Insert)
            {
                GridViewUpdateHelper<KundendokumentSpaltenlabel, KundendokumentSpaltenlabelViewModel>.DoUpdateInsert(() =>
                {
                    if (updateValues.IsValid(row))
                    {
                        var kundendokumentID = row.KundendokumentId;
                        var kundendokument = _unitOfWork.KundendokumentRepository.GetByID(kundendokumentID, "KundendokumentSpaltenLabel");
                        kundendokument.KundendokumentSpaltenlabel = row.GetModel();
                        _unitOfWork.KundendokumentRepository.Update(kundendokument);
                        _unitOfWork.Save();
                    }
                }, row, updateValues);
            }

            foreach (var rowId in updateValues.DeleteKeys)
            {
                GridViewUpdateHelper<KundendokumentSpaltenlabel, KundendokumentSpaltenlabelViewModel>.DoDelete(() =>
                {
                    var kundendokumentID = rowId;
                    var kundendokument = _unitOfWork.KundendokumentRepository.GetByID(kundendokumentID, "KundendokumentSpaltenLabel");
                    kundendokument.KundendokumentSpaltenlabel = null;
                    _unitOfWork.KundendokumentRepository.Update(kundendokument);
                    _unitOfWork.Save();

                    var klabel = _context.KundendokumentSpaltenlabel.FirstOrDefault(k => k.KundendokumentSpaltenlabelID == rowId);
                    _context.KundendokumentSpaltenlabel.Remove(klabel);
                    _context.SaveChanges();

                }, _unitOfWork, rowId, updateValues);
            }

            var cols = InitializeKundendokumentSpaltenlabelsGridViewData(sh, null);
            ViewData["KundeID"] = sh.GetCurrentUser().KundeID;
            return PartialView("KundendokumentSpaltenlabelsGridView", cols);
        }


        /// <summary>
        /// Shows the spalte 1-10 of the specified kundendokument forderung
        /// </summary>
        /// <param name="kundendokumentForderungsversionsID">The kundendokument forderungsversions identifier.</param>
        /// <param name="standortID">The standort identifier.</param>
        /// <param name="kundendokumentID">The kundendokument identifier.</param>
        /// <returns></returns>
        public ActionResult ForderungsversionAdditionalDataGridView(int kundendokumentForderungsversionsID, int? standortID, int? kundendokumentID)
        {
            ViewData["KundendokumentID"] = kundendokumentID;
            ViewData["StandortID"] = standortID;
            ViewData["KundendokumentForderungsversionID"] = kundendokumentForderungsversionsID;
            var viewModel = _unitOfWork.KundendokumentForderungsversionRepository.GetViewModelByID(kundendokumentForderungsversionsID);

            return PartialView("ForderungsversionAdditionalDataGridView", (IQueryable<KundendokumentForderungsversionViewModel>)new List<KundendokumentForderungsversionViewModel>() { viewModel });

        }

        /// <summary>
        /// Saves changes in spalte 1-10
        /// </summary>
        /// <param name="updateValues">The update values.</param>
        /// <param name="kundendokumentForderungsversionsID">The kundendokument forderungsversions identifier.</param>
        /// <param name="standortID">The standort identifier.</param>
        /// <param name="kundendokumentID">The kundendokument identifier.</param>
        /// <returns></returns>
        public ActionResult ForderungsversionAdditionalDataGridViewBatchEdit(
            MVCxGridViewBatchUpdateValues<KundendokumentForderungsversionViewModel, int> updateValues, int kundendokumentForderungsversionsID,
            int? standortID, int? kundendokumentID
        )
        {
            ViewData["KundendokumentID"] = kundendokumentID;
            ViewData["StandortID"] = standortID;
            ViewData["KundendokumentForderungsversionID"] = kundendokumentForderungsversionsID;
            foreach (var forderung in updateValues.Update)
            {
                GridViewUpdateHelper<KundendokumentForderungsversion, KundendokumentForderungsversionViewModel>.DoUpdateInsert(() =>
                {
                    if (updateValues.IsValid(forderung))
                    {
                        _unitOfWork.KundendokumentForderungsversionRepository.UpdateAdditionalData(forderung);
                        _unitOfWork.Save();
                    }
                }, forderung, updateValues);

            }
            var viewModel = _unitOfWork.KundendokumentForderungsversionRepository.GetViewModelByID(kundendokumentForderungsversionsID);
            return PartialView("ForderungsversionAdditionalDataGridView", (IQueryable<KundendokumentForderungsversionViewModel>)new List<KundendokumentForderungsversionViewModel>() { viewModel });

        }

        /// <summary>
        /// Show the changes form one forderungsversion to another
        /// </summary>
        /// <param name="forderungsversionID">The forderungsversion identifier.</param>
        /// <param name="standortID">The standort identifier.</param>
        /// <param name="kundendokumentID">The kundendokument identifier.</param>
        /// <returns></returns>
        public ActionResult ForderungsversionCompareDetailGridView(int forderungsversionID, int? standortID, int? kundendokumentID)
        {
            ViewData["KundendokumentID"] = kundendokumentID;
            ViewData["StandortID"] = standortID;
            var viewModel = new ForderungsversionsCompareViewModel();
            var newVersion = _unitOfWork.ForderungsversionRepository.GetForderungViewModelById(forderungsversionID);

            viewModel.InkraftretenNew = newVersion.Inkrafttretung.ToString("dd.MM.yyyy");

            if (newVersion.VorversionID.HasValue)
            {
                var vorversion = _unitOfWork.ForderungsversionRepository.GetForderungViewModelById(newVersion.VorversionID.Value);
                viewModel.SetOldVersion(vorversion);
                //var diffItems = StringHelper.CompareAndMarkDifferencesInList(vorversion.Beschreibung, newVersion.Beschreibung);
                //newVersion.Beschreibung = StringHelper.MarkDifferences(newVersion.Beschreibung, diffItems);
                viewModel.SetNewVersion(newVersion);
                viewModel.InkraftretenOld = vorversion.Inkrafttretung.ToString("dd.MM.yyyy");
            }


            return PartialView("ForderungsversionCompareDetailGridView", viewModel);

        }

        public ActionResult ShowShortcutPopupForForderungen(int standortID, int shortcutID, int kundendokumentForderungsVersionID)
        {
            var shortcuts = _unitOfWork.ShortcutRepository.GetShortcutCombobox(standortID);
            var standortName = _unitOfWork.StandortRepository.GetByID(standortID).Name;
            var model = new ShortcutErfassenViewModel
            {
                KundendokumentForderungsVersionID = kundendokumentForderungsVersionID,
                StandortId = standortID,
                StandortName = standortName,
                ShortcutId = shortcutID,
                Shortcuts = shortcuts,
            };
            return PartialView("_ShortcutPopup", model);
        }

        [HttpPost]
        [ValidateInput(false)]
        public ActionResult SaveShortcut(ShortcutErfassenViewModel model)
        {
            if (model.ShortcutId == 0)
            {
                ModelState.AddModelError("ShortcutId", "Das Feld \"Verantvortlich\" ist erforderlich.");
                return PartialView("_ShortcutPopup", model);
            }

            var forderungsVersion = _unitOfWork.KundendokumentForderungsversionRepository.GetByID(model.KundendokumentForderungsVersionID);
            forderungsVersion.Shortcut = _unitOfWork.ShortcutRepository.GetByID(model.ShortcutId);
            _unitOfWork.KundendokumentForderungsversionRepository.Update(forderungsVersion);
            return Content("Gespeichert!");
        }

        public ActionResult ForderungenExportDataAware()
        {

            var viewModels = InitializeKundendokumentForderungenGridView();

            ViewData["showOnlyRemoved"] = false;
            ViewData["SpracheID"] = 1;

            var xlsxExportOptions = new XlsxExportOptionsEx { };
            xlsxExportOptions.CustomizeCell += options_CustomizeCell;
            xlsxExportOptions.ExportType = DevExpress.Export.ExportType.DataAware;
            xlsxExportOptions.AllowSortingAndFiltering = DefaultBoolean.True;
            xlsxExportOptions.AllowGrouping = DefaultBoolean.True;
            xlsxExportOptions.GroupState = GroupState.ExpandAll;
            var modelList = viewModels.ToList();
            foreach (var viewmodel in modelList)
            {
                viewmodel.Beschreibung = WebUtility.HtmlDecode(StringHelper.StripAllTags(viewmodel.Beschreibung));
                viewmodel.InternerKommentar = WebUtility.HtmlDecode(StringHelper.StripAllTags(viewmodel.InternerKommentar));
                viewmodel.Kommentar = WebUtility.HtmlDecode(StringHelper.StripAllTags(viewmodel.Kommentar));
            }

            return GridViewExtension.ExportToXlsx(GetForderungenGridViewSettings(modelList), modelList, xlsxExportOptions);
        }

        public ActionResult ErlassfassungExportDataAware()
        {

            var viewModels = InitializeKundendokumentErlassfassungenGridView();
      
            //ViewData["showOnlyRemoved"] = false;
            ViewData["SpracheID"] = 1;
            var xlsxExportOptions = new XlsxExportOptionsEx { };
            xlsxExportOptions.CustomizeCell += options_CustomizeCell;
            //xlsxExportOptions.CustomizeSheetHeader += options_CustomizeSheetHeader;
            xlsxExportOptions.ExportType = DevExpress.Export.ExportType.DataAware;
            xlsxExportOptions.AllowSortingAndFiltering = DefaultBoolean.True;
            xlsxExportOptions.AllowGrouping = DefaultBoolean.True;
            xlsxExportOptions.GroupState = GroupState.ExpandAll;

            var modelList = viewModels.ToList();
            foreach (var viewmodel in modelList)
            {
                viewmodel.Kommentar = WebUtility.HtmlDecode(StringHelper.StripAllTags(viewmodel.Kommentar));
                viewmodel.InternerKommentar = WebUtility.HtmlDecode(StringHelper.StripAllTags(viewmodel.InternerKommentar));
                viewmodel.RelevanterKommentar = WebUtility.HtmlDecode(StringHelper.StripAllTags(viewmodel.RelevanterKommentar));
                viewmodel.Kerninhalte = WebUtility.HtmlDecode(StringHelper.StripAllTags(viewmodel.Kerninhalte));
            }

            return GridViewExtension.ExportToXlsx(GetErlassfassungGridViewSettings(modelList), modelList, xlsxExportOptions);
        }


        void options_CustomizeCell(CustomizeCellEventArgs e)
        {
            e.Formatting.Font = new DevExpress.Export.XlCellFont() { Name = "Arial", Size = 10 };
            if (e.AreaType == SheetAreaType.Header)
            {

                e.Formatting.BackColor = System.Drawing.Color.FromArgb(255, 255, 153);
            }
            else if (e.AreaType == SheetAreaType.GroupHeader)
            {
                e.Formatting.Font = new DevExpress.Export.XlCellFont() { Name = "Arial", Size = 10, Bold = true };
            }
            else
            {
                e.Formatting.Alignment = new DevExpress.Export.Xl.XlCellAlignment() { WrapText = true, HorizontalAlignment = DevExpress.Export.Xl.XlHorizontalAlignment.Left, VerticalAlignment = DevExpress.Export.Xl.XlVerticalAlignment.Top };
            }
            e.Handled = true;
        }


        private GridViewSettings GetForderungenGridViewSettings(List<MasterKundendokumentForderungsversionViewModel> model)
        {
            var settings = new GridViewSettings();
            IUnitOfWork unitOfWork = new UnitOfWork();
            NeoSysLCS_Dev context = new NeoSysLCS_Dev();
            var sessionHelper = new SessionHelper();
            ViewBag.EnableCheckedListMode = true;
            settings.SettingsPopup.CustomizationWindow.Width = 300;
            settings.SettingsCustomizationDialog.Enabled = true;
            settings.Styles.CustomizationDialog.CssClass = "addScroll";
            settings.Name = "MasterPortalKundendokumentForderungenGridView";
            settings.SettingsExport.ExcelExportMode = DevExpress.Export.ExportType.DataAware;
            settings.SettingsExport.EnableClientSideExportAPI = true;

            ViewContext viewContext = new ViewContext();
            settings.Toolbars.Add(t =>
            {
                t.EnableAdaptivity = true;
                t.Items.Add(GridViewToolbarCommand.ExportToXlsx);
            });

            settings.KeyFieldName = "KundendokumentForderungsversionID";
            GridViewHelper.ApplyDefaultSettings(settings);
            settings.SettingsCookies.Enabled = false;

            settings.SettingsContextMenu.Enabled = true;
            settings.SettingsContextMenu.EnableRowMenu = DefaultBoolean.False;

            settings.Styles.Cell.Wrap = DefaultBoolean.True;
            settings.SettingsBehavior.AllowEllipsisInText = true;
            settings.SettingsResizing.ColumnResizeMode = ColumnResizeMode.Control;
            settings.SettingsCookies.StoreColumnsWidth = true;
            settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;
            settings.Styles.GroupPanel.CssClass = "noWrapClass";
            settings.ClientSideEvents.BatchEditEndEditing = "OnBatchEditEndEditing";
            settings.ClientSideEvents.BatchEditStartEditing = "OnBatchStartEditing";

            settings.CommandColumn.AllowDragDrop = DefaultBoolean.False;
            settings.CommandColumn.Visible = true;

            settings.CustomUnboundColumnData = (s, e) =>
            {
                if (e.Column.FieldName == "RechtsbereicheUnbound")
                {
                    var dataItem = e.GetListSourceFieldValue("Rechtsbereiche");
                    IQueryable<RechtsbereichViewModel> rechtsbereiche = dataItem as IQueryable<RechtsbereichViewModel>;
                    List<string> tbl = new List<string>();

                    foreach (RechtsbereichViewModel t in rechtsbereiche)
                    {
                        tbl.Add(t.Name);
                    }
                    e.Value = String.Join(", ", tbl);
                }
            };

            settings.Columns.Add(column =>
            {
                column.FieldName = "RechtsbereicheUnbound";
                column.Caption = TranslationHelper.GetTranslation(typeof(MasterKundendokumentForderungsversionViewModel), "Rechtsbereiche");
                column.MinWidth = 140;
                column.ExportWidth = 140;
                column.ReadOnly = true;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.EditFormSettings.Visible = DefaultBoolean.False;

                column.Settings.ShowFilterRowMenu = DefaultBoolean.False;
                column.UnboundType = UnboundColumnType.String;
                column.Settings.AllowSort = DefaultBoolean.False;
            });


            settings.AutoFilterCellEditorCreate = (s, e) =>
            {
                if (e.Column.FieldName == "RechtsbereicheUnbound")
                {
                    ComboBoxProperties combo = new ComboBoxProperties();
                    e.EditorProperties = combo;
                }
            };

            settings.CustomUnboundColumnData = (s, e) =>
            {
                if (e.Column.FieldName == "RechtsbereicheUnbound")
                {
                    var dataItem = e.GetListSourceFieldValue("Rechtsbereiche");
                    IQueryable<RechtsbereichViewModel> rechtsbereiche = dataItem as IQueryable<RechtsbereichViewModel>;
                    List<string> tbl = new List<string>();

                    foreach (RechtsbereichViewModel t in rechtsbereiche)
                    {
                        tbl.Add(t.Name);
                    }
                    e.Value = String.Join(", ", tbl);
                }
            };

            settings.AutoFilterCellEditorInitialize = (s, e) =>
            {
                var gv = s as MVCxGridView;
                var expr = gv.FilterExpression;
                if (e.Column.FieldName == "RechtsbereicheUnbound")
                {
                    MVCxComboBox cb = ((MVCxComboBox)e.Editor);
                    // For MasterKundendokument, we need to get rechtsbereiche for all standorte
                    var kundeID = sessionHelper.GetCurrentUser().KundeID;
                    var standortIds = getStandortIdsForCustomer(kundeID, sessionHelper);
                    var allRechtsbereiche = new List<string>();
                    foreach (var standortId in standortIds)
                    {
                        //var rechtsbereiche = unitOfWork.RechtsbereichRepository.GetRechtsbereichComboBox(standortId, null, 1).Select(i => i.Name);
                        //allRechtsbereiche.AddRange(rechtsbereiche);
                    }
                    cb.DataSource = allRechtsbereiche.Distinct();
                    cb.DataBindItems();
                    if (Session["RechtsbereicheFilter"] != null && expr.Contains("Rechtsbereiche"))
                    {
                        e.Editor.Value = Session["RechtsbereicheFilter"];
                    }
                }
            };

            settings.ProcessColumnAutoFilter = (sender, e) =>
            {
                var gv = sender as MVCxGridView;
                string filter = gv.FilterExpression;
                var erfuellungList = from KundendokumentErfuellung value in Enum.GetValues(typeof(KundendokumentErfuellung))
                                     where (int)value != 4
                                     select new
                                     {
                                         Id = (int)value,
                                         Name = value.GetTranslation()
                                     };
                var test = (from x in model select x.Status).Distinct().ToList();
                var statusList = from KundendokumentItemStatus value in Enum.GetValues(typeof(KundendokumentItemStatus))
                                 where test.Contains(value)
                                 select new
                                 {
                                     Id = (int)value,
                                     Name = value.GetTranslation()
                                 };


                if (e.Column.FieldName == "RechtsbereicheUnbound")
                {
                    string value = e.Value;
                    if (gv.FilterExpression != "")
                    {
                        e.Criteria = CriteriaOperator.Parse(gv.FilterExpression + "OR Rechtsbereiche[contains(Name, ?)]", value);
                        gv.FilterExpression = null;
                        Session["RechtsbereicheFilter"] = "";
                    }
                    else
                    {
                        e.Criteria = DevExpress.Data.Filtering.CriteriaOperator.Parse("Rechtsbereiche[contains(Name, ?)]", value);
                        Session["RechtsbereicheFilter"] = e.Value;
                    }

                }
            };



            settings.Columns.Add(column =>
            {
                column.FieldName = "StandortObjektTitel";
                column.Caption = TranslationHelper.GetTranslation(typeof(MasterKundendokumentForderungsversionViewModel), column.FieldName);
                column.MinWidth = 200;
                column.ExportWidth = 200;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ReadOnly = true;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.GroupIndex = 0;
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "StandortName";
                column.Caption = "Standort";
                column.MinWidth = 200;
                column.ExportWidth = 200;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ReadOnly = true;
                column.EditFormSettings.Visible = DefaultBoolean.False;
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "ErlassSrNummer";
                column.Caption = Resources.Properties.Resources.Erlassnummer;
                column.MinWidth = 125;
                column.ExportWidth = 125;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ReadOnly = true;
                column.EditFormSettings.Visible = DefaultBoolean.False;
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "ErlassTitel";
                column.Caption = TranslationHelper.GetTranslation(typeof(MasterKundendokumentForderungsversionViewModel), column.FieldName);
                column.MinWidth = 200;
                column.ExportWidth = 200;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ReadOnly = true;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.ExportCellStyle.Wrap = DefaultBoolean.True;
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "ArtikelNummer";
                column.Caption = Resources.Properties.Resources.Entitaet_Artikel_Singular;
                column.MinWidth = 110;
                column.ExportWidth = 110;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.CellStyle.HorizontalAlign = HorizontalAlign.Center;
                column.Settings.FilterMode = ColumnFilterMode.DisplayText;
                column.SetDataItemTemplateContent(
                    container =>
                    {

                        var url = DataBinder.Eval(container.DataItem, "ArtikelQuelle");
                        var nr = DataBinder.Eval(container.DataItem, "ArtikelNummer");
                        string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, nr, false);
                        viewContext.Writer.Write(htmlLink);
                    });
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "Beschreibung";
                column.Caption = TranslationHelper.GetTranslation(typeof(MasterKundendokumentForderungsversionViewModel), column.FieldName);
                column.HeaderStyle.Wrap = DefaultBoolean.True;

                column.ColumnType = MVCxGridViewColumnType.Memo;
                column.MinWidth = 350;
                column.ExportWidth = 350;

                var memoProp = column.PropertiesEdit as MemoProperties;
                if (memoProp != null)
                {
                    memoProp.EncodeHtml = false;
                }
                column.ReadOnly = true;
                column.EditFormSettings.Visible = DefaultBoolean.False;
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "BewilligungspflichtText";
                column.Caption = Resources.Properties.Resources.View_Auswertungen_Bewilligungspflichtig;
                column.MinWidth = 75;
                column.ExportWidth = 75;
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "NachweispflichtText";
                column.Caption = Resources.Properties.Resources.View_Auswertungen_Nachweispflichtig;
                column.MinWidth = 75;
                column.ExportWidth = 75;
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "Status";
                column.Caption = TranslationHelper.GetTranslation(typeof(MasterKundendokumentForderungsversionViewModel), column.FieldName);

                //define as combobox for filtern over the enum
                column.ColumnType = MVCxGridViewColumnType.ComboBox;
                //column.Settings.FilterMode = ColumnFilterMode.DisplayText;
                var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;

                var test = (from x in model select x.Status).Distinct().ToList();

                var list = from KundendokumentItemStatus value in Enum.GetValues(typeof(KundendokumentItemStatus))
                           where test.Contains(value)
                           select new
                           {
                               Id = (int)value,
                               Name = value.GetTranslation()
                           };

                comboBoxProperties.DataSource = list;
                comboBoxProperties.ValueField = "Id";
                comboBoxProperties.TextField = "Name";


                column.MinWidth = 100;
                column.ExportWidth = 100;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ReadOnly = true;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.SetDataItemTemplateContent(container =>
                {
                    var statusCandidate = DataBinder.Eval(container.DataItem, "Status");
                    if (statusCandidate != null)
                    {
                        var status = (KundendokumentItemStatus)statusCandidate;
                        viewContext.Writer.Write("<span class=\"label label-" + status + "\">" + status.GetTranslation() + "</span>");
                    }
                });
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "Erfuellung";
                column.Caption = TranslationHelper.GetTranslation(typeof(MasterKundendokumentForderungsversionViewModel), column.FieldName);
                column.MinWidth = 150;
                column.ExportWidth = 150;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ColumnType = MVCxGridViewColumnType.ComboBox;
                //column.Settings.FilterMode = ColumnFilterMode.DisplayText;
                var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;

                var list = from KundendokumentErfuellung value in Enum.GetValues(typeof(KundendokumentErfuellung))
                           where (int)value != 4
                           select new
                           {
                               Id = (int)value,
                               Name = value.GetTranslation()
                           };

                comboBoxProperties.DataSource = list;
                comboBoxProperties.ValueField = "Id";
                comboBoxProperties.TextField = "Name";
                comboBoxProperties.ValueType = typeof(Int32);
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "LetzterPruefZeitpunkt";
                column.Caption = TranslationHelper.GetTranslation(typeof(MasterKundendokumentForderungsversionViewModel), column.FieldName);
                column.MinWidth = 150;
                column.ExportWidth = 150;
                column.ColumnType = MVCxGridViewColumnType.DateEdit;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
            });
            settings.Columns.Add(column =>
            {
                column.FieldName = "NaechstePruefungAm";
                column.Caption = TranslationHelper.GetTranslation(typeof(MasterKundendokumentForderungsversionViewModel), column.FieldName);
                column.MinWidth = 180;
                column.ExportWidth = 180;
                column.ColumnType = MVCxGridViewColumnType.DateEdit;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
            });
            settings.Columns.Add(column =>
            {
                column.FieldName = "Pruefmethode";
                column.Caption = TranslationHelper.GetTranslation(typeof(MasterKundendokumentForderungsversionViewModel), column.FieldName);
                column.MinWidth = 140;
                column.ExportWidth = 140;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ColumnType = MVCxGridViewColumnType.Memo;
                column.Settings.AllowEllipsisInText = DefaultBoolean.False;
                column.SetDataItemTemplateContent(
                    container =>
                    {

                        var url = DataBinder.Eval(container.DataItem, "Pruefmethode");
                        string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url, true);
                        viewContext.Writer.Write(htmlLink);
                    });
            });
            settings.Columns.Add(column =>
            {

                column.FieldName = "Shortcut";
                column.Caption = TranslationHelper.GetTranslation(typeof(MasterKundendokumentForderungsversionViewModel), "Verantwortlich");
                column.MinWidth = 140;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.UnboundType = UnboundColumnType.String;
                column.Settings.AllowSort = DefaultBoolean.False;

            });
            settings.Columns.Add(column =>
            {
                column.FieldName = "Ablageort";
                column.Caption = TranslationHelper.GetTranslation(typeof(MasterKundendokumentForderungsversionViewModel), column.FieldName);
                column.MinWidth = 150;
                column.ExportWidth = 150;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ColumnType = MVCxGridViewColumnType.Memo;
                column.Settings.AllowEllipsisInText = DefaultBoolean.False;
                column.SetDataItemTemplateContent(
                    container =>
                    {

                        var url = DataBinder.Eval(container.DataItem, "Ablageort");
                        string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url, false);
                        viewContext.Writer.Write(htmlLink);
                    });
            });
            settings.Columns.Add(column =>
            {
                column.FieldName = "Kommentar";
                column.Caption = TranslationHelper.GetTranslation(typeof(MasterKundendokumentForderungsversionViewModel), column.FieldName);
                column.MinWidth = 110;
                column.ExportWidth = 110;
                column.HeaderStyle.Wrap = DefaultBoolean.True;

                column.ColumnType = MVCxGridViewColumnType.Memo;
                column.Settings.AllowEllipsisInText = DefaultBoolean.False;
                var prop = column.PropertiesEdit as MemoProperties;
                prop.Rows = 10;
            });

            //Additinal Columns
            settings.Columns.Add(column =>
            {
                column.FieldName = "Spalte1";
                column.MinWidth = 135;
                column.ExportWidth = 135;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ColumnType = MVCxGridViewColumnType.Memo;
                column.Settings.AllowEllipsisInText = DefaultBoolean.False;
                column.Caption = TranslationHelper.GetTranslation(typeof(MasterKundendokumentForderungsversionViewModel), column.FieldName);
                column.SetDataItemTemplateContent(
                    container =>
                    {

                        var url = DataBinder.Eval(container.DataItem, "Spalte1");
                        string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url, true);
                        viewContext.Writer.Write(htmlLink);
                    });
            });
            settings.Columns.Add(column =>
            {
                column.FieldName = "Spalte2";
                column.MinWidth = 135;
                column.ExportWidth = 135;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ColumnType = MVCxGridViewColumnType.Memo;
                column.Settings.AllowEllipsisInText = DefaultBoolean.False;
                column.Caption = TranslationHelper.GetTranslation(typeof(MasterKundendokumentForderungsversionViewModel), column.FieldName);
                column.SetDataItemTemplateContent(
                    container =>
                    {

                        var url = DataBinder.Eval(container.DataItem, "Spalte2");
                        string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, true);
                        viewContext.Writer.Write(htmlLink);
                    });
            });
            settings.Columns.Add(column =>
            {
                column.FieldName = "Spalte3";
                column.MinWidth = 135;
                column.ExportWidth = 135;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ColumnType = MVCxGridViewColumnType.Memo;
                column.Settings.AllowEllipsisInText = DefaultBoolean.False;
                column.Caption = TranslationHelper.GetTranslation(typeof(MasterKundendokumentForderungsversionViewModel), column.FieldName);
                column.SetDataItemTemplateContent(
                    container =>
                    {

                        var url = DataBinder.Eval(container.DataItem, "Spalte3");
                        string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url, true);
                        viewContext.Writer.Write(htmlLink);
                    });
            });
            settings.Columns.Add(column =>
            {
                column.FieldName = "Spalte4";
                column.MinWidth = 135;
                column.ExportWidth = 135;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ColumnType = MVCxGridViewColumnType.Memo;
                column.Settings.AllowEllipsisInText = DefaultBoolean.False;
                column.Caption = TranslationHelper.GetTranslation(typeof(MasterKundendokumentForderungsversionViewModel), column.FieldName);
                column.SetDataItemTemplateContent(
                    container =>
                    {

                        var url = DataBinder.Eval(container.DataItem, "Spalte4");
                        string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, true);
                        viewContext.Writer.Write(htmlLink);
                    });

            });
            settings.Columns.Add(column =>
            {
                column.FieldName = "Spalte5";
                column.MinWidth = 135;
                column.ExportWidth = 135;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ColumnType = MVCxGridViewColumnType.Memo;
                column.Settings.AllowEllipsisInText = DefaultBoolean.False;
                column.Caption = TranslationHelper.GetTranslation(typeof(MasterKundendokumentForderungsversionViewModel), column.FieldName);
                column.SetDataItemTemplateContent(
                    container =>
                    {

                        var url = DataBinder.Eval(container.DataItem, "Spalte5");
                        string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url, true);
                        viewContext.Writer.Write(htmlLink);
                    });
            });
            settings.Columns.Add(column =>
            {
                column.FieldName = "Spalte6";
                column.MinWidth = 135;
                column.ExportWidth = 135;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ColumnType = MVCxGridViewColumnType.Memo;
                column.Settings.AllowEllipsisInText = DefaultBoolean.False;
                column.Caption = TranslationHelper.GetTranslation(typeof(MasterKundendokumentForderungsversionViewModel), column.FieldName);
                column.SetDataItemTemplateContent(
                    container =>
                    {

                        var url = DataBinder.Eval(container.DataItem, "Spalte6");
                        string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, true);
                        viewContext.Writer.Write(htmlLink);
                    });
            });
            settings.Columns.Add(column =>
            {
                column.FieldName = "Spalte7";
                column.MinWidth = 135;
                column.ExportWidth = 135;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ColumnType = MVCxGridViewColumnType.Memo;
                column.Settings.AllowEllipsisInText = DefaultBoolean.False;
                column.Caption = TranslationHelper.GetTranslation(typeof(MasterKundendokumentForderungsversionViewModel), column.FieldName);
                column.SetDataItemTemplateContent(
                    container =>
                    {
                        var url = DataBinder.Eval(container.DataItem, "Spalte7");
                        string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url, true);
                        viewContext.Writer.Write(htmlLink);
                    });
            });
            settings.Columns.Add(column =>
            {
                column.FieldName = "Spalte8";
                column.MinWidth = 135;
                column.ExportWidth = 135;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ColumnType = MVCxGridViewColumnType.Memo;
                column.Settings.AllowEllipsisInText = DefaultBoolean.False;
                column.Caption = TranslationHelper.GetTranslation(typeof(MasterKundendokumentForderungsversionViewModel), column.FieldName);
                column.SetDataItemTemplateContent(
                    container =>
                    {
                        var url = DataBinder.Eval(container.DataItem, "Spalte8");
                        string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url, true);
                        viewContext.Writer.Write(htmlLink);
                    });
            });
            settings.Columns.Add(column =>
            {
                column.FieldName = "Spalte9";
                column.MinWidth = 135;
                column.ExportWidth = 135;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ColumnType = MVCxGridViewColumnType.Memo;
                column.Settings.AllowEllipsisInText = DefaultBoolean.False;
                column.Caption = TranslationHelper.GetTranslation(typeof(MasterKundendokumentForderungsversionViewModel), column.FieldName);
                column.SetDataItemTemplateContent(
                    container =>
                    {
                        var url = DataBinder.Eval(container.DataItem, "Spalte9");
                        string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url, true);
                        viewContext.Writer.Write(htmlLink);
                    });
            });
            settings.Columns.Add(column =>
            {
                column.FieldName = "Spalte10";
                column.MinWidth = 135;
                column.ExportWidth = 135;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ColumnType = MVCxGridViewColumnType.Memo;
                column.Settings.AllowEllipsisInText = DefaultBoolean.False;
                column.Caption = TranslationHelper.GetTranslation(typeof(MasterKundendokumentForderungsversionViewModel), column.FieldName);
                column.SetDataItemTemplateContent(
                    container =>
                    {
                        var url = DataBinder.Eval(container.DataItem, "Spalte10");
                        string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url, true);
                        viewContext.Writer.Write(htmlLink);
                    });
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "ErlassfassungInkraftretung";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), "Inkrafttretung");
                column.MinWidth = 160;
                column.ExportWidth = 160;
                column.ColumnType = MVCxGridViewColumnType.DateEdit;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ReadOnly = true;
                column.EditFormSettings.Visible = DefaultBoolean.False;
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "ErlassfassungBearbeitetAm";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), "Beschluss");
                column.MinWidth = 160;
                column.ExportWidth = 160;
                column.ColumnType = MVCxGridViewColumnType.DateEdit;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ReadOnly = true;
                column.EditFormSettings.Visible = DefaultBoolean.False;
            });

            GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);

            settings.HtmlDataCellPrepared += (sender, e) =>
            {
                var erfuellung = "";
                if (e.VisibleIndex >= 0)
                {
                    erfuellung = e.GetValue("Erfuellung").ToString();
                }
                var bewilligungspflichtig = Convert.ToBoolean(e.GetValue("Bewilligungspflicht"));
                var nachweispflichtig = Convert.ToBoolean(e.GetValue("Nachweispflicht"));
                if (bewilligungspflichtig)
                {
                    e.Cell.BackColor = System.Drawing.Color.Lavender;
                }
                if (nachweispflichtig)
                {
                    e.Cell.BackColor = System.Drawing.Color.BlanchedAlmond;
                }
                if (bewilligungspflichtig && nachweispflichtig)
                {
                    e.Cell.BackColor = System.Drawing.Color.PowderBlue;
                }
                if (erfuellung == "Yes")
                {
                    e.Cell.BackColor = System.Drawing.ColorTranslator.FromHtml("#E2EFDA");
                }
            };


            settings.SettingsPopup.HeaderFilter.Height = Unit.Pixel(440);
            settings.SettingsPopup.HeaderFilter.Width = Unit.Pixel(300);
            foreach (GridViewDataColumn column in settings.Columns)
            {
                if (column.FieldName == "LetzterPruefZeitpunkt" || column.FieldName == "NaechstePruefungAm" || column.FieldName == "ErlassfassungInkraftretung" || column.FieldName == "ErlassfassungBearbeitetAm")
                {
                    column.Settings.AllowHeaderFilter = DefaultBoolean.True;
                    column.SettingsHeaderFilter.Mode = GridHeaderFilterMode.DateRangePicker;
                }
                else if (column.FieldName == "StandortObjektTitel" || column.FieldName == "Pruefmethode" || column.FieldName == "ShortcutID" || column.FieldName == "Ablageort" || column.FieldName == "ErlassID"
                                || column.FieldName == "ErlassSrNummer" || column.FieldName == "Erfuellung" || column.FieldName == "Status")
                {
                    column.Settings.AllowHeaderFilter = DefaultBoolean.True;
                    column.SettingsHeaderFilter.Mode = GridHeaderFilterMode.CheckedList;
                }
                else
                {
                    column.Settings.AllowHeaderFilter = DefaultBoolean.False;
                }
            }
            return settings;
        }

        private GridViewSettings GetErlassfassungGridViewSettings(List<MasterKundendokumentErlassfassungViewModel> model)
        {
            var settings = new GridViewSettings();
            IUnitOfWork unitOfWork = new UnitOfWork();
            NeoSysLCS_Dev context = new NeoSysLCS_Dev();
            var sessionHelper = new SessionHelper();
            ViewBag.EnableCheckedListMode = true;
            settings.SettingsPopup.CustomizationWindow.Width = 300;
            settings.SettingsCustomizationDialog.Enabled = true;
            settings.Styles.CustomizationDialog.CssClass = "addScroll";
            settings.Name = "MasterErlassfassungenGridView";
            settings.SettingsExport.ExcelExportMode = DevExpress.Export.ExportType.DataAware;
            settings.SettingsExport.EnableClientSideExportAPI = true;

            ViewContext viewContext = new ViewContext();
            settings.Toolbars.Add(t =>
            {
                t.EnableAdaptivity = true;
                t.Items.Add(GridViewToolbarCommand.ExportToXlsx);
            });

            settings.KeyFieldName = "KundendokumentErlassfassungID";
            GridViewHelper.ApplyDefaultSettings(settings);
            settings.SettingsCookies.Enabled = false;

            settings.SettingsContextMenu.Enabled = true;
            settings.SettingsContextMenu.EnableRowMenu = DefaultBoolean.False;

            settings.Styles.Cell.Wrap = DefaultBoolean.True;
            settings.SettingsBehavior.AllowEllipsisInText = true;
            settings.SettingsResizing.ColumnResizeMode = ColumnResizeMode.Control;
            settings.SettingsCookies.StoreColumnsWidth = true;
            settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;
            settings.Styles.GroupPanel.CssClass = "noWrapClass";
            settings.ClientSideEvents.BatchEditEndEditing = "OnBatchEditEndEditing";
            settings.ClientSideEvents.BatchEditStartEditing = "OnBatchStartEditing";

            settings.CommandColumn.AllowDragDrop = DefaultBoolean.False;
            settings.CommandColumn.Visible = true;

            settings.Columns.Add(column =>
            {
                column.FieldName = "Betroffen";
                column.ColumnType = MVCxGridViewColumnType.CheckBox;
                column.Caption = TranslationHelper.GetTranslation(typeof(MasterKundendokumentErlassfassungViewModel), column.FieldName);
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.MinWidth = 70;
                column.Width = 70;
            });

            settings.AutoFilterCellEditorInitialize = (s, e) =>
            {
                if (e.Column.FieldName == "Betroffen")
                {
                    ASPxComboBox combo = e.Editor as ASPxComboBox;
                    combo.Items.Clear();
                    combo.ValueType = typeof(string);
                    combo.Items.Add(Resources.Properties.Resources.Entitaet_KundendokumentErlassfassung_Betroffen, true);
                    combo.Items.Add(Resources.Properties.Resources.Entitaet_KundendokumentErlassfassung_NichtBetroffen, false);
                }

            };

            bool eventIsHandled = false;
            settings.AutoFilterCellEditorCreate = (s, e) =>
            {
                MVCxGridView g = s as MVCxGridView;
                if (!eventIsHandled)
                {
                    g.ProcessColumnAutoFilter += (sender, args) =>
                    {
                        if (args.Column.FieldName == "Betroffen")
                        {
                            if (args.Kind == GridViewAutoFilterEventKind.ExtractDisplayText)
                            {

                                if (args.Value == "False")
                                {
                                    args.Value = Resources.Properties.Resources.Entitaet_KundendokumentErlassfassung_NichtBetroffen;
                                }
                                if (args.Value == "True")
                                {
                                    args.Value = Resources.Properties.Resources.Entitaet_KundendokumentErlassfassung_Betroffen;
                                }
                            }

                        }

                    };
                    eventIsHandled = true;
                }
            };

            settings.ProcessColumnAutoFilter = (sender, e) =>
            {
                var gv = sender as MVCxGridView;
                string filter = gv.FilterExpression;
                var erfuellungList = from KundendokumentErfuellung value in Enum.GetValues(typeof(KundendokumentErfuellung))
                                     where (int)value != 4
                                     select new
                                     {
                                         Id = (int)value,
                                         Name = value.GetTranslation()
                                     };
                var test = (from x in model select x.Status).Distinct().ToList();
                var statusList = from KundendokumentItemStatus value in Enum.GetValues(typeof(KundendokumentItemStatus))
                                 where test.Contains(value)
                                 select new
                                 {
                                     Id = (int)value,
                                     Name = value.GetTranslation()
                                 };
            };

            settings.Columns.Add(column =>
            {
                column.FieldName = "StandortName";
                column.Caption = TranslationHelper.GetTranslation(typeof(MasterKundendokumentErlassfassungViewModel), column.FieldName);
                column.MinWidth = 200;
                column.ExportWidth = 200;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ReadOnly = true;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.ExportCellStyle.Wrap = DefaultBoolean.True;
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "ErlassTitel";
                column.Caption = TranslationHelper.GetTranslation(typeof(MasterKundendokumentErlassfassungViewModel), column.FieldName);
                column.MinWidth = 200;
                column.ExportWidth = 200;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ReadOnly = true;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.ExportCellStyle.Wrap = DefaultBoolean.True;
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "ErlassSrNummer";
                column.Caption = Resources.Properties.Resources.Erlassnummer;
                column.MinWidth = 70;
                column.Width = 70;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.SortAscending();
                column.SortIndex = 2;
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "HerausgeberName";
                column.Caption = TranslationHelper.GetTranslation(typeof(MasterKundendokumentErlassfassungViewModel), column.FieldName);
                column.MinWidth = 200;
                column.ExportWidth = 200;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ReadOnly = true;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.ExportCellStyle.Wrap = DefaultBoolean.True;
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "Beschluss";
                column.MinWidth = 100;
                column.Width = 100;
                column.ColumnType = MVCxGridViewColumnType.DateEdit;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.Caption = TranslationHelper.GetTranslation(typeof(MasterKundendokumentErlassfassungViewModel), column.FieldName);
                column.PropertiesEdit.DisplayFormatString = "d";

            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "Inkrafttretung";
                column.MinWidth = 100;
                column.Width = 100;
                column.ColumnType = MVCxGridViewColumnType.DateEdit;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.SortDescending();
                column.SortIndex = 3;
                column.Caption = TranslationHelper.GetTranslation(typeof(MasterKundendokumentErlassfassungViewModel), column.FieldName);
                column.PropertiesEdit.DisplayFormatString = "d";

            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "Quelle";
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.MinWidth = 70;
                column.Width = 70;
                column.SetDataItemTemplateContent(
                container =>
                {
                    var url = DataBinder.Eval(container.DataItem, "Quelle");
                    string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url, true);
                    viewContext.Writer.Write(htmlLink);
                });
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "RelevanterKommentar";
                column.MinWidth = 150;
                column.Width = 150;
                column.Caption = Resources.Properties.Resources.View_Kundendokument_Erlassfassung_NeosysKommentar;
                column.Settings.AutoFilterCondition = AutoFilterCondition.Contains;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.ColumnType = MVCxGridViewColumnType.Memo;
                column.PropertiesEdit.EncodeHtml = false;
                column.ReadOnly = true;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.CellStyle.HorizontalAlign = HorizontalAlign.Center;

                var memoProp = column.PropertiesEdit as MemoProperties;
                if (memoProp != null)
                {
                    memoProp.EncodeHtml = false;
                }

                column.SetDataItemTemplateContent(
                    container =>
                    {
                        var url = DataBinder.Eval(container.DataItem, "RelevanterKommentar");
                        if (url != null)
                        {
                            if ((url.ToString().StartsWith("www")) || (url.ToString().StartsWith("http")))
                            {
                                string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url, true);
                                viewContext.Writer.Write(htmlLink);
                            }

                            else
                            {
                                viewContext.Writer.Write(container.Text);
                            }
                        }
                    });
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "Kerninhalte";
                column.MinWidth = 150;
                column.Width = 150;
                column.Caption = Resources.Properties.Resources.Entitaet_Erlass_Kerninhalte;
                column.Settings.AutoFilterCondition = AutoFilterCondition.Contains;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.ColumnType = MVCxGridViewColumnType.Memo;
                column.PropertiesEdit.EncodeHtml = false;
                column.ReadOnly = true;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.CellStyle.HorizontalAlign = HorizontalAlign.Center;

                var memoProp = column.PropertiesEdit as MemoProperties;
                if (memoProp != null)
                {
                    memoProp.EncodeHtml = false;
                }

                column.SetDataItemTemplateContent(
                    container =>
                    {
                        var url = DataBinder.Eval(container.DataItem, "Kerninhalte");
                        if (url != null)
                        {
                            if ((url.ToString().StartsWith("www")) || (url.ToString().StartsWith("http")))
                            {
                                string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url, true);
                                viewContext.Writer.Write(htmlLink);
                            }

                            else
                            {
                                viewContext.Writer.Write(container.Text);
                            }
                        }
                    });
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "Status";
                column.Caption = TranslationHelper.GetTranslation(typeof(MasterKundendokumentErlassfassungViewModel), column.FieldName);
                column.MinWidth = 70;
                column.Width = 70;
                //define as combobox for filtern over the enum
                column.ColumnType = MVCxGridViewColumnType.ComboBox;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                var statusComboBoxProperties = column.PropertiesEdit as ComboBoxProperties;

                var test = (from x in model select x.Status).Distinct().ToList();

                var list = from KundendokumentItemStatus value in Enum.GetValues(typeof(KundendokumentItemStatus))
                           where test.Contains(value)
                           select new
                           {
                               Id = (int)value,
                               Name = value.GetTranslation()
                           };

                statusComboBoxProperties.DataSource = list;
                statusComboBoxProperties.ValueField = "Id";
                statusComboBoxProperties.TextField = "Name";

                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ReadOnly = true;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.SetDataItemTemplateContent(container =>
                {
                    var statusCandidate = DataBinder.Eval(container.DataItem, "Status");
                    if (statusCandidate != null)
                    {
                        var status = (KundendokumentItemStatus)statusCandidate;
                        viewContext.Writer.Write("<span class=\"label label-" + status + "\">" + status.GetTranslation() + "</span>");
                    }
                });
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "Kommentar";
                column.Caption = Resources.Properties.Resources.View_Kundendokument_Erlassfassung_Kommentar;
                column.EditFormSettings.Visible = DefaultBoolean.True;
                column.Settings.AutoFilterCondition = AutoFilterCondition.Contains;
            });

            GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);

            settings.SettingsPopup.HeaderFilter.Height = Unit.Pixel(440);
            settings.SettingsPopup.HeaderFilter.Width = Unit.Pixel(300);
            foreach (GridViewDataColumn column in settings.Columns)
            {
                if (column.FieldName == "Beschluss" || column.FieldName == "Inkrafttretung")
                {
                    column.Settings.AllowHeaderFilter = DefaultBoolean.True;
                    column.SettingsHeaderFilter.Mode = GridHeaderFilterMode.DateRangePicker;
                }
                else
                {
                    column.Settings.AllowHeaderFilter = DefaultBoolean.False;
                }
            }

            settings.ClientLayout = (sender, e) =>
            {
                MVCxGridView gridView = sender as MVCxGridView;
                var cookieID = gridView.SettingsCookies.CookiesID + "_" + ViewData["KundendokumentID"];
                if (e.LayoutMode == ClientLayoutMode.Loading)
                {
                    var layout = CookieHelper.GetCookie(cookieID);
                    if (layout != "")
                    {
                        e.LayoutData = layout;
                    }
                }
                gridView.ExpandAll();
            };

            return settings;
        }

    }
}