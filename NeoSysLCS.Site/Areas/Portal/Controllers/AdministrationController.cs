using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;
using NeoSysLCS.Site.Controllers;
using NeoSysLCS.Site.Helpers;
using System.Globalization;
using System.Web;
using Microsoft.AspNet.Identity.Owin;


namespace NeoSysLCS.Site.Areas.Portal.Controllers
{
    public class AdministrationController : BaseController
    {
        private readonly IUnitOfWork _unitOfWork;

        public AdministrationController()
        {
            _unitOfWork = new UnitOfWork();
        }

        private ApplicationRoleManager RoleManager
        {
            get
            {
                return HttpContext.GetOwinContext().Get<ApplicationRoleManager>();
            }
        }

        public ActionResult Index()
        {
            return View("Index");
        }

        public ActionResult AdministrationPageControllCallbacksPartial()
        {
            return PartialView("AdministrationPageControllCallbacksPartial");
        }

        public ActionResult StandortAdministration(int? id)
        {
            Index();
            ViewData["StandortID"] = id;
            var sessionHelper = new SessionHelper();
            var kundeID = sessionHelper.GetCurrentUser().KundeID;
            var viewModels = _unitOfWork.KundeRepository.GetStandorteByKundeViewModel(kundeID.HasValue ? kundeID.Value : 0);

            return PartialView("StandortAdministration", viewModels);
        }

        public ActionResult KundendokumentIndex(int id, int? kundendokumentId)
        {
            ViewData["StandortID"] = id;

            KundendokumentViewModel viewModel;

            if (kundendokumentId.HasValue)
            {
                viewModel = _unitOfWork.KundendokumentRepository.GetFreigegenesKundendokumentViewModelByID(kundendokumentId.Value);
            }
            else
            {
                viewModel = _unitOfWork.KundendokumentRepository.GetCurrentKundendokumentViewModel(id);
            }
            if (viewModel != null)
            {
                ViewData["KundendokumentID"] = viewModel.KundendokumentID;
            }


            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            Sprache sprache = _unitOfWork.SpracheRepository.Get(s => s.Lokalisierung == language).FirstOrDefault();
            var standortSprachen = _unitOfWork.StandortRepository.GetByID(id).Sprachen.OrderBy(s => s.SpracheID);

            if (standortSprachen.Contains(sprache))
            {
                ViewData["SpracheID"] = sprache.SpracheID;
            }
            else
            {
                ViewData["SpracheID"] = standortSprachen.FirstOrDefault().SpracheID;
            }

            return View("KundendokumentIndex", viewModel);
        }

        public ActionResult KundendokumentPageControlCallbacksPartial(int standortID, int kundendokumentID, int spracheID)
        {
            ViewData["StandortID"] = standortID;
            ViewData["KundendokumentID"] = kundendokumentID;
            ViewData["SpracheID"] = spracheID;

            return PartialView("KundendokumentPageControlCallbacksPartial");
        }

        public ActionResult UserIndex(bool displayDeactivated = false)
        {
            var sh = new SessionHelper();
            var kunden = new List<Kunde>();

            var currentUser = sh.GetCurrentUser();

            var model = _unitOfWork.UserRepository.GetAllUserViewModels(RoleManager.Roles.ToList(), "", displayDeactivated);
            ViewData["displayDeactivated"] = displayDeactivated;

            if (currentUser.KundeID != 1)
            {
                var currentUserKundeId = currentUser.KundeID;
                var customerModel = _unitOfWork.UserRepository.GetUserViewModelByKundeId(RoleManager.Roles.ToList(), currentUserKundeId, displayDeactivated);

                kunden.Add(_unitOfWork.KundeRepository.GetByID((int)sh.GetCurrentUser().KundeID));
                ViewData["kunden"] = kunden;

                return View(customerModel);
            }


            kunden = _unitOfWork.KundeRepository.Get().ToList();
            ViewData["kunden"] = kunden;

            return View("UserIndex", model);
        }
    }
}