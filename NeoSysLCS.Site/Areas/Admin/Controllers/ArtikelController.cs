using System.Web.Mvc;
using System.Web.SessionState;
using DevExpress.Web.Mvc;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;
using NeoSysLCS.Site.Controllers;
using NeoSysLCS.Site.Helpers;

namespace NeoSysLCS.Site.Areas.Admin.Controllers
{
    [SessionState(SessionStateBehavior.ReadOnly)]
    public class ArtikelController : BaseController
    {
        private readonly IUnitOfWork _unitOfWork;

        public ArtikelController()
        {
            _unitOfWork = new UnitOfWork();
        }

        public ArtikelController(IUnitOfWork unitOfWorkCandidate)
        {
            _unitOfWork = unitOfWorkCandidate;
        }


        /// <summary>
        /// Index action for artikel view
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns></returns>
        public ActionResult Index(int id)
        {
            var viewModels = _unitOfWork.ArtikelRepository.GetAllArtikelViewModels(id);

            ViewData["ErlassID"] = id;

            return View("Index", viewModels);
        }

        /// <summary>
        /// Shows all artikel of the specified erlass
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns></returns>
        [ValidateInput(false)]
        public ActionResult ArtikelGridView(int id)
        {
            var viewModels = _unitOfWork.ArtikelRepository.GetAllArtikelViewModels(id);

            ViewData["ErlassID"] = id;

            return PartialView("_ArtikelGridView", viewModels);
        }

        /// <summary>
        /// Update action for artikel grid view
        /// </summary>
        /// <param name="updateValues">The update values.</param>
        /// <param name="id">The id.</param>
        /// <returns></returns>
        [ValidateInput(false)]
        public ActionResult ArtikelGridViewBatchEditUpdate(MVCxGridViewBatchUpdateValues<ArtikelViewModel, int> updateValues, int id)
        {
            foreach (var artikel in updateValues.Insert)
            {
                GridViewUpdateHelper<Artikel, ArtikelViewModel>.DoUpdateInsert(() =>
                {
                    if (updateValues.IsValid(artikel))
                    {
                        artikel.ErlassID = id;
                        _unitOfWork.ArtikelRepository.Insert(artikel);
                        _unitOfWork.Save();
                    }
                }, artikel, updateValues);
            }
            foreach (var artikel in updateValues.Update)
            {
                GridViewUpdateHelper<Artikel, ArtikelViewModel>.DoUpdateInsert(() =>
                {
                    if (updateValues.IsValid(artikel))
                    {
                        _unitOfWork.ArtikelRepository.Update(artikel);
                        _unitOfWork.Save();
                    }
                }, artikel, updateValues);
            }
            foreach (var artikelID in updateValues.DeleteKeys)
            {
                GridViewUpdateHelper<Artikel, ArtikelViewModel>.DoDelete(() =>
                {
                    _unitOfWork.ArtikelRepository.Delete(artikelID);
                    _unitOfWork.Save();
                }, _unitOfWork.ArtikelRepository, artikelID, updateValues);
            }

            ViewData["ErlassID"] = id;
            var viewModels = _unitOfWork.ArtikelRepository.GetAllArtikelViewModels(id);
            
            return PartialView("_ArtikelGridView", viewModels);
        }



        /// <summary>
        /// Shows all uebersetzungen of the specified artikel
        /// </summary>
        /// <param name="erlassId">The erlass identifier.</param>
        /// <param name="artikelId">The artikel identifier.</param>
        /// <returns></returns>
        [ValidateInput(false)]
        public ActionResult ArtikelUebersetzungenGridView(int erlassId, int artikelId)
        {
            var uebersetzungen = _unitOfWork.ArtikelRepository.GetArtikelUebersetzungenByArtikel(artikelId);

            ViewData["ErlassID"] = erlassId;
            ViewData["ArtikelID"] = artikelId;
            return PartialView("_ArtikelUebersetzungenGridView", uebersetzungen);
        }

        /// <summary>
        /// Update action for artikel uebersetzungen grid view
        /// </summary>
        /// <param name="updateValues">The update values.</param>
        /// <param name="erlassId">The erlass id.</param>
        /// <param name="artikelId">The artikel id.</param>
        /// <returns></returns>
        [ValidateInput(false)]
        public ActionResult ArtikelUebersetzungenGridViewBatchEditUpdate(MVCxGridViewBatchUpdateValues<ArtikelViewModel, int> updateValues, int erlassId, int artikelId)
        {
            foreach (var uebersetzung in updateValues.Update)
            {
                GridViewUpdateHelper<ArtikelViewModel, ArtikelViewModel>.DoUpdateInsert(() =>
                {
                    uebersetzung.ArtikelID = artikelId;
                    _unitOfWork.ArtikelRepository.UpdateArtikelUebersetzung(uebersetzung);
                    _unitOfWork.Save();
                }, uebersetzung, updateValues);
            }

            ViewData["ErlassID"] = erlassId;
            ViewData["ArtikelID"] = artikelId;
            var uebersetzungen = _unitOfWork.ArtikelRepository.GetArtikelUebersetzungenByArtikel(artikelId);

            return PartialView("_ArtikelUebersetzungenGridView", uebersetzungen);
        }
    }
}
