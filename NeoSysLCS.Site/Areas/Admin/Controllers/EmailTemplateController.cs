using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using DevExpress.Web;
using DevExpress.Web.Mvc;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;
using NeoSysLCS.Site.AzureStorage;
using NeoSysLCS.Site.Controllers;
using NeoSysLCS.Site.Helpers;
using Microsoft.AspNet.Identity;
using Microsoft.AspNet.Identity.EntityFramework;
using Microsoft.AspNet.Identity.Owin;
using System.Web.Http;

namespace NeoSysLCS.Site.Areas.Admin.Controllers
{
    public class EmailTemplateController : BaseController
    {

        private readonly IUnitOfWork _unitOfWork;
        NeoSysLCS_Dev context = new NeoSysLCS_Dev();

        public EmailTemplateController()
        {
            _unitOfWork = new UnitOfWork();
        }

        public ActionResult Index()
        {
            var template = _unitOfWork.EmailTemplateRepository.GetSingleViewModel();

            if (template == null)
            {
                template = new EmailTemplateViewModel(); // prevent null model
            }

            return View(template);
        }

        public ActionResult SaveEmailTemplate([FromBody] EmailTemplate model)
        {
            var EmailTemplateNew = new EmailTemplate
            {
                De_Sub = model.De_Sub,
                Fr_Sub = model.Fr_Sub,
                It_Sub = model.It_Sub,
                En_Sub = model.En_Sub,
                De_Body = model.De_Body,
                Fr_Body = model.Fr_Body,
                It_Body = model.It_Body,
                En_Body = model.En_Body,
                ErstelltAm = DateTime.Now,
                BearbeitetAm = DateTime.Now,
                BearbeitetVonID = User.Identity.GetUserId(),
                ErstelltVonID = User.Identity.GetUserId()
            };
            var KundeEmailTemplateWithKundeID = _unitOfWork.EmailTemplateRepository.GetEmailTemplate(1);
            if (KundeEmailTemplateWithKundeID != null)
            {
                _unitOfWork.EmailTemplateRepository.UpdateEmailTemplate(EmailTemplateNew);
                _unitOfWork.Save();
            }
            else
            {

                _unitOfWork.EmailTemplateRepository.InsertEmailTemplate(EmailTemplateNew);
                _unitOfWork.Save();
            }
            return Json(new { success = true });
        }

    }
}