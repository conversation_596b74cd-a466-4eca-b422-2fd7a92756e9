using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Web.Mvc;
using System.Web.SessionState;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Site.Controllers;

namespace NeoSysLCS.Site.Areas.Admin.Controllers
{
    [SessionState(SessionStateBehavior.ReadOnly)]
    public class KommentarErlasseController : BaseController
    {
        private readonly IUnitOfWork _unitOfWork;

        public KommentarErlasseController()
        {
            _unitOfWork = new UnitOfWork();
        }

        public KommentarErlasseController(IUnitOfWork unitOfWorkCandidate)
        {
            _unitOfWork = unitOfWorkCandidate;
        }

        public ActionResult Index(int id)
        {
            ViewData["KommentarID"] = id;
            return View("Index", _unitOfWork.ErlassRepository.GetAllErlassViewModelsByKommentar(id));
        }

        /// <summary>
        /// Shows the listbox with the selected values
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns></returns>
        public ActionResult KommentarErlassePartial(int id)
        {
            ViewData["KommentarID"] = id;
            return PartialView("_KommentarErlassePartial");
        }

        /// <summary>
        /// Shows the gridview with the Erlasse that can be selected
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns></returns>
        public ActionResult ErlasseSelectionPartial(int id)
        {
            string selectedIDs = Request.Params["selectedIDsHF"];
            ViewData["_selectedIDs"] = selectedIDs;
            ViewData["KommentarID"] = id;
            return PartialView("_ErlasseSelectionPartial");
        }

        public ActionResult SaveKommentarErlasse()
        {
            //Get all selected keys from e.customArgs on GridView callback
            string selectedIDs = Request.Params["selectedIDsHF"];
            int kommentarID = Convert.ToInt32(Request.Params["id"]);
            ViewData["_selectedIDs"] = selectedIDs;
            ViewData["KommentarID"] = kommentarID;

            string message = _unitOfWork.KommentarRepository.SaveKommentarErlasse(selectedIDs, kommentarID);
            if (message != "")
            {
                return Content(message);
            }
            else
            {
                _unitOfWork.Save();

                return Json(new { Url = Url.Action("Index", "Kommentar") });
            }
            //return Content("Die Zuordnungen wurden gespeichert");
        }
    }
}
