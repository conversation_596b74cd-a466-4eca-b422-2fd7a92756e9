using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using System.Web.Mvc;
using System.Web.SessionState;
using Hangfire;
using Microsoft.AspNet.Identity;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;
using NeoSysLCS.Site.Controllers;
using NeoSysLCS.Site.Cortec.task;
using NeoSysLCS.Site.Cortec.task.kommentar;

namespace NeoSysLCS.Site.Areas.Admin.Controllers
{
    [SessionState(SessionStateBehavior.ReadOnly)]
    public class KommentarHauptErlassController : BaseController
    {
        private readonly IUnitOfWork _unitOfWork;

        public KommentarHauptErlassController()
        {
            _unitOfWork = new UnitOfWork();
        }

        public KommentarHauptErlassController(IUnitOfWork unitOfWorkCandidate)
        {
            _unitOfWork = unitOfWorkCandidate;
        }

        public ActionResult Index(int id)
        {
            ViewData["KommentarID"] = id;
            return View("Index", _unitOfWork.ErlassRepository.GetHauptErlassViewModelsByKommentar(id));
        }

        /// <summary>
        /// Shows the listbox with the selected values
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns></returns>
        public ActionResult KommentarHauptErlassPartial(int id)
        {
            ViewData["KommentarID"] = id;
            return PartialView("_KommentarHauptErlassPartial");
        }

        /// <summary>
        /// Shows the gridview with the Erlasse that can be selected
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns></returns>
        public ActionResult HauptErlassSelectionPartial(int id)
        {
            string selectedIDs = Request.Params["selectedIDsHF"];
            ViewData["_selectedIDs"] = selectedIDs;
            ViewData["KommentarID"] = id;
            return PartialView("_HauptErlassSelectionPartial");
        }

        public ActionResult SaveKommentarHauptErlass()
        {
            //Get all selected keys from e.customArgs on GridView callback
            string selectedIDs = Request.Params["selectedIDsHF"];
            int selectedID = Convert.ToInt32(selectedIDs);
            int kommentarID = Convert.ToInt32(Request.Params["id"]);
            ViewData["_selectedID"] = selectedID;
            ViewData["KommentarID"] = kommentarID;


            var userId = User.Identity.GetUserId();
            var user = _unitOfWork.UserRepository.GetById(userId);
            _unitOfWork.KommentarRepository.SaveKommentarHauptErlass(selectedID, kommentarID);
            _unitOfWork.Save();


            CreateCortecTask(kommentarID, user.CortecId ?? -1);

            return Json(new { Url = Url.Action("Index", "Kommentar") });
            
        }

        public void CreateCortecTask(int kommentarId, int appUserCortecId)
        {
            var kommentarVM = _unitOfWork.KommentarRepository.GetKommentarViewModelById(kommentarId);
            // Create an adapter for the Kommentar, making it compatible with the factory
            var adapter = new KommentarAdapter(kommentarVM, _unitOfWork, appUserCortecId);

            // Use the factory and strategies to create tasks based on the adapted Kommentar
            var cortecTaskFactory = new CortecTaskFactory<KommentarViewModel>(adapter, _unitOfWork);

            cortecTaskFactory.CreateCortecTasks();

            _unitOfWork.KommentarRepository.SetCortecTaskCreatedDate(kommentarId);
            _unitOfWork.Save();
        }
    }
}
