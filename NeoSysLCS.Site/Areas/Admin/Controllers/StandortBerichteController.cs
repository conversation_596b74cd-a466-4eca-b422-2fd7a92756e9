using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using DevExpress.Web;
using DevExpress.Web.Mvc;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;
using NeoSysLCS.Site.AzureStorage;
using NeoSysLCS.Site.Controllers;
using NeoSysLCS.Site.Helpers;

namespace NeoSysLCS.Site.Areas.Admin.Controllers
{
    public class StandortBerichteController : BaseController
    {

        private readonly IUnitOfWork _unitOfWork;
        NeoSysLCS_Dev context = new NeoSysLCS_Dev();

        public StandortBerichteController()
        {
            _unitOfWork = new UnitOfWork();
        }

        /// <summary>
        /// Shows all StandortBerichte of the specified standort
        /// </summary>
        /// <param name="standortID">Standort identifier.</param>
        /// <returns></returns>
        public ActionResult Index(int standortID)
        {

            var standort = _unitOfWork.StandortRepository.GetByID(standortID);
            var kunde = _unitOfWork.KundeRepository.GetByID(standort.KundeID);
            
            ViewData["StandortID"] = standortID;
            ViewData["StandortName"] = standort.Name;
            ViewData["KundeName"] = kunde.Name;
            
            var viewModels = _unitOfWork.StandortBerichtRepository.GetAllViewModels(new List<int>(){ standortID });
            return View("Index", viewModels);
        }

        public ActionResult StandortBerichteGridView(int standortID)
        {
            var viewModels = _unitOfWork.StandortBerichtRepository.GetAllViewModels(new List<int>() { standortID });

            return PartialView("_StandortBerichteGridView", viewModels);
        }

        [ValidateInput(false)]
        public ActionResult GridViewBatchEditUpdate(MVCxGridViewBatchUpdateValues<StandortBerichtViewModel, int> updateValues, int standortID)
        {
            foreach (var sb in updateValues.Insert)
            {
                GridViewUpdateHelper<StandortBericht, StandortBerichtViewModel>.DoUpdateInsert(() =>
                {
                    if (updateValues.IsValid(sb))
                    {
                        sb.StandortID = standortID;
                        _unitOfWork.StandortBerichtRepository.Insert(sb);
                        _unitOfWork.Save();
                    }
                }, sb, updateValues);
            }
            foreach (var sb in updateValues.Update)
            {
                GridViewUpdateHelper<StandortBericht, StandortBerichtViewModel>.DoUpdateInsert(() =>
                {
                    if (updateValues.IsValid(sb))
                    {
                        _unitOfWork.StandortBerichtRepository.Update(sb);
                        _unitOfWork.Save();
                    }
                }, sb, updateValues);
            }
            foreach (var sbID in updateValues.DeleteKeys)
            {
                GridViewUpdateHelper<StandortBericht, StandortBerichtViewModel>.DoDelete(() =>
                {
                    _unitOfWork.StandortBerichtRepository.Delete(standortID, sbID);
                    _unitOfWork.Save();
                }, _unitOfWork.StandortBerichtRepository, sbID, updateValues);

            }

            var viewModels = _unitOfWork.StandortBerichtRepository.GetAllViewModels(new List<int>() { standortID });
            return PartialView("_StandortBerichteGridView", viewModels);
        }


        public ActionResult ShowUploadPopup(int standortID, int standortBerichtID, string url, string field, int languageId = -1)
        {
            ViewData["standortID"] = standortID;
            ViewData["standortBerichtID"] = standortBerichtID;
            ViewData["url"] = url;
            ViewData["field"] = field;
            ViewData["languageId"] = languageId;
            return PartialView("StandortBerichteUploadPopupContent");
        }

        public ActionResult FileUpload(int standortID, int standortBerichtID, string field, int languageId)
        {
            UploadedFile[] files = UploadControlExtension.GetUploadedFiles("uploadControl");

            // Your custom logic to process uploaded files.
            if (files != null && files.Length > 0)
            {
                UploadedFile file = files[0];

                string filePath = ConfigurationManager.AppSettings["FilePath"];
                string resultFileUrl;
                if (filePath == "Azure")
                {
                    IBlobStorageClientFactory factory = new BlobStorageClientFactory();
                    IBlobStorageClient client = factory.CreateStandortBerichteFilesBlobStorageClient(standortBerichtID);
                    var uploadResult = client.Upload(file.FileName, file.ContentType, file.FileContent);
                    resultFileUrl = uploadResult.Url;
                }
                else
                {
                    string uploadDirectory = "~/Upload/StandortBericht/"; // Directly upload the file to the specific directory
                    resultFileUrl = uploadDirectory + standortID + "_" + file.FileName;
                    string resultFilePath = Request.MapPath(resultFileUrl);

                    file.SaveAs(resultFilePath);
                    resultFileUrl = ConfigurationManager.AppSettings["FilePath"] + "StandortBericht/" + standortID + "_" + file.FileName;
                }

                _unitOfWork.StandortBerichtRepository.UpdateUploadedFileUrl(standortBerichtID, field, resultFileUrl, languageId);
            }

            return null;
        }

        public ActionResult FileDelete(int standortBerichtID, string field, int languageId)
        {
            ViewData["standortBerichtID"] = standortBerichtID;
            ViewData["field"] = field;
            ViewData["languageId"] = languageId;

            _unitOfWork.StandortBerichtRepository.UpdateUploadedFileUrl(standortBerichtID, field, null, languageId);

            return PartialView("StandortBerichteUploadPopupContent");
        }
    }
}