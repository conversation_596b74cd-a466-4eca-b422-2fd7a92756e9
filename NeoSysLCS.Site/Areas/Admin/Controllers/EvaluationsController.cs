using System.Collections.Generic;
using System.Web.Mvc;
using System.Web.SessionState;
using Microsoft.AspNet.Identity;
using Microsoft.AspNet.Identity.EntityFramework;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Site.Controllers;
using NeoSysLCS.Site.Helpers;

namespace NeoSysLCS.Site.Areas.Admin.Controllers
{
    [SessionState(SessionStateBehavior.ReadOnly)]
    public class EvaluationsController : BaseController
    {

        public const string EvaluationsUpdatesPerMonthPartialView = "~/Areas/Admin/Views/Evaluations/_EvaluationsUpdatesPerMonthPartialView.cshtml";
        public const string EvaluationsDaysOfDelayPartialView = "~/Areas/Admin/Views/Evaluations/_EvaluationsDaysOfDelayPartialView.cshtml";
        public const string EvaluationsAmountOfDelayedUpdatesPartialView = "~/Areas/Admin/Views/Evaluations/_EvaluationsAmountOfDelayedUpdatesPartialView.cshtml";
        public const string EvaluationsOfferVolumesNewCustomerPartialView = "~/Areas/Admin/Views/Evaluations/_EvaluationsOfferVolumesNewCustomerPartialView.cshtml";
        public const string EvaluationsOfferVolumesExistingCustomerPartialView = "~/Areas/Admin/Views/Evaluations/_EvaluationsOfferVolumesExistingCustomerPartialView.cshtml";
        private readonly IUnitOfWork _unitOfWork;

        public EvaluationsController()
        {
            _unitOfWork = new UnitOfWork();
        }

        public ActionResult Index(int? expanded)
        {
            ViewData["UpdatePerMonthUserId"] = "";
            ViewData["DelayPerMonthUserId"] = "";
            ViewData["AmountDelayPerMonthUserId"] = "";
            ViewData["ExistingNewCustomerUserId"] = "";
            ViewData["YearId"] = "";
            ViewData["ExistingCustomerUserId"] = "";
            ViewData["ExistingYearId"] = "";
            return View("Index");
        }

        public ActionResult EvaluationsPageControlCallbacksPartial(string UpdatePerMonthUserId = "", string DelayPerMonthUserId = "", string AmountDelayPerMonthUserId = "", string ExistingNewCustomerUserId = "", string YearId = "", string ExistingCustomerUserId = "", string ExistingYearId = "")
        {
            ViewData["UpdatePerMonthUserId"] = UpdatePerMonthUserId;
            ViewData["DelayPerMonthUserId"] = DelayPerMonthUserId;
            ViewData["AmountDelayPerMonthUserId"] = AmountDelayPerMonthUserId;
            ViewData["ExistingNewCustomerUserId"] = ExistingNewCustomerUserId;
            ViewData["ExistingCustomerUserId"] = ExistingCustomerUserId;
            ViewData["YearId"] = YearId;
            ViewData["ExistingYearId"] = ExistingYearId;
            return PartialView("_EvaluationsPageControlCallbacksPartial");
        }

        public ActionResult UserUpdateIndex(string UpdatePerMonthUserId)
        {
            ViewData["UpdatePerMonthUserId"] = UpdatePerMonthUserId;
            var viewModels = _unitOfWork.EvaluationRepository.GetUpdatesPerMonth(UpdatePerMonthUserId);

            return View("Index", viewModels);
        }

        public ActionResult UserDelayIndex(string DelayPerMonthUserId)
        {
            ViewData["DelayPerMonthUserId"] = DelayPerMonthUserId;
            var viewModels = _unitOfWork.EvaluationRepository.GetDelaysPerMonth(DelayPerMonthUserId);

            return View("Index", viewModels);
        }

        public ActionResult UserAmountDelayIndex(string AmountDelayPerMonthUserId, string YearId)
        {
            string id = AmountDelayPerMonthUserId.Split(',')[0];
            string year = AmountDelayPerMonthUserId.Split(',')[1];

            ViewData["AmountDelayPerMonthUserId"] = id;
            ViewData["YearId"] = year;
            var viewModels = _unitOfWork.EvaluationRepository.GetAmountOfDelaysPerMonth(AmountDelayPerMonthUserId, year);

            return View("Index", viewModels);
        }

        public ActionResult YearUserAmountDelayIndex(string YearId, string AmountDelayPerMonthUserId)
        {
            string id = YearId.Split(',')[1];
            string year = YearId.Split(',')[0];

            ViewData["YearId"] = year;
            ViewData["AmountDelayPerMonthUserId"] = id;
            var viewModels = _unitOfWork.EvaluationRepository.GetAmountOfDelaysPerMonth(AmountDelayPerMonthUserId, year);

            return View("Index", viewModels);
        }

        public ActionResult UserOfferNewCustomerIndex(string ExistingNewCustomerUserId, string YearId)
        {
            string id = ExistingNewCustomerUserId.Split(',')[0];
            string year = ExistingNewCustomerUserId.Split(',')[1];

            ViewData["ExistingNewCustomerUserId"] = id;
            ViewData["YearId"] = year;
            var viewModels = _unitOfWork.EvaluationRepository.GetOfferNewCustomer(id, year);

            return View("Index", viewModels);
        }

        public ActionResult YearNewCustomerIndex(string YearId, string ExistingNewCustomerUserId)
        {
            string id = YearId.Split(',')[1];
            string year = YearId.Split(',')[0];

            ViewData["YearId"] = year;
            ViewData["ExistingNewCustomerUserId"] = id;
            var viewModels = _unitOfWork.EvaluationRepository.GetOfferNewCustomer(id, year);

            return View("Index", viewModels);
        }

        public ActionResult UserOfferExistingCustomerIndex(string ExistingCustomerUserId, string ExistingYearId)
        {
            string id = ExistingCustomerUserId.Split(',')[0];
            string year = ExistingCustomerUserId.Split(',')[1];

            ViewData["ExistingCustomerUserId"] = id;
            ViewData["ExistingYearId"] = year;

            var viewModels = _unitOfWork.EvaluationRepository.GetOfferExistingCustomer(id, year);

            return View("Index", viewModels);
        }

        public ActionResult YearExistingCustomerIndex(string ExistingYearId, string ExistingCustomerUserId)
        {
            string id = ExistingYearId.Split(',')[1];
            string year = ExistingYearId.Split(',')[0];

            ViewData["ExistingYearId"] = year;
            ViewData["ExistingCustomerUserId"] = id;

            var viewModels = _unitOfWork.EvaluationRepository.GetOfferExistingCustomer(id, year);

            return View("Index", viewModels);
        }

        public List<ApplicationUser> GetProjektleiterUsers(bool addUserAll)
        {
            SessionHelper sessionHelper = new SessionHelper();

            List<ApplicationUser> users = new List<ApplicationUser>();
            RoleManager<IdentityRole> roleMngr = sessionHelper.GetRoleManager();
            ApplicationUserManager usrMngr = sessionHelper.GetUserManager();
            IdentityRole projektleiterRole = roleMngr.FindByName("PROJECTMANAGER");
            if (addUserAll)
            {
                users.Add(new ApplicationUser() { Id = "ALL", Vorname = "Alle", Nachname = "Projektleiter" }); // Add dummy user for all projektleiter selection
            }
            foreach (var identityUserRole in projektleiterRole.Users)
            {
                ApplicationUser usr = usrMngr.FindById(identityUserRole.UserId);
                users.Add(usr);
            }

            return users;
        }
    }
}
