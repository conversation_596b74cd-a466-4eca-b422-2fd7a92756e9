using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Web.Mvc;
using System.Web.SessionState;
using DevExpress.Data.ODataLinq.Helpers;
using DevExpress.Web;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;
using NeoSysLCS.Site.Controllers;
using DevExpress.Web.Mvc;
using DevExpress.Xpo.Helpers;
using NeoSysLCS.Site.Helpers;

namespace NeoSysLCS.Site.Areas.Admin.Controllers
{
    [SessionState(SessionStateBehavior.ReadOnly)]
    public class ObjektObligationController : BaseController
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly NeoSysLCS_Dev context;

        public ObjektObligationController()
        {
            _unitOfWork = new UnitOfWork();
            context = new NeoSysLCS_Dev();
        }

        public ObjektObligationController(IUnitOfWork unitOfWorkCandidate)
        {
            _unitOfWork = unitOfWorkCandidate;

        }

        public ActionResult Index(int objektID)
        {
            ViewData["ObjektID"] = objektID;

            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            Sprache sprache = context.Sprachen.FirstOrDefault(s => s.Lokalisierung == language);
            var _currentLang = sprache.SpracheID;
            ViewData["SpracheID"] = _currentLang;

            var obligations = _unitOfWork.ObligationRepository.GetObligationViewModelsByObjektId(objektID);
            var test = obligations.ToList();
            return View("Index", obligations);
        }

        /// <summary>
        /// Shows the obligations related to the Objekt
        /// </summary>
        /// <param name="objektID">The objekt identifier.</param>
        /// <returns></returns>
        public ActionResult ObligationGridView(int objektId)
        {
            ViewData["ObjektID"] = objektId;

            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            Sprache sprache = context.Sprachen.FirstOrDefault(s => s.Lokalisierung == language);
            var _currentLang = sprache.SpracheID;
            ViewData["SpracheID"] = _currentLang;

            var standortObjekte = _unitOfWork.ObligationRepository.GetObligationViewModelsByObjektId(objektId);
            return PartialView("_ObligationGridView", standortObjekte);
        }

        /// <summary>
        /// Update action for the obligation grid view
        /// </summary>
        /// <param name="updateValues">The update values.</param>
        /// <param name="displayArchived">if set to <c>true</c> [display archived].</param>
        /// <returns></returns>
        [ValidateInput(false)]
        public ActionResult ObligationGridViewBatchEditUpdate(MVCxGridViewBatchUpdateValues<ObligationViewModel, int> updateValues, int objektID)
        {

            foreach (var obligation in updateValues.Insert)
            {
                obligation.ObjektID = objektID;
                GridViewUpdateHelper<Obligation, ObligationViewModel>.DoUpdateInsert(() =>
                {
                    if (updateValues.IsValid(obligation))
                    {
                        _unitOfWork.ObligationRepository.Insert(obligation);
                        _unitOfWork.Save();
                    }
                }, obligation, updateValues);
            }
            foreach (var obligation in updateValues.Update)
            {
                GridViewUpdateHelper<Obligation, ObligationViewModel>.DoUpdateInsert(() =>
                {
                    if (updateValues.IsValid(obligation))
                    {
                        _unitOfWork.ObligationRepository.Update(obligation);
                        _unitOfWork.Save();
                    }
                }, obligation, updateValues);
            }
            foreach (var obligationId in updateValues.DeleteKeys)
            {
                GridViewUpdateHelper<Obligation, ObligationViewModel>.DoDelete(() =>
                {
                    _unitOfWork.ObligationRepository.Delete(obligationId);
                    _unitOfWork.Save();
                }, _unitOfWork.ObligationRepository, obligationId, updateValues);

            }

            var viewModels = _unitOfWork.ObligationRepository.GetObligationViewModelsByObjektId(objektID);
            return PartialView("_ObligationGridView", viewModels);

        }

        /// <summary>
        /// Shows all uebersetzungen of the specified obligation
        /// </summary>
        /// <param name="obligationID">The obligation id.</param>
        /// <returns></returns>
        [ValidateInput(false)]
        public ActionResult ObligationUebersetzungenGridView(int obligationID)
        {
            var obligation = _unitOfWork.ObligationRepository.GetTranslatedViewModelsBy(obligationID);

            ViewData["ObligationID"] = obligationID;
            return PartialView("_ObligationUebersetzungenGridView", obligation);
        }

        /// <summary>
        /// Update action for the obligation uebersetzungen grid view.
        /// </summary>
        /// <param name="updateValues">The update values.</param>
        /// <param name="obligaitonID">The obligation id.</param>
        /// <returns></returns>
        [ValidateInput(false)]
        public ActionResult ObligationUebersetzungenGridViewBatchEditUpdate(MVCxGridViewBatchUpdateValues<ObligationViewModel, int> updateValues, int obligationID)
        {
            foreach (var obligation in updateValues.Update)
            {
                GridViewUpdateHelper<Obligation, ObligationViewModel>.DoUpdateInsert(() =>
                {
                    if (updateValues.IsValid(obligation))
                    {
                        _unitOfWork.ObligationRepository.InsertUebersetzungObligation(obligation, obligationID);
                        _unitOfWork.Save();
                    }
                }, obligation, updateValues);
            }

            ViewData["ObligationID"] = obligationID;
            var obligations = _unitOfWork.ObligationRepository.GetTranslatedViewModelsBy(obligationID);

            return PartialView("_ObligationUebersetzungenGridView", obligations);
        }
    }

}