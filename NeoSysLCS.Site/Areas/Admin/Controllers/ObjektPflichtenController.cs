using System.Web.Mvc;
using System.Web.SessionState;
using NeoSysLCS.Repositories;
using NeoSysLCS.Site.Controllers;

namespace NeoSysLCS.Site.Areas.Admin.Controllers
{
    [SessionState(SessionStateBehavior.ReadOnly)]
    public class ObjektPflichtenController : BaseController
    {
        private readonly IUnitOfWork _unitOfWork;

        public ObjektPflichtenController()
        {
            _unitOfWork = new UnitOfWork();
        }

        public ObjektPflichtenController(IUnitOfWork unitOfWorkCandidate)
        {
            _unitOfWork = unitOfWorkCandidate;
        }


        /// <summary>
        /// Shows the listbox with the selected values
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns></returns>
        public ActionResult ObjektPflichtenPartial(int id)
        {
            ViewData["ObjektID"] = id;

            return PartialView("_ObjektPflichtenPartial", _unitOfWork.PflichtRepository.GetByObject(id));
        }

        /// <summary>
        /// Shows the gridview with the pflichten that can be selected
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns></returns>
        public ActionResult PflichtSelectionPartial(int id)
        {
            string _selectedIDs = Request.Params["selectedIDsHF"];
            ViewData["_selectedIDs"] = _selectedIDs;
            ViewData["ObjektID"] = id;

            return PartialView("_PflichtSelectionPartial", _unitOfWork.PflichtRepository.GetAllViewModels());
        }

        /// <summary>
        /// Saves the selected pflichten in the objekt
        /// </summary>
        /// <returns></returns>
        public ActionResult SavePflichten()
        {
            //Get all selected keys from e.customArgs on GridView callback
            string _selectedIDs = Request.Params["selectedIDsHF"];
            string _objektID = Request.Params["id"];
            ViewData["_selectedIDs"] = _selectedIDs;
            ViewData["ObjektID"] = _objektID;

            _unitOfWork.ObjektRepository.SavePflichten(_selectedIDs, _objektID);
            _unitOfWork.Save();


            return Content(Resources.Properties.Resources.Notification_Zuordnungen_Gespeichert);
        }
    }
}
