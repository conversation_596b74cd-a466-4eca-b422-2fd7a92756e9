using System;
using System.Collections.Generic;
using System.Web.Mvc;
using System.Linq;
using System.Web.SessionState;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using DevExpress.Web.Mvc;
using NeoSysLCS.Repositories.ViewModels;
using NeoSysLCS.Site.Controllers;
using NeoSysLCS.Site.Helpers;

namespace NeoSysLCS.Site.Areas.Admin.Controllers
{
    [SessionState(SessionStateBehavior.ReadOnly)]
    public class ObjektkategorienController : BaseController
    {
        private readonly IUnitOfWork _unitOfWork;

        public ObjektkategorienController()
        {
            _unitOfWork = new UnitOfWork();
        }

        public ObjektkategorienController(IUnitOfWork unitOfWorkCandidate)
        {
            _unitOfWork = unitOfWorkCandidate;
        }

        public ActionResult Index()
        {
            var objekte = _unitOfWork.ObjektkategorieRepository.GetAllObjektkategorieViewModels();

            return View("Index", objekte.ToList());
        }

        /// <summary>
        /// Show a treelist with all objektkategorien
        /// </summary>
        /// <returns></returns>
        [ValidateInput(false)]
        public ActionResult ObjektkategorienTreeList()
        {
            var objekte = _unitOfWork.ObjektkategorieRepository.GetAllObjektkategorieViewModels();

            return PartialView("ObjektkategorienTreeList", objekte.ToList());
        }

        /// <summary>
        /// Shows the uebersetzungen or the new form of the objektkategorie
        /// </summary>
        /// <param name="objektkategorieID">The objektkategorie identifier.</param>
        /// <returns></returns>
        [ValidateInput(false)]
        public ActionResult ObjektkategorienGridView(int? objektkategorieID)
        {
            if (objektkategorieID.HasValue)
            {
                var objektkategorieUebersetzungen = _unitOfWork.ObjektkategorieRepository.GetObjektkategorieUebersetzungenByObjekt(objektkategorieID.Value);
                ViewData["ObjektkategorieID"] = objektkategorieID.Value;
                return PartialView("ObjektkategorieUebersetzungenGridView", objektkategorieUebersetzungen);
            }

            var objektkategorieViewModel = new ObjektkategorieViewModel();
            var objektkategorien = _unitOfWork.ObjektkategorieRepository.GetAllObjektkategorieViewModels();
           
            var selectListItems = new List<SelectListItem>();
            foreach (var objektkategorie in objektkategorien)
            {
                selectListItems.Add(new SelectListItem()
                {
                    Text = objektkategorie.Name,
                    Value = objektkategorie.ObjektkategorieID.ToString()
                });
            }

            ViewData["Objektkategorien"] = selectListItems.OrderBy(e => e.Text);
            return PartialView("ObjektkategorienNewForm", objektkategorieViewModel);

        }

        /// <summary>
        /// Saves a new objekt kategorie
        /// </summary>
        /// <param name="objektkategorie">The objektkategorie.</param>
        /// <returns></returns>
        [HttpPost]
        public ActionResult ObjektkategrienNewForm(ObjektkategorieViewModel objektkategorie)
        {
            if (ModelState.IsValid)
            {
                _unitOfWork.ObjektkategorieRepository.Insert(objektkategorie);
                _unitOfWork.Save();
            }
            return RedirectToAction("Index");


        }

        /// <summary>
        /// Moves a objektkategorie to a new parent
        /// </summary>
        /// <param name="objektkategorieID">The objektkategorie identifier.</param>
        /// <param name="parentObjektkategorieID">The parent objektkategorie identifier.</param>
        /// <returns></returns>
        public ActionResult ObjektkategorienTreeListMove(int objektkategorieID, int? parentObjektkategorieID)
        {
            var objektkategorie = _unitOfWork.ObjektkategorieRepository.GetAllObjektkategorieViewModels()
                .FirstOrDefault(o => o.ObjektkategorieID == objektkategorieID);
            if (objektkategorie != null)
            {
                objektkategorie.ParentObjektkategorieID = parentObjektkategorieID;
                _unitOfWork.ObjektkategorieRepository.Update(objektkategorie);
                _unitOfWork.Save();
            }
            
            var objekte = _unitOfWork.ObjektkategorieRepository.GetAllObjektkategorieViewModels();
            return PartialView("ObjektkategorienTreeList", objekte.ToList());
        }

        /// <summary>
        /// Deletes a objektkategorie
        /// </summary>
        /// <param name="objektkategorieID">The objektkategorie identifier.</param>
        /// <returns></returns>
        public ActionResult ObjektkategorienTreeListDelete(int objektkategorieID)
        {
            try
            {
                _unitOfWork.ObjektkategorieRepository.Delete(objektkategorieID);
                _unitOfWork.Save();
            }
            catch (Exception e)
            {
                ViewData["EditError"] = Resources.Properties.Resources.Fehler_BatchEditDeleteFailed + " (" +e.Message + ")";
            }
            var objekte = _unitOfWork.ObjektkategorieRepository.GetAllObjektkategorieViewModels();
            return PartialView("ObjektkategorienTreeList", objekte.ToList());

        }

        /// <summary>
        /// Saves the uebersetzugnen of an objektkategorie
        /// </summary>
        /// <param name="updateValues">The update values.</param>
        /// <param name="objektkategorieID">The objektkategorie identifier.</param>
        /// <returns></returns>
        public ActionResult ObjektkategorieUebersetzungenGridViewBatchEditingUpdate(MVCxGridViewBatchUpdateValues<ObjektkategorieViewModel, int> updateValues, int ObjektkategorieID)
        {
            foreach (var uebersetzung in updateValues.Update)
            {
                GridViewUpdateHelper<ObjektkategorieViewModel, ObjektkategorieViewModel>.DoUpdateInsert(() =>
                {
                    uebersetzung.ObjektkategorieID = ObjektkategorieID;
                    _unitOfWork.ObjektkategorieRepository.UpdateObjektkategorieUebersetzung(uebersetzung);
                    _unitOfWork.Save();
                }, uebersetzung, updateValues);

            }
            _unitOfWork.Save();
            return RedirectToAction("Index");
        }
    }
}
