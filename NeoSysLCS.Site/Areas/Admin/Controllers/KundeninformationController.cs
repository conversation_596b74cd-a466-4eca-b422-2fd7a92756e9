using System.Collections.Generic;
using System.Web.Mvc;
using System.Web.SessionState;
using DevExpress.Web.Mvc;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Site.Helpers;

namespace NeoSysLCS.Site.Areas.Admin.Controllers
{
    [SessionState(SessionStateBehavior.ReadOnly)]
    public class KundeninformationController : Controller
    {
        private readonly IUnitOfWork _unitOfWork;

        public KundeninformationController()
        {
            _unitOfWork = new UnitOfWork();
        }

        public KundeninformationController(IUnitOfWork unitOfWorkCandidate)
        {
            _unitOfWork = unitOfWorkCandidate;
        }

        public ActionResult Index()
        {
            var kundeninformationen = _unitOfWork.AllgemeineKundeninformationRepository.GetAllgmeineKundeninformationUebersetzungen();

            return View("Index", kundeninformationen);
        }

        /// <summary>
        /// Shows the kundeninformation gridview
        /// </summary>
        /// <returns></returns>
        public ActionResult KundeninformationUebersetzungenGridView()
        {
            var kundeninformationen = _unitOfWork.AllgemeineKundeninformationRepository.GetAllgmeineKundeninformationUebersetzungen();

            return PartialView("_KundeninformationUebersetzungenGridView", kundeninformationen);
        }

        /// <summary>
        /// Saves the changes of kundendokumentinformationen
        /// </summary>
        /// <param name="updateValues">The update values.</param>
        /// <returns></returns>
        [ValidateInput(false)]
        public ActionResult KundeninformationUebersetzungenGridViewBatchEditUpdate(MVCxGridViewBatchUpdateValues<AllgemeineKundeninformation, int> updateValues)
        {
            foreach (var allgemeineKundeninformation in updateValues.Update)
            {

                GridViewUpdateHelper<AllgemeineKundeninformation, AllgemeineKundeninformation>.DoUpdateInsert(() =>
                {
                    if (updateValues.IsValid(allgemeineKundeninformation))
                    {
                        allgemeineKundeninformation.Text = StringHelper.StripHtmlTags(allgemeineKundeninformation.Text);
                        allgemeineKundeninformation.Text = StringHelper.SearchUrlInString(allgemeineKundeninformation.Text);
                        _unitOfWork.AllgemeineKundeninformationRepository.Update(allgemeineKundeninformation);
                        _unitOfWork.Save();
                    }
                }, allgemeineKundeninformation, updateValues);

            }

            var kundeninformationen = _unitOfWork.AllgemeineKundeninformationRepository.GetAllgmeineKundeninformationUebersetzungen();
            return PartialView("_KundeninformationUebersetzungenGridView", kundeninformationen);
        }
    }
}