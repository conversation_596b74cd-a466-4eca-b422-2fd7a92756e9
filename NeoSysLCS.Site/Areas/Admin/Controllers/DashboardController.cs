using System.Collections.Generic;
using System.Web.Mvc;
using System.Web.SessionState;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;
using NeoSysLCS.Site.Controllers;

namespace NeoSysLCS.Site.Areas.Admin.Controllers
{
    [SessionState(SessionStateBehavior.ReadOnly)]
    public class DashboardController : BaseController
    {
        private readonly UnitOfWork _unitOfWork;
        public DashboardController()
        {
            _unitOfWork = new UnitOfWork();
        }

        public ActionResult Index()
        {
            return View();
        }

        public ActionResult DashboardPageControlCallbacksPartial()
        {
            return PartialView("DashboardPageControlCallbacksPartial");
        }

        public ActionResult KundeGridView()
        {
            var kunden = _unitOfWork.KundeRepository.GetAllKundenViewModels();

            return PartialView("_KundeGridView", kunden);
        }

        public ActionResult ObjektGridView()
        {
            var objekte = _unitOfWork.ObjektRepository.GetAllObjektViewModels();

            return PartialView("_ObjektGridView", objekte);
        }

        public ActionResult ErlassGridView()
        {
            var erlasse = _unitOfWork.ErlassRepository.GetAllErlassViewModels();

            return PartialView("_ErlassGridView", erlasse);

        }

        public ActionResult StandortGridView()
        {
            var standorte = _unitOfWork.StandortRepository.GetAllStandortViewModels();

            return PartialView("_StandortGridView", standorte);
        }
    }
}