using System;
using System.Linq;
using System.Threading.Tasks;
using System.Web.Mvc;
using log4net;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.Helper;
using NeoSysLCS.Repositories.ViewModels;
using NeoSysLCS.Site.Controllers;
using NeoSysLCS.Site.Services.Newsletter;
using NeoSysLCS.Site.Helpers;

namespace NeoSysLCS.Site.Areas.Admin.Controllers
{
    /// <summary>
    /// Controller for newsletter management in the administration area
    /// </summary>
    [NewsletterAccessAttribute]
    public class NewsletterController : BaseController
    {
        private static readonly ILog log = LogManager.GetLogger(typeof(NewsletterController));

        private readonly IUnitOfWork _unitOfWork;
        private readonly INewsletterManagementService _newsletterManagementService;

        /// <summary>
        /// Default constructor with default services
        /// </summary>
        public NewsletterController()
            : this(
                new UnitOfWork(),
                new NewsletterManagementService(
                    new UnitOfWork(),
                    new NewsletterEmailService(),
                    new NewsletterPdfGenerationService(new UnitOfWork()),
                    new NewsletterFileUploadService()))
        {
        }

        /// <summary>
        /// Constructor with dependency injection for testing
        /// </summary>
        public NewsletterController(
            IUnitOfWork unitOfWork,
            INewsletterManagementService newsletterManagementService)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _newsletterManagementService = newsletterManagementService ?? throw new ArgumentNullException(nameof(newsletterManagementService));
        }

        /// <summary>
        /// Main newsletter management page
        /// </summary>
        public ActionResult Index()
        {
            try
            {
                ViewBag.Title = "Newsletter Management";
                
                // Initialize with empty search criteria
                var searchModel = new NewsletterUserSearchViewModel();
                return View(searchModel);
            }
            catch (Exception ex)
            {
                log.Error($"Error loading newsletter management page: {ex.Message}", ex);
                ViewBag.ErrorMessage = "An error occurred while loading the newsletter management page.";
                return View(new NewsletterUserSearchViewModel());
            }
        }

        /// <summary>
        /// Partial view for user search results
        /// </summary>
        [HttpPost]
        [ValidateInput(false)]
        public ActionResult UserSearchPartial(NewsletterUserSearchViewModel searchModel)
        {
            try
            {
                if (searchModel == null)
                {
                    searchModel = new NewsletterUserSearchViewModel();
                }

                // Store search criteria in session for pagination callbacks
                Session["NewsletterSearchCriteria"] = searchModel;

                var users = _newsletterManagementService.SearchUsers(searchModel);
                ViewData["SearchCriteria"] = searchModel;
                
                return PartialView("_UserSelectionGridPartial", users);
            }
            catch (Exception ex)
            {
                log.Error($"Error searching users for newsletter: {ex.Message}", ex);
                ViewBag.ErrorMessage = "An error occurred while searching for users.";
                return PartialView("_UserSelectionGridPartial", Enumerable.Empty<NewsletterUserSelectionViewModel>().AsQueryable());
            }
        }

        /// <summary>
        /// Grid view for user selection
        /// </summary>
        public ActionResult UserSelectionGridView(NewsletterUserSearchViewModel searchModel = null)
        {
            try
            {
                if (searchModel == null)
                {
                    searchModel = new NewsletterUserSearchViewModel();
                    
                    // Try to get search criteria from ViewData if available
                    var sessionSearchCriteria = Session["NewsletterSearchCriteria"] as NewsletterUserSearchViewModel;
                    if (sessionSearchCriteria != null)
                    {
                        searchModel = sessionSearchCriteria;
                    }
                }
                else
                {
                    // Save search criteria to session for pagination callbacks
                    Session["NewsletterSearchCriteria"] = searchModel;
                }

                var users = _newsletterManagementService.SearchUsers(searchModel);
                return PartialView("_UserSelectionGridPartial", users);
            }
            catch (Exception ex)
            {
                log.Error($"Error loading user selection grid: {ex.Message}", ex);
                return PartialView("_UserSelectionGridPartial", Enumerable.Empty<NewsletterUserSelectionViewModel>().AsQueryable());
            }
        }

        /// <summary>
        /// Grid view callback for user selection - handles pagination
        /// </summary>
        public ActionResult UserSelectionGridViewCallback(NewsletterUserSearchViewModel searchModel = null)
        {
            try
            {
                if (searchModel == null)
                {
                    searchModel = new NewsletterUserSearchViewModel();
                    
                    // Try to get search criteria from session if available
                    var sessionSearchCriteria = Session["NewsletterSearchCriteria"] as NewsletterUserSearchViewModel;
                    if (sessionSearchCriteria != null)
                    {
                        searchModel = sessionSearchCriteria;
                    }
                }
                else
                {
                    // Save search criteria to session for pagination callbacks
                    Session["NewsletterSearchCriteria"] = searchModel;
                }

                var users = _newsletterManagementService.SearchUsers(searchModel);
                
                // Important: Return only the grid content, not the entire partial view
                return PartialView("_UserSelectionGridViewCallbackPartial", users);
            }
            catch (Exception ex)
            {
                log.Error($"Error during grid callback: {ex.Message}", ex);
                return PartialView("_UserSelectionGridViewCallbackPartial", Enumerable.Empty<NewsletterUserSelectionViewModel>().AsQueryable());
            }
        }

        /// <summary>
        /// Validates selected users before sending
        /// </summary>
        [HttpPost]
        public JsonResult ValidateSelectedUsers(string[] selectedUserIds)
        {
            try
            {
                if (selectedUserIds == null || selectedUserIds.Length == 0)
                {
                    return Json(new { success = false, message = "No users selected." });
                }

                var validationResult = _newsletterManagementService.ValidateUsersForSending(selectedUserIds);
                
                return Json(new
                {
                    success = validationResult.IsValid,
                    validUserCount = validationResult.ValidUserCount,
                    invalidUserCount = validationResult.InvalidUserCount,
                    errors = validationResult.Errors,
                    warnings = validationResult.Warnings
                });
            }
            catch (Exception ex)
            {
                log.Error($"Error validating selected users: {ex.Message}", ex);
                return Json(new { success = false, message = "An error occurred while validating users." });
            }
        }

        /// <summary>
        /// Sends newsletters to selected users
        /// </summary>
        [HttpPost]
        public async Task<JsonResult> SendToSelectedUsers(NewsletterBatchSendViewModel model)
        {
            try
            {
                if (model?.SelectedUserIds == null || model.SelectedUserIds.Length == 0)
                {
                    return Json(new { success = false, message = "No users selected for newsletter sending." });
                }

                // Additional validation for test mode parameters
                if (model.IsTestMode && string.IsNullOrWhiteSpace(model.TestRecipientEmail))
                {
                    return Json(new { success = false, message = "Test recipient email is required when test mode is enabled." });
                }

                if (model.IsTestMode && !string.IsNullOrWhiteSpace(model.TestRecipientEmail) && !IsValidEmail(model.TestRecipientEmail))
                {
                    return Json(new { success = false, message = "Please enter a valid test recipient email address." });
                }

                log.Info($"Starting newsletter send to {model.SelectedUserIds.Length} users by {User.Identity.Name}" +
                    (model.IsTestMode ? " in TEST MODE" : ""));

                // Validate users first
                var validationResult = _newsletterManagementService.ValidateUsersForSending(model.SelectedUserIds);
                if (!validationResult.IsValid)
                {
                    return Json(new
                    {
                        success = false,
                        message = "Some users cannot receive newsletters.",
                        errors = validationResult.Errors
                    });
                }

                // Start the sending process with test mode parameters if enabled
                var sendResult = await _newsletterManagementService.SendNewslettersToUsersAsync(
                    model.SelectedUserIds, 
                    model.ForceResend,
                    model.IsTestMode,
                    model.TestRecipientEmail,
                    model.TestLastNewsletterDate);

                return Json(new
                {
                    success = true,
                    message = model.IsTestMode 
                        ? "Test newsletter sending completed. No history records were created." 
                        : "Newsletter sending completed.",
                    isTestMode = model.IsTestMode,
                    testRecipientEmail = model.IsTestMode ? model.TestRecipientEmail : null,
                    testLastNewsletterDate = model.IsTestMode && model.TestLastNewsletterDate.HasValue 
                        ? model.TestLastNewsletterDate.Value.ToString("yyyy-MM-dd") 
                        : null,
                    totalUsers = sendResult.TotalUsers,
                    sent = sendResult.Sent,
                    failed = sendResult.Failed,
                    skipped = sendResult.Skipped,
                    results = sendResult.Results.Select(r => new
                    {
                        userId = r.UserId,
                        userName = r.UserName,
                        email = r.Email,
                        status = r.Status.ToString(),
                        errorMessage = r.ErrorMessage,
                        processedAt = r.ProcessedAt.ToString("yyyy-MM-dd HH:mm:ss"),
                        testRecipientEmail = r.TestRecipientEmail,
                        isTestMode = r.IsTestMode
                    })
                });
            }
            catch (Exception ex)
            {
                log.Error($"Error sending newsletters to selected users: {ex.Message}", ex);
                return Json(new
                {
                    success = false,
                    message = "An error occurred while sending newsletters: " + ex.Message
                });
            }
        }

        /// <summary>
        /// Validates an email address format
        /// </summary>
        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Gets the current status of a newsletter sending operation
        /// </summary>
        [HttpGet]
        public JsonResult GetSendStatus(string operationId)
        {
            try
            {
                var status = _newsletterManagementService.GetSendStatus(operationId);
                if (status == null)
                {
                    return Json(new { success = false, message = "Operation not found." }, JsonRequestBehavior.AllowGet);
                }

                return Json(new
                {
                    success = true,
                    totalUsers = status.TotalUsers,
                    sent = status.Sent,
                    failed = status.Failed,
                    skipped = status.Skipped,
                    inProgress = status.InProgress,
                    isCompleted = status.IsCompleted,
                    progressPercentage = status.ProgressPercentage,
                    duration = status.Duration?.ToString(@"mm\:ss")
                }, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                log.Error($"Error getting send status: {ex.Message}", ex);
                return Json(new { success = false, message = "Error retrieving status." }, JsonRequestBehavior.AllowGet);
            }
        }

        /// <summary>
        /// Shows newsletter history
        /// </summary>
        public ActionResult History(string userId = null)
        {
            try
            {
                var history = _newsletterManagementService.GetNewsletterHistory(userId);
                ViewBag.Title = string.IsNullOrEmpty(userId) ? "Newsletter History" : "User Newsletter History";
                ViewData["UserId"] = userId;
                
                return View(history);
            }
            catch (Exception ex)
            {
                log.Error($"Error loading newsletter history: {ex.Message}", ex);
                ViewBag.ErrorMessage = "An error occurred while loading newsletter history.";
                return View(Enumerable.Empty<NewsletterHistoryViewModel>().AsQueryable());
            }
        }

        /// <summary>
        /// Partial view for newsletter history grid
        /// </summary>
        public ActionResult HistoryGridView(string userId = null)
        {
            try
            {
                var history = _newsletterManagementService.GetNewsletterHistory(userId);
                return PartialView("_NewsletterHistoryGridView", history);
            }
            catch (Exception ex)
            {
                log.Error($"Error loading newsletter history grid: {ex.Message}", ex);
                return PartialView("_NewsletterHistoryGridView", Enumerable.Empty<NewsletterHistoryViewModel>().AsQueryable());
            }
        }

        /// <summary>
        /// Gets user statistics for dashboard
        /// </summary>
        [HttpGet]
        public JsonResult GetUserStatistics()
        {
            try
            {
                var searchModel = new NewsletterUserSearchViewModel();
                var allUsers = _newsletterManagementService.SearchUsers(searchModel).ToList(); // Materialize the query once

                var stats = new
                {
                    totalUsers = allUsers.Count,
                    activeUsers = allUsers.Count(u => u.IsActive),
                    inactiveUsers = allUsers.Count(u => !u.IsActive),
                    usersWithRecentNewsletter = allUsers.Count(u => u.LastNewsletterSent.HasValue &&
                        u.LastNewsletterSent.Value > DateTime.Now.AddDays(-30))
                };

                return Json(stats, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                log.Error($"Error getting user statistics: {ex.Message}", ex);
                return Json(new { error = "Error retrieving statistics." }, JsonRequestBehavior.AllowGet);
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _unitOfWork?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
