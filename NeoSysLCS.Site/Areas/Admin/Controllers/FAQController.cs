using System.Web.Mvc;
using DevExpress.Web;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using DevExpress.Web.Mvc;
using NeoSysLCS.Repositories.ViewModels;
using NeoSysLCS.Site.Controllers;
using NeoSysLCS.Site.Helpers;
using System;
using NeoSysLCS.Site.AzureStorage;
using System.Configuration;

namespace NeoSysLCS.Site.Areas.Admin.Controllers
{
    public class FAQController : BaseController
    {
        private readonly IUnitOfWork _unitOfWork;
        NeoSysLCS_Dev context = new NeoSysLCS_Dev();

        public FAQController()
        {
            _unitOfWork = new UnitOfWork();
        }

        public FAQController(IUnitOfWork unitOfWorkCandidate)
        {
            _unitOfWork = unitOfWorkCandidate;
        }

        public ActionResult Index()
        {
            var viewModels = _unitOfWork.FAQRepository.GetAllFAQViewModels();
            return View("Index", viewModels);
        }
        public ActionResult FAQGridView()
        {
            var viewModels = _unitOfWork.FAQRepository.GetAllFAQViewModels();
            return PartialView("_FAQGridView", viewModels);
        }

        [ValidateInput(false)]
        public ActionResult FAQGridViewBatchEditUpdate(MVCxGridViewBatchUpdateValues<FAQViewModel, int> updateValues)
        {
            foreach (var faq in updateValues.Insert)
            {
                GridViewUpdateHelper<FAQ, FAQViewModel>.DoUpdateInsert(() =>
                {
                    if (updateValues.IsValid(faq))
                    {
                        _unitOfWork.FAQRepository.Insert(faq);
                        _unitOfWork.Save();
                    }
                }, faq, updateValues);
            }
            foreach (var faq in updateValues.Update)
            {
                GridViewUpdateHelper<FAQ, FAQViewModel>.DoUpdateInsert(() =>
                {
                    if (updateValues.IsValid(faq))
                    {
                        _unitOfWork.FAQRepository.Update(faq);
                        _unitOfWork.Save();
                    }
                }, faq, updateValues);
            }
            foreach (var faq in updateValues.DeleteKeys)
            {
                GridViewUpdateHelper<FAQ, FAQViewModel>.DoDelete(() =>
                {
                    _unitOfWork.FAQRepository.Delete(faq);
                    _unitOfWork.Save();
                }, _unitOfWork.FAQRepository, faq, updateValues);

            }

            var viewModels = _unitOfWork.FAQRepository.GetAllFAQViewModels();
            return PartialView("_FAQGridView", viewModels);
        }


        public ActionResult FAQUebersetzungenGridView(int faqID)
        {
            var uebersetzungen = _unitOfWork.FAQRepository.GetFAQUebersetzungenByFAQ(faqID);

            ViewData["faqID"] = faqID;
            return PartialView("_FAQUebersetzungenGridView", uebersetzungen);
        }

        [ValidateInput(false)]
        public ActionResult FAQUebersetzungenGridViewBatchEditUpdate(MVCxGridViewBatchUpdateValues<FAQViewModel, int> updateValues, int faqID)
        {
            foreach (var uebersetzung in updateValues.Update)
            {
                GridViewUpdateHelper<FAQ, FAQViewModel>.DoUpdateInsert(() =>
                {
                    if (updateValues.IsValid(uebersetzung))
                    {
                        uebersetzung.FaqID = faqID;
                        _unitOfWork.FAQRepository.UpdateFAQUebersetzung(uebersetzung);
                        _unitOfWork.Save();
                    }
                }, uebersetzung, updateValues);
            }

            ViewData["faqID"] = faqID;
            var uebersetzungen = _unitOfWork.FAQRepository.GetFAQUebersetzungenByFAQ(faqID);

            return PartialView("_FAQUebersetzungenGridView", uebersetzungen);
        }

        public ActionResult KategorieRowSelectionPartial(int faqID)
        {
            ViewData["faqID"] = faqID;
            return PartialView("_RowSelectionPartialKategorie");
        }

        public ActionResult ShowKategoriePopup(int faqID)
        {
            ViewData["faqID"] = faqID;
            return PartialView("FAQKategoriePopupContent");
        }

        public ActionResult SaveFAQKategorie()
        {
            string selectedIDs = Request.Params["selectedIDsHF"];
            int faqID = Convert.ToInt32(Request.Params["id"]);
            ViewData["_selectedIDs"] = selectedIDs;
            ViewData["faqID"] = faqID;
            _unitOfWork.FAQRepository.SaveKategorieFAQ(selectedIDs, faqID);
            _unitOfWork.Save();


            return Content(Resources.Properties.Resources.Notification_Zuordnungen_Gespeichert);
        }

        public ActionResult ShowFaqUploadPopup(int faqId, string url, string field, int languageId = -1)
        {
            ViewData["faqId"] = faqId;
            ViewData["url"] = url;
            ViewData["field"] = field;
            ViewData["languageId"] = languageId;
            return PartialView("FaqUploadPopupContent");
        }

        public ActionResult FileUpload(int faqId, string field, int languageId)
        {
            UploadedFile[] files = UploadControlExtension.GetUploadedFiles("uploadControl");

            // Your custom logic to process uploaded files.
            if (files != null && files.Length > 0)
            {
                UploadedFile file = files[0];

                string filePath = ConfigurationManager.AppSettings["FilePath"];
                string resultFileUrl;
                if (filePath == "Azure")
                {
                    IBlobStorageClientFactory factory = new BlobStorageClientFactory();
                    IBlobStorageClient client = factory.CreateCommentFilesBlobStorageClient(faqId);
                    var uploadResult = client.Upload(file.FileName, file.ContentType, file.FileContent);
                    resultFileUrl = uploadResult.Url;
                }
                else
                {
                    string uploadDirectory = "~/Upload/FAQ/"; // Directly upload the file to the specific directory
                    resultFileUrl = uploadDirectory + file.FileName;
                    string resultFilePath = Request.MapPath(resultFileUrl);

                    file.SaveAs(resultFilePath);
                    resultFileUrl = ConfigurationManager.AppSettings["FilePath"] + "FAQ/"+ file.FileName;
                }

                _unitOfWork.FAQRepository.UpdateUploadedFileUrl(faqId, field, resultFileUrl, languageId);
            }

            return null;
        }

        public ActionResult FileDelete(int faqId, string field, int languageId)
        {
            ViewData["faqId"] = faqId;
            ViewData["field"] = field;
            ViewData["languageId"] = languageId;

            _unitOfWork.FAQRepository.UpdateUploadedFileUrl(faqId, field, null, languageId);

            return PartialView("FaqUploadPopupContent");
        }
    }
}