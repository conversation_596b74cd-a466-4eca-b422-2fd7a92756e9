@using System.Web.UI.WebControls
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Site.Helpers


@{
    var grid = Html.DevExpress().GridView(settings =>
    {
        settings.Name = "RechtsbereicheGridView";
        GridViewHelper.ApplyDefaultSettings(settings);
        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;


        settings.KeyFieldName = "RechtsbereichID";
        settings.Width = Unit.Empty;

        settings.CallbackRouteValues = new { Controller = "Rechtsbereiche", Action = "RechtsbereicheGridView" };
        settings.SettingsEditing.BatchUpdateRouteValues = new { Controller = "Rechtsbereiche", Action = "RechtsbereicheGridViewBatchEditUpdate" };


        if (User.IsInRole(Role.ObjectGuru))
        {
            settings.SettingsEditing.Mode = GridViewEditingMode.Batch;
            settings.CommandColumn.Visible = true;
            settings.CommandColumn.ShowNewButtonInHeader = true;
            settings.CommandColumn.ShowDeleteButton = true;
            settings.CommandColumn.ShowEditButton = false;
        }

        settings.Settings.ShowGroupPanel = false;

        //Columns
        settings.Columns.Add("Name");

        settings.Columns.Add(column =>
        {
            column.FieldName = "RechtsbereichID";
            column.Caption = "Id";
        });
        //GridViewHelper.AddNewestFirstSortorderColumn(settings, "Name"); Repository Sorting
        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);

        //Detail
        settings.SettingsDetail.ShowDetailRow = true;
        settings.SetDetailRowTemplateContent(c =>
        {
            Html.RenderAction("RechtsbereichUebersetzungenGridView", new { id = DataBinder.Eval(c.DataItem, "RechtsbereichID") });
        });

        settings.ClientSideEvents.BatchEditStartEditing = @"function(s,e){
                currentGrid = s;
	        }";

        settings.ClientSideEvents.ClipboardCellPasting = @"function(s,e){
                e.cancel = currentGrid != s;
	        }";

        if (ViewData["EditError"] != null)
        {
            settings.Settings.ShowFooter = true;
            settings.SetFooterRowTemplateContent(c => Html.ViewContext.Writer.Write(ViewData["EditError"]));
        }
    });


}
@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = "RechtsbereichID";
}).GetHtml()