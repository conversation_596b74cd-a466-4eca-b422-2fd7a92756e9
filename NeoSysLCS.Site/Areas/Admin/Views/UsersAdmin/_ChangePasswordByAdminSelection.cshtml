@using System.Web.Mvc.Html
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Resources.Properties
@model NeoSysLCS.Repositories.ViewModels.ChangePasswordViewModel

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="initial-scale=1.0, maximum-scale=1.0, user-scalable=no, width=device-width">
    <meta content="IE=Edge,chrome=1" http-equiv="X-UA-Compatible">
    @Styles.Render("~/Content/css")
    @Scripts.Render("~/bundles/modernizr")

    @Scripts.Render("~/bundles/jquery")
    @Scripts.Render("~/bundles/bootstrap")

    <meta name="description" content="NeoSysLCS" />

</head>

<body>
    <h4 class="page-header">@Resources.ChangePasswordByAdmin_Header @ViewBag.Title</h4>

        @using (Html.BeginForm("SelectPasswordChangeByAdminManual", "UsersAdmin", new { @class = "form-horizontal", role = "form", FormMethod.Get }))
        {
            @Html.HiddenFor(m => m.UserId)

            @*@Html.AntiForgeryToken()*@


            @Html.ValidationSummary("", new { @class = "text-danger" })
            <fieldset class="divPwdChange">
                <div class="form-group">
                    <div class="col-md-offset-2 col-md-10">
                        <input type="submit" value="@Resources.ChangePasswordManually" class="btn btn-default" />
                    </div>
                </div>

                @*<div class="form-group">
                        <div class="col-md-offset-2 col-md-10">
                            <input type="submit" value="Passwort manuell zurücksetzen" class="btn btn-default" />
                        </div>
                    </div>*@
            </fieldset>

        }

    @using (Html.BeginForm("SelectPasswordChangeByAdminAutomatic", "UsersAdmin", new { @class = "form-horizontal", role = "form", FormMethod.Get }))
    {
        @Html.HiddenFor(m => m.UserId)

        @*@Html.AntiForgeryToken()*@


        @Html.ValidationSummary("", new { @class = "text-danger" })
        <fieldset class="divPwdChange">
            <div class="form-group">
                <div class="col-md-offset-2 col-md-10">
                    <input type="submit" value="@Resources.ChangePasswordAutomatic" class="btn btn-default" />
                </div>
            </div>

            @*<div class="form-group">
                    <div class="col-md-offset-2 col-md-10">
                        <input type="submit" value="Passwort manuell zurücksetzen" class="btn btn-default" />
                    </div>
                </div>*@
        </fieldset>

    }
</body>
</html>
