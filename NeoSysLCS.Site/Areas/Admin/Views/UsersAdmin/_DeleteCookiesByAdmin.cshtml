@using System.Web.Mvc.Html
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Resources.Properties
@model NeoSysLCS.Repositories.ViewModels.DeleteCookiesViewModel

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="initial-scale=1.0, maximum-scale=1.0, user-scalable=no, width=device-width">
    <meta content="IE=Edge,chrome=1" http-equiv="X-UA-Compatible">
    @Styles.Render("~/Content/css")
    @Scripts.Render("~/bundles/modernizr")

    @Scripts.Render("~/bundles/jquery")
    @Scripts.Render("~/bundles/bootstrap")

    <meta name="description" content="NeoSysLCS" />

</head>

<body>
    <h4 class="page-header">@Resources.DeleteCookiesByAdmin_Header @ViewBag.Title</h4>

    @using (Html.BeginForm("DeleteCookiesByAdmin", "UsersAdmin", new { @class = "form-horizontal", role = "form", FormMethod.Post }))
    {
        @Html.HiddenFor(m => m.UserId)

        @*@Html.AntiForgeryToken()*@


        @Html.ValidationSummary("", new { @class = "text-danger" })
        <fieldset class="divPwdChange">
            <div class="form-group">
                @Html.Label(Resources.ChangePasswordByAdmin_AdminPW, new { @class = "col-md-2 control-label" })
                <div class="col-md-10">
                    @Html.PasswordFor(m => m.OldPassword, new { @class = "form-control", @style = "width:250px" })
                </div>
            </div>
        </fieldset>

        <div class="form-group">
            <div class="col-md-offset-2 col-md-10">
                <input type="submit" value="@Resources.DeleteCookiesView_Button_DeleteCookies" class="btn btn-default" />
            </div>
        </div>
    }



</body>
</html>
