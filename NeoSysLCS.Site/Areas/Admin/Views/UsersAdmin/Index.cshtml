@using System.Web.UI.WebControls
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Utilities.Export
@model IEnumerable<NeoSysLCS.Repositories.ViewModels.UserViewModel>

@{
    if (User.IsInRole(Role.SuperUser))
    {
        @*ViewBag.Title = Resources.Titel_Benutzerverwaltung;
            <div class="page-header">
                <div class="btn-group pull-left header-btn-group" style="border:none; background-color: transparent; outline:none" role="group">
                    <button type="button" class="btn btn-default" style="border:none; background-color: transparent; outline:none" onclick="javascript: ShowDetailPopup('/Portal/FAQPortal/ShowFAQPopup?view=Benutzerverwaltung', 'pcModalMode_FAQ_User');"><img src="~/Content/images/Question_Mark.png" style="width:20px;height:20px" /></button>
                </div>
                <h1>@Resources.Titel_Benutzerverwaltung</h1>
            </div>*@
        ViewBag.Title = Resources.Titel_Benutzerverwaltung;
        <div class="page-header" style="display:inline-block">
            <h1 style="display:inline-block">@Resources.Titel_Benutzerverwaltung</h1>
            <button type="button" class="btn btn-default" style="border:none; background-color: transparent; outline:none; padding-left: 5px; padding-right: 0px; padding-bottom:20px; padding-top: 5px"
                    onclick="javascript: ShowDetailPopup('/Portal/FAQPortal/ShowFAQPopup?view=Benutzerverwaltung', 'pcModalMode_FAQ_User');">
                <img src="~/Content/images/Question_Mark.png" style="width:20px;height:20px" />
            </button>
        </div>
        var displayDeactivated = (bool)ViewData["displayDeactivated"];
        if (displayDeactivated)
        {
            <div>
                @Html.ActionLink(Resources.View_Benutzerverwaltung_Inaktive_ausblenden, "Index", "UsersAdmin", new { area = "Admin", displayDeactivated = false }, new { @class = "" })
            </div>
        }
        else
        {
            <div>
                @Html.ActionLink(Resources.View_Benutzerverwaltung_Inaktive_einblenden, "Index", "UsersAdmin", new { area = "Admin", displayDeactivated = true }, new { @class = "" })
            </div>
        }
    }
    else
    {
        ViewBag.Title = Resources.Titel_Benutzerverwaltung;
        ViewContext.Writer.Write("<h1 class='page-header'>" + ViewBag.Title + "</h1>");
        var displayDeactivated = (bool)ViewData["displayDeactivated"];
        if (displayDeactivated)
        {
            @Html.ActionLink(Resources.View_Benutzerverwaltung_Inaktive_ausblenden, "Index", "UsersAdmin", new { area = "Admin", displayDeactivated = false }, new { @class = "" })
        }
        else
        {
            @Html.ActionLink(Resources.View_Benutzerverwaltung_Inaktive_einblenden, "Index", "UsersAdmin", new { area = "Admin", displayDeactivated = true }, new { @class = "" })
        }
    }    //var displayDeactivated = (bool)ViewData["displayDeactivated "];

}

@if (User.IsInRole(Role.SuperUser))
{
    @section breadcrumb{
        <a href="#" onclick="history.go(-1); return false;" title="@Resources.Navigation_Zurueck"><i class="fa fa-arrow-left fa-lg"></i></a> |
        <span class="breadcrumb-noesys-navigation">
            <a href="/">@Resources.View_Breadcrumb_Home</a>
            <span> > </span>
            @Html.ActionLink("Administration", "Index", "Administration", new { area = "Portal" }, new { @class = "" })
            <span> > </span>
            <span>@ViewBag.Title</span>
        </span>

    }
}

@using (Html.BeginForm("ExportUsers", "UsersAdmin", new { area = "Admin", displayDeactivated = false }, FormMethod.Post, new { target = "_blank", id = "UsersExportWrapperForm" }))
{
    <div class="divToolbar">
        <div class="divButtonsLeft">
            @foreach (KeyValuePair<ExportType, ExportAction> entry in ExportUtilities.GetExportTypes(new List<ExportType>() { ExportType.Pdf, ExportType.Xlsx }))
            {
                if (entry.Key == ExportType.Xlsx)
                {
                    <button class="btn btn-default" type="submit" value="@entry.Key" name="exportFormat">@Resources.XLSX_Export_Format</button>
                }
                else if (entry.Key == ExportType.Pdf)
                { 
                    <button class="btn btn-default" type="submit" value="@entry.Key" name="exportFormat">@Resources.PDF_Export_Format</button>
                }
            }
        </div>
    </div>
}

<script type="text/javascript">
    //<![CDATA[

    var rowKeyValueToCopy, userid, selectedKunde, prevSelection;

    function OnBeginCallback(s, e) {
        if (e.command === "CUSTOMCALLBACK") {
            e.customArgs["key"] = rowKeyValueToCopy;
            e.customArgs["userid"] = userid;
        }
    }

    function OnEndCallback(s, e) {
        var c = document.querySelectorAll('[class="dxbButton_ModernoNeosys dxbButtonSys"]');
        var image = document.querySelectorAll('[class="dx-vam"]');
        var button1 = c[0].id;
        var button2 = c[1].id;
        for (i = 0; i < c.length; i++)
        {
            c[i].style.width = "26px";
            c[i].style.marginRight = "10px";
        }
        for (i = 0; i < image.length; i++) {
            if (image[i].id.startsWith(button1)) {
                image[i].style.width = "26px";
                image[i].style.marginRight = "10px";
            }
            if (image[i].id.startsWith(button2)) {
                image[i].style.width = "26px";
                image[i].style.marginRight = "10px";
            }
        }

        var memo = document.querySelectorAll('[class="dxeMemoEditArea_ModernoNeosys dxeMemoEditAreaSys"]')
        //memo[0].style.backgroundColor = "#ff471a";
    }

    function OnCustomButtonClick(s, e) {
        rowKeyValueToCopy = s.GetRowKey(e.visibleIndex);
        if (e.buttonID == "PWReset") {
            PwdReset.SetContentUrl("/Admin/UsersAdmin/ChangePasswordByAdmin?userid=" + rowKeyValueToCopy);

            if (window.height < 520) {
                PwdReset.SetHeight(window.height - 50);
            } else {
                PwdReset.SetHeight(520);
            }

            if (window.width < 650) {
                PwdReset.SetWidth(window.width - 50);
            } else {
                PwdReset.SetWidth(650);
            }
            PwdReset.SetHeaderText("@Resources.Passwort_zuruecksetzen");
            PwdReset.Show();
        }
        else if (e.buttonID == "DeleteCookies") {
            PwdReset.SetContentUrl("/Admin/UsersAdmin/DeleteCookiesByAdmin?userid=" + rowKeyValueToCopy);

            if (window.height < 520) {
                PwdReset.SetHeight(window.height - 50);
            } else {
                PwdReset.SetHeight(520);
            }

            if (window.width < 650) {
                PwdReset.SetWidth(window.width - 50);
            } else {
                PwdReset.SetWidth(650);
            }
            PwdReset.SetHeaderText("@Resources.Cookies_zuruecksetzen");
            PwdReset.Show();
        }
    }

    function onInitKundenSelection(s, e) {
        setVisibleRoles(s.GetValue());
    }

    function onChangedKundeID(s, e) {
        setVisibleRoles(s.GetValue());
        selectedKunde = s.GetValue();
        cp.PerformCallback();
    }

    function setStandortWrite(s, e) {
        selectedKunde = s.GetValue();
        cp.PerformCallback();
    }

    function FindChangedIndex(selection, prevSelection) {
        var result = new Object();
        if (selection.length <= prevSelection.length) {
            //Last item was unchecked
            for (var i = 0; i < prevSelection.length; i++) {
                if (selection[i] !== prevSelection[i]) {
                    result.index = prevSelection[i];
                    result.checked = false;
                    return result;
                }
            }
        }
        for (var i = 0; i < selection.length; i++) {
            if (selection[i] !== prevSelection[i]) {
                result.index = selection[i];
                result.checked = true;
                return result;
            }
        }
        return null;
    }

    function getRolesTable() {
        return $("#GUIRoles").parents().eq(8);
    }

    function getStandortTable() {
        return $("#UserStandortGridView").parents().eq(5);
    }

    function getZeitspanneDropdown() {
        return $("#UserAdminView_DXPEForm_DXEFL_DXEditor17_ET").parents().eq(5);
    }

    function Roles_SelectedIndexChanged(s, e) {

        var selection = s.GetSelectedIndices();
        var changed = FindChangedIndex(selection, prevSelection);
        if (changed == null)
            return;

        if (changed.checked) {
            if (s.GetItem(changed.index).text == "User") {
                getStandortTable().removeClass("hidden");
                s.UnselectIndices([1]);
                //s.UnselectIndices([1]);
                s.UnselectIndices([5]);
            }
            //power user and user cannot be selected togheter, release 2016
            if (s.GetItem(changed.index).text == "Systemadministrator") {
                s.UnselectIndices([0]);
                s.UnselectIndices([1]);
                s.UnselectIndices([6]);
                getStandortTable().addClass("hidden");

            }
            if (s.GetItem(changed.index).text == "Auditor") {
                s.UnselectIndices([1])
                s.UnselectIndices([5]);
            }
            if (s.GetItem(changed.index).text == "Editor") {
                s.UnselectIndices([0]);
                s.UnselectIndices([5]);
                s.UnselectIndices([6]);
                getStandortTable().addClass("hidden");
            }
            if (s.GetItem(changed.index).text === "Newsletter") {
                getZeitspanneDropdown().removeClass("hidden");
            }
            
        } else {
            if (s.GetItem(changed.index).text == "User") {
                getStandortTable().addClass("hidden");
            }
            if (s.GetItem(changed.index).text === "Newsletter") {
                getZeitspanneDropdown().addClass("hidden");
            }
        }
        //refresh selection because of possible de selection
        selection = s.GetSelectedIndices();
        prevSelection = selection;
    }

    function Roles_SelectedIndexChanged_SuperUser(s, e) {
        var selection = s.GetSelectedIndices();
        var changed = FindChangedIndex(selection, prevSelection);
        if (changed == null)
            return;

        if (changed.checked) {
            if (s.GetItem(changed.index).text == "User") {
                s.UnselectIndices([1]);
                s.UnselectIndices([2]);
                getStandortTable().removeClass("hidden");
            }
            //power user and user cannot be selected togheter, release 2016
            if (s.GetItem(changed.index).text == "Systemadministrator") {
                s.UnselectIndices([0]);
                s.UnselectIndices([1]);
                s.UnselectIndices([3]);
                getStandortTable().addClass("hidden");

            }
            if (s.GetItem(changed.index).text == "Auditor") {
                s.UnselectIndices([1]);
                s.UnselectIndices([2]);
            }
            if (s.GetItem(changed.index).text == "Editor") {
                s.UnselectIndices([0]);
                s.UnselectIndices([2]);
                s.UnselectIndices([3]);
                getStandortTable().addClass("hidden");
            }
            if (s.GetItem(changed.index).text == "Newsletter") {
                getZeitspanneDropdown().removeClass("hidden");
            }

        } else {
            if (s.GetItem(changed.index).text == "User")
                getStandortTable().addClass("hidden");

            if (s.GetItem(changed.index).text == "Newsletter") {
                getZeitspanneDropdown().addClass("hidden");
            }
        }
        //refresh selection because of possible de selection
        selection = s.GetSelectedIndices();
        prevSelection = selection;
    }

    function preselectCheckbox(s, e) {
        var items = s.GetSelectedItems();
        if (items.length <= 0) {
            getStandortTable().addClass("hidden");
            getZeitspanneDropdown().addClass("hidden");
        }

        var newsLetterSelected = items.find(i => i.text === "Newsletter");
        console.log("Newsletter selected", newsLetterSelected);
        if (newsLetterSelected) {
            getZeitspanneDropdown().removeClass("hidden");
        } else {
            getZeitspanneDropdown().addClass("hidden");
        }


        for (var i = 0; i <= items.length; i++) {
            if (items[i] && items[i].text != "User") {
                getStandortTable().addClass("hidden");
            } else if (items[i]) {
                getStandortTable().removeClass("hidden");
                break;
            }
        }

        prevSelection = new Array();
        prevSelection = s.GetSelectedIndices();
    }

    function preselectCheckbox_SuperUser(s, e) {
        var items = s.GetSelectedItems();
        if (items.length <= 0) {
            getStandortTable().addClass("hidden");
            getZeitspanneDropdown().addClass("hidden");
        }

        var newsLetterSelected = items.find(i => i.text === "Newsletter");
        console.log("Newsletter selected", newsLetterSelected);
        if (newsLetterSelected) {
            getZeitspanneDropdown().removeClass("hidden");
        } else {
            getZeitspanneDropdown().addClass("hidden");
        }

        for (var i = 0; i <= items.length; i++) {
            if (items[i] && items[i].text != "User") {
                getStandortTable().addClass("hidden");
            } else if (items[i]) {
                getStandortTable().removeClass("hidden");
                break;
            }
        }

        prevSelection = new Array();
        prevSelection = s.GetSelectedIndices();
    }

    function setVisibleRoles(kid) {
        if (kid != 1 && kid != null) {
            @* todo: if the displayname of the roles changes, or the roles are translatet, this need to be changed in oder to show or hide the neosys roles *@
            //remove objektguru
            $("#GUIRoles_RB2").addClass("hidden");
            //remove projektleiter
            $("#GUIRoles_RB3").addClass("hidden");
            //remove sysadmin
            $("#GUIRoles_RB4").addClass("hidden");
        } else {
            //show objektguru
            $("#GUIRoles_RB2").removeClass("hidden");
            //show projektleiter
            $("#GUIRoles_RB3").removeClass("hidden");
            //show sysadmin
            $("#GUIRoles_RB4").removeClass("hidden");
        }
    }
    //Fixed NEOS-322 Clear Timer on StartEditing
    var timerHandle = -1;
    function OnBatchStartEditing(s, e) {
        clearTimeout(timerHandle);
    }

    function OnBatchEditEndEditing(s, e) {
        setTimeout(function () {
            if (s.batchEditApi.HasChanges()) {
                s.UpdateEdit();
            }
        }, 2000);
    }

    function ShowDetailPopup(url, win) {
        modal = eval(win);

        if (window.height < 600) {
            modal.SetHeight(window.height - 50);
        } else {
            modal.SetHeight(600);
        }

        if (window.width < 800) {
            modal.SetWidth(window.width - 50);
        } else {
            modal.SetWidth(800);
        }

        modal.SetContentUrl(url);
        modal.Show();
    }

    // ]]>
</script>


@Html.DevExpress().PopupControl(settings =>
{

    settings.Name = "PwdReset";
    settings.ScrollBars = ScrollBars.Auto;
    settings.ShowPageScrollbarWhenModal = true;
    settings.Width = 400;
    settings.Height = 510;
    settings.ResizingMode = ResizingMode.Live;
    settings.AutoUpdatePosition = true;
    settings.AllowDragging = true;
    settings.CloseAction = CloseAction.CloseButton;
    settings.PopupAnimationType = AnimationType.None;
    settings.Modal = true;
    settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
    settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
}).GetHtml()

@Html.DevExpress().PopupControl(settings =>
{

    settings.Name = "pcModalMode_FAQ_User";
    //settings.ScrollBars = ScrollBars.Auto;
    settings.ShowPageScrollbarWhenModal = true;
    settings.ResizingMode = ResizingMode.Live;
    settings.ShowRefreshButton = false;
    settings.AutoUpdatePosition = true;
    settings.AllowDragging = true;
    settings.CloseAction = CloseAction.CloseButton;
    settings.PopupAnimationType = AnimationType.None;
    settings.HeaderText = "FAQ";
    settings.Modal = true;
    settings.AllowResize = true;
    settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
    settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
    settings.ClientSideEvents.Init = "function(s, e) { s.SetWidth(400); }";
    settings.ClientSideEvents.Shown = "function(s, e) { s.SetWidth(400); }";
}).GetHtml()

@Html.Partial("_UserAdminViewPartial", Model)

