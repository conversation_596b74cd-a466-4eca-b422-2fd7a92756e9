@using System.Globalization
@using System.Linq
@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers
@model IQueryable<NeoSysLCS.Repositories.ViewModels.ObligationViewModel>

@{
    var grid = Html.DevExpress().GridView(settings =>
    {
        settings.Name = "ObligationGridView";
        GridViewHelper.ApplyDefaultSettings(settings);
        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;
        IUnitOfWork unitOfWork = new UnitOfWork();

        NeoSysLCS_Dev context = new NeoSysLCS_Dev();
        var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
        Sprache sprache = context.Sprachen.FirstOrDefault(s => s.Lokalisierung == language);
        var _currentLang = sprache.SpracheID;

        settings.KeyFieldName = "ObligationID";

        settings.CallbackRouteValues = new { Controller = "ObjektObligation", Action = "ObligationGridView", objektID = ViewData["ObjektID"] };

        settings.SettingsEditing.BatchUpdateRouteValues = new { Controller = "ObjektObligation", Action = "ObligationGridViewBatchEditUpdate", objektID = ViewData["ObjektID"] };

        if (User.IsInRole(Role.ProjectManager))
        {
            settings.SettingsEditing.Mode = GridViewEditingMode.Batch;
            settings.CommandColumn.Visible = true;
            settings.CommandColumn.ShowNewButtonInHeader = true;
            settings.CommandColumn.ShowDeleteButton = true;
            settings.CommandColumn.ShowEditButton = false;
        }

        settings.Columns.Add("Name");

        settings.Columns.Add(column =>
        {
            column.FieldName = "LastChange";
            column.Caption = Resources.Entitaet_Forderungsversion_Inkrafttretung;
            column.ColumnType = MVCxGridViewColumnType.DateEdit;
            column.HeaderStyle.Wrap = DefaultBoolean.True;
            column.EditFormSettings.Visible = DefaultBoolean.False;
        });

        settings.Columns.Add(column =>
        {
            column.Caption = Resources.Allgemein_Zuordnungen;
            column.Name = "Assignements";
            column.ReadOnly = true;
            column.EditFormSettings.Visible = DefaultBoolean.False;

            column.SetDataItemTemplateContent(container =>
            {

                Html.DevExpress().HyperLink(hyperlink =>
                {
                    var keyValue = container.KeyValue;
                    hyperlink.Name = "Forderungsversions" + keyValue;
                    int integerKeyValue = 0;
                    if (keyValue != null)
                    {
                        integerKeyValue = Convert.ToInt32(keyValue);
                    }
                    hyperlink.Properties.Text = Resources.Entitaet_Forderungsversion_Plural + " (" + Model.Where(x => x.ObligationID == integerKeyValue).Select(k => k.ForderungsversionsCount).ToList().FirstOrDefault() + ")"; ;
                    hyperlink.NavigateUrl = Url.Action("Index", "ObligationForderungsversions", new { obligationId = integerKeyValue, view = "ObjektObligation" });

                }).Render();

            });
        });

        //Detail Template
        settings.SettingsDetail.ShowDetailRow = true;
        settings.SetDetailRowTemplateContent(c =>
        {
            Html.RenderAction("ObligationUebersetzungenGridView", new { obligationID = DataBinder.Eval(c.DataItem, "ObligationID") });
        });


        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);

        var errorList = ViewData["ErrorList"];

        if (errorList != null)
        {
            settings.Settings.ShowFooter = true;
            settings.SetFooterRowTemplateContent(c => Html.ViewContext.Writer.Write("StandortObjekt ist noch in Verwendung und kann nicht gelöscht werden"));
        }


        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);

        settings.ClientSideEvents.BatchEditStartEditing = @"function(s,e){
                currentGrid = s;
	        }";

        settings.ClientSideEvents.ClipboardCellPasting = @"function(s,e){
                e.cancel = currentGrid != s;
	        }";
    });

    if (ViewData["EditError"] != null)
    {
        grid.SetEditErrorText((string)ViewData["EditError"]);
    }
}
@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = "ObligationID";
}).GetHtml()

