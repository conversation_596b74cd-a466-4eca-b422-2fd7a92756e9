@using System.Web.UI.WebControls
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Site.Helpers
@using NeoSysLCS.Repositories.ViewModels
@model IQueryable<NewsletterUserSelectionViewModel>

@{
    var grid = Html.DevExpress().GridView(settings =>
    {
        settings.Name = "UserSelectionGridView";
        GridViewHelper.ApplyDefaultSettings(settings);
        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;

        settings.KeyFieldName = "Id";
        settings.Width = Unit.Percentage(100);

        settings.CallbackRouteValues = new { Area = "Admin", Controller = "Newsletter", Action = "UserSelectionGridViewCallback" };

        // Grid settings
        settings.SettingsPager.Visible = true;
        settings.SettingsPager.PageSize = 20;
        settings.SettingsPager.AlwaysShowPager = true;
        settings.Settings.ShowGroupPanel = false;
        settings.Settings.ShowFilterRow = true;
        settings.SettingsBehavior.AllowSelectByRowClick = false;
        settings.SettingsBehavior.EnableRowHotTrack = true;
        settings.CommandColumn.Visible = false;

        // Add client-side events for pagination
        settings.ClientSideEvents.BeginCallback = "function(s, e) { saveUserSelections(); }";
        settings.ClientSideEvents.EndCallback = "function(s, e) { restoreUserSelections(); updateButtonStates(); }";

        // Selection column - simplified for now
        settings.Columns.Add(column =>
        {
            column.FieldName = "Id";
            column.Caption = "Select";
            column.Width = 60;
            column.Settings.AllowAutoFilter = DefaultBoolean.False;
            column.Settings.AllowSort = DefaultBoolean.False;
            column.HeaderStyle.HorizontalAlign = HorizontalAlign.Center;
            column.CellStyle.HorizontalAlign = HorizontalAlign.Center;
            column.SetDataItemTemplateContent(content =>
            {
                Html.ViewContext.Writer.Write("<input type='checkbox' class='user-select-checkbox' value='" + content.Text + "' onchange='onUserCheckboxChanged(this)' />");
            });
        });

        // First Name column
        settings.Columns.Add(column =>
        {
            column.FieldName = "FirstName";
            column.Caption = "First Name";
            column.Width = 120;
        });

        // Last Name column
        settings.Columns.Add(column =>
        {
            column.FieldName = "LastName";
            column.Caption = "Last Name";
            column.Width = 120;
        });

        // Email column
        settings.Columns.Add(column =>
        {
            column.FieldName = "Email";
            column.Caption = "Email";
            column.Width = 200;
        });

        // Customer column
        settings.Columns.Add(column =>
        {
            column.FieldName = "CustomerName";
            column.Caption = "Customer";
            column.Width = 150;
        });

        // Language column
        settings.Columns.Add(column =>
        {
            column.FieldName = "Language";
            column.Caption = "Language";
            column.Width = 80;
        });

        // Newsletter Period column
        settings.Columns.Add(column =>
        {
            column.FieldName = "NewsletterPeriod";
            column.Caption = "Period (Months)";
            column.Width = 100;
            column.HeaderStyle.HorizontalAlign = HorizontalAlign.Center;
            column.CellStyle.HorizontalAlign = HorizontalAlign.Center;
        });

        // Last Newsletter Sent column
        settings.Columns.Add(column =>
        {
            column.FieldName = "LastNewsletterSent";
            column.Caption = "Last Sent";
            column.Width = 120;
            column.ColumnType = MVCxGridViewColumnType.DateEdit;
            column.PropertiesEdit.DisplayFormatString = "dd.MM.yyyy";
            column.HeaderStyle.HorizontalAlign = HorizontalAlign.Center;
            column.CellStyle.HorizontalAlign = HorizontalAlign.Center;
        });

        // Active status column
        settings.Columns.Add(column =>
        {
            column.FieldName = "IsActive";
            column.Caption = "Active";
            column.Width = 60;
            column.ColumnType = MVCxGridViewColumnType.CheckBox;
            column.HeaderStyle.HorizontalAlign = HorizontalAlign.Center;
            column.CellStyle.HorizontalAlign = HorizontalAlign.Center;
        });

        // Row styling based on active status
        settings.HtmlRowPrepared += (sender, e) =>
        {
            if (e.RowType != GridViewRowType.Data) return;

            var isActive = (bool)e.GetValue("IsActive");
            if (!isActive)
            {
                e.Row.BackColor = System.Drawing.Color.FromArgb(255, 248, 248); // Light red background for inactive users
                e.Row.ForeColor = System.Drawing.Color.Gray;
            }
        };

        // Custom styles
        settings.Styles.Header.BackColor = System.Drawing.Color.FromArgb(51, 122, 183);
        settings.Styles.Header.ForeColor = System.Drawing.Color.White;
        settings.Styles.Header.Font.Bold = true;
    });
}

@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = "Id";
}).GetHtml()
