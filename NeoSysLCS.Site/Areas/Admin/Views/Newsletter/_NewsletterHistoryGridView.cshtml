@using System.Web.UI.WebControls
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Site.Helpers
@using NeoSysLCS.Repositories.ViewModels
@model IQueryable<NewsletterHistoryViewModel>

@{
    var grid = Html.DevExpress().GridView(settings =>
    {
        settings.Name = "NewsletterHistoryGridView";
        GridViewHelper.ApplyDefaultSettings(settings);
        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;

        settings.KeyFieldName = "Id";
        settings.Width = Unit.Percentage(100);

        settings.CallbackRouteValues = new { Controller = "Newsletter", Action = "HistoryGridView" };

        // Grid settings
        settings.SettingsPager.Visible = true;
        settings.SettingsPager.PageSize = 25;
        settings.Settings.ShowGroupPanel = false;
        settings.Settings.ShowFilterRow = true;
        settings.SettingsBehavior.AllowSelectByRowClick = false;
        settings.CommandColumn.Visible = false;

        // User Name column
        settings.Columns.Add(column =>
        {
            column.FieldName = "UserName";
            column.Caption = "User Name";
            column.Width = 150;
        });

        // Email column
        settings.Columns.Add(column =>
        {
            column.FieldName = "Email";
            column.Caption = "Email";
            column.Width = 200;
        });

        // Customer column
        settings.Columns.Add(column =>
        {
            column.FieldName = "CustomerName";
            column.Caption = "Customer";
            column.Width = 150;
        });

        // Date Sent column
        settings.Columns.Add(column =>
        {
            column.FieldName = "DateSent";
            column.Caption = "Date Sent";
            column.Width = 120;
            column.ColumnType = MVCxGridViewColumnType.DateEdit;
            column.PropertiesEdit.DisplayFormatString = "dd.MM.yyyy HH:mm";
            column.HeaderStyle.HorizontalAlign = HorizontalAlign.Center;
            column.CellStyle.HorizontalAlign = HorizontalAlign.Center;
        });

        // Sent By column
        settings.Columns.Add(column =>
        {
            column.FieldName = "SentBy";
            column.Caption = "Sent By";
            column.Width = 120;
        });

        // Manual Send column
        settings.Columns.Add(column =>
        {
            column.FieldName = "IsManualSend";
            column.Caption = "Manual Send";
            column.Width = 80;
            column.ColumnType = MVCxGridViewColumnType.CheckBox;
            column.HeaderStyle.HorizontalAlign = HorizontalAlign.Center;
            column.CellStyle.HorizontalAlign = HorizontalAlign.Center;
        });

        // Newsletter File Link column
        settings.Columns.Add(column =>
        {
            column.FieldName = "NewsletterFileLink";
            column.Caption = "Download";
            column.Width = 80;
            column.Settings.AllowAutoFilter = DefaultBoolean.False;
            column.Settings.AllowSort = DefaultBoolean.False;
            column.HeaderStyle.HorizontalAlign = HorizontalAlign.Center;
            column.CellStyle.HorizontalAlign = HorizontalAlign.Center;
            
            column.SetDataItemTemplateContent(content =>
            {
                if (!string.IsNullOrEmpty(content.Text))
                {
                    Html.Raw($"<a href='{content.Text}' target='_blank' class='btn btn-xs btn-primary'><i class='fa fa-download'></i></a>");
                }
                else
                {
                    Html.Raw("<span class='text-muted'>N/A</span>");
                }
            });
        });

        // Custom styles
        settings.Styles.Header.BackColor = System.Drawing.Color.FromArgb(51, 122, 183);
        settings.Styles.Header.ForeColor = System.Drawing.Color.White;
        settings.Styles.Header.Font.Bold = true;

        // Sort by date sent descending by default
        settings.Columns.Add(column =>
        {
            column.FieldName = "DateSent";
            column.Visible = false;
            column.SortDescending();
        });
    });
}

@if (Model.Any())
{
    <div class="alert alert-info">
        <i class="fa fa-info-circle"></i>
        Showing <strong>@Model.Count()</strong> newsletter send records.
    </div>
    
    @grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
    {
        e.QueryableSource = Model;
        e.KeyExpression = "Id";
    }).GetHtml()
}
else
{
    <div class="alert alert-warning">
        <i class="fa fa-exclamation-triangle"></i>
        No newsletter history records found.
    </div>
}
