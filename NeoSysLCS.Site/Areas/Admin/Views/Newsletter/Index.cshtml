@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Resources.Properties
@using DevExpress.Web
@model NewsletterUserSearchViewModel

@{
    ViewBag.Title = "Newsletter Management";
    ViewContext.Writer.Write("<h1 class='page-header'>" + ViewBag.Title + "</h1>");
}

@section Scripts {
    @Scripts.Render("~/Scripts/jquery.unobtrusive-ajax.min.js")
    <script type="text/javascript">
        var selectedUserIds = [];
        var isSearchPerformed = false;

        $(document).ready(function() {
            // Initialize the page
            initializeNewsletterManagement();
            
            // Use delegated event handling for the confirm send button
            $(document).on('click', '#confirmSendBtn', function() {
                $('#confirmDialog').modal('hide');
                sendNewslettersToSelected();
            });
        });

        function initializeNewsletterManagement() {
            // Bind search form
            $('#searchForm').on('submit', function(e) {
                e.preventDefault();
                performUserSearch();
            });

            // Bind send newsletter button
            $('#sendNewsletterBtn').on('click', function() {
                if (selectedUserIds.length === 0) {
                    alert('Please select at least one user to send newsletters to.');
                    return;
                }
                showSendConfirmation();
            });

            // Bind select all/none buttons
            $('#selectAllBtn').on('click', function() { selectAllUsers(); return false; });
            $('#selectNoneBtn').on('click', function() { selectNoUsers(); return false; });
            
            // Bind test mode checkbox
            $('#isTestMode').on('change', toggleTestModeFields);
        }

        function performUserSearch() {
            var formData = $('#searchForm').serialize();
            
            // Show loading panel with search text
            showLoadingPanel("Searching users...");
            
            $.post('@Url.Action("UserSearchPartial", "Newsletter", new { Area = "Admin" })', formData, function(data) {
                $('#userSelectionContainer').html(data);
                isSearchPerformed = true;
                updateButtonStates();
                
                // Hide loading panel
                hideLoadingPanel();
            }).fail(function() {
                alert('Error occurred while searching for users.');
                hideLoadingPanel();
            });
        }

        function updateButtonStates() {
            // Update send button state
            if (selectedUserIds.length > 0) {
                $('#sendNewsletterBtn').prop('disabled', false);
                $('#selectedCountBadge').text(selectedUserIds.length).show();
            } else {
                $('#sendNewsletterBtn').prop('disabled', true);
                $('#selectedCountBadge').text('0').hide();
            }
        }

        function onUserCheckboxChanged(checkbox) {
            var userId = checkbox.value;
            var isChecked = checkbox.checked;

            if (isChecked) {
                if (selectedUserIds.indexOf(userId) === -1) {
                    selectedUserIds.push(userId);
                }
            } else {
                var index = selectedUserIds.indexOf(userId);
                if (index > -1) {
                    selectedUserIds.splice(index, 1);
                }
            }
            updateButtonStates();
        }

        function selectAllUsers() {
            $('.user-select-checkbox').prop('checked', true);
            selectedUserIds = [];
            $('.user-select-checkbox').each(function() {
                selectedUserIds.push($(this).val());
            });
            updateButtonStates();
        }

        function selectNoUsers() {
            $('.user-select-checkbox').prop('checked', false);
            selectedUserIds = [];
            updateButtonStates();
        }

        function restoreUserSelections() {
            // After the grid refreshes, restore checkboxes based on selectedUserIds array
            if (selectedUserIds && selectedUserIds.length > 0) {
                $('.user-select-checkbox').each(function() {
                    var userId = $(this).val();
                    if (selectedUserIds.indexOf(userId) !== -1) {
                        $(this).prop('checked', true);
                    }
                });
            }
            updateButtonStates();
        }

        function saveUserSelections() {
            // No need to save anything as selectedUserIds is maintained in the parent page
            // and persists across grid callbacks
        }

        function showProgressDialog() {
            $('#progressDialog').show();
            $('#progressBar').css('width', '0%');
        }

        function hideProgressDialog() {
            $('#progressDialog').hide();
        }

        function showSendConfirmation() {
            if (selectedUserIds.length === 0) {
                alert('No users selected.');
                return;
            }

            // Validate selected users first
            $.post('@Url.Action("ValidateSelectedUsers", "Newsletter")', 
                { selectedUserIds: selectedUserIds }, 
                function(response) {
                    if (response.success) {
                        var isTestMode = $('#isTestMode').is(':checked');
                        var testRecipientEmail = $('#testRecipientEmail').val();
                        
                        var message = '';
                        var title = 'Confirm Newsletter Sending';
                        
                        if (isTestMode) {
                            title = 'TEST MODE';
                            message += '<p><strong>This will send a test newsletter to:</strong> ' + testRecipientEmail + '</p>';
                            message += '<p><strong>No newsletter history will be saved.</strong></p>';
                            message += '<hr>';
                        }
                        
                        message += '<p>Send newsletters to <strong>' + response.validUserCount + '</strong> selected users?</p>';
                        if (response.warnings && response.warnings.length > 0) {
                            message += '<div class="alert alert-warning">';
                            message += '<h4>Warnings:</h4>';
                            message += '<ul>';
                            for (var i = 0; i < response.warnings.length; i++) {
                                message += '<li>' + response.warnings[i] + '</li>';
                            }
                            message += '</ul>';
                            message += '</div>';
                        }
                        
                        // Set the modal content
                        $('#confirmDialog .modal-title').html(title);
                        $('#confirmDialogContent').html(message);
                        
                        // Show the modal
                        $('#confirmDialog').modal('show');
                    } else {
                        var errorMessage = 'Cannot send newsletters:\n' + response.errors.join('\n');
                        alert(errorMessage);
                    }
                }).fail(function() {
                    alert('Error validating selected users.');
                });
        }

        function sendNewslettersToSelected() {
            var forceResend = $('#forceResend').is(':checked');
            var isTestMode = $('#isTestMode').is(':checked');
            var testRecipientEmail = $('#testRecipientEmail').val();
            var testLastNewsletterDate = $('#testLastNewsletterDate').val();
            
            // Show loading panel with send text
            showLoadingPanel("Sending newsletters...");
            
            $.post('@Url.Action("SendToSelectedUsers", "Newsletter")', {
                SelectedUserIds: selectedUserIds,
                ForceResend: forceResend,
                IsTestMode: isTestMode,
                TestRecipientEmail: testRecipientEmail,
                TestLastNewsletterDate: testLastNewsletterDate
            }, function(response) {
                hideLoadingPanel();
                
                if (response.success) {
                    showSendResults(response);
                    // Clear selection after successful send
                    selectedUserIds = [];
                    updateButtonStates();
                } else {
                    alert('Error sending newsletters: ' + response.message);
                }
            }).fail(function() {
                hideLoadingPanel();
                alert('Error occurred while sending newsletters.');
            });
        }

        function showSendResults(response) {
            // Clear previous results
            $('#failedUsersTableBody').empty();
            
            // Update counts
            $('#resultsTotalCount').text(response.totalUsers);
            $('#resultsSentCount').text(response.sent);
            $('#resultsFailedCount').text(response.failed);
            $('#resultsSkippedCount').text(response.skipped);
            
            // Handle test mode info
            if (response.isTestMode) {
                var testModeText = 'Test recipient email: ' + response.testRecipientEmail;
                if (response.testLastNewsletterDate) {
                    testModeText += '<br>Test last newsletter date: ' + response.testLastNewsletterDate;
                }
                $('#testModeDetails').html(testModeText);
                $('#testModeResultsInfo').show();
            } else {
                $('#testModeResultsInfo').hide();
            }
            
            // Handle failed users
            if (response.failed > 0) {
                $('#failedUsersContainer').show();
                
                // Populate failed users table
                response.results.forEach(function(result) {
                    if (result.status === 'Failed') {
                        var row = $('<tr></tr>');
                        $('<td></td>').text(result.userName).appendTo(row);
                        $('<td></td>').text(result.errorMessage).appendTo(row);
                        $('#failedUsersTableBody').append(row);
                    }
                });
            } else {
                $('#failedUsersContainer').hide();
            }
            
            // Show the results dialog
            $('#resultsDialog').modal('show');
        }
        
        // Toggle test mode fields visibility
        function toggleTestModeFields() {
            var isTestMode = $('#isTestMode').is(':checked');
            if (isTestMode) {
                $('#testModeFields').show();
            } else {
                $('#testModeFields').hide();
            }
        }

        // Show/hide loading panel functions
        function showLoadingPanel(message) {
            $('#loadingPanelText').text(message || "Processing...");
            $('#loadingPanel').modal('show');
        }
        
        function hideLoadingPanel() {
            $('#loadingPanel').modal('hide');
        }
    </script>
}

<style>
    /* Fix for checkbox and label alignment */
    .checkbox label {
        padding-left: 25px;  /* Increase left padding to create more space */
        position: relative;
        display: inline-block;
    }
    
    .checkbox input[type="checkbox"] {
        position: absolute;
        left: 0;
        margin-left: 0;
        margin-top: 2px;
    }
    
    /* Additional spacing for the checkbox containers */
    .checkbox {
        margin-top: 10px;
        margin-bottom: 10px;
    }
    
    /* Fix alignment in grid views */
    .user-select-checkbox {
        margin: 0 auto;
        display: block;
    }
    
    /* Equal height panels */
    .equal-height-row {
        display: flex;
        flex-wrap: wrap;
    }
    
    .equal-height-row > [class*='col-'] {
        display: flex;
        flex-direction: column;
    }
    
    .equal-height-row .panel {
        flex: 1;
        display: flex;
        flex-direction: column;
    }
    
    .equal-height-row .panel-body {
        flex: 1;
    }
</style>

<div class="row">
    <div class="col-md-12">
        @if (!string.IsNullOrEmpty(ViewBag.ErrorMessage))
        {
            <div class="alert alert-danger">
                @ViewBag.ErrorMessage
            </div>
        }

        <div class="row equal-height-row">
            <!-- Search Form -->
            <div class="col-md-6">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">Search Users</h3>
                    </div>
                    <div class="panel-body">
                        @using (Html.BeginForm("UserSearchPartial", "Newsletter", FormMethod.Post, new { id = "searchForm" }))
                        {
                            <div class="form-group">
                                <label>@Html.DisplayNameFor(m => m.FirstName)</label>
                                @Html.TextBoxFor(m => m.FirstName, new { @class = "form-control", placeholder = "First name..." })
                            </div>
                            
                            <div class="form-group">
                                <label>@Html.DisplayNameFor(m => m.LastName)</label>
                                @Html.TextBoxFor(m => m.LastName, new { @class = "form-control", placeholder = "Last name..." })
                            </div>
                            
                            <div class="form-group">
                                <label>@Html.DisplayNameFor(m => m.Email)</label>
                                @Html.TextBoxFor(m => m.Email, new { @class = "form-control", placeholder = "Email address..." })
                            </div>
                            
                            <div class="checkbox">
                                <label>
                                    @Html.CheckBoxFor(m => m.IncludeInactive)
                                    @Html.DisplayNameFor(m => m.IncludeInactive)
                                </label>
                            </div>
                            
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fa fa-search"></i> Search
                                </button>
                            </div>
                        }
                    </div>
                </div>
            </div>

            <!-- Send Options -->
            <div class="col-md-6">
                <div class="panel panel-default" id="sendOptionsPanel">
                    <div class="panel-heading">
                        <h3 class="panel-title">Send Options</h3>
                    </div>
                    <div class="panel-body">
                        <div class="checkbox">
                            <label>
                                <input type="checkbox" id="forceResend" />
                                <strong>Force resend</strong>
                            </label>
                            <p class="help-block">
                                Override the standard newsletter period restrictions. This will send newsletters to selected users 
                                regardless of when they last received one, ignoring their configured newsletter frequency.
                            </p>
                        </div>
                        <div class="checkbox">
                            <label>
                                <input type="checkbox" id="isTestMode" />
                                <strong>Test mode</strong>
                            </label>
                            <p class="help-block">
                                Send test newsletters without affecting production data. All emails will be redirected to a test email address
                                and no newsletter history records will be created. Use this to verify content and formatting.
                            </p>
                        </div>
                        <div id="testModeFields" style="display: none;">
                            <div class="alert alert-warning">
                                <i class="fa fa-exclamation-triangle"></i> 
                                Test mode will not create newsletter history records or upload PDF files to storage.
                            </div>
                            <div class="form-group">
                                <label class="col-sm-5 control-label">Test recipient email:</label>
                                <div class="col-sm-7">
                                    <input type="email" id="testRecipientEmail" class="form-control" placeholder="Email address for testing...">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-5 control-label">Test last newsletter date:</label>
                                <div class="col-sm-7">
                                    <input type="date" id="testLastNewsletterDate" class="form-control" placeholder="Date for content selection...">
                                </div>
                            </div>
                            <p class="help-block">Set this date to test content that would appear for different time periods. This affects which legal updates will be included in the newsletter.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Selection and Actions -->
        <div class="panel panel-default">
            <div class="panel-heading">
                <h3 class="panel-title">
                    User Selection 
                    <span class="badge" id="selectedCountBadge">0</span> selected
                </h3>
                <div class="panel-actions">
                    <button type="button" id="selectAllBtn" class="btn btn-sm btn-default">
                        <i class="fa fa-check-square-o"></i> Select All
                    </button>
                    <button type="button" id="selectNoneBtn" class="btn btn-sm btn-default">
                        <i class="fa fa-square-o"></i> Select None
                    </button>
                    <button type="button" id="sendNewsletterBtn" class="btn btn-sm btn-success" disabled>
                        <i class="fa fa-envelope"></i> Send Newsletter
                    </button>
                </div>
            </div>
            <div class="panel-body">
                <div id="userSelectionContainer">
                    <div class="alert alert-info">
                        <i class="fa fa-info-circle"></i> 
                        Use the search form above to find users for newsletter sending.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Panel -->
<div id="loadingPanel" class="modal fade" data-backdrop="static" data-keyboard="false" style="display: none;">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-body text-center">
                <i class="fa fa-spinner fa-spin fa-3x fa-fw" style="margin-bottom: 15px;"></i>
                <h5 id="loadingPanelText">Processing...</h5>
            </div>
        </div>
    </div>
</div>

<!-- Progress Dialog -->
<div id="progressDialog" class="modal fade" style="display: none;">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Sending Newsletters</h4>
            </div>
            <div class="modal-body">
                <p>Please wait while newsletters are being sent...</p>
                <div class="progress">
                    <div id="progressBar" class="progress-bar progress-bar-striped active" style="width: 0%"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Confirm Dialog -->
<div id="confirmDialog" class="modal fade" data-backdrop="static" data-keyboard="false" style="display: none;">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title"></h4>
            </div>
            <div class="modal-body" id="confirmDialogContent"></div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                <button type="button" id="confirmSendBtn" class="btn btn-primary">Send</button>
            </div>
        </div>
    </div>
</div>

<!-- Results Dialog -->
<div id="resultsDialog" class="modal fade" data-backdrop="static" data-keyboard="false" style="display: none;">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Newsletter Sending Results</h4>
            </div>
            <div class="modal-body">
                <div id="resultsDialogContent"></div>
                
                <div id="testModeResultsInfo" class="alert alert-warning" style="display: none; margin-top: 15px;">
                    <h4><i class="fa fa-exclamation-triangle"></i> Test Mode</h4>
                    <p id="testModeDetails"></p>
                    <p><strong>Note:</strong> No newsletter history records were created.</p>
                </div>
                
                <div class="row" style="margin-top: 15px;">
                    <div class="col-md-3">
                        <div class="panel panel-default">
                            <div class="panel-heading text-center">Total</div>
                            <div class="panel-body text-center">
                                <h3 id="resultsTotalCount">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="panel panel-success">
                            <div class="panel-heading text-center">Sent</div>
                            <div class="panel-body text-center">
                                <h3 id="resultsSentCount">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="panel panel-danger">
                            <div class="panel-heading text-center">Failed</div>
                            <div class="panel-body text-center">
                                <h3 id="resultsFailedCount">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="panel panel-warning">
                            <div class="panel-heading text-center">Skipped</div>
                            <div class="panel-body text-center">
                                <h3 id="resultsSkippedCount">0</h3>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div id="failedUsersContainer" style="display: none; margin-top: 15px;">
                    <h4>Failed Users</h4>
                    <div class="table-responsive">
                        <table class="table table-striped table-bordered">
                            <thead>
                                <tr>
                                    <th>User</th>
                                    <th>Error</th>
                                </tr>
                            </thead>
                            <tbody id="failedUsersTableBody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
