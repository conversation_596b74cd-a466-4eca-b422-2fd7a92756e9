@using System.Globalization
@using System.Linq
@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Site.Helpers
@model IQueryable<NeoSysLCS.Repositories.ViewModels.StandortObjektViewModel>

@{
    var grid = Html.DevExpress().GridView(settings =>
    {
        settings.Name = "StandortObjekt";
        GridViewHelper.ApplyDefaultSettings(settings);
        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;
        IUnitOfWork unitOfWork = new UnitOfWork();

        NeoSysLCS_Dev context = new NeoSysLCS_Dev();
        var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
        Sprache sprache = context.Sprachen.FirstOrDefault(s => s.Lokalisierung == language);
        var _currentLang = sprache.SpracheID;

        settings.KeyFieldName = "StandortObjektID";

        settings.CallbackRouteValues = new { Controller = "StandortObjekt", Action = "StandortObjektGridView", standortID = ViewData["StandortID"] };

        settings.SettingsEditing.BatchUpdateRouteValues = new { Controller = "StandortObjekt", Action = "StandortObjektGridViewBatchEditUpdate", standortID = ViewData["StandortID"] };


        if (User.IsInRole(Role.ProjectManager))
        {
            settings.SettingsEditing.Mode = GridViewEditingMode.Batch;
            settings.CommandColumn.Visible = true;
            settings.CommandColumn.ShowNewButtonInHeader = false;
            settings.CommandColumn.ShowDeleteButton = true;
            settings.CommandColumn.ShowEditButton = false;
        }

        settings.Columns.Add(column =>
        {
            column.Caption = "#";

            column.SetHeaderCaptionTemplateContent(c => Html.DevExpress().HyperLink(hyperlink =>
            {
                if (User.IsInRole(Role.ProjectManager))
                {
                    hyperlink.Properties.ImageUrl = "/Content/images/pencil-26.png";
                    hyperlink.Properties.ImageWidth = 20;

                    hyperlink.Attributes.Add("onClick", "javascript:ShowNewPopup(this,'');");
                    hyperlink.NavigateUrl = Url.Content("#");
                }

            }).Render());


            column.Settings.AllowDragDrop = DefaultBoolean.False;
            column.Settings.AllowSort = DefaultBoolean.False;
            column.Width = 50;

            //hide edit column
            column.EditFormSettings.Visible = DefaultBoolean.False;
            column.SetEditItemTemplateContent(c =>
            {
                @Html.DevExpress().TextBox(TextBoxSettings =>
                {
                    TextBoxSettings.Name = "txt5";
                    TextBoxSettings.ReadOnly = true;
                    TextBoxSettings.ClientVisible = false;
                }).GetHtml();
            });

        });

        settings.Columns.Add("Name");
        settings.Columns.Add("Beschreibung");

        settings.SettingsDetail.ShowDetailRow = true;
        settings.SetDetailRowTemplateContent(c =>
        {
            Html.RenderAction("_StandortObjektUebersetzungenGridView", new { id = DataBinder.Eval(c.DataItem, "StandortObjektID") });
        });

        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);

        var errorList = ViewData["ErrorList"];

        if (errorList != null)
        {
            settings.Settings.ShowFooter = true;
            settings.SetFooterRowTemplateContent(c => Html.ViewContext.Writer.Write("StandortObjekt ist noch in Verwendung und kann nicht gelöscht werden"));
        }

        settings.Columns.Add(column =>
        {
            column.FieldName = "ObjektID";
            column.Caption = "Basiert auf Objekt";

            column.ColumnType = MVCxGridViewColumnType.ComboBox;
            column.Settings.FilterMode = ColumnFilterMode.DisplayText;
            var standortId = Convert.ToInt32(Request.Params["StandortID"]);
            column.ColumnType = MVCxGridViewColumnType.ComboBox;
            column.Settings.FilterMode = ColumnFilterMode.DisplayText;

            var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;
            comboBoxProperties.CallbackPageSize = 1000;
            comboBoxProperties.DataSource = (Model.Select(x => new { x.ObjektID, x.Objektname }).OrderBy(x => x.Objektname)).Distinct().ToList();
            comboBoxProperties.TextField = "Objektname";
            comboBoxProperties.ValueField = "ObjektID";
            comboBoxProperties.ValueType = typeof(int);
        });


        MVCxGridViewColumn objCat = settings.Columns.Add("Objektkat");
        objCat.Caption = "Kategorie";
        objCat.ReadOnly = true;


        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);

        settings.ClientSideEvents.BatchEditStartEditing = @"function(s,e){
                currentGrid = s;
	        }";

        settings.ClientSideEvents.ClipboardCellPasting = @"function(s,e){
                e.cancel = currentGrid != s;
	        }";
    });

    if (ViewData["EditError"] != null)
    {
        grid.SetEditErrorText((string)ViewData["EditError"]);
    }
}
@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = "StandortObjektID";
}).GetHtml()

