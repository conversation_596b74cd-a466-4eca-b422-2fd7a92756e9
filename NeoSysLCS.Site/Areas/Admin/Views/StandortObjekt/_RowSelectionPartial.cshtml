@using System.Web.UI.WebControls;
@using DevExpress.XtraGrid
@using NeoSysLCS.Repositories

@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Areas.Admin.Controllers
@using NeoSysLCS.Site.Helpers

@model IQueryable<NeoSysLCS.Repositories.ViewModels.NewStandortObjektViewModel>


@Html.DevExpress().TreeList(
    settings =>
    {
        IUnitOfWork unitOfWork = new UnitOfWork();
        var standortobjekte = unitOfWork.StandortObjektRepository.GetObjectNameByStandort(Int32.Parse(ViewData["StandortID"].ToString())).Select(x => x.Name).ToList();

        settings.Name = "NewStandortObjektGrid";
        settings.CallbackRouteValues = new { Controller = "StandortObjekt", Action = "NewStandortObjekteGridView", standortId = ViewData["StandortID"] };
        settings.Width = Unit.Percentage(100);

        settings.AutoGenerateColumns = false;
        settings.KeyFieldName = "ObjektkategorieID";
        settings.ParentFieldName = "ParentObjektkategorieID";

        settings.Columns.Add(column =>
        {
            column.FieldName = "Name";
            column.Caption = Resources.Colunm_Name;
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "ObjektID";
            column.Caption = "ObjektID";
            column.Visible = false;
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "Beschreibung";
            column.Caption = TranslationHelper.GetTranslation(typeof(ObjektViewModel), column.FieldName);
        });

        settings.HtmlDataCellPrepared = (s, e) =>
        {

            MVCxTreeList treeList = (MVCxTreeList)s;
            TreeListNodeIterator iterator = treeList.CreateNodeIterator();
            TreeListNode node = iterator.Current;

            if (e.Column.FieldName == "Name")
            {
                if (e.CellValue != null)
                {
                    //if (standortobjekte.Contains(e.CellValue.ToString()))
                    if (standortobjekte.Any(x => x == e.CellValue.ToString()))
                    {
                        e.Cell.BackColor = System.Drawing.Color.FromArgb(211, 235, 183);
                    }
                }
            }
        };

        settings.SettingsBehavior.AutoExpandAllNodes = false;
        settings.SettingsCookies.Enabled = false;
        settings.SettingsCookies.StoreExpandedNodes = false;
        settings.SettingsCookies.StoreSelection = false;

        settings.SettingsSelection.Enabled = true;
        settings.SettingsSelection.Recursive = true;
        settings.SettingsSelection.AllowSelectAll = true;

        settings.ClientSideEvents.SelectionChanged = "function(s, e) { s.PerformCallback(); }";


    }
).Bind(Model).GetHtml()


