@using NeoSysLCS.DomainModel.Models;
@using NeoSysLCS.Repositories;
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Site.Helpers

@Html.DevExpress().GridView(
    settings =>
    {
        UnitOfWork unitOfWork = new UnitOfWork();

        GridViewHelper.ApplyUebersetzungsSettings(settings);

        settings.Name = "ErlassUebersetzungenGridView_" + ViewData["ErlassID"];
        settings.KeyFieldName = "ErlassUebersetzungID";

        settings.CallbackRouteValues = new { Controller = "Erlasse", Action = "ErlassUebersetzungenGridView", erlassID = ViewData["ErlassID"] };

        settings.SettingsEditing.BatchUpdateRouteValues = new { Controller = "Erlasse", Action = "ErlassUebersetzungenGridViewBatchEditUpdate", erlassID = ViewData["ErlassID"] };
        if (User.IsInRole(Role.ProjectManager))
        {
            settings.SettingsEditing.Mode = GridViewEditingMode.Batch;
        }

        settings.CommandColumn.Visible = false;

        //Columns
        settings.Columns.Add("Abkuerzung");
        settings.Columns.Add("Titel");
        settings.Columns.Add("Sachgebiete");
        settings.Columns.Add(column =>
        {
            column.FieldName = "Quelle";
            column.SetDataItemTemplateContent(
                container =>
                {
                    var url = DataBinder.Eval(container.DataItem, "Quelle");
                    string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url);
                    ViewContext.Writer.Write(htmlLink);
                });
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "Kerninhalte";
            column.ColumnType = MVCxGridViewColumnType.Memo;
            var memoProp = column.PropertiesEdit as MemoProperties;
            if (memoProp != null)
            {
                memoProp.EncodeHtml = false;
            }
        });
        settings.Columns.Add(column =>
        {
            column.FieldName = "SpracheID";
            column.Caption = "Sprache";
            column.ReadOnly = true;
            column.ColumnType = MVCxGridViewColumnType.ComboBox;
            column.Settings.FilterMode = ColumnFilterMode.DisplayText;
            var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;

            comboBoxProperties.DataSource = unitOfWork.SpracheRepository.Get().ToList();
            comboBoxProperties.TextField = "Name";
            comboBoxProperties.ValueField = "SpracheID";
            comboBoxProperties.ValueType = typeof(int);
        });

        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);

        settings.ClientSideEvents.BatchEditStartEditing = @"function(s,e){
                currentGrid = s;
		        if(e.focusedColumn.fieldName == 'Kerninhalte'){ openPopupOnBatchEditBegin(s, e); }
	        }";

        settings.ClientSideEvents.ClipboardCellPasting = @"function(s,e){
                e.cancel = currentGrid != s;
	        }";

    }).BindToLINQ(string.Empty, string.Empty, (s, e) =>
    {
        e.QueryableSource = Model;
        e.KeyExpression = "ErlassUebersetzungID";
    }).GetHtml()