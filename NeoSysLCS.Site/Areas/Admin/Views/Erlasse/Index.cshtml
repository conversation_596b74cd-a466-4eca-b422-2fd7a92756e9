@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Areas.Admin.Controllers
@using NeoSysLCS.Site.Utilities.Export
@using NeoSysLCS.Repositories.Helper

@model IQueryable<NeoSysLCS.Repositories.ViewModels.ErlassViewModel>

@{
    ViewBag.Title = "Erlasse";
    ViewContext.Writer.Write("<h1 class='page-header'>" + ViewBag.Title + "</h1>");
}

@section Scripts {
    @Scripts.Render("~/Scripts/helpers/BatchEditDeleteHelper.js")
}



@using (Html.BeginForm("ExportTo", "Erlasse", new { area = "Admin", standortErlasse = (bool)ViewData["standortErlasse"] }, FormMethod.Post, new { target = "_blank", id = "ErlasseExportWrapperForm" }))
{
    <div class="divToolbar">
        <div class="divButtonsLeft">
            @foreach (KeyValuePair<ExportType, ExportAction> entry in ExportUtilities.GetExportTypes(new List<ExportType>() { ExportType.Pdf, ExportType.Xlsx }))
            {
                <button class="btn btn-default" type="submit" value="@entry.Key" name="exportFormat">@entry.Value.Title</button>
            }
        </div>
    </div>


            var displayArchived = (bool)ViewData["displayArchived"];
            var standortErlasse = (bool)ViewData["standortErlasse"];
            if (displayArchived)
            {
                @Html.ActionLink("Archivierte ausblenden", "Index", "Erlasse", new { area = "Admin", displayArchived = false, standortErlasse = standortErlasse }, new { @class = "" })
            }
            else
            {
                @Html.ActionLink("Archivierte einblenden", "Index", "Erlasse", new { area = "Admin", displayArchived = true, standortErlasse = standortErlasse }, new { @class = "" })
            }

            @Html.Partial("_HtmlEditPopup", User.IsInRole(Role.ProjectManager))
            @Html.Partial("_ErlasseGridView", Model)
}


<script type="text/javascript">
    //<![CDATA[
    $("#ErlasseExportWrapperForm").keypress(function (e) {
        //Enter key
        if (e.which == 13) {
            return false;
        }
    });

    //init batchEditHelper
    var batchEditDeleteHelper = new BatchEditDeleteHelper(
        "@ErlasseController.BaseGridViewSettings.GetGridViewName()",
        "ErlassID",
        "Titel",
        "@Resources.View_Erlasse_DeleteText"
    );

    // ]]>
</script>