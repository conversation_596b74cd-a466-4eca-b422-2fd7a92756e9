@using System.Web.UI.WebControls;
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers


@{
    var grid = Html.DevExpress().GridView(settings =>
    {
        settings.Name = "ErlassfassungenGridView";
        GridViewHelper.ApplyDefaultSettings(settings);
        settings.KeyFieldName = "ErlassfassungID";

        settings.CallbackRouteValues = new { Controller = "Erlassfassungen", Action = "ErlassfassungenGridView", id = ViewData["ErlassID"] };

        settings.SettingsEditing.BatchUpdateRouteValues = new { Controller = "Erlassfassungen", Action = "ErlassfassungenGridViewBatchEditUpdate", id = ViewData["ErlassID"] };

        if (User.IsInRole(Role.ProjectManager))
        {
            settings.SettingsEditing.Mode = GridViewEditingMode.Batch;
            settings.CommandColumn.Visible = true;
            settings.CommandColumn.ShowNewButtonInHeader = true;
            settings.CommandColumn.ShowDeleteButton = true;
            settings.CommandColumn.ShowEditButton = false;
        }

        settings.ClientSideEvents.BatchEditStartEditing = @"function(s,e){
            currentGrid = s;
            if(e.focusedColumn.fieldName == 'BetroffenKommentar' || e.focusedColumn.fieldName == 'NichtBetroffenKommentar'){ openPopupOnBatchEditBegin(s, e); }  
        }";

        settings.ClientSideEvents.ClipboardCellPasting = @"function(s,e){
                e.cancel = currentGrid != s;
	        }";

        //Columns
        settings.Columns.Add(column =>
        {
            column.Caption = "#";
            column.ReadOnly = true;
            column.EditFormSettings.Visible = DefaultBoolean.False;

            column.SetDataItemTemplateContent(container =>
            {
                Html.DevExpress().HyperLink(hyperlink =>
                {
                    var keyValue = container.KeyValue;
                    hyperlink.Name = "Forderungen" + keyValue;
                    hyperlink.Properties.Text = "Forderungen";
                    hyperlink.NavigateUrl = Url.Action("Index", "Forderungen", new { id = keyValue });

                }).Render();
                ViewContext.Writer.Write("<br>");

                Html.DevExpress().HyperLink(hyperlink =>
                {
                    var keyValue = container.KeyValue;
                    hyperlink.Name = "Pflichten" + keyValue;
                    hyperlink.Properties.Text = "Pflichten";
                    hyperlink.NavigateUrl = Url.Action("Index", "Pflichten", new { id = keyValue });

                }).Render();
                ViewContext.Writer.Write("&nbsp;&nbsp;");

            });
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "Beschluss";
            column.ColumnType = MVCxGridViewColumnType.DateEdit;
            column.Caption = "�nderung vom";
            column.SortIndex = 1;
            column.SortOrder = DevExpress.Data.ColumnSortOrder.Descending;
            column.PropertiesEdit.DisplayFormatString = "dd/MM/yyyy";
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "Inkrafttretung";
            column.ColumnType = MVCxGridViewColumnType.DateEdit;
            column.SortIndex = 0;
            column.SortOrder = DevExpress.Data.ColumnSortOrder.Descending;
            column.PropertiesEdit.DisplayFormatString = "dd/MM/yyyy";

        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "Quelle";
            column.SetDataItemTemplateContent(
                container =>
                {
                    var url = DataBinder.Eval(container.DataItem, "Quelle");

                    var displayText = "";
                    if (url == null || "".Equals(url))
                    {
                        displayText = Resources.Fehler_Keine_Uebersetzung;
                    }
                    else
                    {
                        displayText = LinkHelper.GenerateHtmlLinkIfPossible(url, url);
                    }
                    ViewContext.Writer.Write(StringHelper.TruncateHtml(displayText, 50));
                });
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "BetroffenKommentar";
            column.Caption = "Betroffen Kommentar";
            column.Width = Unit.Percentage(50);

            column.ColumnType = MVCxGridViewColumnType.Memo;
            var memoProp = column.PropertiesEdit as MemoProperties;
            memoProp.EncodeHtml = false;
            column.ReadOnly = true;
            var prop = (column.PropertiesEdit as EditProperties);
            if (prop != null)
            {
                prop.ValidationSettings.Display = Display.Dynamic;
            }
            column.SetDataItemTemplateContent(
                container =>
                {
                    var kommentar = (string)DataBinder.Eval(container.DataItem, "BetroffenKommentar");
                    if (string.IsNullOrEmpty(kommentar))
                    {
                        ViewContext.Writer.Write(Resources.Fehler_Keine_Uebersetzung);
                    }
                    else
                    {
                        ViewContext.Writer.Write(StringHelper.TruncateHtml(kommentar, 50));
                    }
                }
            );
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "LinkBetroffenKommentar";
            column.Caption = "Link betroffen Kommentar";
            column.SetDataItemTemplateContent(container =>
            {

                var url = DataBinder.Eval(container.DataItem, "LinkBetroffenKommentar");
                string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url);
                ViewContext.Writer.Write(StringHelper.TruncateHtml(htmlLink, 50));
            });
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "NichtBetroffenKommentar";
            column.Caption = "Nicht betroffen Kommentar";
            column.Width = Unit.Percentage(50);

            column.ColumnType = MVCxGridViewColumnType.Memo;
            var memoProp = column.PropertiesEdit as MemoProperties;
            memoProp.EncodeHtml = false;
            column.ReadOnly = true;
            var prop = (column.PropertiesEdit as EditProperties);
            if (prop != null)
            {
                prop.ValidationSettings.Display = Display.Dynamic;
            }
            column.SetDataItemTemplateContent(
                container =>
                {
                    var kommentar = (string)DataBinder.Eval(container.DataItem, "NichtBetroffenKommentar");
                    if (string.IsNullOrEmpty(kommentar))
                    {
                        ViewContext.Writer.Write(Resources.Fehler_Keine_Uebersetzung);
                    }
                    else
                    {
                        ViewContext.Writer.Write(StringHelper.TruncateHtml(kommentar, 50));
                    }
                }
            );

        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "LinkNichtBetroffenKommentar";
            column.Caption = "Link nicht betroffen Kommentar";
            column.SetDataItemTemplateContent(container =>
            {

                var url = DataBinder.Eval(container.DataItem, "LinkNichtBetroffenKommentar");
                string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url);
                ViewContext.Writer.Write(StringHelper.TruncateHtml(htmlLink, 50));
            });
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "InternerKommentar";
            column.Caption = "Interner Kommentar";
            column.ColumnType = MVCxGridViewColumnType.Memo;
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "KommentarID";
            column.Caption = "Kommentar ID";
            column.EditFormSettings.Visible = DefaultBoolean.True;
            column.ReadOnly = true;
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "PLFreigabe";
            column.Caption = "PL-Freigabe";
            column.ColumnType = MVCxGridViewColumnType.CheckBox;
            column.EditFormSettings.Visible = DefaultBoolean.True;
            column.ReadOnly = true;
        });

        settings.HtmlDataCellPrepared += (sender, e) =>
        {
            if (e.DataColumn.FieldName == "BetroffenKommentar" || e.DataColumn.FieldName == "NichtBetroffenKommentar")
            {

                if (!User.IsInRole(Role.ProjectManager))
                {
                    //readonly
                    e.Cell.Attributes.Add(
                       "onclick",
                       "openPopupReadonly(ErlassfassungenGridView, '" + e.DataColumn.FieldName + "', " + e.VisibleIndex + ", '" + e.DataColumn.Caption + "')"
                       );

                }
            }
        };

        //GridViewHelper.AddNewestFirstSortorderColumn(settings, "ErlassfassungID");
        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);

        //Detailpage
        settings.SettingsDetail.ShowDetailRow = true;
        settings.SetDetailRowTemplateContent(c =>
        {
            Html.DevExpress().PageControl(pageControlSettings =>
            {
                pageControlSettings.Name = "ErlassFassungPageControl_" + DataBinder.Eval(c.DataItem, "ErlassfassungID");
                pageControlSettings.Width = Unit.Percentage(100);
                // TODO �bersetzung
                pageControlSettings.TabPages.Add("�bersetzungen").SetContent(() =>
                {

                    Html.RenderAction("ErlassfassungUebersetzungenGridView",
                        new
                        {
                            erlassId = ViewData["ErlassID"],
                            erlassfassungId = DataBinder.Eval(c.DataItem, "ErlassfassungID")
                        });


                });

            }).Render();

        });


    });
}

@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = "ErlassfassungID";
}).GetHtml()