@using NeoSysLCS.DomainModel.Models;
@using NeoSysLCS.Repositories;
@using System.Web.UI.WebControls;
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Site.Helpers


@Html.DevExpress().GridView(
    settings =>
    {
        UnitOfWork unitOfWork = new UnitOfWork();

        GridViewHelper.ApplyUebersetzungsSettings(settings);

        settings.Name = "ErlassfassungUebersetzungenGridView_" + ViewData["ErlassID"] + "_" + ViewData["ErlassfassungID"];
        settings.KeyFieldName = "ErlassfassungUebersetzungID";

        settings.CallbackRouteValues = new { Controller = "Erlassfassungen", Action = "ErlassfassungUebersetzungenGridView", ErlassID = ViewData["ErlassID"], ErlassfassungID = ViewData["ErlassfassungID"] };
        settings.SettingsEditing.BatchUpdateRouteValues = new { Controller = "Erlassfassungen", Action = "ErlassfassungUebersetzungenGridViewBatchEditUpdate", ErlassID = ViewData["ErlassID"], ErlassfassungID = ViewData["ErlassfassungID"] };

        if (User.IsInRole(Role.ProjectManager))
        {
            settings.SettingsEditing.Mode = GridViewEditingMode.Batch;
        }
        settings.ClientSideEvents.BatchEditStartEditing = @"function(s,e){
            currentGrid = s;
            if(e.focusedColumn.fieldName == 'Remark' ){ openPopupOnBatchEditBegin(s, e); }  
        }";
        settings.CommandColumn.Visible = false;

        //Columns
        settings.Columns.Add(column =>
        {
            column.FieldName = "Quelle";
            column.SetDataItemTemplateContent(
                container =>
                {
                    var url = DataBinder.Eval(container.DataItem, "Quelle");
                    string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url);
                    ViewContext.Writer.Write(htmlLink);
                });
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "BetroffenKommentar";
            column.Caption = "Betroffen Kommentar";
            column.Width = Unit.Percentage(50);

            column.ColumnType = MVCxGridViewColumnType.Memo;
            var memoProp = column.PropertiesEdit as MemoProperties;
            memoProp.EncodeHtml = false;
            column.ReadOnly = true;
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "LinkBetroffenKommentar";
            column.Caption = "Link betroffen Kommentar";
            column.SetDataItemTemplateContent(container =>
            {

                var url = DataBinder.Eval(container.DataItem, "LinkBetroffenKommentar");
                string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url);
                ViewContext.Writer.Write(StringHelper.TruncateHtml(htmlLink, 50));
            });
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "NichtBetroffenKommentar";
            column.Caption = "Nicht Betroffen Kommentar";
            column.Width = Unit.Percentage(50);
            column.ColumnType = MVCxGridViewColumnType.Memo;
            var memoProp = column.PropertiesEdit as MemoProperties;
            memoProp.EncodeHtml = false;
            column.ReadOnly = true;

        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "LinkNichtBetroffenKommentar";
            column.Caption = "Link nicht betroffen Kommentar";
            column.SetDataItemTemplateContent(container =>
            {

                var url = DataBinder.Eval(container.DataItem, "LinkNichtBetroffenKommentar");
                string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url);
                ViewContext.Writer.Write(StringHelper.TruncateHtml(htmlLink, 50));
            });
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "InternerKommentar";
            column.Caption = "Interner Kommentar";
        });

        settings.HtmlDataCellPrepared += (sender, e) =>
        {
            if (e.DataColumn.FieldName == "BetroffenKommentar" || e.DataColumn.FieldName == "NichtBetroffenKommentar")
            {
                if (!User.IsInRole(Role.ProjectManager))
                {
                    //readonly
                    e.Cell.Attributes.Add(
                       "onclick",
                       "openPopupReadonly("+ settings.Name +", '" + e.DataColumn.FieldName + "', " + e.VisibleIndex + ", '" + e.DataColumn.Caption + "')"
                       );

                }
            }
        };
        settings.ClientSideEvents.BatchEditStartEditing = @"function(s,e){
            currentGrid = s;
		    if(e.focusedColumn.fieldName == 'BetroffenKommentar' || e.focusedColumn.fieldName == 'NichtBetroffenKommentar'){
					    openPopupOnBatchEditBegin(s, e);
				    }
	        }";

        settings.ClientSideEvents.ClipboardCellPasting = @"function(s,e){
                e.cancel = currentGrid != s;
	        }";

        settings.Columns.Add(column =>
        {
            column.FieldName = "SpracheID";
            column.Caption = "Sprache";
            column.ReadOnly = true;

            column.ColumnType = MVCxGridViewColumnType.ComboBox;
            column.Settings.FilterMode = ColumnFilterMode.DisplayText;
            var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;

            comboBoxProperties.DataSource = unitOfWork.SpracheRepository.Get().ToList();
            comboBoxProperties.TextField = "Name";
            comboBoxProperties.ValueField = "SpracheID";
            comboBoxProperties.ValueType = typeof(int);
        });
        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);
    }).Bind(Model).GetHtml()


