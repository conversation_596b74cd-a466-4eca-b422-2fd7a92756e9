@using NeoSysLCS.Repositories
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Resources.Properties

@model IQueryable<NeoSysLCS.Repositories.ViewModels.ErlassfassungViewModel>

@{
    IUnitOfWork unitOfWork = new UnitOfWork();
    ErlassViewModel erlassViewModel = unitOfWork.ErlassRepository.GetErlassForErlassfassung((int)ViewData["ErlassID"]);
    ViewBag.Title = "Fassungen von Erlass «" + erlassViewModel.Titel + "» (" + erlassViewModel.SrNummer + ")";
    ViewContext.Writer.Write("<h1 class='page-header'>" + ViewBag.Title + "</h1>");
}

@section Scripts {
    @Scripts.Render("~/Scripts/helpers/BatchEditDeleteHelper.js")
}

@section breadcrumb{
    <a href="#" onclick="history.go(-1); return false;" title="Zurück"><i class="fa fa-arrow-left fa-lg"></i></a> |
    <span class="breadcrumb-noesys-navigation">
        <a href="/">Home</a>
        <span> > </span>
        @Html.ActionLink("Erlasse", "Index", "Erlasse", new { area = "Admin" }, new { @class = "" })
        <span> > </span>
        <span>Fassungen von Erlass «@erlassViewModel.Titel» (@erlassViewModel.SrNummer)</span>
    </span>

}
@Html.Partial("_HtmlEditPopup", User.IsInRole(Role.ProjectManager))
@Html.Partial("_ErlassfassungenGridView", Model)


<script type="text/javascript">
    //<![CDATA[
    //init batch edit helper
    var batchEditDeleteHelper = new BatchEditDeleteHelper("ErlassfassungenGridView", "ErlassfassungID", "Inkrafttretung", "@Resources.View_Erlassfassungen_DeleteText");

    // ]]>
</script>


