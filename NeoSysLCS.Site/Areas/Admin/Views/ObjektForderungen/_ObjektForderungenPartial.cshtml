@using System.Web.UI.WebControls
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Resources.Properties
@model IQueryable<NeoSysLCS.Repositories.ViewModels.ForderungsversionViewModel>



<div class="selection_form">
    <div class="row">
        <div class="col-lg-2">
            <div class="text">
                Selektierte Werte:
            </div>
            @Html.DevExpress().ListBox(
                settings =>
                {
                    settings.Name = "SelectedForderungenRows_" + ViewData["ObjektID"];
                    settings.Properties.EnableItemsVirtualRendering = DefaultBoolean.False;
                    settings.Properties.EnableClientSideAPI = true;
                    settings.Height = 350;
                    settings.Width = Unit.Percentage(100);
                    settings.Properties.ValueField = "ForderungsversionID";
                    settings.Properties.ValueType = typeof(int);
                    settings.Properties.ItemStyle.Wrap = DefaultBoolean.True;

                }).GetHtml()
            <div class="text">
                @Resources.Popup_Ausgewaehlt:&nbsp;<strong id='forderungenCount_@ViewData["ObjektID"]'>0</strong>
            </div>
        </div>

        <div class="col-lg-10">
            @{
                Html.RenderAction("ForderungSelectionPartial", new { id = ViewData["ObjektID"] });
            }
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
            @if (User.IsInRole(Role.ObjectGuru))
            {
                @Html.DevExpress().Button(settings =>
                {
                    settings.Name = "btnSubmitForderungen" + ViewData["ObjektID"];
                    settings.Text = Resources.Button_Zuordnung_speichern;

                    settings.ControlStyle.CssClass = "pull-right";
                    settings.ClientSideEvents.Click = @"function(s,e){
                        OnSubmitForderungenClick(" + @ViewData["ObjektID"] + @");
                    }";
                }).GetHtml()
            }
            <div id='forderungResult_@ViewData["ObjektID"]' class="pull-right label label-success"></div>
        </div>
    </div>
</div>
