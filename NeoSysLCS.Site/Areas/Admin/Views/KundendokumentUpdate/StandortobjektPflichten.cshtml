@using NeoSysLCS.Repositories
@using NeoSysLCS.Resources.Properties
@model IEnumerable<NeoSysLCS.Repositories.ViewModels.KundendokumentStandortobjektPflichtViewModel>

@{
    ViewBag.Title = "Kundendokument: Resultierende Pflichten pro Standortobjekt";
    ViewContext.Writer.Write("<h1 class='page-header'>" + ViewBag.Title + "</h1>");
}

@{
    UnitOfWork unitOfWork = new UnitOfWork();
    var standort = unitOfWork.StandortRepository.GetStanodrtById((int)ViewData["standortID"]);
    var kunde = unitOfWork.KundeRepository.GetByID(standort.KundeID);
}

@section breadcrumb{
    <a href="#" onclick="history.go(-1); return false;" title="Zurück"><i class="fa fa-arrow-left fa-lg"></i></a> |
    <span class="breadcrumb-noesys-navigation">
        <a href="/">@Resources.View_Breadcrumb_Home</a>
        <span> > </span>
        @Html.ActionLink(Resources.View_Breadcrumb_Kundenübersicht, "Index", "Kunden", new { area = "Admin" }, new { @class = "" })
        <span> > </span>
        @Html.ActionLink(Resources.View_Breadcrumb_KundendokumenteVonStandort.Replace("##KUNDE##", kunde.Name).Replace("##STANDORT##", standort.Name),
             "Index",
             "Kundendokumente",
             new { area = "Admin", id = standort.StandortID }, new { @class = "" }
        )
        <span> > </span>
        <span>@ViewBag.Title</span>
    </span>
}


<a class="btn btn-default" type="button" onclick="UpdateStandortobjektPflichtenGridView.CollapseAll(); return false;">
    <i class="fa fa-minus-square"></i>&nbsp; @Resources.Allgemein_CollapseAllRows
</a>

<a class="btn btn-default" type="button" onclick="UpdateStandortobjektPflichtenGridView.ExpandAll(); return false;">
    <i class="fa fa-plus-square"></i>&nbsp; @Resources.Allgemein_ExpandAllRows
</a>

<script type="text/javascript">
    function OnBatchEditEnd(s, e) {

        var item = { Key: s.GetRowKey(e.visibleIndex), Value: e.rowValues[8].value };
        if ($("input[name='pflichtKundenbezuege']").val().length > 0) {

            var items = JSON.parse($("input[name='pflichtKundenbezuege']").val());
            var itemfound = false;
            for (var i = 0; i < items.length; i++) {
                if (items[i].Key == item.Key) {
                    //item found --> replace value
                    items[i].Value = item.Value;
                    itemfound = true;
                    break;
                }
            }
            if (!itemfound) {
                //item was not found --> add it to array
                items.push(item);
            }

        } else {
            var items = [item];

        }
        $("input[name='pflichtKundenbezuege']").val(JSON.stringify(items));
    }
</script>



@Html.Partial("_StandortobjektPflichtenGridView", Model)


@using (Html.BeginForm("SelectErlassfassungen", "KundendokumentUpdate", FormMethod.Post, new { name = "nextForm" }))
{
    @Html.Hidden("StandortID", ViewData["StandortID"])
    @Html.Hidden("pflichtKundenbezuege", ViewData["pflichtKundenbezuege"])

    @Html.DevExpress().Button(settings =>
                {
                    settings.Name = "btnNext";
                    settings.Text = Resources.Navigation_Weiter;

                    settings.UseSubmitBehavior = true;
                    settings.ControlStyle.CssClass = "pull-right";
                }
            ).GetHtml()
}

@using (Html.BeginForm("SelectPflichten", "KundendokumentUpdate", FormMethod.Post))
{
    @Html.Hidden("StandortID", ViewData["StandortID"])
    @Html.Hidden("pflichtKundenbezuege", ViewData["pflichtKundenbezuege"])

    @Html.DevExpress().Button(settings =>
        {
            settings.Name = "btnBack";
            settings.Text = Resources.Navigation_Zurueck;

            settings.UseSubmitBehavior = true;
            settings.ControlStyle.CssClass = "pull-right";
        }
    ).GetHtml()
}