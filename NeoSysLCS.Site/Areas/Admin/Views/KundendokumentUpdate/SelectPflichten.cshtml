@using NeoSysLCS.Repositories
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers
@model IQueryable<NeoSysLCS.Repositories.ViewModels.KundendokumentPflichtViewModel>

@{
    ViewBag.Title = "Kundendokument: Pflichten selektieren";
    ViewContext.Writer.Write("<h1 class='page-header'>" + ViewBag.Title + "</h1>");
}

@{
    UnitOfWork unitOfWork = new UnitOfWork();
    var standort = unitOfWork.StandortRepository.GetStanodrtById((int)ViewData["standortID"]);
    var kunde = unitOfWork.KundeRepository.GetByID(standort.KundeID);
}

@section breadcrumb{
    <a href="#" onclick="history.go(-1); return false;" title="Zurück"><i class="fa fa-arrow-left fa-lg"></i></a> |
    <span class="breadcrumb-noesys-navigation">
        <a href="/">@Resources.View_Breadcrumb_Home</a>
        <span> > </span>
        @Html.ActionLink(Resources.View_Breadcrumb_Kundenübersicht, "Index", "Kunden", new { area = "Admin" }, new { @class = "" })
        <span> > </span>
        @Html.ActionLink(Resources.View_Breadcrumb_KundendokumenteVonStandort.Replace("##KUNDE##", kunde.Name).Replace("##STANDORT##", standort.Name),
             "Index",
             "Kundendokumente",
             new { area = "Admin", id = standort.StandortID }, new { @class = "" }
        )
        <span> > </span>
        <span>@ViewBag.Title</span>
    </span>
}

@{
    var postfix = "";
    if (SessionHelper.GetKundendokumentData((int)ViewData["StandortID"]).VorgaengerKundendokumentID == null)
    {
        postfix = "Initial";
    }

}

<a class="btn btn-default" type="button" onclick="SelectPflichtenGridView_@(postfix).CollapseAll(); return false;">
    <i class="fa fa-minus-square"></i>&nbsp; @Resources.Allgemein_CollapseAllRows
</a>

<a class="btn btn-default" type="button" onclick="SelectPflichtenGridView_@(postfix).ExpandAll(); return false;">
    <i class="fa fa-plus-square"></i>&nbsp; @Resources.Allgemein_ExpandAllRows
</a>

<script type="text/javascript">
    //<![CDATA[
    function OnCallbackError(s, e) {
        e.handled = true;
        console.log (e.message);
        alert("Es ist ein Fehler während der Datenübertragung aufgetreten. Bitte wiederholen Sie Ihre letzte Eingabe." + e.message);
    }
    function OnSelectionChanged(s, e) {
        s.GetSelectedFieldValues('PflichtID', OnGetSelectedFieldValues);
    }

    function OnGetSelectedFieldValues(values) {
        console.log("selected pflichten ids: " + values);

        document.nextForm.elements["selectedPflichtenIDs"].value = values;
        document.backForm.elements["selectedPflichtenIDs"].value = values;
    }

    // ]]>
</script>

@Html.Partial("_SelectPflichtenGridView", Model)

@using (Html.BeginForm("StandortobjektPflichten", "KundendokumentUpdate", FormMethod.Post, new { name = "nextForm" }))
{
    @Html.Hidden("StandortID", ViewData["StandortID"])
    @Html.Hidden("selectedPflichtenIDs")

    @Html.DevExpress().Button(settings =>
        {
            settings.Name = "btnNext";
            settings.Text = Resources.Navigation_Weiter;

            settings.UseSubmitBehavior = true;
            settings.ControlStyle.CssClass = "pull-right";
        }
    ).GetHtml()
}

@using (Html.BeginForm("StandortobjektForderungen", "KundendokumentUpdate", FormMethod.Post, new { name = "backForm" }))
{
    @Html.Hidden("StandortID", ViewData["StandortID"])
    @Html.Hidden("selectedPflichtenIDs")

    @Html.DevExpress().Button(settings =>
        {
            settings.Name = "btnBack";
            settings.Text = Resources.Navigation_Zurueck;

            settings.UseSubmitBehavior = true;
            settings.ControlStyle.CssClass = "pull-right";
        }).GetHtml()

    @Html.DevExpress().LoadingPanel(
        settings =>
        {
            settings.Name = "LoadingPanel";
            settings.Modal = true;
        }).GetHtml()
}