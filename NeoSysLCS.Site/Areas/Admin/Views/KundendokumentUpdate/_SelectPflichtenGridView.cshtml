
@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories;
@using System.Web.UI.WebControls;
@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers
@using System.Globalization


@{

    var grid = Html.DevExpress().GridView(
        settings =>
        {
            IUnitOfWork unitOfWork = new UnitOfWork();
            var isInitialVersion = SessionHelper.GetKundendokumentData((int)ViewData["StandortID"]).VorgaengerKundendokumentID == null;

            settings.Name = "SelectPflichtenGridView_" + (isInitialVersion ? "Initial" : "");
            settings.KeyFieldName = "PflichtID";
            GridViewHelper.ApplyDefaultSettings(settings);

            settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;
            settings.SettingsBehavior.AutoExpandAllGroups = true;
            settings.CommandColumn.Visible = true;
            settings.CommandColumn.SelectAllCheckboxMode = GridViewSelectAllCheckBoxMode.AllPages;
            settings.CommandColumn.ShowSelectCheckbox = true;
            settings.SettingsBehavior.AllowSelectByRowClick = false;

            settings.SettingsLoadingPanel.Enabled = false;
            settings.SettingsLoadingPanel.Mode = GridViewLoadingPanelMode.Disabled;

            settings.CallbackRouteValues = new
            {
                Controller = "KundendokumentUpdate",
                Action = "_SelectPflichtenGridView",
                standortId = ViewData["StandortID"]
            };
            settings.ClientSideEvents.SelectionChanged = "OnSelectionChanged";
            settings.ClientSideEvents.CallbackError = "OnCallbackError";
            settings.ClientSideEvents.BeginCallback = "function(s, e) { LoadingPanel.Show(); }";
            settings.ClientSideEvents.EndCallback = "function(s, e) { LoadingPanel.Hide(); }";

            settings.Columns.Add(column =>
            {
                column.FieldName = "Beschreibung";
                column.Caption = Resources.Allgemein_Beschreibung;
                column.Width = Unit.Percentage(50);
                
                column.ColumnType = MVCxGridViewColumnType.Memo;
                var memoProp = column.PropertiesEdit as MemoProperties;
                if (memoProp != null)
                {
                    memoProp.EncodeHtml = false;
                }
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "ErlassTitel";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
                column.MinWidth = 140;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ReadOnly = true;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.ColumnType = MVCxGridViewColumnType.TextBox;
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "ErlassfassungID";
                column.Caption = Resources.Entitaet_Erlassfassung_Singular;
                column.ColumnType = MVCxGridViewColumnType.ComboBox;
                column.Settings.FilterMode = ColumnFilterMode.DisplayText;
                // we only want the date to display not the date and time. The combox doesn't provide any options to
                IEnumerable<Erlassfassung>
                            erlassefassungen = unitOfWork.ErlassfassungRepository.Get();
                var datasource = new List<Object>();
                foreach (Erlassfassung erlassfassungCand in erlassefassungen)
                {
                    datasource.Add(new
                    {
                        erlassfassungCand.ErlassfassungID,
                        Inkrafttretung = erlassfassungCand.Inkrafttretung.ToString("d")
                    });
                }

                var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;
                if (comboBoxProperties != null)
                {
                    comboBoxProperties.DataSource = datasource;
                    comboBoxProperties.TextField = "Inkrafttretung";
                    comboBoxProperties.ValueField = "ErlassfassungID";
                    comboBoxProperties.ValueType = typeof(int);
                }
            });

            settings.Columns.Add("GueltigVon", MVCxGridViewColumnType.DateEdit);
            settings.Columns.Add("GueltigBis", MVCxGridViewColumnType.DateEdit);

            if (!isInitialVersion)
            {
                settings.Columns.Add(column =>
                {
                    column.FieldName = "Status";
                    column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);

                    //define as combobox for filtern over the enum
                    column.ColumnType = MVCxGridViewColumnType.ComboBox;
                    var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;

                    //var test = (from x in Model select x.Status).Distinct().ToList();

                    var list = from KundendokumentItemStatus value in Enum.GetValues(typeof(KundendokumentItemStatus))
                                   //where test.Contains(value)
                               select new
                               {
                                   Id = (int)value,
                                   Name = value.GetTranslation()
                               };

                    comboBoxProperties.DataSource = list;
                    comboBoxProperties.ValueField = "Id";
                    comboBoxProperties.TextField = "Name";

                    column.HeaderStyle.Wrap = DefaultBoolean.True;
                    column.ReadOnly = true;
                    column.EditFormSettings.Visible = DefaultBoolean.False;
                    column.SetDataItemTemplateContent(container =>
                    {
                        var statusCandidate = DataBinder.Eval(container.DataItem, "Status");
                        if (statusCandidate != null)
                        {
                            var status = (KundendokumentItemStatus)statusCandidate;
                            ViewContext.Writer.Write("<span class=\"label label-" + status + "\">" + status.GetTranslation() + "</span>");
                        }
                    });
                });
            }

            settings.PreRender = (sender, e) =>
            {
                var selectedPflichtenIds = SessionHelper.GetKundendokumentData((int)ViewData["StandortID"]).SelectedPflichtenIDs;
                if (selectedPflichtenIds != null && !selectedPflichtenIds.Any())
                // favour user's selection over automatic preselection
                {
                    MVCxGridView gridView = sender as MVCxGridView;
                    if (gridView != null)
                    {
                        foreach (KundendokumentPflichtViewModel viewModel in Model)
                        {
                            if (viewModel.Status == KundendokumentItemStatus.Unchanged && viewModel.Relevant)
                            {
                                gridView.Selection.SelectRowByKey(viewModel.PflichtID);
                            }
                        }
                    }
                }
            };

            settings.DataBound += (s, e) =>
            {
                MVCxGridView g = s as MVCxGridView;

                foreach (int id in SessionHelper.GetKundendokumentData((int)ViewData["StandortID"]).SelectedPflichtenIDs)
                {
                    g.Selection.SelectRowByKey(id);
                }
            };

            GridViewHelper.AddNewestFirstSortorderColumn(settings, "PflichtID");
            GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);


        });
}

@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = "PflichtID";
}).GetHtml()