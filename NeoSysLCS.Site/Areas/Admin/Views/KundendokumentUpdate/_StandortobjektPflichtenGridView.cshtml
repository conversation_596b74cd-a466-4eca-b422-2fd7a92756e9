@using System.Web.UI.WebControls
@using System.Globalization

@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories
@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers

@{

    var grid = Html.DevExpress().GridView(
        settings =>
        {
            IUnitOfWork unitOfWork = new UnitOfWork();

            settings.KeyFieldName = "ID";
            settings.Name = "UpdateStandortobjektPflichtenGridView";
            GridViewHelper.ApplyDefaultSettings(settings);

            settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;
            settings.SettingsBehavior.AutoExpandAllGroups = true;
            settings.CommandColumn.Visible = true;

            settings.CallbackRouteValues = new
            {
                Controller = "KundendokumentUpdate",
                Action = "_StandortobjektPflichtenGridView",
                standortId = ViewData["StandortID"],
            };

            settings.ClientSideEvents.BeginCallback = "function(s,e){" +
                                                      "e.customArgs['pflichtKundenbezuege'] = $('input[name=\"pflichtKundenbezuege\"]').val();" +
                                                      "}";

            //kundenbezugfield is editable
            settings.SettingsEditing.Mode = GridViewEditingMode.Batch;
            settings.CommandColumn.ShowNewButtonInHeader = false;
            settings.CommandColumn.ShowEditButton = false;
            settings.CommandColumn.Visible = true;
            settings.SettingsEditing.BatchEditSettings.ShowConfirmOnLosingChanges = false;
            //hides the savebuttons which are not needed here
            settings.Settings.ShowStatusBar = GridViewStatusBarMode.Hidden;
            settings.Columns.Add(column =>
            {
                column.FieldName = "PflichtID";
                column.Caption = Resources.Allgemein_Version_Singular;
                column.ReadOnly = true;
                column.EditFormSettings.Visible = DefaultBoolean.False;
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "Beschreibung";
                column.Caption = Resources.Allgemein_Beschreibung;
                column.ReadOnly = true;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.Width = Unit.Percentage(50);

                column.ColumnType = MVCxGridViewColumnType.Memo;
                var memoProp = column.PropertiesEdit as MemoProperties;
                if (memoProp != null)
                {
                    memoProp.EncodeHtml = false;
                }
            });

            settings.Columns.Add(column =>
            {

                column.FieldName = "StandortObjektTitel";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
                column.MinWidth = 140;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ReadOnly = true;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.ColumnType = MVCxGridViewColumnType.TextBox;
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "ErlassTitel";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
                column.MinWidth = 140;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ReadOnly = true;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.ColumnType = MVCxGridViewColumnType.TextBox;
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "ErlassfassungID";
                column.Caption = Resources.Entitaet_Erlassfassung_Singular;
                column.ReadOnly = true;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.ColumnType = MVCxGridViewColumnType.ComboBox;
                column.Settings.FilterMode = ColumnFilterMode.DisplayText;
                // we only want the date to display not the date and time. The combox doesn't provide any options to format the 'textfield'
                IEnumerable<Erlassfassung>
                                    erlassefassungen = unitOfWork.ErlassfassungRepository.Get();
                var datasource = new List<Object>();
                foreach (Erlassfassung erlassfassungCand in erlassefassungen)
                {
                    datasource.Add(new
                    {
                        erlassfassungCand.ErlassfassungID,
                        Inkrafttretung = erlassfassungCand.Inkrafttretung.ToString("d")
                    });
                }

                var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;
                if (comboBoxProperties != null)
                {
                    comboBoxProperties.DataSource = datasource;
                    comboBoxProperties.TextField = "Inkrafttretung";
                    comboBoxProperties.ValueField = "ErlassfassungID";
                    comboBoxProperties.ValueType = typeof(int);
                }
            });


            settings.Columns.Add(column =>
            {
                column.FieldName = "GueltigVon";
                column.ReadOnly = true;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.ColumnType = MVCxGridViewColumnType.DateEdit;
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "GueltigBis";
                column.ReadOnly = true;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.ColumnType = MVCxGridViewColumnType.DateEdit;
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "Kundenbezug";

            });

            settings.ClientSideEvents.BatchEditEndEditing = "OnBatchEditEnd";
            GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);

        });
}

@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = "PflichtID";
}).GetHtml()