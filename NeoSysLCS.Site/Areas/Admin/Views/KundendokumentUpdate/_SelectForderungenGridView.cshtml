@using System.Data.Entity
@using System.Globalization
@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories;
@using System.Web.UI.WebControls;
@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Repositories.ViewModels.Validations
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers

@model IQueryable<NeoSysLCS.Repositories.ViewModels.KundendokumentForderungsversionViewModel>

@{
    var grid = Html.DevExpress().GridView(
        settings =>
        {
            IUnitOfWork unitOfWork = new UnitOfWork();
            int standortId = Convert.ToInt32(ViewData["StandortID"]);
            NeoSysLCS_Dev context = new NeoSysLCS_Dev();

            var isInitialVersion = SessionHelper.GetKundendokumentData(standortId).VorgaengerKundendokumentID == null;

            settings.Name = "SelectForderungenGridView_" + (isInitialVersion ? "Initial" : "");
            settings.KeyFieldName = "ID";

            GridViewHelper.ApplyDefaultSettings(settings);
            settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;

            settings.SettingsBehavior.AutoExpandAllGroups = true;
            settings.CommandColumn.Visible = true;
            settings.CommandColumn.SelectAllCheckboxMode = GridViewSelectAllCheckBoxMode.AllPages;
            settings.CommandColumn.ShowSelectCheckbox = true;
            settings.SettingsBehavior.AllowSelectByRowClick = false;
            settings.SettingsPager.Mode = GridViewPagerMode.ShowPager;

            settings.SettingsLoadingPanel.Enabled = false;
            settings.SettingsLoadingPanel.Mode = GridViewLoadingPanelMode.Disabled;

            settings.SettingsCookies.Enabled = false;
            settings.SettingsCookies.StoreFiltering = false;
            settings.SettingsCookies.StoreGroupingAndSorting = false;
            settings.SettingsCookies.StorePaging = false;

            settings.CallbackRouteValues = new
            {
                Controller = "KundendokumentUpdate",
                Action = "_SelectForderungenGridView",
                standortId = standortId
            };

            settings.CustomJSProperties = (sender, e) =>
            {
                var gridSender = (MVCxGridView)sender;
                e.Properties["cpSelectedKeys"] = gridSender.GetSelectedFieldValues(gridSender.KeyFieldName).Select(k => k.ToString()).ToList();
            };


            settings.ClientSideEvents.SelectionChanged = "OnSelectionChanged";
            //settings.ClientSideEvents.CallbackError = "OnCallbackError";
            settings.ClientSideEvents.BeginCallback = "OnBeginCallback";
            settings.ClientSideEvents.EndCallback = "OnEndCallback";
            //only possible while grid not grouped/filtered
            settings.ClientSideEvents.Init = "OnSelectionChanged";


            settings.Columns.Add(column =>
            {
                column.FieldName = "Beschreibung";
                column.Caption = Resources.Allgemein_Beschreibung;
                column.Width = Unit.Percentage(50);

                column.ColumnType = MVCxGridViewColumnType.Memo;
                var memoProp = column.PropertiesEdit as MemoProperties;
                if (memoProp != null)
                {
                    memoProp.EncodeHtml = false;
                }
            });
            settings.Columns.Add(column =>
            {
                column.FieldName = "ArtikelNummer";
                column.Caption = Resources.Entitaet_Artikel_Singular;
                column.MinWidth = 110;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.CellStyle.HorizontalAlign = HorizontalAlign.Center;
                column.Settings.FilterMode = ColumnFilterMode.DisplayText;
                column.SetDataItemTemplateContent(
                    container =>
                    {

                        var url = DataBinder.Eval(container.DataItem, "ArtikelQuelle");
                        var nr = DataBinder.Eval(container.DataItem, "ArtikelNummer");
                        string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, nr, false);
                        ViewContext.Writer.Write(htmlLink);
                    });
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "SrNummer";
                column.Name = "SrNummer";
                column.Caption = Resources.Entitaet_Erlass_SrNummer;
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "StandortObjektID";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
                column.MinWidth = 140;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ReadOnly = true;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.ColumnType = MVCxGridViewColumnType.ComboBox;
                column.Settings.FilterMode = ColumnFilterMode.DisplayText;
                var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;
                comboBoxProperties.DataSource = (Model.Select(x => new { x.StandortObjektID, x.StandortObjektTitel }).OrderBy(x => x.StandortObjektTitel)).Distinct().ToList();
                comboBoxProperties.TextField = "StandortObjektTitel";
                comboBoxProperties.ValueField = "StandortObjektID";
                comboBoxProperties.ValueType = typeof(int);
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "ErlassID";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
                column.MinWidth = 140;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ReadOnly = true;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.ColumnType = MVCxGridViewColumnType.ComboBox;
                column.Settings.FilterMode = ColumnFilterMode.DisplayText;
                var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;
                comboBoxProperties.DataSource = (Model.Select(x => new { x.ErlassID, ErlassDisplay = x.ErlassTitel + " ( " + (string.IsNullOrWhiteSpace(x.Abkuerzung) ? "-" : x.Abkuerzung) + " )" }).OrderBy(x => x.ErlassDisplay).Distinct().ToList());
                comboBoxProperties.TextField = "ErlassDisplay";
                comboBoxProperties.ValueField = "ErlassID";
                comboBoxProperties.ValueType = typeof(int);
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "ErlassfassungID";
                column.Caption = Resources.Entitaet_Erlassfassung_Singular;
                column.ColumnType = MVCxGridViewColumnType.ComboBox;
                column.Settings.FilterMode = ColumnFilterMode.DisplayText;
                // we only want the date to display not the date and time. The combox doesn't provide any options to
                IEnumerable<Erlassfassung>
                        erlassefassungen = unitOfWork.ErlassfassungRepository.Get();
                var datasource = new List<Object>();
                foreach (Erlassfassung erlassfassungCand in erlassefassungen)
                {
                    datasource.Add(new
                    {
                        erlassfassungCand.ErlassfassungID,
                        Inkrafttretung = erlassfassungCand.Inkrafttretung.ToString("d")
                    });
                }

                var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;
                if (comboBoxProperties != null)
                {
                    comboBoxProperties.DataSource = datasource;
                    comboBoxProperties.TextField = "Inkrafttretung";
                    comboBoxProperties.ValueField = "ErlassfassungID";
                    comboBoxProperties.ValueType = typeof(int);
                }
            });



            settings.Columns.Add("Inkrafttretung", MVCxGridViewColumnType.DateEdit);
            settings.Columns.Add("Aufhebung", MVCxGridViewColumnType.DateEdit);

            settings.Columns.Add(column =>
            {
                column.FieldName = "InternerKommentar";
                column.Caption = Resources.Allgemein_InternerKommentar;
                column.Width = Unit.Percentage(25);

                column.ColumnType = MVCxGridViewColumnType.Memo;
                var memoProp = column.PropertiesEdit as MemoProperties;
                if (memoProp != null)
                {
                    memoProp.EncodeHtml = false;
                }
                column.SetDataItemTemplateContent(
                                container =>
                                {
                                    var kommentar = (string)DataBinder.Eval(container.DataItem, "InternerKommentar");
                                    if (!string.IsNullOrEmpty(kommentar))
                                    {
                                        ViewContext.Writer.Write(kommentar);
                                    }
                                }
                                );
            });

            if (!isInitialVersion)
            {
                settings.Columns.Add(column =>
                {
                    column.FieldName = "Status";
                    column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);

                    //define as combobox for filtern over the enum
                    column.ColumnType = MVCxGridViewColumnType.ComboBox;
                    column.Settings.FilterMode = ColumnFilterMode.DisplayText;
                    var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;

                    var test = (from x in Model select x.Status).Distinct().ToList();

                    var list = from KundendokumentItemStatus value in Enum.GetValues(typeof(KundendokumentItemStatus))
                               where test.Contains(value)
                               select new
                               {
                                   Id = (int)value,
                                   Name = value.GetTranslation()
                               };

                    comboBoxProperties.DataSource = list;
                    comboBoxProperties.ValueField = "Id";
                    comboBoxProperties.TextField = "Name";

                    column.HeaderStyle.Wrap = DefaultBoolean.True;
                    column.ReadOnly = true;
                    column.EditFormSettings.Visible = DefaultBoolean.False;
                    column.SetDataItemTemplateContent(container =>
                    {
                        var statusCandidate = DataBinder.Eval(container.DataItem, "Status");
                        if (statusCandidate != null)
                        {
                            var status = (KundendokumentItemStatus)statusCandidate;
                            ViewContext.Writer.Write("<span class=\"label label-" + status + "\">" + status.GetTranslation() + "</span>");
                        }
                    });
                });
            }

            settings.Columns.Add(column =>
            {
                column.FieldName = "QsFreigabe";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ColumnType = MVCxGridViewColumnType.CheckBox;
                column.ReadOnly = true;
            });

            settings.DataBound += (s, e) =>
            {
                MVCxGridView g = s as MVCxGridView;

                foreach (string id in SessionHelper.GetKundendokumentData(standortId).SelectedForderungenIDs)
                {
                    g.Selection.SelectRowByKey(id);
                }
            };

            settings.PreRender = (sender, e) =>
            {
                var selectedForderungen = SessionHelper.GetKundendokumentData(standortId).SelectedForderungenIDs;
                if (selectedForderungen != null/* && !selectedForderungen.Any()*/) // Fix NEOS-514 forderung is not marked relevant when updated
                // favour user's selection over automatic preselection
                {
                    MVCxGridView gridView = sender as MVCxGridView;
                    if (gridView != null)
                    {
                        foreach (KundendokumentForderungsversionViewModel viewModel in Model)
                        {
                            //select relevant version with status unchanged or old version
                            //preselect also new versions

                            if (((viewModel.Status == KundendokumentItemStatus.Unchanged || viewModel.Status == KundendokumentItemStatus.OldVersion
                                    || viewModel.Status == KundendokumentItemStatus.NewVersion) && viewModel.Relevant || viewModel.Betroffen))
                            {
                                gridView.Selection.SelectRowByKey(viewModel.ID);
                            }
                        }
                    }
                }
            };

            settings.HtmlDataCellPrepared += (sender, e) =>
            {
                var bewilligungspflichtig = Convert.ToBoolean(e.GetValue("Bewilligungspflicht"));
                var nachweispflichtig = Convert.ToBoolean(e.GetValue("Nachweispflicht"));
                if (bewilligungspflichtig)
                {
                    e.Cell.BackColor = System.Drawing.Color.Lavender;
                }
                if (nachweispflichtig)
                {
                    e.Cell.BackColor = System.Drawing.Color.BlanchedAlmond;
                }
                if (bewilligungspflichtig && nachweispflichtig)
                {
                    e.Cell.BackColor = System.Drawing.Color.PowderBlue;
                }
            };

            GridViewHelper.AddNewestFirstSortorderColumn(settings, "ForderungsversionID");
            GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);


        });
}
    
@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = "ID";
}).GetHtml()