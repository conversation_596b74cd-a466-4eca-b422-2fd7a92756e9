@using System.Web.UI.WebControls
@using System.Globalization

@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories
@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers
@model IQueryable<KundendokumentForderungsversionViewModel>
@{

    var grid = Html.DevExpress().GridView(
        settings =>
        {
            IUnitOfWork unitOfWork = new UnitOfWork();

            settings.KeyFieldName = "ID";
            settings.Name = "UpdateStandortobjektForderungenGridView";
            GridViewHelper.ApplyDefaultSettings(settings);

            settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;
            settings.SettingsBehavior.AutoExpandAllGroups = true;
            //settings.SettingsPager.Mode = GridViewPagerMode.ShowAllRecords;

            //kundenbezugfield is editable
            settings.SettingsEditing.Mode = GridViewEditingMode.Batch;
            settings.CommandColumn.ShowNewButtonInHeader = false;
            settings.CommandColumn.ShowEditButton = false;
            settings.CommandColumn.Visible = true;
            settings.SettingsEditing.BatchEditSettings.ShowConfirmOnLosingChanges = false;
            //hides the savebuttons which are not needed here
            settings.Settings.ShowStatusBar = GridViewStatusBarMode.Hidden;
            settings.CallbackRouteValues = new
            {
                Controller = "KundendokumentUpdate",
                Action = "_StandortobjektForderungenGridView",
                standortId = ViewData["StandortID"]
            };

            settings.ClientSideEvents.BeginCallback = "function(s,e){" +
                "e.customArgs['forderungKundenbezuege'] = $('input[name=\"forderungKundenbezuege\"]').val();" +
            "}";

            //colums
            settings.Columns.Add(column =>
            {
                column.FieldName = "VersionsNummer";
                column.Caption = Resources.Allgemein_Version_Singular;
                column.ReadOnly = true;
                column.EditFormSettings.Visible = DefaultBoolean.False;
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "Beschreibung";
                column.Caption = Resources.Allgemein_Beschreibung;
                column.ReadOnly = true;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.Width = Unit.Percentage(50);

                column.ColumnType = MVCxGridViewColumnType.Memo;
                var memoProp = column.PropertiesEdit as MemoProperties;
                if (memoProp != null)
                {
                    memoProp.EncodeHtml = false;
                }
            });

            settings.Columns.Add(column =>
            {

                column.FieldName = "StandortObjektID";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
                column.MinWidth = 140;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ReadOnly = true;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.ColumnType = MVCxGridViewColumnType.ComboBox;
                column.Settings.FilterMode = ColumnFilterMode.DisplayText;
                var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;
                comboBoxProperties.DataSource = (Model.Select(x => new { x.StandortObjektID, x.StandortObjektTitel }).OrderBy(x => x.StandortObjektTitel)).Distinct().ToList();
                comboBoxProperties.TextField = "StandortObjektTitel";
                comboBoxProperties.ValueField = "StandortObjektID";
                comboBoxProperties.ValueType = typeof(int);
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "ErlassID";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
                column.MinWidth = 140;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ReadOnly = true;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.ColumnType = MVCxGridViewColumnType.ComboBox;
                column.Settings.FilterMode = ColumnFilterMode.DisplayText;
                var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;
                comboBoxProperties.DataSource = (Model.Select(x => new { x.ErlassID, x.ErlassTitel }).OrderBy(x => x.ErlassTitel)).Distinct().ToList();
                comboBoxProperties.TextField = "ErlassTitel";
                comboBoxProperties.ValueField = "ErlassID";
                comboBoxProperties.ValueType = typeof(int);
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "ErlassfassungID";
                column.Caption = Resources.Entitaet_Erlassfassung_Singular;
                column.ReadOnly = true;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.ColumnType = MVCxGridViewColumnType.ComboBox;
                column.Settings.FilterMode = ColumnFilterMode.DisplayText;
                // we only want the date to display not the date and time. The combox doesn't provide any options to format the 'textfield'
                IEnumerable<Erlassfassung>
                                erlassefassungen = unitOfWork.ErlassfassungRepository.Get();
                var datasource = new List<Object>();
                foreach (Erlassfassung erlassfassungCand in erlassefassungen)
                {
                    datasource.Add(new
                    {
                        erlassfassungCand.ErlassfassungID,
                        Inkrafttretung = erlassfassungCand.Inkrafttretung.ToString("d")
                    });
                }

                var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;
                if (comboBoxProperties != null)
                {
                    comboBoxProperties.DataSource = datasource;
                    comboBoxProperties.TextField = "Inkrafttretung";
                    comboBoxProperties.ValueField = "ErlassfassungID";
                    comboBoxProperties.ValueType = typeof(int);
                }
            });


            settings.Columns.Add(column =>
            {
                column.FieldName = "Inkrafttretung";
                column.ReadOnly = true;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.ColumnType = MVCxGridViewColumnType.DateEdit;
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "Aufhebung";
                column.ReadOnly = true;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.ColumnType = MVCxGridViewColumnType.DateEdit;
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "InternerKommentar";
                column.ReadOnly = true;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.Caption = Resources.Allgemein_InternerKommentar;
                column.Width = Unit.Percentage(25);

                column.ColumnType = MVCxGridViewColumnType.Memo;
                var memoProp = column.PropertiesEdit as MemoProperties;
                if (memoProp != null)
                {
                    memoProp.EncodeHtml = false;
                }
                column.SetDataItemTemplateContent(
                    container =>
                    {
                        var kommentar = (string)DataBinder.Eval(container.DataItem, "InternerKommentar");
                        if (!string.IsNullOrEmpty(kommentar))
                        {
                            ViewContext.Writer.Write(StringHelper.TruncateHtml(kommentar, 100));
                        }
                    }
                );
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "Kundenbezug";

            });
            settings.ClientSideEvents.BatchEditEndEditing = "OnBatchEditEnd";
            settings.HtmlDataCellPrepared += (sender, e) =>
            {
                var bewilligungspflichtig = Convert.ToBoolean(e.GetValue("Bewilligungspflicht"));
                var nachweispflichtig = Convert.ToBoolean(e.GetValue("Nachweispflicht"));
                if (bewilligungspflichtig)
                {
                    e.Cell.BackColor = System.Drawing.Color.Lavender;
                }
                if (nachweispflichtig)
                {
                    e.Cell.BackColor = System.Drawing.Color.BlanchedAlmond;
                }
                if (bewilligungspflichtig && nachweispflichtig)
                {
                    e.Cell.BackColor = System.Drawing.Color.PowderBlue;
                }
            };
            GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);

        });

}

@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = "ID";
}).GetHtml()