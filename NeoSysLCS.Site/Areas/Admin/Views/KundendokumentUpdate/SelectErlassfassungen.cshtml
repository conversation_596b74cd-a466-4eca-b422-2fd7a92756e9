@using System.Web.UI.WebControls
@using NeoSysLCS.Repositories
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers

@model IQueryable<NeoSysLCS.Repositories.ViewModels.KundendokumentErlassfassungViewModel>

@{
    ViewBag.Title = "Kundendokument: Erlassfassungen selektieren";
    ViewContext.Writer.Write("<h1 class='page-header'>" + ViewBag.Title + "</h1>");
}

@{
    UnitOfWork unitOfWork = new UnitOfWork();
    var standort = unitOfWork.StandortRepository.GetStanodrtById((int)ViewData["standortID"]);
    var kunde = unitOfWork.KundeRepository.GetByID(standort.KundeID);
}

@section breadcrumb{
    <a href="#" onclick="history.go(-1); return false;" title="Zurück"><i class="fa fa-arrow-left fa-lg"></i></a> |
    <span class="breadcrumb-noesys-navigation">
        <a href="/">@Resources.View_Breadcrumb_Home</a>
        <span> > </span>
        @Html.ActionLink(Resources.View_Breadcrumb_Kundenübersicht, "Index", "Kunden", new { area = "Admin" }, new { @class = "" })
        <span> > </span>
        @Html.ActionLink(Resources.View_Breadcrumb_KundendokumenteVonStandort.Replace("##KUNDE##", kunde.Name).Replace("##STANDORT##", standort.Name),
             "Index",
             "Kundendokumente",
             new { area = "Admin", id = standort.StandortID }, new { @class = "" }
        )
        <span> > </span>
        <span>@ViewBag.Title</span>
    </span>
}

<p>
    @Resources.View_Kundendokument_ErlassfassungenWaehlen
</p>


<script type="text/javascript">
    //<![CDATA[
    function OnCallbackError(s, e) {
        e.handled = true;
        console.log(e.message);
        alert("Es ist ein Fehler während der Datenübertragung aufgetreten. Bitte wiederholen Sie Ihre letzte Eingabe." + e.message);
    }

    // Required for speed-up of call backs (http://www.devexpress.com/Support/Center/Question/Details/Q569354)
    function OnSelectionChanged(s, e) {
        if (e.isChangedOnServer) return;


        //alert(s.cpSelectedKeys);

        //if something selected
        if (e.visibleIndex !== -1) {

            //get initialized values and add/remove selected forderungen and put it back into values
            
            var key = s.GetRowKey(e.visibleIndex);
            if (e.isSelected)
                s.cpSelectedKeys.push(key);
            else  {
                var index = s.cpSelectedKeys.indexOf(key);
                if (index > -1) {
                    s.cpSelectedKeys.splice(index, 1);
                }
            }

        //s.GetSelectedFieldValues('ErlassfassungID', OnGetSelectedFieldValues);

            if (typeof document.updateForm !== 'undefined') {
                document.updateForm.elements["selectedErlasssfassungenIDs"].value = s.cpSelectedKeys;
            } else {
                document.createForm.elements["selectedErlasssfassungenIDs"].value = s.cpSelectedKeys;
            }
        }


    }

    function OnGetSelectedFieldValues(values) {
        console.log("selected erlassfassungen ids: " + values);

        if (typeof document.updateForm !== 'undefined') {
            document.updateForm.elements["selectedErlasssfassungenIDs"].value = values;
        } else {
            document.createForm.elements["selectedErlasssfassungenIDs"].value = values;
        }
    }


   

    function OnSubmitFinal(s, e) {
        if (typeof document.updateForm !== 'undefined') {
            e.customArgs['selectedErlasssfassungenIDs'] = document.updateForm.elements['selectedErlasssfassungenIDs'].value;
        } else {
            e.customArgs['selectedErlasssfassungenIDs'] = document.createForm.elements['selectedErlasssfassungenIDs'].value;
        }      
    }

    /*function OnInitGrid(s,e) {
        if (typeof document.updateForm !== 'undefined') {
            document.updateForm.elements['selectedErlasssfassungenIDs'].value = s.GetSelectedKeysOnPage();
        } else {
            document.createForm.elements['selectedErlasssfassungenIDs'].value = s.GetSelectedKeysOnPage();
        } 
    }*/

    function OnBeginCallback(s, e) {
        //console.log(s.cpSelectedKeys);
        console.log("OnBeginCallback called");
        LoadingPanel.Show();
    }

    function OnEndCallback(s, e) {
        //on select all checkbox, reassign selected values
        //document.nextForm.elements["selectedForderungenIDs"].value = s.cpSelectedKeys;
        if (typeof document.updateForm !== 'undefined') {
            document.updateForm.elements['selectedErlasssfassungenIDs'].value = s.cpSelectedKeys;
        } else {
            document.createForm.elements['selectedErlasssfassungenIDs'].value = s.cpSelectedKeys;
        }
        console.log("OnEndCallback called");
        LoadingPanel.Hide();
        //var hasFilter = (s.cpFilterExpression && s.cpFilterExpression !== "") || s.cpIsGrouped > 0;

        //btnNext.SetEnabled(!hasFilter);
        //$('#alerthint').toggleClass('hide');

    }

    // ]]>
</script>
@Html.Partial("_HtmlEditPopup", false)

@Html.Partial("_SelectErlassfassungenGridView", Model)

@{
    if (SessionHelper.GetKundendokumentData((int)ViewData["StandortID"]).KundendokumentID == null)
    {
        // create new kundendokument
        using (Html.BeginForm("Create", "KundendokumentUpdate", FormMethod.Post, new { name = "createForm" }))
        {
            @Html.Hidden("StandortID", ViewData["StandortID"])
            @Html.Hidden("selectedErlasssfassungenIDs")

            @Html.DevExpress().Button(settings =>
            {
                settings.Name = "btnCreate";
                settings.Text = Resources.View_Kundendokument_Button_Erstellen;
                settings.ClientSideEvents.Click = "function(s, e) { cp.PerformCallback(); }";
                settings.UseSubmitBehavior = true;
                settings.ControlStyle.CssClass = "pull-right";
            }).GetHtml()

        }
    }
    else
    {
        <p>&nbsp;</p>

        <div class='alert alert-warning'>
            <p>
                Das Aktualisieren des Kundendokuments führt dazu, dass sämtliche Eingaben des Kunden (z.B. Erfüllungsstatus von Forderungen) verloren gehen!<br /> Davon ist aber lediglich DIESE VERSION des Kundendokumentes betroffen. Diese Meldung ist deshalb nur relevant, sollte DIESE VERSION des Kundendokumentes bereits einmalig dem Kunden freigegeben worden sein.
            </p>
        </div>

        //  update current kundendokument.
        using (Html.BeginForm("Update", "KundendokumentUpdate", FormMethod.Post, new { name = "updateForm" }))
        {
            @Html.Hidden("StandortID", ViewData["StandortID"])
            @Html.Hidden("selectedErlasssfassungenIDs")

            @Html.DevExpress().Button(settings =>
            {
                settings.Name = "btnUpdate";
                settings.Text = Resources.View_Kundendokument_Button_Aktualisieren;
                settings.ClientSideEvents.Click = "function(s, e) { cp.PerformCallback(); }";
                settings.UseSubmitBehavior = true;
                settings.ControlStyle.CssClass = "pull-right";
            }).GetHtml()
        }





    }

    // back button
    using (Html.BeginForm("StandortobjektForderungen", "KundendokumentUpdate", FormMethod.Post, new { name = "backForm" }))
    {
        @Html.Hidden("StandortID", ViewData["StandortID"])
        @Html.Hidden("selectedErlasssfassungenIDs")

        @Html.DevExpress().Button(settings =>
        {
            settings.Name = "btnBack";
            settings.Text = Resources.Navigation_Zurueck;
            settings.ClientSideEvents.Click = "function(s, e) { cp.PerformCallback(); }";
            settings.UseSubmitBehavior = true;
            settings.ControlStyle.CssClass = "pull-right";
        }).GetHtml()
        @Html.DevExpress().LoadingPanel(
        settings =>
        {
            settings.Name = "LoadingPanel";
            settings.Modal = true;
        }).GetHtml()

        @Html.DevExpress().CallbackPanel(cpsettings =>
        {
            cpsettings.Name = "cp";
            cpsettings.CallbackRouteValues = new { Controller = "KundendokumentUpdate", Action = "CallbackPanelPartialErlassfassungen", StandortID = Convert.ToInt32(ViewData["StandortID"]) };
            cpsettings.ClientSideEvents.BeginCallback = "OnSubmitFinal";

        }).GetHtml()

    }
}