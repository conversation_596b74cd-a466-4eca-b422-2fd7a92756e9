@using NeoSysLCS.Repositories;
@using System.Drawing;
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Site.Helpers


@{
    var grid = Html.DevExpress().GridView(settings =>
    {
        UnitOfWork unitOfWork = new UnitOfWork();

        settings.Name = "ObjektkategorienGridView";

        GridViewHelper.ApplyDefaultSettings(settings);

        settings.StylesEditors.ReadOnly.BackColor = Color.FromArgb(230, 230, 230);


        settings.CallbackRouteValues = new
        {
            Controller = "Objektkategorien",
            Action = "ObjektkategorienGridView",
            ObjektkategorieID = ViewData["ObjektkategorieID"]
        };

        settings.SettingsEditing.BatchUpdateRouteValues = new
        {
            Controller = "Objektkategorien",
            Action = "ObjektkategorienGridViewBatchEditUpdate"
        };

        if (User.IsIn<PERSON>ole(Role.Admin))
        {
            settings.SettingsEditing.Mode = GridViewEditingMode.Batch;

            settings.CommandColumn.Visible = true;
            settings.CommandColumn.ShowNewButtonInHeader = true;
            settings.CommandColumn.ShowDeleteButton = false;
            settings.CommandColumn.ShowEditButton = false;
        }
        settings.KeyFieldName = "ObjektkategorieID";

        settings.SettingsPager.Visible = false;
        settings.Settings.ShowGroupPanel = false;
        settings.Settings.ShowFilterRow = false;
        settings.Settings.ShowFilterRowMenu = false;
        settings.Settings.ShowFilterBar = GridViewStatusBarMode.Auto;
        settings.SettingsBehavior.AllowSelectByRowClick = false;

        MVCxGridViewColumn nameCol = settings.Columns.Add("Name");
        settings.SettingsBehavior.AllowSort = false;
        settings.SettingsDetail.ShowDetailRow = true;

        settings.SetDetailRowTemplateContent(c =>
        {
            Html.RenderAction("ObjektkategorieUebersetzungenGridViewBatchEditingUpdate",
                new { ObjektkategorieID = ViewData["ObjektkategorieID"] });
        });

        settings.PreRender = (sender, e) =>
        {
            ((MVCxGridView)sender).DetailRows.ExpandAllRows();
        };

    });

    if (ViewData["EditError"] != null)
    {
        grid.SetEditErrorText((string)ViewData["EditError"]);
    }
}
@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = "ObjektkategorieID";
}).GetHtml()

