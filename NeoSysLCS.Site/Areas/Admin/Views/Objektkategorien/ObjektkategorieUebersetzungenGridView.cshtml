@using NeoSysLCS.DomainModel.Models;
@using NeoSysLCS.Repositories;
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers



@Html.DevExpress().GridView(
    settings =>
    {
        UnitOfWork unitOfWork = new UnitOfWork();

        GridViewHelper.ApplyUebersetzungsSettings(settings);

        settings.Name = "ObjektkategorieUebersetzungenGridView_" + ViewData["ObjektID"];
        settings.KeyFieldName = "ObjektkategorieUebersetzungID";
        settings.CallbackRouteValues = new
        {
            Controller = "Objektkategorie",
            Action = "ObjektkategorieUebersetzungenGridView",
            ObjektkategorieID = ViewData["ObjektkategorieID"]
        };
        settings.SettingsEditing.Mode = GridViewEditingMode.Batch;
        settings.SettingsEditing.BatchUpdateRouteValues = new
        {
            Controller = "Objektkategorien",
            Action = "ObjektkategorieUebersetzungenGridViewBatchEditingUpdate",
            ObjektkategorieID = ViewData["ObjektkategorieID"]
        };

        settings.CommandColumn.Visible = false;

        settings.Columns.Add("Name");

        settings.Columns.Add(column =>
        {
            column.FieldName = "SpracheID";
            column.Caption = "Sprache";
            column.ReadOnly = true;
            column.ColumnType = MVCxGridViewColumnType.ComboBox;
            column.Settings.FilterMode = ColumnFilterMode.DisplayText;

            var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;

            comboBoxProperties.DataSource = unitOfWork.SpracheRepository.Get().ToList();
            comboBoxProperties.TextField = "Name";
            comboBoxProperties.ValueField = "SpracheID";
            comboBoxProperties.ValueType = typeof(int);
        });

        settings.SetStatusBarTemplateContent(c =>
        {
            ViewContext.Writer.Write("<div style='text-align: right'>");

            Html.DevExpress().HyperLink(hlSettings =>
            {
                hlSettings.Name = "hlSave";
                hlSettings.Properties.Text = Resources.Button_Speichern;
                hlSettings.Properties.ClientSideEvents.Click = "function(s, e){ ObjektkategorieUebersetzungenGridView_.UpdateEdit();}";
            }).Render();
            ViewContext.Writer.Write(" ");

            Html.DevExpress().HyperLink(hlSettings =>
            {
                hlSettings.Name = "hlCancel";
                hlSettings.Properties.Text = Resources.Button_Abbrechen;
                hlSettings.Properties.ClientSideEvents.Click = "function(s, e){ ObjektkategorieUebersetzungenGridView_.CancelEdit(); ObjektkategorienTreeList.CancelEdit();}";
            }).Render();

            ViewContext.Writer.Write("</div>");
        });

        settings.ClientSideEvents.BatchEditStartEditing = @"function(s,e){
                currentGrid = s;
            }";

        settings.ClientSideEvents.ClipboardCellPasting = @"function(s,e){
                e.cancel = currentGrid != s;
            }";
    }).Bind(Model).GetHtml()