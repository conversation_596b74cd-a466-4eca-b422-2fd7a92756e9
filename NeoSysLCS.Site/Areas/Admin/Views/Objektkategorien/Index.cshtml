@using NeoSysLCS.Repositories.Helper
@model IEnumerable<NeoSysLCS.Repositories.ViewModels.ObjektkategorieViewModel>



@{
    ViewBag.Title = "Objektkategorien";
    ViewContext.Writer.Write("<h1 class='page-header'>" + ViewBag.Title + "</h1>");
}


@if (User.IsInRole(Role.ObjectGuru))
{

    <script type="text/javascript">
        // <![CDATA[
        function InitPopupMenuHandler(s, e) {
            $("#ObjektkategorienTreeList_U").bind("contextmenu", OnGridContextMenu);
        }

        function OnGridContextMenu(s, evt) {
            PopupMenu.ShowAtPos(evt.htmlEvent.pageX, evt.htmlEvent.pageY);
            var nodekey = evt.objectKey;
            var nodetype = evt.objectType;
            if (nodetype != "Node") {
                $('#' + PopupMenu.GetItemElementId(0)).show();
                $('#' + PopupMenu.GetItemElementId(1)).hide();
                $('#' + PopupMenu.GetItemElementId(2)).hide();
            } else {
                $('#' + PopupMenu.GetItemElementId(1)).show();
                $('#' + PopupMenu.GetItemElementId(2)).show();
                $('#' + PopupMenu.GetItemElementId(0)).hide();
            }
            OnPreventContextMenu(evt.htmlEvent);
            PopupMenu.OnItemClick = function (s, e) {
                if (s == "0") {
                    if (nodetype == "Node") {
                        ObjektkategorienTreeList.StartEditNewNode(nodekey);
                    } else {
                        ObjektkategorienTreeList.StartEditNewNode();
                    }
                }
                else if (s == "1") {
                    if (nodetype == "Node") {
                        ObjektkategorienTreeList.StartEdit(nodekey);
                    }
                } else if (s == "2") {
                    if (nodetype == "Node") {
                        if (ObjektkategorienTreeList.confirmDeleteMsg != null && !confirm(ObjektkategorienTreeList.confirmDeleteMsg)) {
                            return;
                        }
                        ObjektkategorienTreeList.DeleteNode(nodekey);
                    }


                }
                PopupMenu.Hide();
            }
        }

        function OnNewButtonClick(s, e) {
            ObjektkategorienTreeList.StartEditNewNode();
        }

        function OnPreventContextMenu(evt) {
            evt.preventDefault();
        }

        // ]]>
    </script>

    Html.DevExpress().PopupMenu(settings =>
    {
        settings.Name = "PopupMenu";
        settings.ShowPopOutImages = DefaultBoolean.True;
        settings.Items.Add(item =>
        {
            item.Text = "Neue Kategorie";
            item.Name = "New";
            item.GroupName = "Edit";
            item.Image.Width = 15;
            item.Image.Url = "/Content/images/pencil-26.png";
        });
        settings.Items.Add(item =>
        {
            item.Text = "Kategorie bearbeiten";
            item.Name = "Edit";
            item.GroupName = "Edit";
            item.Image.Width = 15;
            item.Image.Url = "/Content/images/edit-26.png";
        });
        settings.Items.Add(item =>
        {
            item.Text = "Kategorie löschen";
            item.Name = "Delete";
            item.GroupName = "Edit";
            item.Image.Width = 15;
            item.Image.Url = "/Content/images/delete_sign-26.png";

        });


    }).GetHtml();


    <div class="btn-new-category">
        @Html.DevExpress().Button(settings =>
              {
                  settings.ClientSideEvents.Click = "OnNewButtonClick";
                  settings.Name = "NewNode";
                  settings.Text = " Neue Kategorie";
                  settings.Images.Image.Url = "/Content/images/pencil-26.png";
                  settings.Images.Image.Width = 12;
                  settings.Styles.Style.Paddings.PaddingLeft = 0;
                  settings.Styles.Style.Paddings.PaddingRight = 0;

              }).GetHtml()

    </div>
}
@Html.Partial("ObjektkategorienTreeList", Model)

@if (User.IsInRole(Role.ObjectGuru))
{
    <p class="help-block">Zum Bearbeiten der Kategorien, klicken Sie mit der rechten Maustaste auf die Kategorie.</p>
}