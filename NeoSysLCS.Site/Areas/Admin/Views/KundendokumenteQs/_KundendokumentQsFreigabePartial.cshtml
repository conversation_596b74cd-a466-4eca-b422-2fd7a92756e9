@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Resources.Properties

@model NeoSysLCS.Repositories.ViewModels.KundendokumentViewModel

@using (Ajax.BeginForm("SaveKundendokumenteQs", "KundendokumenteQs",
        FormMethod.Post,
        new AjaxOptions { UpdateTargetId = "resultQsFreigabe" },
        new { @class = "form-horizontal", role = "form", style = "min-height:350px;" }
    ))
{
    @*@Html.AntiForgeryToken()*@
    @Html.HiddenFor(m => m.KundendokumentID)

    if (Model.Status == KundendokumentStatus.Approved)
    {
        //already approved for the enduser
        <div class="alert alert-success">
            <p>
                @Resources.View_KundendokumentQs_AlreadyApproved.Replace(
                    "##PUBLIZIEREN_AM##", (!Model.PublizierenAm.HasValue ? "" : Model.PublizierenAm.Value.ToString("dd.MM.yyyy")))
            </p>
        </div>
    }

    if (!(bool)ViewData["QsFreigabePossible"] && Model.Status == KundendokumentStatus.InQs)
    {
        // the qs-freigabe can only be given, if all forderungen have the qs-freigabe
        <div class="alert alert-warning">
            <p>@Resources.View_KundendokumentQs_QSFreigabeNotYetPossible</p>
        </div>
    }


    <div class="form-group">
        @Html.LabelFor(m => m.HasQsFreigabe, new { @class = "col-lg-1 col-md-2 control-label" })
        <div class="col-md-10 checkbox">
            @if ((bool)ViewData["QsFreigabePossible"] && (Model.Status == KundendokumentStatus.InQs || Model.Status == KundendokumentStatus.QsApproved))
            {
                //qs-freigabe possible
                @Html.CheckBoxFor(m => m.HasQsFreigabe)
            }
            else
            {
                //qs-freigabe not yet or not anymore possible
                @Html.CheckBoxFor(m => m.HasQsFreigabe, new { disabled = "disabled" })
            }
        </div>
    </div>


    <!-- wenn nicht freigegeben kann Kommentar editiert werden -->
    if (!(bool) ViewData["QsFreigabePossible"] || (Model.Status != KundendokumentStatus.InQs && Model.Status != KundendokumentStatus.QsApproved))
    {
        using (Ajax.BeginForm("SaveComment",
            "KundendokumenteQs",
            FormMethod.Post,
            new AjaxOptions {UpdateTargetId = "resultCommentSave"},
            new {@class = "form-horizontal", role = "form", style = "min-height:350px;"}))
        {
            @*@Html.AntiForgeryToken()*@
            @Html.HiddenFor(m => m.KundendokumentID)
            <div class="form-group">
                @Html.LabelFor(m => m.QsKommentar, new {@class = "col-lg-1 col-md-2 control-label"})
                <div class="col-md-10">
                    @Html.TextAreaFor(m => m.QsKommentar, new {@class = "form-control", rows = 5})
                </div>
            </div>


            <div class="form-group">
                <div class="col-lg-offset-1 col-md-offset-2 col-md-10">
                    <input type="submit" class="btn btn-default" value="@Resources.Allgemein_AenderungenSpeichern"/>
                </div>
            </div>
            <div class="form-group">
                <div class="col-lg-offset-1 col-md-offset-2 col-md-10">
                    <div id="resultCommentSave"></div>
                </div>
            </div>
        }
    }
    else
    {
        <div class="form-group">
            @Html.LabelFor(m => m.QsKommentar, new { @class = "col-lg-1 col-md-2 control-label" })
            <div class="col-md-10">
                @Html.TextAreaFor(m => m.QsKommentar, new { @class = "form-control", rows = 5 })
            </div>
        </div>
    }

    <div class="form-group">
        <div class="col-lg-offset-1 col-md-offset-2 col-md-10">

            @if ((bool)ViewData["QsFreigabePossible"] && (Model.Status == KundendokumentStatus.InQs || Model.Status == KundendokumentStatus.QsApproved))
            {
                //qs-freigabe possible
                <input type="submit" class="btn btn-default" value="@Resources.Allgemein_AenderungenSpeichern" />
            }
        </div>
    </div>
    <div class="form-group">
        <div class="col-lg-offset-1 col-md-offset-2 col-md-10">
            <div id="resultQsFreigabe"></div>
        </div>
    </div>
}
