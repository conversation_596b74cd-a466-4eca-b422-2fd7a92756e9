@using System.Web.UI.WebControls
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers

@Html.DevExpress().PopupControl(
    settings =>
    {
        settings.Name = "popupHtmlEditor";
        settings.Modal = true;
        settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
        settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
        settings.HeaderText = "Kommentar bearbeiten";

        settings.AllowResize = true;
        settings.AllowDragging = true;
        settings.ClientSideEvents.Shown = @"function(s,e){
                htmlEditorKommentar.Focus();
            }";
        settings.SetContent(() =>
        {
            Html.DevExpress().HtmlEditor(editorSettings =>
                {
                    editorSettings.Name = "htmlEditorKommentar";
                    HtmlEditorHelper.ApplyHtmlEditorSettings(editorSettings);
                    editorSettings.Height = Unit.Pixel(400);
                    editorSettings.Width = Unit.Pixel(600);
                    editorSettings.Enabled = false;
                }).GetHtml();
            Html.DevExpress().Button(
                buttonSettings =>
                {
                    buttonSettings.Name = "btnClose";
                    buttonSettings.ControlStyle.CssClass = "button";
                    buttonSettings.Width = 80;
                    buttonSettings.Text = Resources.Button_Schliessen;
                    buttonSettings.ClientSideEvents.Click = "function(s, e){ popupHtmlEditor.Hide(); }";
                }
            ).GetHtml();
        });

    }
).GetHtml()

