@using System.Web.UI.WebControls
@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Resources.Properties

@model  KundendokumentViewModel

@Html.DevExpress().PageControl(pageControlSettings =>
{
    pageControlSettings.Name = "KundendokumentPageControl";
    pageControlSettings.Width = Unit.Percentage(100);

    pageControlSettings.SaveStateToCookies = true;

    //reload active tab in order to keep the state up-to-date
    pageControlSettings.ClientSideEvents.ActiveTabChanging = "function (s,e) { e.reloadContentOnCallback = true; }";
    pageControlSettings.CallbackRouteValues = new { Controller = "KundendokumenteQs", Action = "KundendokumenteQsPageControl", id = Model.KundendokumentID };

    pageControlSettings.TabPages.Add(Resources.View_KundendokumentQs_TabErlassfassungen).SetContent(() =>
    {
        if (Model.Status == KundendokumentStatus.QsApproved)
        {
            ViewContext.Writer.Write("<div class='alert alert-success'><p>");
            ViewContext.Writer.Write(Resources.View_KundendokumentQS_AlreadyQsFreigabe);
            ViewContext.Writer.Write("</p></div>");
        }
        else if (Model.Status == KundendokumentStatus.Approved)
        {
            ViewContext.Writer.Write("<div class='alert alert-success'><p>");
            ViewContext.Writer.Write(Resources.View_KundendokumentQs_AlreadyApproved.Replace(
                "##PUBLIZIEREN_AM##", (!Model.PublizierenAm.HasValue ? "" : Model.PublizierenAm.Value.ToString("dd.MM.yyyy"))));
            ViewContext.Writer.Write("</p></div>");
        }
        else
        {
            if (User.IsInRole(Role.ProjectManager))
            {
                ViewContext.Writer.Write("<div class='alert alert-info'><p>");
                ViewContext.Writer.Write("Möchten Sie alle Erlassversionen zusammen freigeben?</p><p>&nbsp;</p>");
                Html.DevExpress().Button(
                    settings =>
                    {
                        settings.Name = "CreateKundendokumentErlassversionen";
                        settings.Text = "Alle Erlassversionen freigeben";
                        settings.RouteValues = new
                        {
                            Controller = "KundendokumenteQs",
                            Action = "ApproveAllErlassversionen",
                            standortID = ViewData["StandortID"],
                            kundendokumentID = ViewData["KundendokumentID"]
                        };
                    }
                    ).GetHtml();
                ViewContext.Writer.Write("</div>");
            }
        }

        Html.RenderAction(
            "KundendokumentErlassfassungenGridView",
            "KundendokumenteQs",
            new { standortID = ViewData["StandortID"], kundendokumentID = ViewData["KundendokumentID"] });
    });

    pageControlSettings.TabPages.Add(Resources.View_KundendokumentQs_TabForderungen).SetContent(() =>
    {
        if (Model.Status == KundendokumentStatus.QsApproved)
        {
            ViewContext.Writer.Write("<div class='alert alert-success'><p>");
            ViewContext.Writer.Write(Resources.View_KundendokumentQS_AlreadyQsFreigabe);
            ViewContext.Writer.Write("</p></div>");
        }
        else if (Model.Status == KundendokumentStatus.Approved)
        {
            ViewContext.Writer.Write("<div class='alert alert-success'><p>");
            ViewContext.Writer.Write(Resources.View_KundendokumentQs_AlreadyApproved.Replace(
                "##PUBLIZIEREN_AM##", (!Model.PublizierenAm.HasValue ? "" : Model.PublizierenAm.Value.ToString("dd.MM.yyyy"))));
            ViewContext.Writer.Write("</p></div>");
        }
        else
        {
            if (User.IsInRole(Role.ProjectManager))
            {
                ViewContext.Writer.Write("<div class='alert alert-info'><p>");
                ViewContext.Writer.Write("Möchten Sie alle Forderungen zusammen freigeben?</p><p>&nbsp;</p>");
                Html.DevExpress().Button(
                    settings =>
                    {
                        settings.Name = "CreateKundendokumentForderungen";
                        settings.Text = "Alle Forderungen freigeben";
                        settings.RouteValues = new
                        {
                            Controller = "KundendokumenteQs",
                            Action = "ApproveAllForderungen",
                            standortID = ViewData["StandortID"],
                            kundendokumentID = ViewData["KundendokumentID"]
                        };
                    }
                    ).GetHtml();
                ViewContext.Writer.Write("</div>");
            }
        }


        Html.RenderAction(
            "KundendokumentForderungenGridView",
            "KundendokumenteQs",
            new { standortID = ViewData["StandortID"], kundendokumentID = ViewData["KundendokumentID"] });
    }
    );
    //Die Pflichten werden im Moment nicht genutzt --> Schriit wird ausgeblendet //Patrick Schmed, 26.06.2017
    //pageControlSettings.TabPages.Add(Resources.View_KundendokumentQs_TabPflichten).SetContent(() =>
    //{
    //    if (Model.Status == KundendokumentStatus.QsApproved)
    //    {
    //        ViewContext.Writer.Write("<div class='alert alert-success'><p>");
    //        ViewContext.Writer.Write(Resources.View_KundendokumentQS_AlreadyQsFreigabe);
    //        ViewContext.Writer.Write("</p></div>");
    //    }
    //    else if (Model.Status == KundendokumentStatus.Approved)
    //    {
    //        ViewContext.Writer.Write("<div class='alert alert-success'><p>");
    //        ViewContext.Writer.Write(Resources.View_KundendokumentQs_AlreadyApproved.Replace(
    //            "##PUBLIZIEREN_AM##", (!Model.PublizierenAm.HasValue ? "" : Model.PublizierenAm.Value.ToString("dd.MM.yyyy"))));
    //        ViewContext.Writer.Write("</p></div>");
    //    }

    //    Html.RenderAction(
    //        "KundendokumentPflichtenGridView",
    //        "KundendokumenteQs",
    //        new { standortID = ViewData["StandortID"], kundendokumentID = ViewData["KundendokumentID"] });
    //});


    pageControlSettings.TabPages.Add("Geänderte Einträge").SetContent(() =>
    {
        ViewContext.Writer.Write("<h2>Geänderte Einträge<h2/>");
        Html.RenderAction(
             "KundendokumentForderungenGridView",
             "KundendokumenteQs",
             new { standortID = ViewData["StandortID"], kundendokumentID = ViewData["KundendokumentID"], showOnlyRemoved = true });
        //Die Pflichten werden im Moment nicht genutzt --> Schriit wird ausgeblendet //Patrick Schmed, 26.06.2017
        //ViewContext.Writer.Write("<h2>" + Resources.View_KundendokumentQs_RemovedPflichten + "<h2/>");
        //Html.RenderAction(
        //    "KundendokumentPflichtenGridView",
        //    "KundendokumenteQs",
        //    new { standortID = ViewData["StandortID"], kundendokumentID = ViewData["KundendokumentID"], showOnlyRemoved = true });

    });

    if (User.IsInRole(Role.ProjectManager))
    {
        pageControlSettings.TabPages.Add(Resources.View_KundendokumentQs_TabQsFreigabe).SetContent(() =>
            Html.RenderPartial("_KundendokumentQsFreigabePartial", Model)
            );

        pageControlSettings.TabPages.Add(Resources.View_KundendokumentQs_TabFreigabe).SetContent(() =>
            Html.RenderPartial("_KundendokumentFreigabePartial", Model)
            );
    }

}).GetHtml()

