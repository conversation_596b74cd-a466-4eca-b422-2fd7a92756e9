@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Resources.Properties

@model NeoSysLCS.Repositories.ViewModels.KundendokumentViewModel

@if ((bool)ViewData["FreigabePossible"])
{
    if (Model.Status == KundendokumentStatus.Approved)
    {
        <div class="alert alert-success">
            <p>
                @Resources.View_KundendokumentQs_AlreadyApproved.Replace(
                    "##PUBLIZIEREN_AM##", (!Model.PublizierenAm.HasValue ? "" : Model.PublizierenAm.Value.ToString("dd.MM.yyyy")))
            </p>
        </div>
    }

    using (Ajax.BeginForm("SaveKundendokumenteFreigabe",
        "KundendokumenteQs",
        FormMethod.Post,
        new AjaxOptions { UpdateTargetId = "resultFreigabe" },
        new { @class = "form-horizontal", role = "form", style = "min-height:350px;" }))
    {
        @*@Html.AntiForgeryToken()*@
        @Html.HiddenFor(m => m.KundendokumentID)
        <div class="form-group">
            @Html.LabelFor(m => m.<PERSON>F<PERSON>igabe, new { @class = "col-lg-1 col-md-2 control-label" })
            <div class="col-md-10 checkbox">
                @Html.CheckBoxFor(m => m.HasFreigabe)
            </div>
        </div>
        <div class="form-group">
            @Html.LabelFor(m => m.PublizierenAm, new { @class = "col-lg-1 col-md-2 control-label" })
            <div class="col-md-10 checkbox">
                @Html.DevExpress().DateEditFor(m => m.PublizierenAm, settings =>
                {
                    if (Model.PublizierenAm == null)
                    {
                        Model.PublizierenAm = DateTime.Now;
                    }
                    settings.Properties.ValidationSettings.CausesValidation = true;
                    settings.Properties.ValidationSettings.RequiredField.IsRequired = true;
                }).GetHtml()
            </div>
        </div>
        <div class="form-group">
            <div class="col-lg-offset-1 col-md-offset-2 col-md-10">
                <input type="submit" class="btn btn-default" value="@Resources.Allgemein_AenderungenSpeichern" />
            </div>
        </div>
        <div class="form-group">
            <div class="col-lg-offset-1 col-md-offset-2 col-md-10">
                <div id="resultFreigabe"></div>
            </div>
        </div>
    }
}
else
{
    <div class="alert alert-warning">
        @Resources.View_KundendokumentQs_ApprovalNotYetPossible
    </div>

}