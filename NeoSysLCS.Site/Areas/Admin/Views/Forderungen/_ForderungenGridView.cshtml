@using System.Web.UI.WebControls
@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories;
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Areas.Admin.Controllers
@using NeoSysLCS.Site.Helpers

@model IQueryable<NeoSysLCS.Repositories.ViewModels.ForderungsversionViewModel>

@{

    var grid = Html.DevExpress().GridView(
        settings =>
        {
            IUnitOfWork unitOfWork = new UnitOfWork();
            Erlassfassung erlassfassung = unitOfWork.ErlassfassungRepository.GetByID(ViewData["ErlassfassungID"]);

            settings.Name = ForderungenController.BaseGridViewSettings.GetGridViewName();
            GridViewHelper.ApplyDefaultSettings(settings);


            settings.KeyFieldName = ForderungenController.BaseGridViewSettings.GetGridViewKeyFieldName();

            settings.CallbackRouteValues = new { Controller = "Forderungen", Action = "ForderungenGridView", id = ViewData["ErlassfassungID"] };

            settings.SettingsEditing.BatchUpdateRouteValues = new { Controller = "Forderungen", Action = "ForderungenGridViewBatchEditUpdate", id = ViewData["ErlassfassungID"] };


            if (User.IsInRole(Role.ProjectManager))
            {
                settings.SettingsEditing.Mode = GridViewEditingMode.Batch;
                settings.CommandColumn.Visible = true;
                settings.CommandColumn.ShowNewButtonInHeader = true;
                settings.CommandColumn.ShowDeleteButton = true;
                settings.CommandColumn.ShowEditButton = false;
            }

            //columns
            foreach (MVCxGridViewColumn column in ForderungenController.BaseGridViewSettings.GetDataColumns(erlassfassung.ErlassfassungID, User.IsInRole(Role.ProjectManager)))
            {
                settings.Columns.Add(column);

                switch (column.Name)
                {
                    case "NewVersion":
                        {
                            column.SetDataItemTemplateContent(container =>
                            {
                                var erlassfassungId = DataBinder.Eval(container.DataItem, "ErlassfassungID");
                                var nachfolgeversionId = DataBinder.Eval(container.DataItem, "NachfolgeversionID");

                                var test = ViewData["ErlassfassungID"];

                                if (erlassfassungId != null && !erlassfassungId.Equals(ViewData["ErlassfassungID"]))
                                {
                                    if (nachfolgeversionId == null)
                                    {
                                        Html.DevExpress().HyperLink(hyperlink =>
                                        {
                                            var keyValue = container.KeyValue;
                                            hyperlink.Name = "Neue Version" + keyValue;
                                            hyperlink.Properties.Text = Resources.Allgemein_Neue_Version;
                                            hyperlink.NavigateUrl = Url.Action("NewVersion", "Forderungen", new { id = keyValue, erlassfassungId = ViewData["ErlassfassungID"] });
                                        }).Render();
                                    }
                                }
                            });
                            break;
                        }
                    case "Assignements":
                        {
                            column.SetDataItemTemplateContent(container =>
                            {

                                Html.DevExpress().HyperLink(hyperlink =>
                                {
                                    var keyValue = container.KeyValue;
                                    hyperlink.Name = "Objekte" + keyValue;
                                    int integerKeyValue = 0;
                                    if (keyValue != null)
                                    {
                                        integerKeyValue = Convert.ToInt32(keyValue);
                                    }
                                    hyperlink.Properties.Text = Resources.Entitaet_Objekt_Plural + " (" + Model.Where(x => x.ForderungsversionID == integerKeyValue).Select(k => k.objekteCount).ToList().FirstOrDefault() + ")";
                                    hyperlink.NavigateUrl = Url.Action("Index", "ForderungObjekte", new { id = keyValue, erlassfassungId = ViewData["ErlassfassungID"] });

                                }).Render();
                                ViewContext.Writer.Write("<br>");


                                Html.DevExpress().HyperLink(hyperlink =>
                                {
                                    var keyValue = container.KeyValue;
                                    hyperlink.Name = "Rechtsbereiche" + keyValue;
                                    int integerKeyValue = 0;
                                    if (keyValue != null)
                                    {
                                        integerKeyValue = Convert.ToInt32(keyValue);
                                    }
                                    hyperlink.Properties.Text = Resources.Entitaet_Rechtsbereich_Plural + " (" + Model.Where(x => x.ForderungsversionID == integerKeyValue).Select(k => k.rechtsbereichCount).ToList().FirstOrDefault() + ")";
                                    hyperlink.NavigateUrl = Url.Action("Index", "ForderungRechtsbereiche", new { id = keyValue, erlassfassungId = ViewData["ErlassfassungID"] });

                                }).Render();
                            });
                            break;
                        }
                    case "ErlassfassungInkrafttretung":
                        {
                            column.SetDataItemTemplateContent(
                              container =>
                              {
                                  var erlassfassungId = DataBinder.Eval(container.DataItem, "ErlassfassungID");

                                  if (erlassfassungId != null && !erlassfassungId.Equals(ViewData["ErlassfassungID"]))
                                  {
                                      var erlassfassungInkrafttretung = (DateTime)DataBinder.Eval(container.DataItem, "ErlassfassungInkrafttretung");

                                      Html.DevExpress().HyperLink(hyperlink =>
                                      {
                                          var keyValue = container.KeyValue;
                                          hyperlink.Name = "Definition" + keyValue;
                                          hyperlink.Properties.Text = Resources.Entitaet_Erlassfassung_Singular + ": " + erlassfassungInkrafttretung.ToShortDateString();
                                          hyperlink.NavigateUrl = Url.Action("Index", "Forderungen", new { id = erlassfassungId });
                                      }).Render();
                                  }
                                  else if (erlassfassungId != null && erlassfassungId.Equals(ViewData["ErlassfassungID"]))
                                  {
                                      ViewContext.Writer.Write("DIESE Fassung.");
                                  }
                              });
                            break;
                        }
                    case "VersionPlural":
                        {
                            column.SetDataItemTemplateContent(
                            container =>
                            {
                                var nachfolgeversionId = DataBinder.Eval(container.DataItem, "NachfolgeversionID");
                                var vorversionId = DataBinder.Eval(container.DataItem, "VorversionID");


                                if (vorversionId != null)
                                {
                                    var vorversion = unitOfWork.ForderungsversionRepository.GetByID(vorversionId);
                                    ViewContext.Writer.Write("(" + vorversion.VersionsNummer + ") <- ");
                                }

                                if (nachfolgeversionId != null)
                                {
                                    var nachfolgeversion = unitOfWork.ForderungsversionRepository.GetByID(nachfolgeversionId);
                                    ViewContext.Writer.Write("-> (" + nachfolgeversion.VersionsNummer + ")");
                                }
                            });
                            break;
                        }
                    case "Beschreibung":
                        {
                            column.Width = Unit.Percentage(50);
                            column.SetDataItemTemplateContent(
                                container =>
                                {
                                    var beschreibung = (string)DataBinder.Eval(container.DataItem, "Beschreibung");
                                    if (!string.IsNullOrEmpty(beschreibung))
                                    {
                                        ViewContext.Writer.Write(StringHelper.TruncateHtml(beschreibung, 100));
                                    }
                                }
                            );
                            break;
                        }
                    case "InternerKommentar":
                        {
                            column.SetDataItemTemplateContent(
                                container =>
                                {
                                    var kommentar = (string)DataBinder.Eval(container.DataItem, "InternerKommentar");
                                    if (!string.IsNullOrEmpty(kommentar))
                                    {
                                        ViewContext.Writer.Write(StringHelper.TruncateHtml(kommentar, 100));
                                    }
                                }
                            );
                            break;
                        }
                }
            }

            GridViewHelper.AddNewestFirstSortorderColumn(settings, "ForderungsversionID");
            GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);

            settings.ClientSideEvents.BatchEditStartEditing = @"function(s,e){
                currentGrid = s;
                if(s.batchEditApi.GetCellValue(e.visibleIndex, 'Bewilligungspflicht') == null){
                    s.batchEditApi.SetCellValue(e.visibleIndex, 'Bewilligungspflicht', false)
                }
                if(s.batchEditApi.GetCellValue(e.visibleIndex, 'Nachweispflicht') == null){
                    s.batchEditApi.SetCellValue(e.visibleIndex, 'Nachweispflicht', false)
                }
                if(s.batchEditApi.GetCellValue(e.visibleIndex, 'Inkrafttretung') == null){
                    var date = new Date(" + erlassfassung.Inkrafttretung.Year + "," + (erlassfassung.Inkrafttretung.Month - 1) + "," + erlassfassung.Inkrafttretung.Day + @");
                    s.batchEditApi.SetCellValue(e.visibleIndex, 'Inkrafttretung', date)
                }

                if(e.focusedColumn.fieldName == 'InternerKommentar' || e.focusedColumn.fieldName == 'Beschreibung'){
                    openPopupOnBatchEditBegin(s, e);
                }

            }";

            settings.ClientSideEvents.ClipboardCellPasting = @"function(s,e){
                e.cancel = currentGrid != s;
	        }";

            settings.HtmlDataCellPrepared += (sender, e) =>
            {
                var bewilligungspflichtig = Convert.ToBoolean(e.GetValue("Bewilligungspflicht"));
                var nachweispflichtig = Convert.ToBoolean(e.GetValue("Nachweispflicht"));
                if (bewilligungspflichtig)
                {
                    e.Cell.BackColor = System.Drawing.Color.Lavender;
                }
                if (nachweispflichtig)
                {
                    e.Cell.BackColor = System.Drawing.Color.BlanchedAlmond;
                }
                if (bewilligungspflichtig && nachweispflichtig)
                {
                    e.Cell.BackColor = System.Drawing.Color.PowderBlue;
                }

                //register popup event
                if (e.DataColumn.FieldName == "InternerKommentar" || e.DataColumn.FieldName == "Beschreibung")
                {
                    if (!User.IsInRole(Role.ProjectManager))
                    {
                        //readonly
                        e.Cell.Attributes.Add(
                                       "onclick",
                                       "openPopupReadonly(" + settings.Name + ", '" + e.DataColumn.FieldName + "', " + e.VisibleIndex + ", '" + e.DataColumn.Caption + "')"
                                       );

                    }
                }
            };

            //Detail
            settings.SettingsDetail.ShowDetailRow = true;
            settings.SetDetailRowTemplateContent(c =>
            {
                Html.RenderAction("ForderungsversionUebersetzungenGridView", new { erlassfassungId = ViewData["ErlassfassungID"], ForderungsversionID = DataBinder.Eval(c.DataItem, "ForderungsversionID") });
            });
        });
}

@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = ForderungenController.BaseGridViewSettings.GetGridViewKeyFieldName();
}).GetHtml()