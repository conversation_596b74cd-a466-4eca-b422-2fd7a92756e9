@using NeoSysLCS.Site.Areas.Admin.Controllers
@using NeoSysLCS.Site.Helpers
@model IQueryable<NeoSysLCS.Repositories.ViewModels.Objekt2KundenAssignmentViewModel>

@{
    var grid = Html.DevExpress().GridView(settings =>
    {
        settings.Name = AuswertungenController.Objekt2KundenAssignmentGridViewSettings.GetGridViewName();
        GridViewHelper.ApplyDefaultSettings(settings);
        settings.KeyFieldName = "ObjektID";
        settings.CallbackRouteValues = new { Controller = "Auswertungen", Action = "_Objekt2KundenAssignmentGridView" };
        settings.CommandColumn.Visible = true;
        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;
        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);
        settings.SettingsExport.EnableClientSideExportAPI = true;
        settings.SettingsExport.ExcelExportMode = DevExpress.Export.ExportType.WYSIWYG;
        foreach (MVCxGridViewColumn col in AuswertungenController.Objekt2KundenAssignmentGridViewSettings.GetDataColumns(ViewContext, Model))
        {
            settings.Columns.Add(col);
        }
    });

    if (ViewData["EditError"] != null)
    {
        grid.SetEditErrorText((string)ViewData["EditError"]);
    }
}
@grid.Bind(Model.ToList()).GetHtml()
@*@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = "ObjektID";
}).GetHtml()*@
