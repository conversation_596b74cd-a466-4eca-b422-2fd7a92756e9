@using System.Web.UI.WebControls
@using NeoSysLCS.Repositories;
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Site.Helpers
@using System.Globalization;

@Html.DevExpress().GridView(
    settings =>
    {
        UnitOfWork unitOfWork = new UnitOfWork();

        GridViewHelper.ApplyUebersetzungsSettings(settings);
        var sprache = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
        settings.Name = "KundeninformationUebersetzungenGridView";
        settings.KeyFieldName = "AllgemeineKundeninformationID";

        settings.CallbackRouteValues = new { Controller = "Kundeninformation", Action = "KundeninformationUebersetzungenGridView" };
        settings.SettingsEditing.BatchUpdateRouteValues = new { Controller = "Kundeninformation", Action = "KundeninformationUebersetzungenGridViewBatchEditUpdate" };

        if (User.IsIn<PERSON>ole(Role.Admin))
        {
            settings.SettingsEditing.Mode = GridViewEditingMode.Batch;
        }

        settings.CommandColumn.Visible = false;

        settings.Columns.Add(column =>
        {
            column.FieldName = "Text";
            column.Caption = "Text";
            column.Width = Unit.Percentage(80);
            
            column.ColumnType = MVCxGridViewColumnType.Memo;
            var memoProp = column.PropertiesEdit as MemoProperties;
            memoProp.EncodeHtml = false;
            column.ReadOnly = true;
            var prop = (column.PropertiesEdit as EditProperties);
            if (prop != null)
            {
                prop.ValidationSettings.Display = Display.Dynamic;
            }
            column.SetDataItemTemplateContent(
                container =>
                {
                    var text = (string)DataBinder.Eval(container.DataItem, "Text");
                    if (!string.IsNullOrEmpty(text))
                    {
                        ViewContext.Writer.Write(StringHelper.TruncateHtml(text, 50));
                    }
                }
            );
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "SpracheID";
            column.Caption = "Sprache";
            column.ReadOnly = true;
            column.ColumnType = MVCxGridViewColumnType.ComboBox;
            column.Settings.FilterMode = ColumnFilterMode.DisplayText;
            var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;

            comboBoxProperties.DataSource = unitOfWork.SpracheRepository.Get().ToList();
            comboBoxProperties.TextField = "Name";
            comboBoxProperties.ValueField = "SpracheID";
            comboBoxProperties.ValueType = typeof(int);
        });

        settings.HtmlDataCellPrepared += (sender, e) =>
        {
            if (e.DataColumn.FieldName == "Text")
            {
                if (!User.IsInRole(Role.ProjectManager))
                {
                    //readonly
                    e.Cell.Attributes.Add(
                       "onclick",
                       "openPopupReadonly(" + settings.Name + ", '" + e.DataColumn.FieldName + "', " + e.VisibleIndex + ", '" + e.DataColumn.Caption + "')"
                       );

                }
            }
        };

        settings.ClientSideEvents.BatchEditStartEditing = @"function(s,e){
		    if(e.focusedColumn.fieldName == 'Text'){
					    openPopupOnBatchEditBegin(s, e);
				    }
	        }";

        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);
    }).Bind(Model).GetHtml()