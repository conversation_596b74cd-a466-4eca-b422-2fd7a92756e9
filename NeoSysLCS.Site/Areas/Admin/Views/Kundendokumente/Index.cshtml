@using NeoSysLCS.Repositories
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Resources.Properties

@model  IQueryable<KundendokumentViewModel>

@{
    UnitOfWork unitOfWork = new UnitOfWork();

    var standort = unitOfWork.StandortRepository.GetStanodrtById((int)ViewData["standortID"]);
    var kunde = unitOfWork.KundeRepository.GetByID(standort.KundeID);
    ViewBag.Title = Resources.View_Breadcrumb_KundendokumenteVonStandort.Replace("##KUNDE##", kunde.Name).Replace("##STANDORT##", standort.Name);
    ViewContext.Writer.Write("<h1 class='page-header'>" + ViewBag.Title + "</h1>");
}

@section breadcrumb{
    <a href="#" onclick="history.go(-1); return false;" title="Zurück"><i class="fa fa-arrow-left fa-lg"></i></a> |
    <span class="breadcrumb-noesys-navigation">
        <a href="/">@Resources.View_Breadcrumb_Home</a>
        <span> > </span>
        @Html.ActionLink(Resources.View_Breadcrumb_Kundenübersicht, "Index", "Kunden", new { area = "Admin", expanded = standort.KundeID }, new { @class = "" })
        <span> > </span>
        <span>@ViewBag.Title</span>
    </span>
}

@{
    if (User.IsInRole(Role.ProjectManager))
    {

        if (ViewData["CreateKundendokumentError"] != null)
        {
            <div class='alert alert-danger'>
                <p>
                    @ViewData["CreateKundendokumentError"]
                </p>
            </div>
        }

        if (Model.Count() == 0)
        {
            <div class='alert alert-info'>
                <p>
                    @Resources.View_Kundendokument_NochKeinKundendokument
                <p>

                    @Html.DevExpress().Button(
                    settings =>
                    {
                        settings.Name = "CreateKundendokument";
                        settings.Text = Resources.View_Kundendokument_Button_Erstellen;
                        settings.RouteValues = new { Controller = "KundendokumentUpdate", Action = "KundendokumentCreateInitialVersion", standortId = standort.StandortID };
                    }
                    ).GetHtml()
                </p>
            </div>
        }
        else if (Model.ToList().FindAll(kundendokument => kundendokument.HasFreigabe == false).Any())
        {
            <div class='alert alert-info'>
                <p>
                    @Resources.View_Kundendokument_QsFreigabeAusstehen
                <p>
            </div>
        }
        else
        {
            <div class='alert alert-info'>
                <p>
                    @Resources.View_Kundendokument_KundendokumentAktualisieren
                <p>

                    @Html.DevExpress().Button(
                settings =>
                {
                    settings.Name = "CreateKundendokument";
                    settings.Text = Resources.View_Kundendokument_Button_Aktualisieren;
                    settings.RouteValues = new { Controller = "KundendokumentUpdate", Action = "KundendokumentCreateUpdate", standortId = standort.StandortID };
                }
                ).GetHtml()
            </div>
        }
    }
    @Html.Partial("_KundendokumenteGridView", Model)

}

