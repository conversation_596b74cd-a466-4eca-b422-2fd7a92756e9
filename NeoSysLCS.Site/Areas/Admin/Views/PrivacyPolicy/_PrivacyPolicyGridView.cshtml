@using System.Web.UI.WebControls
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers


@model IQueryable<NeoSysLCS.Repositories.ViewModels.PrivacyPolicyViewModel>

@{


    var grid = Html.DevExpress().GridView(settings =>
    {
        settings.Name = "PrivacyPolicyGridView";
        GridViewHelper.ApplyDefaultSettings(settings);
        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;


        settings.SettingsResizing.ColumnResizeMode = ColumnResizeMode.NextColumn;
        settings.KeyFieldName = "PrivacyPolicyID";

        settings.CallbackRouteValues = new { Controller = "PrivacyPolicy", Action = "PrivacyPolicyGridView", privacyPolicyID = ViewData["privacyPolicyID"] };

        settings.SettingsEditing.BatchUpdateRouteValues = new { Controller = "PrivacyPolicy", Action = "GridViewBatchEditUpdate", privacyPolicyID = ViewData["privacyPolicyID"] };

        settings.SettingsEditing.Mode = GridViewEditingMode.Batch;
        settings.CommandColumn.Visible = true;
        settings.CommandColumn.ShowNewButtonInHeader = true;
        settings.CommandColumn.ShowDeleteButton = true;
        settings.CommandColumn.ShowEditButton = false;
        settings.Settings.ShowGroupPanel = false;
        settings.SettingsContextMenu.Enabled = false;

        settings.Columns.Add(column =>
        {
            column.FieldName = "Name";
            column.Caption = Resources.Colunm_Name;
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "Version";
            column.SetDataItemTemplateContent(
                container =>
                {
                    var versionNumber = (string) DataBinder.Eval(container.DataItem, "Version");
                    var version = "v";
                    if (versionNumber != "---")
                    {
                        version += versionNumber;
                    }
                    else
                    {
                        version += "0";
                    }
                    ViewContext.Writer.Write(version);
                });
        });


        settings.Columns.Add(column =>
        {
            column.FieldName = "Quelle";
            column.EditFormSettings.Visible = DefaultBoolean.False;
            column.SetEditItemTemplateContent(c => Html.DevExpress().TextBox(TextBoxSettings =>
            {
                TextBoxSettings.Name = "txt3";

                TextBoxSettings.ReadOnly = true;
                TextBoxSettings.ClientVisible = false;

            }).Render());
            column.SetDataItemTemplateContent(
                container =>
                {
                    var privacyPolicyID = container.KeyValue;
                    ViewData["privacyPolicyID"] = privacyPolicyID;

                    var url = (string)DataBinder.Eval(container.DataItem, "Quelle");
                    if (!string.IsNullOrEmpty(url) && url != "---")
                    {
                        url = !url.StartsWith("http") ? "http://" + url : url;
                        Html.DevExpress().HyperLink(hyperlink =>
                        {
                            hyperlink.EncodeHtml = false;
                            hyperlink.Properties.Text = "<i class=\" fa fa-link\"></i>";
                            hyperlink.NavigateUrl = url;
                            hyperlink.Properties.Target = "_blank";
                        }).Render();

                        ViewContext.Writer.Write("&nbsp;&nbsp;");
                    }

                    if (privacyPolicyID != null)
                    {
                        // new file
                        Html.DevExpress().HyperLink(hyperlink =>
                        {
                            hyperlink.EncodeHtml = false;
                            hyperlink.Properties.Text = "<i class=\" fa fa-edit\"></i>";
                            hyperlink.Attributes.Add("onClick", "javascript:ShowDetailPopup('" + Url.Action("ShowUploadPopup", "PrivacyPolicy", new { privacyPolicyID = privacyPolicyID, url = url, field = "Quelle" }) + "','pcModalMode_PrivacyPolicyFileUpload');");
                            hyperlink.NavigateUrl = Url.Content("#");

                        }).Render();
                    }
                });
        });

        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);

        //Detail Template
        settings.SettingsDetail.ShowDetailRow = true;
        settings.SetDetailRowTemplateContent(c =>
        {
            Html.RenderAction("UebersetzungenGridView", new { privacyPolicyID = DataBinder.Eval(c.DataItem, "PrivacyPolicyID") });
        });
    });

    if (ViewData["EditError"] != null)
    {
        grid.SetEditErrorText((string)ViewData["EditError"]);
    }
}

@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = "PrivacyPolicyID";
}).GetHtml()


@Html.DevExpress().PopupControl(settings =>
{
    settings.Name = "pcModalMode_PrivacyPolicyFileUpload";
    settings.ScrollBars = ScrollBars.None;
    settings.ShowPageScrollbarWhenModal = true;
    settings.ResizingMode = ResizingMode.Live;
    settings.ShowRefreshButton = false;
    settings.AutoUpdatePosition = true;
    settings.AllowDragging = true;
    settings.CloseAction = CloseAction.CloseButton;
    settings.PopupAnimationType = AnimationType.None;
    settings.HeaderText = "File upload";
    settings.Modal = true;
    settings.AllowResize = true;
    settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
    settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
    settings.ClientSideEvents.Init = "function(s, e) { s.SetWidth(400); s.SetHeight(200); }";
    settings.ClientSideEvents.Shown = "function(s, e) { s.SetWidth(400); s.SetHeight(200); }";
    settings.ClientSideEvents.Closing = "function(s, e){ location.reload(); }";
}).GetHtml()