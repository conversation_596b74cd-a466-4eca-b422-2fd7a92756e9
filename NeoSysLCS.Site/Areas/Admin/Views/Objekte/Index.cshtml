@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Areas.Admin.Controllers
@using NeoSysLCS.Site.Utilities.Export

@section Scripts {
    @Scripts.Render("~/Scripts/helpers/SelectionHelper.js")
    @Scripts.Render("~/Scripts/helpers/BatchEditDeleteHelper.js")
}

@model IQueryable<NeoSysLCS.Repositories.ViewModels.ObjektViewModel>

@{
    ViewBag.Title = "Objekte";
    ViewContext.Writer.Write("<h1 class='page-header'>" + ViewBag.Title + "</h1>");
}

<script type="text/javascript">
    //<![CDATA[


    //selectionhelper
    var pflichtSelection = null;
    var forderungSelection = null;

    function PflichtSelectionInit(objektID, grid, selectbox, countSelector, resultSelector) {
        if (pflichtSelection == null) {
            pflichtSelection = new SelectionHelper(
                '@Url.Action("SavePflichten", "ObjektPflichten", new{ Area = "Admin"})',
                "{0} ({1}) / {2} / {3}"
            );
        }
        pflichtSelection.AddSelection(objektID, grid, selectbox, countSelector, resultSelector);
    }

    function ForderungSelectionInit(objektID, grid, selectbox, countSelector, resultSelector) {
        if (forderungSelection == null) {
            forderungSelection = new SelectionHelper(
                '@Url.Action("SaveForderungen", "ObjektForderungen", new{ Area = "Admin"})',
                "{0} ({1}) / {2} / {3}"
            );
        }
        forderungSelection.AddSelection(objektID, grid, selectbox, countSelector, resultSelector);
    }

    function PflichtSelectionChanged(objektID, e) {
        pflichtSelection.SelectionChanged(objektID, "PflichtID;ErlassTitel;" +
            "ErlassSrNummer;" +
            "GueltigVon;PflichtID;" +
            "CurrentID",e);
    }
    function ForderungSelectionChanged(objektID, e) {
       
        forderungSelection.SelectionChanged(objektID, "ForderungsversionID;ErlassTitel;" +
            "ErlassSrNummer;Inkrafttretung;" +
            "VersionsNummer;CurrentID", e);

    }

    function OnSubmitPflichtenClick(objektID) {
        pflichtSelection.OnSubmitClick(objektID);
    }

    function OnSubmitForderungenClick(objektID) {
        forderungSelection.OnSubmitClick(objektID);
    }

    // ]]>
</script>

@using (Html.BeginForm("ExportTo", "Objekte", new { area = "Admin" }, FormMethod.Post, new { target = "_blank" }))
{
    <div class="divToolbar">
        <div class="divButtonsLeft">
            @foreach (KeyValuePair<ExportType, ExportAction> entry in ExportUtilities.GetExportTypes(new List<ExportType>() { ExportType.Pdf, ExportType.Xlsx }))
            {
                <button class="btn btn-default" type="submit" value="@entry.Key" name="exportFormat">@entry.Value.Title</button>
            }
        </div>
    </div>
    @Html.Partial("ObjekteGridView", Model)
}


<script type="text/javascript">
    //init batch edit helper
    var batchEditDeleteHelper = new BatchEditDeleteHelper(
        "@ObjekteController.BaseGridViewSettings.GetGridViewName()",
        "ObjektID",
        "Name",
        "@Resources.View_Objekte_DeleteText");
</script>