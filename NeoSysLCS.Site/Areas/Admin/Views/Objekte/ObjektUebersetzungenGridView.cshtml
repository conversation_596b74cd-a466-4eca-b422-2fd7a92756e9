@using NeoSysLCS.DomainModel.Models;
@using NeoSysLCS.Repositories;
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Site.Helpers


@{
    var grid = Html.DevExpress().GridView(
        settings =>
        {
            UnitOfWork unitOfWork = new UnitOfWork();

            GridViewHelper.ApplyUebersetzungsSettings(settings);

            settings.Name = "ObjektUebersetzungenGridView_" + ViewData["ObjektID"];
            settings.KeyFieldName = "ObjektUebersetzungID";

            settings.CallbackRouteValues = new { Controller = "Objekte", Action = "ObjektUebersetzungenGridView", ObjektID = ViewData["ObjektID"] };
            if (User.IsInRole(Role.ObjectGuru))
            {
                settings.SettingsEditing.Mode = GridViewEditingMode.Batch;
            }
            settings.SettingsEditing.BatchUpdateRouteValues = new { Controller = "Objekte", Action = "ObjektUebersetzungenGridViewBatchEditingUpdate", ObjektID = ViewData["ObjektID"] };
            settings.CommandColumn.Visible = false;

            //Columns
            settings.Columns.Add("Name");
            settings.Columns.Add("Beschreibung");

            settings.Columns.Add(column =>
            {
                column.FieldName = "SpracheID";
                column.Caption = "Sprache";
                column.ReadOnly = true;

                column.ColumnType = MVCxGridViewColumnType.ComboBox;
                column.Settings.FilterMode = ColumnFilterMode.DisplayText;
                var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;

                comboBoxProperties.DataSource = unitOfWork.SpracheRepository.Get().ToList();
                comboBoxProperties.TextField = "Name";
                comboBoxProperties.ValueField = "SpracheID";
                comboBoxProperties.ValueType = typeof(int);
            });

            GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);

            settings.ClientSideEvents.BatchEditStartEditing = @"function(s,e){
                currentGrid = s;
	        }";

            settings.ClientSideEvents.ClipboardCellPasting = @"function(s,e){
                e.cancel = currentGrid != s;
	        }";
        });
    if (ViewData["EditError"] != null)
    {
        grid.SetEditErrorText((string)ViewData["EditError"]);
    }

}
@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = "ObjektUebersetzungID";
}).GetHtml()
