@using System.Web.UI.WebControls;
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Site.Areas.Admin.Controllers
@using NeoSysLCS.Site.Helpers


@{

    var grid = Html.DevExpress().GridView(settings =>
    {
        settings.Name = ObjekteController.BaseGridViewSettings.GetGridViewName();
        GridViewHelper.ApplyDefaultSettings(settings);
        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;
        settings.Name = "ObjekteGridView";
        settings.KeyFieldName = "ObjektID";

        settings.CallbackRouteValues = new { Controller = "Objekte", Action = "ObjekteGridView" };

        settings.SettingsEditing.BatchUpdateRouteValues = new { Controller = "Objekte", Action = "ObjekteGridViewBatchEditUpdate" };

        if (User.IsIn<PERSON>ole(Role.ObjectGuru))
        {
            settings.SettingsEditing.Mode = GridViewEditingMode.Batch;
            settings.SettingsEditing.BatchEditSettings.EditMode = GridViewBatchEditMode.Cell;
            settings.SettingsEditing.BatchEditSettings.StartEditAction = GridViewBatchStartEditAction.Click;
            settings.CommandColumn.Visible = true;
            settings.CommandColumn.ShowNewButtonInHeader = true;
            settings.CommandColumn.ShowDeleteButton = true;
            settings.CommandColumn.ShowEditButton = false;
        }
        settings.KeyFieldName = ObjekteController.BaseGridViewSettings.GetGridViewKeyFieldName();

        //Columns
        settings.Columns.Add(column =>
        {
            column.Caption = "#";
            column.ReadOnly = true;
            column.EditFormSettings.Visible = DefaultBoolean.False;

            column.SetDataItemTemplateContent(container =>
            {
                if (container.KeyValue != null)
                {
                    Html.RenderPartial("~/Views/Objekte/_ObjektForderungenExportPartial.cshtml", container.KeyValue);
                }

                Html.DevExpress().HyperLink(hyperlink =>
                {
                    var keyValue = container.KeyValue;
                    hyperlink.Name = "Pflichten" + keyValue;
                    hyperlink.Properties.Text = "Pflichten";
                    hyperlink.NavigateUrl = Url.Action("Index", "ObjektObligation", new { objektID = keyValue });

                }).Render();
                ViewContext.Writer.Write("&nbsp;&nbsp;");
            });
        });

        foreach (MVCxGridViewColumn col in ObjekteController.BaseGridViewSettings.GetDataColumns())
        {
            settings.Columns.Add(col);
        }

        //GridViewHelper.AddNewestFirstSortorderColumn(settings, ObjekteController.BaseGridViewSettings.GetGridViewKeyFieldName());

        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);

        //Detail
        settings.SettingsDetail.ShowDetailRow = true;
        settings.SetDetailRowTemplateContent(c =>
        {
            Html.DevExpress().PageControl(pageControlSettings =>
            {
                pageControlSettings.Name = "ObjektPageControl_" + DataBinder.Eval(c.DataItem, "ObjektID");
                pageControlSettings.Width = Unit.Percentage(100);
                pageControlSettings.TabPages.Add("Objekt�bersetzungen").SetContent(() =>
                {
                    Html.RenderAction("ObjektUebersetzungenGridView", new { ObjektID = DataBinder.Eval(c.DataItem, "ObjektID") });
                });

                pageControlSettings.TabPages.Add("Forderungen").SetContent(() =>
                {
                    Html.RenderAction("ObjektForderungenPartial", "ObjektForderungen", new { id = DataBinder.Eval(c.DataItem, "ObjektID") });

                });
                //pageControlSettings.TabPages.Add("Pflichten").SetContent(() =>
                //{
                //    Html.RenderAction("ObjektPflichtenPartial", "ObjektPflichten", new { id = DataBinder.Eval(c.DataItem, "ObjektID") });

                //});

            }).Render();
        });

        settings.ClientSideEvents.BatchEditStartEditing = @"function(s,e){
                currentGrid = s;
	        }";

        settings.ClientSideEvents.ClipboardCellPasting = @"function(s,e){
                e.cancel = currentGrid != s;
	        }";

    });

    if (ViewData["EditError"] != null)
    {
        grid.SetEditErrorText((string)ViewData["EditError"]);
    }
}
@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = ObjekteController.BaseGridViewSettings.GetGridViewKeyFieldName();
}).GetHtml()