@using Microsoft.AspNet.Identity
@using NeoSysLCS.Site
@using Microsoft.AspNet.Identity.EntityFramework
@using NeoSysLCS.Resources.Properties
@using System.Web.UI.WebControls
@using Microsoft.Ajax.Utilities
@using NeoSysLCS.Site.Helpers
@using NeoSysLCS.Repositories
@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Site.Areas.Admin.Controllers

@model IQueryable<NeoSysLCS.Repositories.ViewModels.KommentarViewModel>

@{
                UnitOfWork unitOfWork = new UnitOfWork();
                var sprachen = unitOfWork.SpracheRepository.Get();
                SessionHelper sessionHelper = new SessionHelper();

                var grid = Html.DevExpress().GridView(settings =>
                {
                    settings.Name = "KommentareGridView";
                    GridViewHelper.ApplyDefaultSettings(settings);
                    settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;

                    settings.SettingsResizing.ColumnResizeMode = ColumnResizeMode.NextColumn;

                    settings.KeyFieldName = "KommentarID";

                    settings.CallbackRouteValues = new { Controller = "Kommentar", Action = "KommentareGridView", kommentarId = ViewData["KommentarID"] };

                    settings.SettingsEditing.BatchUpdateRouteValues = new { Controller = "Kommentar", Action = "KommentareGridViewBatchEditUpdate" };

                    settings.SettingsEditing.Mode = GridViewEditingMode.Batch;
                    settings.CommandColumn.Visible = true;
                    settings.CommandColumn.ShowNewButtonInHeader = true;
                    settings.CommandColumn.ShowDeleteButton = true;

                    settings.Columns.Add(column =>
                    {
                        column.FieldName = "Eintrag";
                        column.Caption = "Eintrag";
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                        column.PropertiesEdit.DisplayFormatString = "d";
                        column.SortDescending();
                    });

                    settings.Columns.Add(column =>
                    {
                        column.FieldName = "KommentarID";
                        column.Caption = "KommentarID";
                        column.EditFormSettings.Visible = DefaultBoolean.False;
                    });

                    settings.Columns.Add(column =>
                    {
                        column.Caption = Resources.Allgemein_Zuordnungen;
                        column.Name = "Assignements";
                        column.ReadOnly = true;
                        column.EditFormSettings.Visible = DefaultBoolean.False;

                        column.SetDataItemTemplateContent(container =>
                        {

                            Html.DevExpress().HyperLink(hyperlink =>
                            {
                                var keyValue = container.KeyValue;
                                hyperlink.Name = "Erlasse" + keyValue;
                                int integerKeyValue = 0;
                                if (keyValue != null)
                                {
                                    integerKeyValue = Convert.ToInt32(keyValue);
                                }
                                hyperlink.Properties.Text = Resources.Entitaet_Erlass_Plural + " (" + Model.Where(x => x.KommentarID == integerKeyValue).Select(k => k.ErlasseCount).ToList().FirstOrDefault() + ")"; ;
                                hyperlink.NavigateUrl = Url.Action("Index", "KommentarErlasse", new { id = keyValue });

                            }).Render();
                            ViewContext.Writer.Write("<br>");


                            Html.DevExpress().HyperLink(hyperlink =>
                            {
                                var keyValue = container.KeyValue;
                                hyperlink.Name = "Rechtsbereiche" + keyValue;
                                int integerKeyValue = 0;
                                if (keyValue != null)
                                {
                                    integerKeyValue = Convert.ToInt32(keyValue);
                                }
                                hyperlink.Properties.Text = Resources.Entitaet_Rechtsbereich_Plural + " (" + Model.Where(x => x.KommentarID == integerKeyValue).Select(k => k.RechtsbereicheCount).ToList().FirstOrDefault() + ")";
                                hyperlink.NavigateUrl = Url.Action("Index", "KommentarRechtsbereiche", new { id = keyValue });

                            }).Render();
                            ViewContext.Writer.Write("<br>");
                            Html.DevExpress().HyperLink(hyperlink =>
                            {
                                var keyValue = container.KeyValue;
                                hyperlink.Name = "Objekte" + keyValue;
                                int integerKeyValue = 0;
                                if (keyValue != null)
                                {
                                    integerKeyValue = Convert.ToInt32(keyValue);
                                }
                                hyperlink.Properties.Text = Resources.Entitaet_Objekt_Plural + " (" + Model.Where(x => x.KommentarID == integerKeyValue).Select(k => k.ObjekteCount).ToList().FirstOrDefault() + ")"; ;
                                hyperlink.NavigateUrl = Url.Action("Index", "KommentarObjekte", new { id = keyValue });

                            }).Render();

                        });
                    });


                    foreach (MVCxGridViewColumn col in KommentarController.BaseGridViewSettings.GetDataColumns(ViewContext, Model))
                    {
                        settings.Columns.Add(col);
                    }

                    settings.Columns.Add(column =>
                    {
                        column.Caption = "HauptErlass";
                        column.Name = "HauptErlassID";
                        column.ReadOnly = true;
                        column.EditFormSettings.Visible = DefaultBoolean.False;

                        column.SetDataItemTemplateContent(container =>
                        {

                            Html.DevExpress().HyperLink(hyperlink =>
                            {
                                var keyValue = container.KeyValue;
                                hyperlink.Name = "HauptErlass" + keyValue;
                                int integerKeyValue = 0;
                                if (keyValue != null)
                                {
                                    integerKeyValue = Convert.ToInt32(keyValue);
                                }

                                if (Model.Where(x => x.KommentarID == integerKeyValue).Select(k => k.HauptErlassID).ToList().FirstOrDefault() == 0 || Model.Where(x => x.KommentarID == integerKeyValue).Select(k => k.HauptErlassID).ToList().FirstOrDefault() == null)
                                {

                                    hyperlink.Properties.Text = Resources.Entitaet_Erlass_Plural;
                                }
                                else
                                {
                                    hyperlink.Properties.Text = Resources.Entitaet_Erlass_Plural + " (1)";
                                }
                                hyperlink.NavigateUrl = Url.Action("Index", "KommentarHauptErlass", new { id = keyValue });

                            }).Render();
                            ViewContext.Writer.Write("<br>");

                        });
                    });

                    settings.Columns.Add(column =>
                    {
                        column.FieldName = "Quelle";
                        column.SetDataItemTemplateContent(
                            container =>
                            {

                                var url = DataBinder.Eval(container.DataItem, "Quelle");
                                string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url, true);
                                ViewContext.Writer.Write(htmlLink);
                            });

                    });

                    settings.Columns.Add(column =>
                    {
                        column.FieldName = "BetroffenKommentar";
                        column.Caption = "Betroffen Kommentar";
                        column.ColumnType = MVCxGridViewColumnType.Memo;
                        var memoProp = column.PropertiesEdit as MemoProperties;
                        memoProp.EncodeHtml = false;
                        column.ReadOnly = true;
                        var prop = (column.PropertiesEdit as EditProperties);
                        if (prop != null)
                        {
                            prop.ValidationSettings.Display = Display.Dynamic;
                        }
                        column.SetDataItemTemplateContent(
                            container =>
                            {
                                var kommentar = (string)DataBinder.Eval(container.DataItem, "BetroffenKommentar");
                                if (string.IsNullOrEmpty(kommentar))
                                {
                                    ViewContext.Writer.Write(Resources.Fehler_Keine_Uebersetzung);
                                }
                                else
                                {
                                    ViewContext.Writer.Write(StringHelper.TruncateHtml(kommentar, 50));
                                }
                            }
                        );
                    });


                    MVCxGridViewColumn linkBetroffenColumn = settings.Columns.Add("LinkBetroffenKommentar");
                    linkBetroffenColumn.Caption = "Link betroffen Kommentar";
                    linkBetroffenColumn.SetDataItemTemplateContent(container =>
                    {

                        var commentId = container.KeyValue;
                        ViewData["commentId"] = commentId;

                        var url = (string)DataBinder.Eval(container.DataItem, "LinkBetroffenKommentar");
                        if (!string.IsNullOrEmpty(url) && url != "---")
                        {
                            url = !url.StartsWith("http") ? "http://" + url : url;
                            Html.DevExpress().HyperLink(hyperlink =>
                            {
                                hyperlink.EncodeHtml = false;
                                hyperlink.Properties.Text = "<i class=\" fa fa-link\"></i>";
                                hyperlink.NavigateUrl = url;
                                hyperlink.Properties.Target = "_blank";
                            }).Render();

                            ViewContext.Writer.Write("&nbsp;&nbsp;");
                        }

                        if (commentId != null)
                        {
                            // new file
                            Html.DevExpress().HyperLink(hyperlink =>
                            {
                                hyperlink.EncodeHtml = false;
                                hyperlink.Properties.Text = "<i class=\" fa fa-edit\"></i>";
                                hyperlink.Attributes.Add("onClick", "javascript:ShowDetailPopup('" + Url.Action("ShowUploadPopup", "Kommentar", new { commentId = commentId, url = url, field = "LinkBetroffenKommentar", popupType = "KommentarLinkBetroffen" }) + "','pcModalMode_KommentarFileUploadLinkBetroffen');");
                                hyperlink.NavigateUrl = Url.Content("#");

                            }).Render();
                        }
                    });

                    linkBetroffenColumn.EditFormSettings.Visible = DefaultBoolean.False;
                    linkBetroffenColumn.SetEditItemTemplateContent(c => Html.DevExpress().TextBox(TextBoxSettings =>
                    {
                        TextBoxSettings.Name = "txt3";

                        TextBoxSettings.ReadOnly = true;
                        TextBoxSettings.ClientVisible = false;

                    }).Render());


                    settings.Columns.Add(column =>
                    {
                        column.FieldName = "NichtBetroffenKommentar";
                        column.Caption = "Nicht betroffen Kommentar";
                        column.ColumnType = MVCxGridViewColumnType.Memo;
                        var memoProp = column.PropertiesEdit as MemoProperties;
                        memoProp.EncodeHtml = false;
                        column.ReadOnly = true;
                        var prop = (column.PropertiesEdit as EditProperties);
                        if (prop != null)
                        {
                            prop.ValidationSettings.Display = Display.Dynamic;
                        }
                        column.SetDataItemTemplateContent(
                            container =>
                            {
                                var kommentar = (string)DataBinder.Eval(container.DataItem, "NichtBetroffenKommentar");
                                if (string.IsNullOrEmpty(kommentar))
                                {
                                    ViewContext.Writer.Write(Resources.Fehler_Keine_Uebersetzung);
                                }
                                else
                                {
                                    ViewContext.Writer.Write(StringHelper.TruncateHtml(kommentar, 50));
                                }
                            }
                        );
                    });

                    MVCxGridViewColumn linkNichtBetroffenColumn = settings.Columns.Add("LinkNichtBetroffenKommentar");
                    linkNichtBetroffenColumn.Caption = "Link nicht betroffen Kommentar";
                    linkNichtBetroffenColumn.SetDataItemTemplateContent(container =>
                    {

                        var commentId = container.KeyValue;
                        ViewData["commentId"] = commentId;

                        var url = (string)DataBinder.Eval(container.DataItem, "LinkNichtBetroffenKommentar");
                        if (!string.IsNullOrEmpty(url) && url != "---")
                        {
                            url = !url.StartsWith("http") ? "http://" + url : url;
                            Html.DevExpress().HyperLink(hyperlink =>
                            {
                                hyperlink.EncodeHtml = false;
                                hyperlink.Properties.Text = "<i class=\" fa fa-link\"></i>";
                                hyperlink.NavigateUrl = url;
                                hyperlink.Properties.Target = "_blank";
                            }).Render();

                            ViewContext.Writer.Write("&nbsp;&nbsp;");
                        }

                        if (commentId != null)
                        {
                            // new file
                            Html.DevExpress().HyperLink(hyperlink =>
                            {
                                hyperlink.EncodeHtml = false;
                                hyperlink.Properties.Text = "<i class=\" fa fa-edit\"></i>";
                                hyperlink.Attributes.Add("onClick", "javascript:ShowDetailPopup('" + Url.Action("ShowUploadPopup", "Kommentar", new { commentId = commentId, url = url, field = "LinkNichtBetroffenKommentar", popupType = "KommentarLinkNichtBetroffen" }) + "','pcModalMode_KommentarFileUploadLinkNichtBetroffen');");
                                hyperlink.NavigateUrl = Url.Content("#");

                            }).Render();
                        }
                    });

                    linkNichtBetroffenColumn.EditFormSettings.Visible = DefaultBoolean.False;
                    linkNichtBetroffenColumn.SetEditItemTemplateContent(c => Html.DevExpress().TextBox(TextBoxSettings =>
                    {
                        TextBoxSettings.Name = "txt3";

                        TextBoxSettings.ReadOnly = true;
                        TextBoxSettings.ClientVisible = false;

                    }).Render());

                    settings.Columns.Add(column =>
                    {
                        column.FieldName = "InternerKommentar";
                        column.Caption = "Interner Kommentar";
                        column.ColumnType = MVCxGridViewColumnType.Memo;
                    });

                    settings.Columns.Add(column =>
                    {
                        column.FieldName = "PLFreigabe";
                        column.Caption = "PL-Freigabe";
                        column.ColumnType = MVCxGridViewColumnType.CheckBox;
                        column.EditFormSettings.Visible = DefaultBoolean.True;
                    });

                    settings.Columns.Add(column =>
                    {
                        column.FieldName = "PLFreigabeDate";
                        column.Caption = "PL-Freigabe Datum";
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                        column.PropertiesEdit.DisplayFormatString = "d";
                    });


                    settings.Columns.Add(column =>
                    {
                        column.FieldName = "Status";
                        column.Caption = Resources.View_Kundendokument_Status;
                        column.HeaderStyle.Wrap = DefaultBoolean.True;
                        column.ColumnType = MVCxGridViewColumnType.ComboBox;
                        column.Settings.FilterMode = ColumnFilterMode.DisplayText;
                        var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;

                        var list = from KommentarStatus value in Enum.GetValues(typeof(KommentarStatus))
                                   select new
                                   {
                                       Id = (int)value,
                                       Name = value.GetTranslation()
                                   };

                        comboBoxProperties.DataSource = list;
                        comboBoxProperties.ValueField = "Id";
                        comboBoxProperties.TextField = "Name";
                        comboBoxProperties.ValueType = typeof(Int32);
                    });

                    settings.Columns.Add(column =>
                    {
                        column.FieldName = "Newsletter";
                        column.Caption = "Newsletter";
                        column.ColumnType = MVCxGridViewColumnType.CheckBox;
                        column.EditFormSettings.Visible = DefaultBoolean.True;
                    });

                    settings.Columns.Add(column =>
                    {
                        column.FieldName = "NewsletterDate";
                        column.Caption = "Newsletter Datum";
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                        column.PropertiesEdit.DisplayFormatString = "d";
                    });

                    settings.Columns.Add(column =>
                    {
                        column.FieldName = "ShortText";
                        column.Caption = "Kurz Text";
                        column.ColumnType = MVCxGridViewColumnType.Memo;
                        var memoProp = column.PropertiesEdit as MemoProperties;
                        memoProp.EncodeHtml = false;
                        column.ReadOnly = true;
                        var prop = (column.PropertiesEdit as EditProperties);
                        if (prop != null)
                        {
                            prop.ValidationSettings.Display = Display.Dynamic;
                        }
                        column.SetDataItemTemplateContent(
                            container =>
                            {
                                var kommentar = (string)DataBinder.Eval(container.DataItem, "ShortText");
                                if (string.IsNullOrEmpty(kommentar))
                                {
                                    ViewContext.Writer.Write(Resources.Fehler_Keine_Uebersetzung);
                                }
                                else
                                {
                                    ViewContext.Writer.Write(StringHelper.TruncateHtml(kommentar, 50));
                                }
                            }
                            );
                    });

                    MVCxGridViewColumn kommentarImageColumn = settings.Columns.Add("KommentarImage");
                    kommentarImageColumn.Caption = "Image";
                    kommentarImageColumn.SetDataItemTemplateContent(container =>
                    {

                        var commentId = container.KeyValue;
                        ViewData["commentId"] = commentId;

                        var url = (string)DataBinder.Eval(container.DataItem, "KommentarImage");
                        if (!string.IsNullOrEmpty(url) && url != "---")
                        {
                            url = !url.StartsWith("http") ? "http://" + url : url;
                            Html.DevExpress().HyperLink(hyperlink =>
                            {
                                hyperlink.EncodeHtml = false;
                                hyperlink.Properties.Text = "<i class=\" fa fa-link\"></i>";
                                hyperlink.NavigateUrl = url;
                                hyperlink.Properties.Target = "_blank";
                            }).Render();

                            ViewContext.Writer.Write("&nbsp;&nbsp;");
                        }

                        if (commentId != null)
                        {
                            // new file
                            Html.DevExpress().HyperLink(hyperlink =>
                            {
                                hyperlink.EncodeHtml = false;
                                hyperlink.Properties.Text = "<i class=\" fa fa-edit\"></i>";
                                hyperlink.Attributes.Add("onClick", "javascript:ShowDetailPopup('" + Url.Action("ShowUploadPopup", "Kommentar", new { commentId = commentId, url = url, field = "KommentarImage", popupType = "KommentarImage" }) + "','pcModalMode_KommentarFileUploadImage');");
                    hyperlink.NavigateUrl = Url.Content("#");

                }).Render();
            }
        });

        kommentarImageColumn.EditFormSettings.Visible = DefaultBoolean.False;
        kommentarImageColumn.SetEditItemTemplateContent(c => Html.DevExpress().TextBox(TextBoxSettings =>
        {
            TextBoxSettings.Name = "txt5";

            TextBoxSettings.ReadOnly = true;
            TextBoxSettings.ClientVisible = false;

        }).Render());

        //GridViewHelper.AddNewestFirstSortorderColumn(settings, ErlasseController.BaseGridViewSettings.GetGridViewKeyFieldName());
        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);

        //Detail Template
        settings.SettingsDetail.ShowDetailRow = true;
        //settings.SetDetailRowTemplateContent(c =>
        //{
        //    Html.RenderAction("KommentarUebersetzungGridView", new { kommentarID = DataBinder.Eval(c.DataItem, "KommentarID") });
        //});
        settings.SetDetailRowTemplateContent(c =>
        {
            Html.DevExpress().PageControl(pageControlSettings =>
            {
            pageControlSettings.Name = "KommentarPageControl_" + DataBinder.Eval(c.DataItem, "KommentarID");
            pageControlSettings.Width = Unit.Percentage(100);
            // TODO �bersetzung
            pageControlSettings.TabPages.Add("�bersetzungen").SetContent(() =>
            {

                Html.RenderAction("KommentarUebersetzungGridView",
                    new
                    {
                            //erlassId = ViewData["KommentarID"],
                            kommentarID = DataBinder.Eval(c.DataItem, "KommentarID")
                    });


            });

        }).Render();

    });
    settings.HtmlDataCellPrepared += (sender, e) =>
    {
        if (e.DataColumn.FieldName == "BetroffenKommentar"
            || e.DataColumn.FieldName == "NichtBetroffenKommentar"
            || e.DataColumn.FieldName == "ShortText")
        {
            e.Cell.Attributes.Add(
            "onclick",
            "openPopupReadonly(" + settings.Name + ", '" + e.DataColumn.FieldName + "', " + e.VisibleIndex + ", '" + e.DataColumn.Caption + "')");
        }
    };

    settings.ClientSideEvents.BatchEditStartEditing = @"function(s,e){
    if(e.focusedColumn.fieldName == 'BetroffenKommentar'
        || e.focusedColumn.fieldName == 'NichtBetroffenKommentar'
        || e.focusedColumn.fieldName == 'ShortText'){
                openPopupOnBatchEditBegin(s, e);
            }
    }";
    });



    if (ViewData["EditError"] != null)
    {
        grid.SetEditErrorText((string)ViewData["EditError"]);
    }
}

@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = "KommentarID";
}).GetHtml()

@Html.DevExpress().PopupControl(settings =>
{
    settings.Name = "pcModalMode_KommentarFileUploadLinkBetroffen";
    settings.ScrollBars = ScrollBars.None;
    settings.ShowPageScrollbarWhenModal = true;
    settings.ResizingMode = ResizingMode.Live;
    settings.ShowRefreshButton = false;
    settings.AutoUpdatePosition = true;
    settings.AllowDragging = true;
    settings.CloseAction = CloseAction.CloseButton;
    settings.PopupAnimationType = AnimationType.None;
    settings.HeaderText = "File upload";
    settings.Modal = true;
    settings.AllowResize = true;
    settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
    settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
    settings.ClientSideEvents.Init = "function(s, e) { s.SetWidth(400); s.SetHeight(200); }";
    settings.ClientSideEvents.Shown = "function(s, e) { s.SetWidth(400); s.SetHeight(200); }";
    settings.ClientSideEvents.Closing = "function(s, e) { KommentareGridView.Refresh(); }";
}).GetHtml()

@Html.DevExpress().PopupControl(settings =>
{
    settings.Name = "pcModalMode_KommentarFileUploadLinkNichtBetroffen";
    settings.ScrollBars = ScrollBars.None;
    settings.ShowPageScrollbarWhenModal = true;
    settings.ResizingMode = ResizingMode.Live;
    settings.ShowRefreshButton = false;
    settings.AutoUpdatePosition = true;
    settings.AllowDragging = true;
    settings.CloseAction = CloseAction.CloseButton;
    settings.PopupAnimationType = AnimationType.None;
    settings.HeaderText = "File upload";
    settings.Modal = true;
    settings.AllowResize = true;
    settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
    settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
    settings.ClientSideEvents.Init = "function(s, e) { s.SetWidth(400); s.SetHeight(200); }";
    settings.ClientSideEvents.Shown = "function(s, e) { s.SetWidth(400); s.SetHeight(200); }";
    settings.ClientSideEvents.Closing = "function(s, e) { KommentareGridView.Refresh(); }";
}).GetHtml()

@Html.DevExpress().PopupControl(settings =>
{
    settings.Name = "pcModalMode_KommentarFileUploadImage";
    settings.ScrollBars = ScrollBars.None;
    settings.ShowPageScrollbarWhenModal = true;
    settings.ResizingMode = ResizingMode.Live;
    settings.ShowRefreshButton = false;
    settings.AutoUpdatePosition = true;
    settings.AllowDragging = true;
    settings.CloseAction = CloseAction.CloseButton;
    settings.PopupAnimationType = AnimationType.None;
    settings.HeaderText = "File upload";
    settings.Modal = true;
    settings.AllowResize = true;
    settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
    settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
    settings.ClientSideEvents.Init = "function(s, e) { s.SetWidth(400); s.SetHeight(200); }";
    settings.ClientSideEvents.Shown = "function(s, e) { s.SetWidth(400); s.SetHeight(200); }";
    settings.ClientSideEvents.Closing = "function(s, e) { KommentareGridView.Refresh(); }";
}).GetHtml()