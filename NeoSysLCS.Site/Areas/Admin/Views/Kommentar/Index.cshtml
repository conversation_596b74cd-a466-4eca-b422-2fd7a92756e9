@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Site.Utilities.Export

@model IQueryable<NeoSysLCS.Repositories.ViewModels.KommentarViewModel>

@{
    ViewBag.Title = "Kommentare";
    ViewContext.Writer.Write("<h1 class='page-header'>" + ViewBag.Title + "</h1>");
}

@*var displayArchived = (bool)ViewData["displayArchived"];
    if (displayArchived)
    {
        @Html.ActionLink("Archivierte ausblenden", "Index", "Erlasse", new { area = "Admin", displayArchived = false }, new { @class = "" })
    }
    else
    {
        @Html.ActionLink("Archivierte einblenden", "Index", "Erlasse", new { area = "Admin", displayArchived = true }, new { @class = "" })
    }*@

@Html.Partial("_KommentarPageControlCallbacksPartial")
@Html.Partial("_HtmlEditPopup", User.IsIn<PERSON>(Role.ProjectManager))


<script type="text/javascript">
    //<![CDATA[

    function ShowDetailPopup(url, win) {
        modal = eval(win);

        if (window.height < 600) {
            modal.SetHeight(window.height - 50);
        } else {
            modal.SetHeight(600);
        }

        if (window.width < 800) {
            modal.SetWidth(window.width - 50);
        } else {
            modal.SetWidth(800);
        }

        modal.SetContentUrl(url);
        modal.Show();
    }

    function CloseDetailPopup(win) {
        modal = eval(win);
        modal.Hide();
    }

    var rowKeyValueToCopy;
    function OnBeginCallback(s, e) {
        if (e.command === "CUSTOMCALLBACK") {
            e.customArgs["key"] = rowKeyValueToCopy;
        }
    }
    function OnCustomButtonClick(s, e) {
        rowKeyValueToCopy = s.GetRowKey(e.visibleIndex);
        s.PerformCallback();
    }

    //document.addEventListener("DOMContentLoaded", function (event) {
    //    var scrollpos = localStorage.getItem('scrollpos');
    //    if (scrollpos) window.scrollTo(0, scrollpos);
    //});

    //window.onbeforeunload = function (e) {
    //    localStorage.setItem('scrollpos', window.scrollY);
    //};

    // ]]>
</script>