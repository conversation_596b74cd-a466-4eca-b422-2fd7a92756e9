@using System.Web.UI.WebControls
@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories
@using NeoSysLCS.Site.Areas.Admin.Controllers
@using NeoSysLCS.Site.Utilities.Export
@using NeoSysLCS.Resources.Properties

@Html.DevExpress().PageControl(pageControlSettings =>
{
    pageControlSettings.Name = "KommentarPageControl";
    pageControlSettings.Width = Unit.Percentage(100);
    pageControlSettings.Height = Unit.Percentage(100);
    pageControlSettings.SaveStateToCookies = true;
    pageControlSettings.CallbackRouteValues = new { Controller = "Kommentar", Action = "KommentarPageControlCallbacksPartial" };

    UnitOfWork _unitOfWork = new UnitOfWork();

    pageControlSettings.TabPages.Add("Kommentar").SetContent(() =>
    {
        Html.BeginForm("ExportKommentare", "Kommentar", new { area = "Admin", activeOnly = true }, FormMethod.Post, new { target = "_blank", id = "KommentarExportWrapperForm" });
        ViewContext.Writer.Write("<div class=\"divToolbar\"><div class=\"divButtonsLeft\"><button class=\"btn btn-default pull-right\" type=\"submit\" value=\"" + ExportType.Xlsx + "\" name=\"exportFormat\">" + Resources.XLSX_Export_Format + "</button></div></div>");
        Html.EndForm();

        Html.RenderPartial("~/Areas/Admin/Views/Kommentar/_KommentareGridView.cshtml", _unitOfWork.KommentarRepository.GetAllKommentarViewModels());
    });
}).GetHtml()