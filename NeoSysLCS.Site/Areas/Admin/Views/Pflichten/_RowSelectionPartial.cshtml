@using System.Web.UI.WebControls
@using System.Globalization
@using System.Linq
@using DevExpress.Data
@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories;
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Site.Helpers

@functions{
    bool IsSelectValue(int value)
    {
        /*foreach (var interest in Model.Objekte)
        {
            if (interest.Id == value)
                return true;
        }*/
        return true;
    }
}

@Html.DevExpress().GridView(
    settings =>
    {
        IUnitOfWork unitOfWork = new UnitOfWork();

        settings.Name = "gvRowSelection_" + ViewData["PflichtID"];
        GridViewHelper.ApplyDefaultSettings(settings);

        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;

        settings.KeyFieldName = "ObjektID";
        settings.CallbackRouteValues = new { Controller = "Pflichten", Action = "RowSelectionPartial", id = ViewData["PflichtID"] };

        settings.ControlStyle.CssClass = "grid";
        settings.SettingsCookies.StoreFiltering = false;
        settings.CommandColumn.Visible = true;
        settings.CommandColumn.ShowSelectCheckbox = true;
        settings.SettingsBehavior.AllowSelectByRowClick = false;
        settings.Settings.ShowGroupPanel = false;
        //settings.Styles.AlternatingRow.Enabled = DefaultBoolean.True;

        //settings.Width = Unit.Percentage(100);

        //settings.SettingsPager.Visible = true;

        if (User.IsInRole(Role.ObjectGuru))
        {
            settings.ClientSideEvents.Init = @"function(s, e){
                ObjektSelectionInit(" +
                                    ViewData["PflichtID"] + @",
                                       s,
                                       SelectedObjekteRows_" + ViewData["PflichtID"] + @",
                                       '#objekteCount_" + ViewData["PflichtID"] + @"',
                                       '#objekteResult_" + ViewData["PflichtID"] + @"');
            }";
            settings.ClientSideEvents.SelectionChanged = @"function(s,e){
                ObjektSelectionChanged(" + ViewData["PflichtID"] + @", e);
            }";
        }
        settings.Columns.Add("Name");
        settings.Columns.Add("Beschreibung");

        settings.Columns.Add(column =>
        {
            column.FieldName = "Objekt.ObjektkategorieID";
            column.Caption = "Objektkategorie";
            column.ReadOnly = true;

            column.ColumnType = MVCxGridViewColumnType.ComboBox;
            column.Settings.FilterMode = ColumnFilterMode.DisplayText;
            var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;
            comboBoxProperties.DataSource = unitOfWork.ObjektkategorieRepository.GetAllObjektkategorieViewModels();
            comboBoxProperties.TextField = "Name";
            comboBoxProperties.ValueField = "ObjektkategorieID";
            comboBoxProperties.ValueType = typeof(int);
            comboBoxProperties.DropDownStyle = DropDownStyle.DropDownList;

        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "CurrentID";
            column.Visible = false;
            column.UnboundType = UnboundColumnType.Integer;
        });

        settings.CustomUnboundColumnData = (sender, e) =>
        {
            if (e.Column.FieldName == "CurrentID")
            {
                e.Value = ViewData["PflichtID"];
            }
        };

        //setting preselected
        settings.PreRender = (sender, e) =>
        {
            MVCxGridView gridView = sender as MVCxGridView;
            if (gridView != null)
                foreach (var obj in unitOfWork.ObjektRepository.GetByPflicht(Convert.ToInt32(ViewData["PflichtID"])))
                {
                    gridView.Selection.SelectRowByKey(obj.ObjektID);
                }
        };

    }).BindToLINQ(string.Empty, string.Empty, (s, e) =>
    {
        e.KeyExpression = "ObjektID";
        var unitOfWork = new UnitOfWork();
        var query = unitOfWork.ObjektRepository.GetAllObjektViewModels();
        e.QueryableSource = query;

    }).GetHtml()

