@using NeoSysLCS.Repositories
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Resources.Properties

@model IQueryable<NeoSysLCS.Repositories.ViewModels.PflichtViewModel>

@section Scripts {
    @Scripts.Render("~/Scripts/helpers/SelectionHelper.js")
    @Scripts.Render("~/Scripts/helpers/BatchEditDeleteHelper.js")
}

@{

    IUnitOfWork unitOfWork = new UnitOfWork();
    ErlassfassungViewModel erlassfassungViewModel = null;
    ErlassViewModel erlassViewModel = null;
    if ((int)ViewData["ErlassfassungID"] != 0)
    {
        erlassfassungViewModel = unitOfWork.ErlassfassungRepository.GetViewModelByID((int)ViewData["ErlassfassungID"]);
        erlassViewModel = unitOfWork.ErlassRepository.GetViewModelByID(erlassfassungViewModel.ErlassID);
        ViewBag.Title = "Pflichten von Erlass «" + erlassViewModel.Titel + "» (" + @erlassViewModel.SrNummer
                        + ")  vom " + @erlassfassungViewModel.Beschluss.ToString("dd.MM.yyyy");
    }
    else
    {
        ViewBag.Title = "Pflichten";
    }
    ViewContext.Writer.Write("<h1 class='page-header'>" + ViewBag.Title + "</h1>");
}

@section breadcrumb{

    <a href="#" onclick="history.go(-1); return false;" title="Zurück"><i class="fa fa-arrow-left fa-lg"></i></a> |
    @if (erlassViewModel != null)
    {
        ViewContext.Writer.Write("<span class='breadcrumb-noesys-navigation'>");
        ViewContext.Writer.Write(" <a href='/'>Home</a>");
        ViewContext.Writer.Write("<span> > </span>");
        @Html.ActionLink("Erlasse", "Index", "Erlasse", new { area = "Admin" }, new { @class = "" })
        ViewContext.Writer.Write("<span> > </span>");
        @Html.ActionLink("Fassungen von Erlass «" + erlassViewModel.Titel + "» (" + @erlassViewModel.SrNummer + ")", "Index", "Erlassfassungen", new { area = "Admin", id = erlassViewModel.ErlassID }, new { @class = "" })
        ViewContext.Writer.Write("<span> > </span>");
        ViewContext.Writer.Write("<span>Pflichten von Erlasslassung vom " + @erlassfassungViewModel.Beschluss.ToString("dd.MM.yyyy") + "</span>");
        ViewContext.Writer.Write("</span>");
    }
}

@Html.Partial("_HtmlEditPopup", User.IsInRole(Role.ProjectManager))

@Html.Partial("_PflichtenGridView", Model)


<script type="text/javascript">
    //<![CDATA[
    
    var objektSelection = null;

    function ObjektSelectionInit(pflichtID, grid, selectbox, countSelector, resultSelector) {
        if (objektSelection == null) {
            objektSelection = new SelectionHelper(
                '@Url.Action("SaveObjects", "Pflichten", new { Area = "Admin" })',
                 "{0}"
                );
        }
        objektSelection.AddSelection(pflichtID, grid, selectbox, countSelector, resultSelector);
    }

    function ObjektSelectionChanged(pflichtID, e) {
        objektSelection.SelectionChanged(pflichtID, "ObjektID;Name", e);
    }

    function OnSubmitObjektClick(pflichtID) {
        objektSelection.OnSubmitClick(pflichtID);
    }

    // ]]>
</script>

<script type="text/javascript">
    //init batchEditHelper
    var batchEditDeleteHelper = new BatchEditDeleteHelper(
        "PflichtenGridView",
        "PflichtID",
        "Beschreibung",
        "@Resources.View_Pflichten_DeleteText"
    );
</script>