@using System.Web.UI.WebControls
@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Resources.Properties

@model IQueryable<NeoSysLCS.Repositories.ViewModels.ObjektViewModel>



<div class="selection_form">
    <div class="row">
        <div class="col-lg-2">

            <div class="text">
                Selektierte Werte:
            </div>
            @Html.DevExpress().ListBox(
                settings =>
                {
                    settings.Name = "SelectedObjekteRows_" + ViewData["PflichtID"];
                    settings.Properties.EnableItemsVirtualRendering = DefaultBoolean.False;
                    settings.Properties.EnableClientSideAPI = true;
                    settings.Height = 320;
                    settings.Width = Unit.Percentage(100);
                    settings.Properties.ValueField = "ObjektID";
                    settings.Properties.ValueType = typeof(int);
                    settings.Properties.ItemStyle.Wrap = DefaultBoolean.True;
                    
                }).GetHtml()
            <div class="text">
                @Resources.Popup_Ausgewaehlt:&nbsp;<strong id='objekteCount_@ViewData["PflichtID"]'>0</strong>
            </div>
        </div>

        <div class="col-lg-10">
            @{
                Html.RenderAction("RowSelectionPartial", new { id = ViewData["PflichtID"] });
            }
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
            @if (User.IsInRole(Role.ObjectGuru))
            {
                @Html.DevExpress().Button(settings =>
                {
                    settings.Name = "btnSubmit_" + ViewData["PflichtID"];
                    settings.Text = Resources.Button_Zuordnung_speichern;

                    settings.ControlStyle.CssClass = "pull-right";
                    settings.ClientSideEvents.Click = @"function(s,e){
                            OnSubmitObjektClick(" + @ViewData["PflichtID"] + @");
                        }";
                }).GetHtml()
            }


            <div id='objekteResult_@ViewData["PflichtID"]' class="pull-right label label-success"></div>
        </div>
    </div>

</div>
