@using System.Globalization
@using System.Web.UI.WebControls
@using Microsoft.Ajax.Utilities
@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Site.Helpers
@using NeoSysLCS.Resources.Properties


@{
    UnitOfWork unitOfWork = new UnitOfWork();
    var sprachen = unitOfWork.SpracheRepository.Get();
    var grid = Html.DevExpress().GridView(settings =>
    {
        settings.Name = "KundenStandortView_" + ViewData["KundeID"];
        GridViewHelper.ApplyDefaultSettings(settings);

        settings.CallbackRouteValues = new { Controller = "Kunden", Action = "KundenStandortPartialView", kundeId = ViewData["KundeID"], parentTab = ViewData["ParentTab"] };
        settings.SettingsEditing.BatchUpdateRouteValues = new { Controller = "Kunden", Action = "_KundenStandortPartialViewUpdate", kundeId = ViewData["KundeID"], parentTab = ViewData["ParentTab"]  };
        settings.CustomActionRouteValues = new { Controller = "Kunden", Action = "customActionCopyAndUnarchive", kundeId = ViewData["KundeID"], parentTab = ViewData["ParentTab"] };

        settings.SettingsLoadingPanel.Mode = GridViewLoadingPanelMode.ShowOnStatusBar;

        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;

        var copybtn = new GridViewCommandColumnCustomButton();
        copybtn.ID = "CopyCustomer"; //todo Sprache
        copybtn.Text = "<img title='Kunde kopieren' src='/Content/images/copy-24.png'>"; //todo Sprache

        var archivebtn = new GridViewCommandColumnCustomButton();
        archivebtn.ID = "Archive"; //todo Sprache
        archivebtn.Text = "<img title='Archiv' src='/Content/images/archive.png'>"; //todo Sprache

        settings.ClientSideEvents.BeginCallback = "OnBeginCallback";
        settings.ClientSideEvents.CustomButtonClick = "OnCustomButtonClick";
        settings.ClientSideEvents.EndCallback = "OnEndCallback";

        if (User.IsInRole(Role.ProjectManager))


        {

            settings.SettingsEditing.Mode = GridViewEditingMode.Batch;
            settings.SettingsEditing.BatchEditSettings.EditMode = GridViewBatchEditMode.Row;
            settings.SettingsBehavior.ConfirmDelete = true;

            settings.CommandColumn.Visible = true;

            settings.CommandColumn.ShowNewButtonInHeader = true;
            settings.CommandColumn.ShowDeleteButton = false;
            settings.CommandColumn.CustomButtons.Add(archivebtn);
            settings.CommandColumn.CustomButtons.Add(copybtn);


        }
        settings.KeyFieldName = "StandortID";

        settings.SettingsPager.Visible = true;
        settings.Settings.ShowGroupPanel = false;
        settings.Settings.ShowFilterRow = true;
        settings.SettingsBehavior.AllowSelectByRowClick = true;

        //columns
        settings.Columns.Add(column =>
        {
            column.Caption = "#";

            column.ReadOnly = true;
            column.EditFormSettings.Visible = DefaultBoolean.False;

            column.SetDataItemTemplateContent(container =>
            {
                if (container.KeyValue != null)
                {
                    Html.DevExpress().HyperLink(hyperlink =>
                    {
                        var keyValue = container.KeyValue;
                        hyperlink.Name = "Objekte" + keyValue;
                        hyperlink.Properties.Text = "Standortobjekte";
                        hyperlink.NavigateUrl = Url.Action("Index", "StandortObjekt", new { StandortID = keyValue });

                    }).Render();
                    ViewContext.Writer.Write("<br />");
                    Html.DevExpress().HyperLink(hyperlink =>
                    {
                        var keyValue = container.KeyValue;
                        hyperlink.Name = "Kundendokumente" + keyValue;
                        hyperlink.Properties.Text = "Kundendokumente";
                        hyperlink.NavigateUrl = Url.Action("Index", "Kundendokumente", new { id = keyValue });

                    }).Render();
                    ViewContext.Writer.Write("<br />");
                    Html.DevExpress().HyperLink(hyperlink =>
                    {
                        var keyValue = container.KeyValue;
                        hyperlink.Name = "Berichte" + keyValue;
                        hyperlink.Properties.Text = "Berichte";
                        hyperlink.NavigateUrl = Url.Action("Index", "StandortBerichte", new { standortID = keyValue });

                    }).Render();
                }
            });
            column.SetEditItemTemplateContent(c =>
            {
                @Html.DevExpress().TextBox(TextBoxSettings =>
                {
                    TextBoxSettings.Name = "txt";
                    TextBoxSettings.ReadOnly = true;
                    TextBoxSettings.ClientVisible = false;
                }).GetHtml();
            });

        });
        MVCxGridViewColumn idCol = settings.Columns.Add("StandortID");
        idCol.EditFormSettings.Visible = DefaultBoolean.False;
        idCol.SetEditItemTemplateContent(c => Html.DevExpress().TextBox(TextBoxSettings =>
        {
            TextBoxSettings.Name = "txtId";

            TextBoxSettings.ReadOnly = true;
            TextBoxSettings.ClientVisible = false;

        }).Render());

        MVCxGridViewColumn nameCol = settings.Columns.Add("Name");
        nameCol.SortAscending();

        settings.Columns.Add("InterneNotiz", MVCxGridViewColumnType.Memo);


        MVCxGridViewColumn rechteColumn = settings.Columns.Add("Kontakte");
        rechteColumn.Caption = "Kontakte";

        rechteColumn.SetDataItemTemplateContent(container => Html.DevExpress().HyperLink(hyperlink =>
        {

            var keyValue = container.KeyValue;

            string linktext = "";

            var mylist = (IQueryable<Kontakt>)DataBinder.Eval(container.DataItem, "Kontakte");
            if (mylist != null)
                mylist.ForEach(u => linktext += u.FullName + ", ");


            if (User.IsInRole(Role.ProjectManager))
            {
                linktext = (linktext == "" ? "bearbeiten" : linktext.Substring(0, linktext.Length - 2));
            }
            else
            {
                linktext = (linktext == "" ? "" : linktext.Substring(0, linktext.Length - 2));
            }

            hyperlink.Properties.Text = (keyValue == null ? "" : linktext);

            if (User.IsInRole(Role.ProjectManager) && keyValue != null)
            {
                hyperlink.Attributes.Add("onClick", "javascript:ShowDetailPopup('" + Url.Action("ShowKontaktePopUp", "Kunden", new { StandortID = keyValue, KundeID = ViewData["KundeID"] }) + "','pcModalMode_Kontakte" + ViewData["KundeID"] + "');");
                hyperlink.NavigateUrl = Url.Content("#");
            }

        }).Render());

        //hide edit box
        rechteColumn.EditFormSettings.Visible = DefaultBoolean.False;
        rechteColumn.SetEditItemTemplateContent(c => Html.DevExpress().TextBox(TextBoxSettings =>
        {
            TextBoxSettings.Name = "txt2";

            TextBoxSettings.ReadOnly = true;
            TextBoxSettings.ClientVisible = false;

        }).Render());



        MVCxGridViewColumn herausgeberColumn = settings.Columns.Add("Herausgeber");

        herausgeberColumn.Caption = "Herausgeber";

        herausgeberColumn.SetDataItemTemplateContent(container => Html.DevExpress().HyperLink(hyperlink =>
        {

            var keyValue = container.KeyValue;

            string linktext = "";

            var currentsprache = sprachen.FirstOrDefault(r => r.Lokalisierung == CultureInfo.CurrentUICulture.TwoLetterISOLanguageName);

            var mylist = (IQueryable<Herausgeber>)DataBinder.Eval(container.DataItem, "Herausgeber");
            if (mylist != null)
            {
                foreach (var herausgeber in mylist)
                {
                    //Fixed NEOS-394 Kundendokumente können nicht bearbeitet werden
                    string uebersetzung = (currentsprache.Lokalisierung == "de" ? (herausgeber.NameDE ?? herausgeber.NameFR ?? herausgeber.NameIT ?? herausgeber.NameEN) : null) ??
                                                        (currentsprache.Lokalisierung == "fr" ? (herausgeber.NameFR ?? herausgeber.NameDE ?? herausgeber.NameIT ?? herausgeber.NameEN) : null) ??
                                                        (currentsprache.Lokalisierung == "it" ? (herausgeber.NameIT ?? herausgeber.NameDE ?? herausgeber.NameFR ?? herausgeber.NameEN) : null) ??
                                                        (currentsprache.Lokalisierung == "en" ? (herausgeber.NameEN ?? herausgeber.NameDE ?? herausgeber.NameFR ?? herausgeber.NameIT) : null);
                    linktext += uebersetzung + ", ";
                }

            }

            if (User.IsInRole(Role.ProjectManager))
            {
                linktext = (linktext == "" ? "bearbeiten" : linktext.Substring(0, linktext.Length - 2));
            }
            else
            {
                linktext = (linktext == "" ? "" : linktext.Substring(0, linktext.Length - 2));
            }

            hyperlink.Properties.Text = (keyValue == null ? "" : linktext);

            if (User.IsInRole(Role.ProjectManager) && keyValue != null)
            {
                hyperlink.Attributes.Add("onClick", "javascript:ShowDetailPopup('" + Url.Action("ShowHerausgeberPopUp", "Kunden", new { StandortID = keyValue, KundeID = ViewData["KundeID"] }) + "','pcModalMode_Herausgeber" + ViewData["KundeID"] + "');");
                hyperlink.NavigateUrl = Url.Content("#");
            }

        }).Render());

        herausgeberColumn.EditFormSettings.Visible = DefaultBoolean.False;
        //hide edit box
        herausgeberColumn.SetEditItemTemplateContent(c => Html.DevExpress().TextBox(TextBoxSettings =>
        {
            TextBoxSettings.Name = "txt3";

            TextBoxSettings.ReadOnly = true;
            TextBoxSettings.ClientVisible = false;

        }).Render());


        MVCxGridViewColumn rechtsColumn = settings.Columns.Add("Rechtsbereiche");
        rechtsColumn.Caption = "Rechtsbereiche";

        rechtsColumn.SetDataItemTemplateContent(container => Html.DevExpress().HyperLink(hyperlink =>
        {

            var keyValue = container.KeyValue;

            string linktext = "";

            var mylist = (IQueryable<Rechtsbereich>)DataBinder.Eval(container.DataItem, "Rechtsbereiche");
            if (mylist != null)
            {
                foreach (var rechtsbereich in mylist)
                {
                    string name = rechtsbereich.NameDE ?? rechtsbereich.NameFR ?? rechtsbereich.NameIT ?? rechtsbereich.NameEN;
                    linktext += name + ", ";
                }

            }

            if (User.IsInRole(Role.ProjectManager))
            {
                linktext = (linktext == "" ? "bearbeiten" : linktext.Substring(0, linktext.Length - 2));
            }
            else
            {
                linktext = (linktext == "" ? "" : linktext.Substring(0, linktext.Length - 2));
            }


            hyperlink.Properties.Text = (keyValue == null ? "" : linktext);
            if (User.IsInRole(Role.ProjectManager) && keyValue != null)
            {
                hyperlink.Attributes.Add("onClick", "javascript:ShowDetailPopup('" + Url.Action("ShowRechtsbereichePopUp", "Kunden", new { StandortID = keyValue, KundeID = ViewData["KundeID"] }) + "','pcModalMode_Rechtsbereiche" + ViewData["KundeID"] + "');");
                hyperlink.NavigateUrl = Url.Content("#");
            }
        }).Render());
        //hide edit box
        rechtsColumn.EditFormSettings.Visible = DefaultBoolean.False;
        rechtsColumn.SetEditItemTemplateContent(c => Html.DevExpress().TextBox(TextBoxSettings =>
        {
            TextBoxSettings.Name = "txt4";

            TextBoxSettings.ReadOnly = true;
            TextBoxSettings.ClientVisible = false;

        }).Render());


        MVCxGridViewColumn sprachenColumn = settings.Columns.Add("Sprachen");
        sprachenColumn.Caption = "Sprachen";

        sprachenColumn.SetDataItemTemplateContent(container => Html.DevExpress().HyperLink(hyperlink =>
        {

            //@Html.ActionLink("Click this link to invoke the MVC Popup Extension in modal mode", "", null, new {href = "javascript:pcModalMode.Show();"});
            var keyValue = container.KeyValue;

            string linktext = "";
            var mylist = (IQueryable<Sprache>)DataBinder.Eval(container.DataItem, "Sprachen");
            if (mylist != null)
            {
                mylist.ForEach(u => linktext += u.Name + ", ");
            }

            if (User.IsInRole(Role.ProjectManager))
            {
                linktext = (linktext == "" ? "bearbeiten" : linktext.Substring(0, linktext.Length - 2));
            }
            else
            {
                linktext = (linktext == "" ? "" : linktext.Substring(0, linktext.Length - 2));
            }


            hyperlink.Properties.Text = (keyValue == null ? "" : linktext);
            if (User.IsInRole(Role.ProjectManager) && keyValue != null)
            {
                hyperlink.Attributes.Add("onClick", "javascript:ShowDetailPopup('" + Url.Action("ShowSprachenPopUp", "Kunden", new { StandortID = keyValue, KundeID = ViewData["KundeID"] }) + "','pcModalMode_Sprachen" + ViewData["KundeID"] + "');");
                hyperlink.NavigateUrl = Url.Content("#");
            }
        }).Render());
        //hide edit box
        sprachenColumn.EditFormSettings.Visible = DefaultBoolean.False;
        sprachenColumn.SetEditItemTemplateContent(c => Html.DevExpress().TextBox(TextBoxSettings =>
        {
            TextBoxSettings.Name = "txt5";

            TextBoxSettings.ReadOnly = true;
            TextBoxSettings.ClientVisible = false;

        }).Render());


        MVCxGridViewColumn standortErlassImportColumn = settings.Columns.Add("ErlassImportDoc");
        standortErlassImportColumn.Caption = "Erlasse Importieren";
        standortErlassImportColumn.SetDataItemTemplateContent(container =>
        {

            var standortId = container.KeyValue;
            ViewData["standortId"] = standortId;

            var url = (string)DataBinder.Eval(container.DataItem, "ErlassImportDoc");
            if (!string.IsNullOrEmpty(url) && url != "---")
            {
                url = !url.StartsWith("http") ? "http://" + url : url;
                Html.DevExpress().HyperLink(hyperlink =>
                {
                    hyperlink.EncodeHtml = false;
                    hyperlink.Properties.Text = "<i class=\" fa fa-link\"></i>";
                    hyperlink.NavigateUrl = url;
                    hyperlink.Properties.Target = "_blank";
                }).Render();

                ViewContext.Writer.Write("&nbsp;&nbsp;");
            }

            if (standortId != null)
            {
                // new file
                Html.DevExpress().HyperLink(hyperlink =>
                {
                    hyperlink.EncodeHtml = false;
                    hyperlink.Properties.Text = "<i class=\" fa fa-edit\"></i>";
                    hyperlink.Attributes.Add("onClick", "javascript:ShowDetailPopup('" + Url.Action("ShowUploadStandortErlassImportPopup", "Kunden", new { standortId = standortId, url = url, field = "ErlassImportDoc" }) + "','pcModalMode_StandortErlassImportFileUpload" + ViewData["KundeID"] + "');");
                    hyperlink.NavigateUrl = Url.Content("#");

                }).Render();
            }
        });

        standortErlassImportColumn.EditFormSettings.Visible = DefaultBoolean.False;
        standortErlassImportColumn.SetEditItemTemplateContent(c => Html.DevExpress().TextBox(TextBoxSettings =>
        {
            TextBoxSettings.Name = "txt4";
            TextBoxSettings.ReadOnly = true;
            TextBoxSettings.ClientVisible = false;
        }).Render());


        settings.ClientSideEvents.BatchEditStartEditing = @"function(s,e){
            currentGrid = s;
        }";

        settings.ClientSideEvents.ClipboardCellPasting = @"function(s,e){
            e.cancel = currentGrid != s;
        }";

        //Detail Template
        settings.SettingsDetail.ShowDetailRow = true;
        settings.SetDetailRowTemplateContent(c =>
        {
            Html.RenderAction("StandortUebersetzungenGridView", new { standortId = c.KeyValue });
        });

    });
    if (ViewData["EditError"] != null)
    {
        grid.SetEditErrorText((string)ViewData["EditError"]);
    }
}

@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = "StandortID";
}).GetHtml()


@Html.DevExpress().PopupControl(settings =>
{
    settings.Name = "pcModalMode_Kontakte" + ViewData["KundeID"];  

    settings.ScrollBars = ScrollBars.Auto;
    settings.ShowPageScrollbarWhenModal = true;
    settings.Width = 800;
    settings.Height = 600;
    settings.ResizingMode = ResizingMode.Live;
    settings.ShowRefreshButton = true;
    settings.AutoUpdatePosition = true;
    settings.AllowDragging = true;
    settings.CloseAction = CloseAction.CloseButton;
    settings.PopupAnimationType = AnimationType.None;
    settings.HeaderText = "Kontakte";
    settings.Modal = true;
    settings.AllowResize = true;
    settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
    settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
    settings.ClientSideEvents.Closing = "function(s, e){ " + ViewData["ParentTab"] + ".Refresh(); }";

}).GetHtml()

@Html.DevExpress().PopupControl(settings =>
{

    settings.Name = "pcModalMode_Herausgeber" + ViewData["KundeID"];
    settings.ScrollBars = ScrollBars.Auto;
    settings.ShowPageScrollbarWhenModal = true;
    /*settings.Width = 1000;
    settings.Height = 600;*/
    settings.ResizingMode = ResizingMode.Live;
    settings.ShowRefreshButton = true;
    settings.AutoUpdatePosition = true;
    settings.AllowDragging = true;
    settings.CloseAction = CloseAction.CloseButton;
    settings.PopupAnimationType = AnimationType.None;
    settings.HeaderText = "Herausgeber";
    settings.Modal = true;
    settings.AllowResize = true;
    settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
    settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
    settings.ClientSideEvents.Init = "function(s, e) { s.SetWidth(1000); s.SetHeight(650); }";
    settings.ClientSideEvents.Shown = "function(s, e) { s.SetWidth(1000); s.SetHeight(650); }";
    settings.ClientSideEvents.Closing = "function(s, e){ " + ViewData["ParentTab"] + ".Refresh(); }";

}).GetHtml()

@Html.DevExpress().PopupControl(settings =>
{

    settings.Name = "pcModalMode_Rechtsbereiche" + ViewData["KundeID"];
    settings.ScrollBars = ScrollBars.Auto;
    settings.ShowPageScrollbarWhenModal = true;
    settings.Width = 800;
    settings.Height = 600;
    settings.ResizingMode = ResizingMode.Live;
    settings.ShowRefreshButton = true;
    settings.AutoUpdatePosition = true;
    settings.AllowDragging = true;
    settings.CloseAction = CloseAction.CloseButton;
    settings.PopupAnimationType = AnimationType.None;
    settings.HeaderText = "Rechtsbereiche";
    settings.Modal = true;
    settings.AllowResize = true;
    settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
    settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
    settings.ClientSideEvents.Init = "function(s, e) { s.SetWidth(800); s.SetHeight(650); }";
    settings.ClientSideEvents.Shown = "function(s, e) { s.SetWidth(800); s.SetHeight(650); }";
    settings.ClientSideEvents.Closing = "function(s, e){ " + ViewData["ParentTab"] + ".Refresh(); }";

}).GetHtml()

@Html.DevExpress().PopupControl(settings =>
{

    settings.Name = "pcModalMode_Sprachen" + ViewData["KundeID"];
    settings.ScrollBars = ScrollBars.Auto;
    settings.ShowPageScrollbarWhenModal = true;
    settings.Width = 800;
    settings.Height = 600;
    settings.ResizingMode = ResizingMode.Live;
    settings.ShowRefreshButton = true;
    settings.AutoUpdatePosition = true;
    settings.AllowDragging = true;
    settings.CloseAction = CloseAction.CloseButton;
    settings.PopupAnimationType = AnimationType.None;
    settings.HeaderText = "Sprachen";
    settings.Modal = true;
    settings.AllowResize = true;
    settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
    settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
    settings.ClientSideEvents.Init = "function(s, e) { s.SetWidth(800); s.SetHeight(650); }";
    settings.ClientSideEvents.Shown = "function(s, e) { s.SetWidth(800); s.SetHeight(650); }";
    settings.ClientSideEvents.Closing = "function(s, e){ " + ViewData["ParentTab"] + ".Refresh(); }";

}).GetHtml()

@Html.DevExpress().PopupControl(settings =>
{
    settings.Name = "pcModalMode_StandortBerichtFileUpload" + ViewData["KundeID"];
    settings.ScrollBars = ScrollBars.None;
    settings.ShowPageScrollbarWhenModal = true;
    settings.ResizingMode = ResizingMode.Live;
    settings.ShowRefreshButton = false;
    settings.AutoUpdatePosition = true;
    settings.AllowDragging = true;
    settings.CloseAction = CloseAction.CloseButton;
    settings.PopupAnimationType = AnimationType.None;
    settings.HeaderText = "File upload";
    settings.Modal = true;
    settings.AllowResize = true;
    settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
    settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
    settings.ClientSideEvents.Init = "function(s, e) { s.SetWidth(400); s.SetHeight(200); }";
    settings.ClientSideEvents.Shown = "function(s, e) { s.SetWidth(400); s.SetHeight(200); }";
    settings.ClientSideEvents.Closing = "function(s, e){ " + ViewData["ParentTab"] + ".Refresh(); }";
}).GetHtml()

@Html.DevExpress().PopupControl(settings =>
{
    settings.Name = "pcModalMode_StandortErlassImportFileUpload" + ViewData["KundeID"];
    settings.ScrollBars = ScrollBars.None;
    settings.ShowPageScrollbarWhenModal = true;
    settings.ResizingMode = ResizingMode.Live;
    settings.ShowRefreshButton = false;
    settings.AutoUpdatePosition = true;
    settings.AllowDragging = true;
    settings.CloseAction = CloseAction.CloseButton;
    settings.PopupAnimationType = AnimationType.None;
    settings.HeaderText = Resources.UploadFile;
    settings.Modal = true;
    settings.AllowResize = true;
    settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
    settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
    settings.ClientSideEvents.Init = "function(s, e) { s.SetWidth(600); s.SetHeight(350); }";
    settings.ClientSideEvents.Shown = "function(s, e) { s.SetWidth(600); s.SetHeight(350); }";
    settings.ClientSideEvents.Closing = "function(s, e){ " + ViewData["ParentTab"] + ".Refresh(); }";
}).GetHtml()