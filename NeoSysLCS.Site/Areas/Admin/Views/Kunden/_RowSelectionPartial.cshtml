@using System.Web.UI.WebControls
@using NeoSysLCS.Repositories;


@Html.DevExpress().GridView(
    settings =>
    {
        UnitOfWork unitOfWork = new UnitOfWork();

        //GridViewHelper.ApplyDefaultSettings(settings);
        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;

        settings.Name = "gvRowSelection";
        settings.KeyFieldName = "RechtsbereichID";
        settings.Width = Unit.Percentage(100);
        settings.CallbackRouteValues = new { Controller = "Kontakte", Action = "RechtsbereicheRowSelectionPartial", id = ViewData["KontaktID"] };
        settings.ControlStyle.CssClass = "grid pull-right";
        settings.Styles.AlternatingRow.Enabled = DefaultBoolean.True;
        
        var column = settings.Columns.Add("Name");
        
        
        //needed for selectionhelper
        settings.SettingsCookies.StoreFiltering = false;
        
        //setting preselected
        settings.PreRender = (sender, e) =>
        {
            MVCxGridView gridView = sender as MVCxGridView;
            if (gridView != null)
                foreach (var obj in unitOfWork.RechtsbereichRepository.GetAllByKontakt(Convert.ToInt32(ViewData["KontaktID"])))
                {
                    gridView.Selection.SelectRowByKey(obj.RechtsbereichID);
                }
        };  
        

        settings.CommandColumn.Visible = true;
        settings.CommandColumn.ShowSelectCheckbox = true;
        settings.SettingsPager.Visible = true;
        settings.Settings.ShowFilterRow = true;
        settings.Settings.ShowFilterRowMenu = true;
        settings.Settings.ShowFilterBar = GridViewStatusBarMode.Auto;
        settings.SettingsBehavior.AllowSelectByRowClick = false;
        
        settings.SettingsPager.Visible = true;
        //register events for selectionhelper
        settings.ClientSideEvents.Init = @"function(s, e){
            SelectionInit(" +
                                      ViewData["KontaktID"] + @", 
                                   s, 
                                   SelectedRows" + @", 
                                   '#count" + @"', 
                                   '#Productresult" + @"');                                       
                                    }";
        settings.ClientSideEvents.SelectionChanged = @"function(s,e){
                SelectionChanged(" + ViewData["KontaktID"] + @", e);
            }";

    }).BindToLINQ(string.Empty, string.Empty, (s, e) =>
    {
        var unitOfWork = new UnitOfWork();
        var query = unitOfWork.RechtsbereichRepository.GetAllRechtsbereichViewModels();
        e.QueryableSource = query;
        e.KeyExpression = "RechtsbereichID";
    }).GetHtml()
    
