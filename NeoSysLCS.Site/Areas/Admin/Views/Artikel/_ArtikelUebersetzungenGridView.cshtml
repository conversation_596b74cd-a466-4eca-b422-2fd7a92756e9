@using NeoSysLCS.DomainModel.Models;
@using NeoSysLCS.Repositories;
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Site.Helpers

@Html.DevExpress().GridView(
    settings =>
    {
        UnitOfWork unitOfWork = new UnitOfWork();

        GridViewHelper.ApplyUebersetzungsSettings(settings);

        settings.Name = "ArtikelUebersetzungenGridView_" + ViewData["ErlassID"] + "_" + ViewData["ArtikelID"];
        settings.KeyFieldName = "ArtikelUebersetzungID";

        settings.CallbackRouteValues = new { Controller = "Artikel", Action = "ArtikelUebersetzungenGridView", ErlassID = ViewData["ErlassID"], ArtikelID = ViewData["ArtikelID"] };

        settings.SettingsEditing.BatchUpdateRouteValues = new { Controller = "Artikel", Action = "ArtikelUebersetzungenGridViewBatchEditUpdate", ErlassID = ViewData["ErlassID"], ArtikelID = ViewData["ArtikelID"] };

        settings.CommandColumn.Visible = false;

        if (User.IsInRole(Role.ProjectManager))
        {
            settings.SettingsEditing.Mode = GridViewEditingMode.Batch;
        }
        //Columns
        settings.Columns.Add(column =>
        {
            column.FieldName = "Quelle";

            column.SetDataItemTemplateContent(
                container =>
                {
                    var url = DataBinder.Eval(container.DataItem, "Quelle");
                    string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url);
                    ViewContext.Writer.Write(htmlLink);
                });
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "SpracheID";
            column.Caption = "Sprache";
            column.ReadOnly = true;
            column.ColumnType = MVCxGridViewColumnType.ComboBox;
            column.Settings.FilterMode = ColumnFilterMode.DisplayText;
            var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;

            comboBoxProperties.DataSource = unitOfWork.SpracheRepository.Get().ToList();
            comboBoxProperties.TextField = "Name";
            comboBoxProperties.ValueField = "SpracheID";
            comboBoxProperties.ValueType = typeof(int);
        });

        settings.ClientSideEvents.BatchEditStartEditing = @"function(s,e){
                currentGrid = s;
            }";

        settings.ClientSideEvents.ClipboardCellPasting = @"function(s,e){
                e.cancel = currentGrid != s;
            }";

        //}).BindToLINQ(string.Empty, string.Empty, (s, e) =>
        //{
        //    e.QueryableSource = Model;
        //    e.KeyExpression = "ArtikelUebersetzungID";
        //}).GetHtml()
    }).Bind(Model).GetHtml()