@using NeoSysLCS.Repositories;
@using NeoSysLCS.Site.Helpers

@Html.DevExpress().GridView(
    settings =>
    {
        UnitOfWork unitOfWork = new UnitOfWork();

        GridViewHelper.ApplyUebersetzungsSettings(settings);

        settings.Name = "PrivacyPolicyUebersetzungenGridView_" + ViewData["privacyPolicyID"];
        settings.KeyFieldName = "PrivacyPolicyUebersetzungID";

        settings.CallbackRouteValues = new { Controller = "PrivacyPolicy", Action = "UebersetzungenGridView", privacyPolicyID = ViewData["privacyPolicyID"] };

        settings.SettingsEditing.BatchUpdateRouteValues = new { Controller = "PrivacyPolicy", Action = "UebersetzungenGridViewBatchEditUpdate", privacyPolicyID = ViewData["privacyPolicyID"] };
        settings.SettingsEditing.Mode = GridViewEditingMode.Batch;

        settings.CommandColumn.Visible = false;


        //Columns
        settings.Columns.Add("Name");

        settings.Columns.Add(column =>
        {
            column.FieldName = "Version";
            column.SetDataItemTemplateContent(
                container =>
                {
                    var versionNumber = (string) DataBinder.Eval(container.DataItem, "Version");
                    var version = "v";
                    if (versionNumber != "---")
                    {
                        version += versionNumber;
                    }
                    else
                    {
                        version += "0";
                    }
                    ViewContext.Writer.Write(version);
                });
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "Quelle";
            column.EditFormSettings.Visible = DefaultBoolean.False;
            column.SetEditItemTemplateContent(c => Html.DevExpress().TextBox(TextBoxSettings =>
            {
                TextBoxSettings.Name = "txt3";

                TextBoxSettings.ReadOnly = true;
                TextBoxSettings.ClientVisible = false;

            }).Render());
            column.SetDataItemTemplateContent(
                container =>
                {
                    var privacyPolicyID = ViewData["privacyPolicyID"];
                    var spracheId = DataBinder.Eval(container.DataItem, "SpracheID");

                    var url = (string)DataBinder.Eval(container.DataItem, "Quelle");
                    if (!string.IsNullOrEmpty(url) && url != "---")
                    {
                        url = !url.StartsWith("http") ? "http://" + url : url;
                        Html.DevExpress().HyperLink(hyperlink =>
                        {
                            hyperlink.EncodeHtml = false;
                            hyperlink.Properties.Text = "<i class=\" fa fa-link\"></i>";
                            hyperlink.NavigateUrl = url;
                            hyperlink.Properties.Target = "_blank";
                        }).Render();

                        ViewContext.Writer.Write("&nbsp;&nbsp;");
                    }

                    if (privacyPolicyID != null)
                    {
                        // new file
                        Html.DevExpress().HyperLink(hyperlink =>
                        {
                            hyperlink.EncodeHtml = false;
                            hyperlink.Properties.Text = "<i class=\" fa fa-edit\"></i>";
                            hyperlink.Attributes.Add("onClick", "javascript:ShowDetailPopup('" + Url.Action("ShowUploadPopup", "PrivacyPolicy", new { privacyPolicyID = privacyPolicyID, url = url, field = "Quelle", languageId = spracheId }) + "','pcModalMode_PrivacyPolicyFileUpload');");
                            hyperlink.NavigateUrl = Url.Content("#");

                        }).Render();
                    }
                });

        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "SpracheID";
            column.Caption = "Sprache";
            column.ReadOnly = true;
            column.ColumnType = MVCxGridViewColumnType.ComboBox;
            column.Settings.FilterMode = ColumnFilterMode.DisplayText;
            var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;

            comboBoxProperties.DataSource = unitOfWork.SpracheRepository.Get().ToList();
            comboBoxProperties.TextField = "Name";
            comboBoxProperties.ValueField = "SpracheID";
            comboBoxProperties.ValueType = typeof(int);
        });

        settings.ClientSideEvents.BatchEditStartEditing = @"function(s,e){
                currentGrid = s;
            }";

        settings.ClientSideEvents.ClipboardCellPasting = @"function(s,e){
                e.cancel = currentGrid != s;
            }";

        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);
    }).BindToLINQ(string.Empty, string.Empty, (s, e) =>
    {
        e.QueryableSource = Model;
        e.KeyExpression = "PrivacyPolicyUebersetzungID";
    }).GetHtml()