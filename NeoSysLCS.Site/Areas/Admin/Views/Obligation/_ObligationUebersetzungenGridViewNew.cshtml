@using NeoSysLCS.DomainModel.Models;
@using NeoSysLCS.Repositories;
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Site.Helpers

@Html.DevExpress().GridView(
    settings =>
    {
        UnitOfWork unitOfWork = new UnitOfWork();

        GridViewHelper.ApplyUebersetzungsSettings(settings);

        settings.Name = "ObligationUebersetzungenGridViewNew_" + ViewData["ObligationID"];
        settings.KeyFieldName = "ObligationUebersetzungID";

        settings.CallbackRouteValues = new { Controller = "Obligation", Action = "ObligationUebersetzungenGridView", obligationID = ViewData["ObligationID"] };

        settings.SettingsEditing.BatchUpdateRouteValues = new { Controller = "Obligation", Action = "ObligationUebersetzungenGridViewBatchEditUpdate", obligationID = ViewData["ObligationID"] };
        if (User.IsInRole(Role.ProjectManager))
        {
            settings.SettingsEditing.Mode = GridViewEditingMode.Batch;
        }

        settings.CommandColumn.Visible = false;

        //Columns
        settings.Columns.Add("Name");
        settings.Columns.Add(column =>
        {
            column.FieldName = "SpracheID";
            column.Caption = "Sprache";
            column.ReadOnly = true;
            column.ColumnType = MVCxGridViewColumnType.ComboBox;
            column.Settings.FilterMode = ColumnFilterMode.DisplayText;
            var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;

            comboBoxProperties.DataSource = unitOfWork.SpracheRepository.Get().ToList();
            comboBoxProperties.TextField = "Name";
            comboBoxProperties.ValueField = "SpracheID";
            comboBoxProperties.ValueType = typeof(int);
        });

        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);

        settings.ClientSideEvents.BatchEditStartEditing = @"function(s,e){
                currentGrid = s;
	        }";


        settings.ClientSideEvents.ClipboardCellPasting = @"function(s,e){
                e.cancel = currentGrid != s;
	        }";

    }).BindToLINQ(string.Empty, string.Empty, (s, e) =>
    {
        e.QueryableSource = Model;
        e.KeyExpression = "ObligationUebersetzungID";
    }).GetHtml()