@using System.Web.UI.WebControls
@using NeoSysLCS.Repositories
@using NeoSysLCS.Resources.Properties

@model IQueryable<NeoSysLCS.Repositories.ViewModels.ObligationViewModel>


@{
    ViewContext.Writer.Write("<h1 class='page-header'>" + "Pflichten" + "</h1>");
}

@section Scripts {
    @Scripts.Render("~/Scripts/helpers/BatchEditDeleteHelper.js")
}

<script type="text/javascript">
</script>

@Html.Partial("_ObligationGridView", Model)




