@using System.Globalization
@using NeoSysLCS.Repositories;
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Site.Helpers

@Html.DevExpress().GridView(
    settings =>
    {
        IUnitOfWork unitOfWork = new UnitOfWork();
        
        settings.Name = "gvRowSelection";
        GridViewHelper.ApplyDefaultSettings(settings);
        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;

        
        settings.KeyFieldName = "ObjektID";
        settings.CallbackRouteValues = new { Controller = "ForderungObjekte", Action = "ObjekteSelectionPartial", id = ViewData["ForderungsversionID"], erlassfassungId = @ViewData["ErlassfassungID"] };
        settings.ControlStyle.CssClass = "grid";

        //needed for selectionhelper
        settings.SettingsCookies.StoreFiltering = false;

        settings.SettingsBehavior.AllowSelectByRowClick = false;
        settings.Settings.ShowGroupPanel = false;

        settings.CommandColumn.ShowSelectCheckbox = true;
        settings.CommandColumn.Visible = true;
        if (User.IsInRole(Role.ObjectGuru))
        {
            //register events for selectionhelper
            settings.ClientSideEvents.Init = @"function(s, e){
            SelectionInit(" +
                                          ViewData["ForderungsversionID"] + @", 
                                   s, 
                                   SelectedRows" + @", 
                                   '#count" + @"', 
                                   '#result" + @"');                                       
                                    }";
            settings.ClientSideEvents.SelectionChanged = @"function(s,e){
                SelectionChanged(" + ViewData["ForderungsversionID"] + @", e);
            }";
        }
        
        //Columns
        var nameCol = settings.Columns.Add("Name");
        nameCol.SortAscending();
        settings.Columns.Add("Beschreibung");

        settings.Columns.Add(column =>
        {
            column.FieldName = "ObjektkategorieID";
            column.Caption = "Objektkategorie";
            column.ReadOnly = true;
            
            column.ColumnType = MVCxGridViewColumnType.ComboBox;
            column.Settings.FilterMode = ColumnFilterMode.DisplayText;
            var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;

            comboBoxProperties.DataSource = unitOfWork.ObjektkategorieRepository.GetAllObjektkategorieViewModels().ToList();
            comboBoxProperties.TextField = "Name";
            comboBoxProperties.ValueField = "ObjektkategorieID";
            comboBoxProperties.ValueType = typeof(int);
        });

        //setting preselected
        settings.PreRender = (sender, e) => 
        {
            MVCxGridView gridView = sender as MVCxGridView;
            if (gridView != null)
                foreach (var obj in unitOfWork.ObjektRepository.GetByForderung(Convert.ToInt32(ViewData["ForderungsversionID"])))
                {
                    gridView.Selection.SelectRowByKey(obj.ObjektID);
                }
        };
      

    }).BindToLINQ(string.Empty, string.Empty, (s, e) =>
    {
        var unitOfWork = new UnitOfWork();
        var query = unitOfWork.ObjektRepository.GetAllObjektViewModels();
        e.QueryableSource = query;
        e.KeyExpression = "ObjektID";
    }).GetHtml()

