@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Areas.Admin.Controllers
@using NeoSysLCS.Site.Utilities.Export

@model IQueryable<NeoSysLCS.Repositories.ViewModels.FAQViewModel>

@{
    ViewBag.Title = "FAQ";
    ViewContext.Writer.Write("<h1 class='page-header'>" + ViewBag.Title + "</h1>");
}

@Html.Partial("_FAQGridView", Model)

<script type="text/javascript">
    //<![CDATA[

    function ShowDetailPopup(url, win) {
        modal = eval(win);
        if (window.height < 600) {
            modal.SetHeight(window.height - 50);
        } else {
            modal.SetHeight(600);
        }

        if (window.width < 800) {
            modal.SetWidth(window.width - 50);
        } else {
            modal.SetWidth(800);
        }

        modal.SetContentUrl(url);
        modal.Show();
    }

    function CloseDetailPopup(win) {
        modal = eval(win);
        modal.Hide();
    }
    // ]]>
</script>