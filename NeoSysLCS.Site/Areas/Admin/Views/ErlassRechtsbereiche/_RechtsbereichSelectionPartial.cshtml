@using NeoSysLCS.Repositories;
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Site.Helpers


@Html.DevExpress().GridView(
    settings =>
    {
        IUnitOfWork unitOfWork = new UnitOfWork();
        
        settings.Name = "gvRowSelection_rechtsbereiche";
        GridViewHelper.ApplyDefaultSettings(settings);
        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;
        
        
        settings.KeyFieldName = "RechtsbereichID";
        settings.CallbackRouteValues = new { Controller = "ErlassRechtsbereiche", Action = "RechtsbereichSelectionPartial", id = ViewData["ErlassID"], erlassfassungId = @ViewData["ErlassfassungID"] };
        settings.ControlStyle.CssClass = "grid";
        
        //needed for selectionhelper
        settings.SettingsCookies.StoreFiltering = false;
        
        settings.CommandColumn.Visible = true;
        settings.CommandColumn.ShowSelectCheckbox = true;
        settings.SettingsBehavior.AllowSelectByRowClick = false;
        settings.Settings.ShowGroupPanel = false;
        
        if (User.IsInRole(Role.ProjectManager))
        {
            //register events for selectionhelper
            settings.ClientSideEvents.Init = @"function(s, e){
            SelectionInit(" +
                                          ViewData["ErlassID"] + @", 
                                   s, 
                                   SelectedRows" + @", 
                                   '#count" + @"', 
                                   '#result" + @"');                                       
                                    }";
            settings.ClientSideEvents.SelectionChanged = @"function(s,e){
                SelectionChanged(" + ViewData["ErlassID"] + @", e);
            }";
        }
        
        //Columns
        var column = settings.Columns.Add("Name");
        column.SortAscending();
        

        //setting preselected
        settings.PreRender = (sender, e) =>
        {
            MVCxGridView gridView = sender as MVCxGridView;
            if (gridView != null)
            {
                int erlassId = Convert.ToInt32(ViewData["ErlassID"]);
                var rechtsbereichUebersetzungen = unitOfWork.RechtsbereichRepository.GetByErlass(erlassId);
                foreach (var rechtsbereichUebersetzung in rechtsbereichUebersetzungen)
                {
                    gridView.Selection.SelectRowByKey(rechtsbereichUebersetzung.RechtsbereichID);
                }
            }
        };

    }).BindToLINQ(string.Empty, string.Empty, (s, e) =>
        {
            var unitOfWork = new UnitOfWork();
            var query = unitOfWork.RechtsbereichRepository.GetAllRechtsbereichViewModels();
            e.QueryableSource = query;
            e.KeyExpression = "RechtsbereichID";
        }).GetHtml()
