@using System.Web.UI.WebControls
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Resources.Properties
@model IQueryable<NeoSysLCS.Repositories.ViewModels.ErlassViewModel>



<div class="selection_form">
    <div class="row">
        <div class="col-lg-2">

            <div class="text">
                Selektierte Werte:
            </div>
            @Html.DevExpress().ListBox(
                settings =>
                {
                    settings.Name = "SelectedRows";
                    settings.Properties.EnableItemsVirtualRendering = DefaultBoolean.False;
                    settings.Properties.EnableClientSideAPI = true;
                    settings.Height = 350;
                    settings.Width = Unit.Percentage(100);
                    settings.Properties.ValueField = "ErlassID";
                    settings.Properties.ValueType = typeof(int);
                }).GetHtml()
            <div class="text">
                @Resources.Popup_Ausgewaehlt:&nbsp;<strong id="count">0</strong>
            </div>
        </div>
        <div class="col-lg-10">
            @{
                Html.RenderAction("HauptErlassSelectionPartial", new { id = ViewData["KommentarID"] });
            }
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
                @Html.DevExpress().Button(settings =>
                    {
                        settings.Name = "btnSubmit";
                        settings.Text = Resources.Button_Zuordnung_speichern;

                        settings.UseSubmitBehavior = true;
                        settings.ControlStyle.CssClass = "pull-right";
                        settings.ClientSideEvents.Click = @"function(s,e){
                            OnSubmitSelectionClick(" + @ViewData["KommentarID"] + @");
                        }";
                    }).GetHtml()
            <div id=Productresult class="pull-right label label-success"></div>
            @*<div id="result" class="pull-right label label-success"></div>*@
        </div>
    </div>
</div>
