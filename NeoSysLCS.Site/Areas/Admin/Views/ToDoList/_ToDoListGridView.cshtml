@using System.Web.UI.WebControls
@using NeoSysLCS.Repositories;
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Site.Helpers
@using System.Globalization;
@using NeoSysLCS.Resources.Properties
@using System;
@using System.Threading;
@using System.Web;
@using System.Web.Mvc;
@using NeoSysLCS.Site.Helpers;

@Html.DevExpress().GridView(
    settings =>
    {
        UnitOfWork unitOfWork = new UnitOfWork();

        GridViewHelper.ApplyUebersetzungsSettings(settings);
        var sprache = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
        settings.Name = "ToDoListGridView";
        settings.KeyFieldName = "ErstelltAm";

        settings.CallbackRouteValues = new { Controller = "ToDoList", Action = "ToDoListGridView" };

        settings.CommandColumn.Visible = false;

        settings.Columns.Add(column =>
        {
            column.FieldName = "Name";
            column.Caption = "Name";
            column.Width = Unit.Percentage(8);

            column.ColumnType = MVCxGridViewColumnType.Default;
            column.ReadOnly = true;
            var prop = (column.PropertiesEdit as EditProperties);
            if (prop != null)
            {
                prop.ValidationSettings.Display = Display.Dynamic;
            }
        });


        settings.Columns.Add(column =>
        {
            column.FieldName = "IsOffer";
            column.Caption = "Type";
            column.ColumnType = MVCxGridViewColumnType.Default;
            column.Width = Unit.Percentage(5);
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "ProjektleiterName";
            column.Caption = Resources.Entitaet_Kunde_PL;
            column.Width = Unit.Percentage(8);

            column.ColumnType = MVCxGridViewColumnType.Default;
            column.ReadOnly = true;
            var prop = (column.PropertiesEdit as EditProperties);
            if (prop != null)
            {
                prop.ValidationSettings.Display = Display.Dynamic;
            }
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "OfferConsultationDate";
            column.Caption = "OfferConsultationDate";
            column.ColumnType = MVCxGridViewColumnType.DateEdit;
            column.Width = Unit.Percentage(5);
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "AnalyseDate";
            column.Caption = "AnalyseDate";
            column.ColumnType = MVCxGridViewColumnType.DateEdit;
            column.Width = Unit.Percentage(5);
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "EducationDate";
            column.Caption = "EducationDate";
            column.ColumnType = MVCxGridViewColumnType.DateEdit;
            column.Width = Unit.Percentage(5);
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "InquiryDate";
            column.Caption = "InquiryDate";
            column.ColumnType = MVCxGridViewColumnType.DateEdit;
            column.Width = Unit.Percentage(5);
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "ContractCreatedDate";
            column.Caption = "ContractCreatedDate";
            column.ColumnType = MVCxGridViewColumnType.DateEdit;
            column.Width = Unit.Percentage(5);
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "DocumentReceivedDate";
            column.Caption = "DocumentReceivedDate";
            column.ColumnType = MVCxGridViewColumnType.DateEdit;
            column.Width = Unit.Percentage(5);
        });


        settings.CustomColumnDisplayText += (s, e) =>
        {
            if (e.Column.FieldName == "IsOffer")
            {
                if ((bool)e.Value)
                {
                    e.DisplayText = "Offer";
                }
                else
                {
                    e.DisplayText = "Kunde";
                }
            }
        };

        settings.Columns.Add(column =>
        {
            column.FieldName = "DueDate";
            column.Caption = "Due Date";
            column.ColumnType = MVCxGridViewColumnType.DateEdit;
            column.Width = Unit.Percentage(5);
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "Message";
            column.Caption = "Message";
            column.Width = Unit.Percentage(35);

            column.ColumnType = MVCxGridViewColumnType.Default;
            column.ReadOnly = true;
            var prop = (column.PropertiesEdit as EditProperties);
            if (prop != null)
            {
                prop.ValidationSettings.Display = Display.Dynamic;
            }
        });


        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);
    }).Bind(Model).GetHtml()