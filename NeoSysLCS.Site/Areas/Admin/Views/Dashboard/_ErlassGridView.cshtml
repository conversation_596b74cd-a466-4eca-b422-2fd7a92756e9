@using System.Web.UI.WebControls
@using NeoSysLCS.Site.Areas.Admin.Controllers
@using NeoSysLCS.Site.Helpers
@model IQueryable<NeoSysLCS.Repositories.ViewModels.ErlassViewModel>

@{


    var grid = Html.DevExpress().GridView(settings =>
    {
        settings.Name = "erlassgridView";
        settings.KeyFieldName = "ErlassID";

        //GridViewHelper.ApplyDefaultSettings(settings);
        settings.Styles.AlternatingRow.Enabled = DefaultBoolean.True;


        settings.CallbackRouteValues = new { Controller = "Dashboard", Action = "ErlassGridView", Area = "Admin" };

        settings.CommandColumn.Visible = false;
        settings.CommandColumn.ShowNewButtonInHeader = false;
        settings.CommandColumn.ShowDeleteButton = false;
        settings.CommandColumn.ShowEditButton = false;
        settings.SettingsPager.Visible = false;

        settings.Width = Unit.Percentage(50);

        //Columns
        foreach (MVCxGridViewColumn col in ErlasseController.BaseGridViewSettings.GetDataColumns(ViewContext, Model))
        {
            settings.Columns.Add(col);
        }

        MVCxGridViewColumn gueltigVon = settings.Columns.Add("BearbeitetAm", MVCxGridViewColumnType.DateEdit);
        gueltigVon.Caption = "Bearbeitet";

        settings.Columns.Add("BearbeitetVon");

        MVCxGridViewColumn ErstelltAm = settings.Columns.Add("ErstelltAm", MVCxGridViewColumnType.DateEdit);
        ErstelltAm.Caption = "Erstellt";
        ErstelltAm.SortDescending();


        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);

    });

    if (ViewData["EditError"] != null)
    {
        grid.SetEditErrorText((string)ViewData["EditError"]);
    }
}

@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
    {
        e.QueryableSource = Model;
        e.KeyExpression = "ErlassID";
    }).GetHtml()