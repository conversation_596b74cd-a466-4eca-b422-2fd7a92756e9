@using System.Web.UI.WebControls
@using NeoSysLCS.Repositories

@Html.DevExpress().PageControl(pageControlSettings =>
{
    pageControlSettings.Name = "DashboardPageControl";
    pageControlSettings.Width = Unit.Percentage(100);
    pageControlSettings.SaveStateToCookies = true;
    pageControlSettings.CallbackRouteValues = new { Controller = "Dashboard", Action = "DashboardPageControlCallbacksPartial" };

    UnitOfWork _unitOfWork = new UnitOfWork();

    pageControlSettings.TabPages.Add("Kunden").SetContent(() =>
    {
        Html.RenderPartial("~/Areas/Admin/Views/Dashboard/_KundeGridView.cshtml", _unitOfWork.KundeRepository.GetAllKundenViewModels());
    });

    pageControlSettings.TabPages.Add("Objekte").SetContent(() =>
    {
        Html.RenderPartial("~/Areas/Admin/Views/Dashboard/_ObjektGridView.cshtml", _unitOfWork.ObjektRepository.GetAllObjektViewModels());
    });

    pageControlSettings.TabPages.Add("Erlasse").SetContent(() =>
    {
        Html.RenderPartial("~/Areas/Admin/Views/Dashboard/_ErlassGridView.cshtml", _unitOfWork.ErlassRepository.GetAllErlassViewModels());
    });

    pageControlSettings.TabPages.Add("Standorte").SetContent(() =>
    {
        Html.RenderPartial("~/Areas/Admin/Views/Dashboard/_StandortGridView.cshtml", _unitOfWork.StandortRepository.GetAllStandortViewModels());
    });

}).GetHtml()