@using NeoSysLCS.Repositories;
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Site.Helpers


@{
    var grid = Html.DevExpress().GridView(
        settings =>
        {
            UnitOfWork unitOfWork = new UnitOfWork();

            GridViewHelper.ApplyDefaultSettings(settings);
            settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;

            settings.Name = "gvRowSelection";
            settings.KeyFieldName = "ErlassID";

            settings.CallbackRouteValues = new { Controller = "ConsultationErlasse", Action = "ErlasseSelectionPartial", id = ViewData["ConsultationId"] };
            settings.ControlStyle.CssClass = "grid";

            settings.SettingsCookies.StoreFiltering = false;
            settings.CommandColumn.Visible = true;
            settings.CommandColumn.ShowSelectCheckbox = true;
            settings.SettingsBehavior.AllowSelectByRowClick = false;
            settings.Settings.ShowGroupPanel = false;

            MVCxGridViewColumn column = settings.Columns.Add("Titel");
            column.Caption = "Titel";

            MVCxGridViewColumn columnNumber = settings.Columns.Add("SrNummer");
            columnNumber.Caption = "Nummer";


            //setting preselected
            settings.PreRender = (sender, e) =>
            {
                MVCxGridView gridView = sender as MVCxGridView;
                if (gridView != null)
                    foreach (var obj in unitOfWork.ErlassRepository.GetAllErlassViewModelsByConsultation(Convert.ToInt32(ViewData["ConsultationId"])))
                    {
                        gridView.Selection.SelectRowByKey(obj.ErlassID);
                    }
            };

            settings.ClientSideEvents.Init = @"function(s, e){
            SelectionInit(" +
                                          ViewData["ConsultationId"] + @",
                                   s,
                                   SelectedRows" + @",
                                   '#count" + @"',
                                   '#Productresult" + @"');
                                    }";
            settings.ClientSideEvents.SelectionChanged = @"function(s,e){
                SelectionChanged(" + ViewData["ConsultationId"] + @", e);
            }";

        });
}

@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
    {
        var unitOfWork = new UnitOfWork();
        var query = unitOfWork.ErlassRepository.GetAllErlassViewModels();
        e.QueryableSource = query;
        e.KeyExpression = "ErlassID";
    }).GetHtml()