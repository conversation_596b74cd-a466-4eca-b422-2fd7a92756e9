@using NeoSysLCS.DomainModel.Models;
@using NeoSysLCS.Repositories;
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Site.Helpers
@using NeoSysLCS.Repositories.ViewModels

@model IQueryable<ChecklistQuestionViewModel>

@Html.DevExpress().GridView(
    settings =>
    {
        UnitOfWork unitOfWork = new UnitOfWork();

        GridViewHelper.ApplyUebersetzungsSettings(settings);
        //GridViewHelper.ApplyDefaultSettings(settings);

        settings.Name = "ChecklistQuestionsGridView_" + ViewData["ChecklistID"];
        settings.KeyFieldName = "ChecklistQuestionID";

        settings.CallbackRouteValues = new { Controller = "Checklist", Action = "ChecklistQuestionsGridView", ChecklistID = ViewData["ChecklistID"] };
        settings.CommandColumn.Visible = false;
        settings.ClientSideEvents.DetailRowExpanding = @"function(s, e) {  
            s.CollapseAllDetailRows();
        }";

        settings.Columns.Add(column =>
        {
            column.FieldName = "HeaderTitle";
            column.Caption = "Abschnitt";
            column.PropertiesEdit.EncodeHtml = false;
            column.GroupIndex = 0;
            column.Settings.AllowSort = DefaultBoolean.True;
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "Numeration";
            column.Caption = "Nummerierung";
            column.Settings.AllowSort = DefaultBoolean.True;
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "ChecklistType";
            column.Caption = "Typ";
            column.Settings.AllowSort = DefaultBoolean.True;
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "Title";
            column.Caption = "Titel";
            column.PropertiesEdit.EncodeHtml = false;
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "SharedImage";
            column.Caption = "Image";
            column.Width = 120;
            column.SetDataItemTemplateContent(c =>
            {
                ViewContext.Writer.Write(String.Format("<img width=\"150px\" src=\"{0}\" />", DataBinder.Eval(c.DataItem, "SharedImage")));
            });
            column.Settings.SortMode = DevExpress.XtraGrid.ColumnSortMode.Custom;
        });
    }).BindToLINQ(string.Empty, string.Empty, (s, e) =>
    {
        e.QueryableSource = Model;
        e.KeyExpression = "ChecklistQuestionID";
    }).GetHtml()