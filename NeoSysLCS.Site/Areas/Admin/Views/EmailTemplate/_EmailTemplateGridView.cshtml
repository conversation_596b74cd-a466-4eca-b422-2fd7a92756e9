@using System.Web.UI.WebControls
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers
@model NeoSysLCS.Repositories.ViewModels.EmailTemplateViewModel

@{
    Html.DevExpress().PageControl(pageControlSettings =>
    {

        pageControlSettings.Name = "EmailTemplateEdit";
        pageControlSettings.Width = Unit.Percentage(100);
        pageControlSettings.SaveStateToCookies = true;

        pageControlSettings.TabPages.Add("Deutsch").SetContent(() =>
        {
            ViewContext.Writer.Write("<label for='txtSubjectDE'>Subject:</label>");
            @Html.DevExpress().TextBox(tbSettings =>
            {
                tbSettings.Name = "txtSubjectDE";
                tbSettings.Width = Unit.Percentage(100);
                tbSettings.Properties.NullText = "Betreff eingeben...";
                tbSettings.Text = Model.De_Sub ?? string.Empty;

            }).GetHtml();

            ViewContext.Writer.Write("<br>");

            ViewContext.Writer.Write("<label for='htmlEditorPopupDE'>Context:</label>");
            @Html.DevExpress().HtmlEditor(editorSettings =>
            {
                editorSettings.Name = "htmlEditorPopupDE";
                HtmlEditorHelper.ApplyHtmlEditorSettings(editorSettings);
                editorSettings.Width = Unit.Percentage(100);
                editorSettings.Height = Unit.Pixel(300);
                editorSettings.Html = Model.De_Body ?? string.Empty;
            }).Render();
        });
        pageControlSettings.TabPages.Add("Français").SetContent(() =>
        {
            ViewContext.Writer.Write("<label for='txtSubjectFR'>Subject:</label>");
            @Html.DevExpress().TextBox(tbSettings =>
            {
                tbSettings.Name = "txtSubjectFR";
                tbSettings.Width = Unit.Percentage(100);
                tbSettings.Properties.NullText = "Betreff eingeben...";
                tbSettings.Text = Model.Fr_Sub ?? string.Empty;
            }).GetHtml();

            ViewContext.Writer.Write("<br>");

            ViewContext.Writer.Write("<label for='htmlEditorPopupFR'>Context:</label>");
            @Html.DevExpress().HtmlEditor(editorSettings =>
            {
                editorSettings.Name = "htmlEditorPopupFR";
                HtmlEditorHelper.ApplyHtmlEditorSettings(editorSettings);
                editorSettings.Width = Unit.Percentage(100);
                editorSettings.Height = Unit.Pixel(300);
                editorSettings.Html = Model.Fr_Body ?? string.Empty;
            }).Render();
        });

        pageControlSettings.TabPages.Add("Italiano").SetContent(() =>
        {
            ViewContext.Writer.Write("<label for='txtSubjectIT'>Subject:</label>");
            @Html.DevExpress().TextBox(tbSettings =>
            {
                tbSettings.Name = "txtSubjectIT";
                tbSettings.Width = Unit.Percentage(100);
                tbSettings.Properties.NullText = "Betreff eingeben...";
                tbSettings.Text = Model.It_Sub ?? string.Empty;
            }).GetHtml();

            ViewContext.Writer.Write("<br>");

            ViewContext.Writer.Write("<label for='htmlEditorPopupIT'>Context:</label>");
            @Html.DevExpress().HtmlEditor(editorSettings =>
            {
                editorSettings.Name = "htmlEditorPopupIT";
                HtmlEditorHelper.ApplyHtmlEditorSettings(editorSettings);
                editorSettings.Width = Unit.Percentage(100);
                editorSettings.Height = Unit.Pixel(300);
                editorSettings.Html = Model.It_Body ?? string.Empty;
            }).Render();
        });

        pageControlSettings.TabPages.Add("English").SetContent(() =>
        {
            ViewContext.Writer.Write("<label for='txtSubjectEN'>Subject:</label>");
            @Html.DevExpress().TextBox(tbSettings =>
            {
                tbSettings.Name = "txtSubjectEN";
                tbSettings.Width = Unit.Percentage(100);
                tbSettings.Properties.NullText = "Betreff eingeben...";
                tbSettings.Text = Model.En_Sub ?? string.Empty;
            }).GetHtml();

            ViewContext.Writer.Write("<br>");

            ViewContext.Writer.Write("<label for='htmlEditorPopupEn'>Context:</label>");
            @Html.DevExpress().HtmlEditor(editorSettings =>
            {
                editorSettings.Name = "htmlEditorPopupEN";
                HtmlEditorHelper.ApplyHtmlEditorSettings(editorSettings);
                editorSettings.Width = Unit.Percentage(100);
                editorSettings.Height = Unit.Pixel(300);
                editorSettings.Html = Model.En_Body ?? string.Empty;
            }).Render();
        });

    }).Render();

    ViewContext.Writer.Write("<div id='saveErrorMessage' style='display: none; color: red; font-weight: bold;'></div>");
    ViewContext.Writer.Write("<br>");

    Html.DevExpress().Button(buttonSettings =>
    {
        buttonSettings.Name = "btnSave";
        buttonSettings.ControlStyle.CssClass = "btn btn-default pull-right";
        buttonSettings.Width = 80;
        buttonSettings.Text = Resources.Button_Speichern;
        buttonSettings.ClientSideEvents.Click = "function(s, e){ saveEmailTemplate(); }";

    }).Render();
}


<script>

    function saveEmailTemplate() {
        var data = {
            De_Sub: ASPxClientTextBox.Cast('txtSubjectDE').GetText(),
            Fr_Sub: ASPxClientTextBox.Cast('txtSubjectFR').GetText(),
            It_Sub: ASPxClientTextBox.Cast('txtSubjectIT').GetText(),
            En_Sub: ASPxClientTextBox.Cast('txtSubjectEN').GetText(),
            De_Body: ASPxClientHtmlEditor.Cast('htmlEditorPopupDE').GetHtml(),
            Fr_Body: ASPxClientHtmlEditor.Cast('htmlEditorPopupFR').GetHtml(),
            IT_Body: ASPxClientHtmlEditor.Cast('htmlEditorPopupIT').GetHtml(),
            En_Body: ASPxClientHtmlEditor.Cast('htmlEditorPopupEN').GetHtml()
        };

        $.ajax({
            url: '@Url.Action("SaveEmailTemplate", "EmailTemplate")',
            type: 'POST',
            data: JSON.stringify(data),
            contentType: 'application/json',
            success: function (response) {
                alert("Template saved!");
            },
            error: function () {
                var errorDiv = document.getElementById("saveErrorMessage");
                errorDiv.style.display = "block";
                errorDiv.innerText = "Error saving email templates.";
            }
        });
    }
</script>