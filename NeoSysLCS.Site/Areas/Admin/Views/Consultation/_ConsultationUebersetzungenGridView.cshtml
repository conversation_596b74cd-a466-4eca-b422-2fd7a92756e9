@using NeoSysLCS.Repositories;
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Site.Helpers

@Html.DevExpress().GridView(
    settings =>
    {
        UnitOfWork unitOfWork = new UnitOfWork();

        GridViewHelper.ApplyUebersetzungsSettings(settings);

        settings.Name = "ConsultationUebersetzungenGridView_" + ViewData["ConsultationID"];
        settings.KeyFieldName = "ConsultationUebersetzungID";
        settings.CallbackRouteValues = new { Controller = "Consultation", Action = "ConsultationUebersetzungenGridView", ConsultationID = ViewData["ConsultationID"] };

        settings.SettingsEditing.BatchUpdateRouteValues = new { Controller = "Consultation", Action = "ConsultationUebersetzungenGridViewBatchEditUpdate", ConsultationID = ViewData["ConsultationID"] };
        settings.CommandColumn.Visible = false;

        if (User.IsIn<PERSON>ole(Role.ProjectManager))
        {
            settings.SettingsEditing.Mode = GridViewEditingMode.Batch;
        }
        //Columns
        settings.Columns.Add("Title");

        settings.Columns.Add(column =>
        {
            column.FieldName = "Quelle";
            column.Caption = "Quelle";
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "SpracheID";
            column.Caption = "Sprache";
            column.ReadOnly = true;

            column.ColumnType = MVCxGridViewColumnType.ComboBox;
            column.Settings.FilterMode = ColumnFilterMode.DisplayText;
            var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;

            comboBoxProperties.DataSource = unitOfWork.SpracheRepository.Get().ToList();
            comboBoxProperties.TextField = "Name";
            comboBoxProperties.ValueField = "SpracheID";
            comboBoxProperties.ValueType = typeof(int);
        });

        settings.ClientSideEvents.BatchEditStartEditing = @"function(s,e){
                currentGrid = s;
	        }";

        settings.ClientSideEvents.ClipboardCellPasting = @"function(s,e){
                e.cancel = currentGrid != s;
	        }";

        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);

    }).Bind(Model).GetHtml()