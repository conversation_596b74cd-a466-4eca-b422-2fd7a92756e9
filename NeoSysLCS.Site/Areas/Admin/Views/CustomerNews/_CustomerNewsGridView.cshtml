@using NeoSysLCS.Resources.Properties
@using System.Web.UI.WebControls
@using NeoSysLCS.Site.Helpers
@using NeoSysLCS.Repositories
@using NeoSysLCS.Site.Areas.Admin.Controllers

@model IQueryable<NeoSysLCS.Repositories.ViewModels.CustomerNewsViewModel>

@{
    UnitOfWork unitOfWork = new UnitOfWork();
    unitOfWork.SpracheRepository.Get();
    SessionHelper sessionHelper = new SessionHelper();

    var grid = Html.DevExpress().GridView(settings =>
    {
        settings.Name = CustomerNewsController.BaseGridViewSettings.GetGridViewName();
        GridViewHelper.ApplyDefaultSettings(settings);
        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;

        settings.SettingsResizing.ColumnResizeMode = ColumnResizeMode.NextColumn;

        settings.KeyFieldName = CustomerNewsController.BaseGridViewSettings.GetGridViewKeyFieldName();

        settings.CallbackRouteValues = new { Controller = "CustomerNews", Action = "CustomerNewsGridView", customerNewsId = ViewData["CustomerNewsID"] };

        settings.SettingsEditing.BatchUpdateRouteValues = new { Controller = "CustomerNews", Action = "CustomerNewsGridViewBatchEditUpdate" };

        settings.SettingsEditing.Mode = GridViewEditingMode.Batch;
        settings.CommandColumn.Visible = true;
        settings.CommandColumn.ShowNewButtonInHeader = true;
        settings.CommandColumn.ShowDeleteButton = true;


        foreach (MVCxGridViewColumn col in CustomerNewsController.BaseGridViewSettings.GetDataColumns(ViewContext, Model))
        {
            settings.Columns.Add(col);
        }

        settings.Columns.Add(column =>
        {
            column.FieldName = "Text";
            column.Caption = Resources.View_News_News;
            column.ColumnType = MVCxGridViewColumnType.Memo;
            var memoProp = column.PropertiesEdit as MemoProperties;
            memoProp.EncodeHtml = false;
            column.ReadOnly = true;
            var prop = (column.PropertiesEdit as EditProperties);
            if (prop != null)
            {
                prop.ValidationSettings.Display = Display.Dynamic;
            }
            column.SetDataItemTemplateContent(
                container =>
                {
                    var text = (string)DataBinder.Eval(container.DataItem, "Text");
                    if (string.IsNullOrEmpty(text))
                    {
                        ViewContext.Writer.Write(Resources.Fehler_Keine_Uebersetzung);
                    }
                    else
                    {
                        ViewContext.Writer.Write(StringHelper.TruncateHtml(text, 50));
                    }
                }
                );
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "Link";
            column.Caption = Resources.View_News_Link;
            column.SetDataItemTemplateContent(
                container =>
                {

                    var url = DataBinder.Eval(container.DataItem, "Link");
                    string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url, true);
                    ViewContext.Writer.Write(htmlLink);
                });

        });

        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);

        //Detail Template
        settings.SettingsDetail.ShowDetailRow = true;
        settings.SetDetailRowTemplateContent(c =>
        {
            Html.DevExpress().PageControl(pageControlSettings =>
            {
                pageControlSettings.Name = "CustomerNewsTranslationPageControl_" + DataBinder.Eval(c.DataItem, "CustomerNewsID");
                pageControlSettings.Width = Unit.Percentage(100);
                pageControlSettings.TabPages.Add("�bersetzungen").SetContent(() =>
                {

                    Html.RenderAction("CustomerNewsTranslationsGridView",
                        new
                        {
                            customerNewsID = DataBinder.Eval(c.DataItem, "CustomerNewsID")
                        });


                });

            }).Render();

        });
        settings.HtmlDataCellPrepared += (sender, e) =>
        {
            if (e.DataColumn.FieldName == "Text")
            {
                e.Cell.Attributes.Add(
                "onclick",
                "openPopupReadonly(" + settings.Name + ", '" + e.DataColumn.FieldName + "', " + e.VisibleIndex + ", '" + e.DataColumn.Caption + "')");
            }
        };

        settings.ClientSideEvents.BatchEditStartEditing = @"function(s,e){
    if(e.focusedColumn.fieldName == 'Text'){
                openPopupOnBatchEditBegin(s, e);
            }
    }";
    });



    if (ViewData["EditError"] != null)
    {
        grid.SetEditErrorText((string)ViewData["EditError"]);
    }
}

@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = "CustomerNewsID";
}).GetHtml()

@Html.DevExpress().PopupControl(settings =>
{
    settings.Name = "pcModalMode_CustomerNewsFileUpload";
    settings.ScrollBars = ScrollBars.None;
    settings.ShowPageScrollbarWhenModal = true;
    settings.ResizingMode = ResizingMode.Live;
    settings.ShowRefreshButton = false;
    settings.AutoUpdatePosition = true;
    settings.AllowDragging = true;
    settings.CloseAction = CloseAction.CloseButton;
    settings.PopupAnimationType = AnimationType.None;
    settings.HeaderText = "File upload";
    settings.Modal = true;
    settings.AllowResize = true;
    settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
    settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
    settings.ClientSideEvents.Init = "function(s, e) { s.SetWidth(400); s.SetHeight(200); }";
    settings.ClientSideEvents.Shown = "function(s, e) { s.SetWidth(400); s.SetHeight(200); }";
    settings.ClientSideEvents.Closing = "function(s, e){ location.reload(); }";
}).GetHtml()