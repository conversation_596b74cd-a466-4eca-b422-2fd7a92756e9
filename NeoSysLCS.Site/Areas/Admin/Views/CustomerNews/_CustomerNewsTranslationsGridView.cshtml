@using NeoSysLCS.DomainModel.Models;
@using NeoSysLCS.Repositories;
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Site.Helpers
@using System.Web.UI.WebControls;
@using NeoSysLCS.Resources.Properties
@Html.DevExpress().GridView(
    settings =>
    {
        UnitOfWork unitOfWork = new UnitOfWork();

        GridViewHelper.ApplyUebersetzungsSettings(settings);

        settings.Name = "CustomerNewsTranslation_" + ViewData["CustomerNewsID"];
        settings.KeyFieldName = "TranslationID";

        settings.CallbackRouteValues = new { Controller = "CustomerNews", Action = "CustomerNewsTranslationsGridView", customerNewsId = ViewData["CustomerNewsID"] };

        settings.SettingsEditing.BatchUpdateRouteValues = new { Controller = "CustomerNews", Action = "CustomerNewsTranslationsGridViewBatchEditUpdate", customerNewsId = ViewData["CustomerNewsID"] };
        if (User.IsIn<PERSON>ole(Role.ProjectManager))
        {
            settings.SettingsEditing.Mode = GridViewEditingMode.Batch;
        }

        settings.CommandColumn.Visible = false;

        settings.Columns.Add(column =>
        {
            column.FieldName = "Text";
            column.Caption = Resources.View_News_News;
            column.ColumnType = MVCxGridViewColumnType.Memo;
            var memoProp = column.PropertiesEdit as MemoProperties;
            memoProp.EncodeHtml = false;
            column.ReadOnly = true;
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "Link";
            column.Caption = Resources.View_News_Link;
            column.SetDataItemTemplateContent(
                container =>
                {
                    var url = DataBinder.Eval(container.DataItem, "Link");
                    string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url);
                    ViewContext.Writer.Write(htmlLink);
                });
        });


        settings.Columns.Add(column =>
        {
            column.FieldName = "LanguageID";
            column.Caption = "Sprache";
            column.ReadOnly = true;
            column.ColumnType = MVCxGridViewColumnType.ComboBox;
            column.Settings.FilterMode = ColumnFilterMode.DisplayText;
            var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;

            comboBoxProperties.DataSource = unitOfWork.SpracheRepository.Get().ToList();
            comboBoxProperties.TextField = "Name";
            comboBoxProperties.ValueField = "SpracheID";
            comboBoxProperties.ValueType = typeof(int);
        });

        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);

        settings.HtmlDataCellPrepared += (sender, e) =>
        {
            if (e.DataColumn.FieldName == "Text")
            {
                if (!User.IsInRole(Role.ProjectManager))
                {
                    //readonly
                    e.Cell.Attributes.Add(
                       "onclick",
                       "openPopupReadonly(" + settings.Name + ", '" + e.DataColumn.FieldName + "', " + e.VisibleIndex + ", '" + e.DataColumn.Caption + "')"
                       );

                }
            }
        };
        settings.ClientSideEvents.BatchEditStartEditing = @"function(s,e){
            if(e.focusedColumn.fieldName == 'Text'){
                        openPopupOnBatchEditBegin(s, e);
                    }
            }";

    }).BindToLINQ(string.Empty, string.Empty, (s, e) =>
    {
        e.QueryableSource = Model;
        e.KeyExpression = "TranslationID";
    }).GetHtml()