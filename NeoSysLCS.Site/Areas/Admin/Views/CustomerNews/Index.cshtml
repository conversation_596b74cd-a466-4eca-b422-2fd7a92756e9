@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Site.Utilities.Export

@model IQueryable<NeoSysLCS.Repositories.ViewModels.CustomerNewsViewModel>

@{
    ViewBag.Title = "News";
    ViewContext.Writer.Write("<h1 class='page-header'>" + ViewBag.Title + "</h1>");
}

@Html.Partial("_HtmlEditPopup", User.IsInRole(Role.ProjectManager))

@Html.Partial("_CustomerNewsGridView", Model)



<script type="text/javascript">
    //<![CDATA[

    function ShowDetailPopup(url, win) {
        modal = eval(win);

        if (window.height < 600) {
            modal.SetHeight(window.height - 50);
        } else {
            modal.SetHeight(600);
        }

        if (window.width < 800) {
            modal.SetWidth(window.width - 50);
        } else {
            modal.SetWidth(800);
        }

        modal.SetContentUrl(url);
        modal.Show();
    }

    function CloseDetailPopup(win) {
        modal = eval(win);
        modal.Hide();
    }

    var rowKeyValueToCopy;
    function OnBeginCallback(s, e) {
        if (e.command === "CUSTOMCALLBACK") {
            e.customArgs["key"] = rowKeyValueToCopy;
        }
    }
    function OnCustomButtonClick(s, e) {
        rowKeyValueToCopy = s.GetRowKey(e.visibleIndex);
        s.PerformCallback();
    }



    // ]]>
</script>