using System;
using System.Collections.Generic;
using System.Configuration;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Site.Cortec;
using NeoSysLCS.Site.Cortec.Dtos;
using NeoSysLCS.Site.Utilities;

namespace NeoSysLCS.Site.Tasks
{

	public class CortecTask
	{

		private readonly IUnitOfWork _unitOfWork;

		public CortecTask()
		{
			_unitOfWork = new UnitOfWork();
		}

		public async Task SyncCustomers()
		{
			Debug.WriteLine("Cortec sync triggered!");

			try
			{
				IQueryable<KundeCortec> dbKundeCortec = _unitOfWork.Context.KundeCortec;
				var client = CortecClientFactory.CreateDefaultClient();

				var customersList = await client.FetchCustomers();
				Debug.WriteLine("Fetched customers list from Cortec, size is: " + customersList.Count);
				foreach (var customerDto in customersList)
				{
					try
                    {
                        var dbCortecCustomer = _unitOfWork.KundeCortecRepository.GetKundeCortecByAuftragNr(customerDto.AuftragNr);
                        if (dbCortecCustomer != null)
                        {
                            // Update existing record
                            _unitOfWork.KundeCortecRepository.Update(updateKundeCortecModel(dbCortecCustomer, customerDto));
                            continue;
                        }

                        // Create new record
                        _unitOfWork.KundeCortecRepository.Insert(prepareKundeCortecModel(customerDto));

                        _unitOfWork.Save();
                    } catch (Exception ex)
					{
                        ExceptionUtility.LogException(ex, "Failed to sync Cortec customer " + customerDto.AuftragNr);
                    }       
				}
			}
			catch (Exception e)
			{
				ExceptionUtility.LogException(e, "Failed to sync Cortec customers list");
			}
		}

		private KundeCortec updateKundeCortecModel(KundeCortec kundeCortec, CustomerDto customerDto)
		{
			kundeCortec.AuftragNr = customerDto.AuftragNr;
            kundeCortec.AuftragID = customerDto.AuftragID;
			kundeCortec.Korrespondenz = customerDto.Korrespondenz;
			kundeCortec.AdrKorrespondenz = prepareKundeCortecCorrespondenceAddressUpdate(customerDto.AdrKorrespondenz, kundeCortec);
			kundeCortec.EmailKorrespondenz = customerDto.EmailKorrespondenz;
			kundeCortec.AuftragsVolumen = customerDto.AuftragsVolumen ?? 0;

			return kundeCortec;
		}

		private KundeCortec prepareKundeCortecModel(CustomerDto customerDto) {
			var kundeCortec = new KundeCortec
			{
				AuftragNr = customerDto.AuftragNr,
				AuftragID = customerDto.AuftragID,
				Korrespondenz = customerDto.Korrespondenz,
				AdrKorrespondenz = prepareKundeCortecCorrespondenceAddress(customerDto.AdrKorrespondenz),
				EmailKorrespondenz = customerDto.EmailKorrespondenz,
				AuftragsVolumen = customerDto.AuftragsVolumen ?? 0,
			};

			return kundeCortec;
		}

		private ICollection<KundeCortecCorrespondenceAddress> prepareKundeCortecCorrespondenceAddressUpdate(List<CorrespondenceAddressDto> adrKorrespondenzDto, KundeCortec kundeCortec) {
			if (kundeCortec.AdrKorrespondenz != null && kundeCortec.AdrKorrespondenz.Count > 0)
			{
				var addrsList = new List<KundeCortecCorrespondenceAddress>();
				foreach (var correspondenceAddressDto in adrKorrespondenzDto)
				{
					var isStored = false;
					foreach (var kundeCortecCorrespondenceAddr in kundeCortec.AdrKorrespondenz) {
						if (kundeCortecCorrespondenceAddr.StrasseNr == correspondenceAddressDto.StrasseNr
							&& kundeCortecCorrespondenceAddr.Ort == correspondenceAddressDto.Ort
							&& kundeCortecCorrespondenceAddr.PLZ == correspondenceAddressDto.PLZ)
                        {
							isStored = true;
                        } 
					}

					if (!isStored) {
						addrsList.Add(prepareKundeCortecCorrespondenceModel(correspondenceAddressDto));
					}
				}

				return addrsList.Concat(kundeCortec.AdrKorrespondenz).ToArray();
			}
			else {
				return prepareKundeCortecCorrespondenceAddress(adrKorrespondenzDto);
			}
		}
		
		private ICollection<KundeCortecCorrespondenceAddress> prepareKundeCortecCorrespondenceAddress(List<CorrespondenceAddressDto> adrKorrespondenzDto) {
			var adrKorrespondenz = new List<KundeCortecCorrespondenceAddress>();
			if (adrKorrespondenzDto != null) {
				foreach (CorrespondenceAddressDto correspondenceAddressDto in adrKorrespondenzDto) {
					adrKorrespondenz.Add(prepareKundeCortecCorrespondenceModel(correspondenceAddressDto));
				}
			}

			return adrKorrespondenz;
		}

		private KundeCortecCorrespondenceAddress prepareKundeCortecCorrespondenceModel(CorrespondenceAddressDto correspondenceAddressDto) {
			var kundeCortecCorrespondence = new KundeCortecCorrespondenceAddress
			{
				StrasseNr = correspondenceAddressDto.StrasseNr,
				PLZ = correspondenceAddressDto.PLZ,
				Ort = correspondenceAddressDto.Ort,
			};

			return kundeCortecCorrespondence;
		}


        public async Task CortecTaskCreation()
        {
            if (ConfigurationManager.AppSettings["CortecTaskCreationEnabled"] != "true")
            {
                ExceptionUtility.LogInfo("CortecTaskCreation is not enabled. Skipping sending!");
                return;
            }
            ExceptionUtility.LogInfo("CortecTask creation triggered!");
            try
            {
                var service = new CortecService();
                await service.SendUnsentTasks();
            }
            catch (Exception e)
            {
                ExceptionUtility.LogException(e, "Cortec task creation failed!");
            }
        }
	}
}
