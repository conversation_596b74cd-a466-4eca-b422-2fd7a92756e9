using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.WebPages;
using System.Xml.Linq;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.Helper;
using NeoSysLCS.Site.Suva;

namespace NeoSysLCS.Site.Tasks
{
    public class SuvaChecklistTask
    {

        private readonly IUnitOfWork _unitOfWork;

        public SuvaChecklistTask()
        {
            _unitOfWork = new UnitOfWork();
        }

        public async Task LoadChecklists()
        {
            Debug.WriteLine("SuvaChecklist triggered!");

            try
            {
                IQueryable<Checklist> dbChecklists = _unitOfWork.Context.Checklist;
                IQueryable<Erlass> erlasse = _unitOfWork.Context.Erlasse;

                var client = SuvaClientFactory.CreateDefaultClient();
                var clientFr = SuvaClientFactory.CreateClientForLocale("fr");
                var clientIt = SuvaClientFactory.CreateClientForLocale("it");
                var clientEn = SuvaClientFactory.CreateClientForLocale("en");

                var languages = new Dictionary<string, Sprache>()
                {
                    { "de", GetLanguageByLocale("de")},
                    { "fr", GetLanguageByLocale("fr")},
                    { "it", GetLanguageByLocale("it")},
                    { "en", GetLanguageByLocale("en")},
                };

                var checklists = await client.GetChecklists();
                foreach (var checklistDto in checklists)
                {
                    // find erlass by SR Nummer
                    var erlass = erlasse.FirstOrDefault(e => e.SrNummer == checklistDto.Id);
                    if (erlass == null)
                    {
                        continue;
                    }

                    // load checklist details for each language
                    var details =
                        new Dictionary<string, SuvaChecklistDetailsDto>
                        {
                            { "de",  await client.GetChecklistDetails(checklistDto.ItemId) },
                            { "fr",  await clientFr.GetChecklistDetails(checklistDto.ItemId) },
                            { "it",  await clientIt.GetChecklistDetails(checklistDto.ItemId) },
                            { "en",  await clientEn.GetChecklistDetails(checklistDto.ItemId) }
                        };

                    var dbChecklist = dbChecklists.FirstOrDefault(c => c.SuvaChecklistID == checklistDto.ItemId);
                    if (dbChecklist != null)
                    {
                        // Update existing checklist
                        UpdateExistingChecklistIfNeeded(dbChecklist, checklistDto, details, languages);
                        continue;
                    }


                    // create new checklist
                    SaveNewChecklist(checklistDto, details, languages, erlass);
                }

                Debug.WriteLine("SuvaChecklist load finished!");
            }
            catch (Exception e)
            {
                Debug.WriteLine("Failed to load checklists", e.ToString());
            }
        }

        private void SaveNewChecklist(
            SuvaChecklistDto dto,
            IReadOnlyDictionary<string, SuvaChecklistDetailsDto> details,
            IReadOnlyDictionary<string, Sprache> languages,
            Erlass erlass)
        {
            var checklist = BuildNewChecklist(dto, details, languages, erlass);

            int erlassfassungID = _unitOfWork.ErlassfassungRepository.getLatestErlassfassungIdByErlass(erlass.ErlassID);
            if (erlassfassungID != 0)
            {
                checklist.ErlassfassungID = erlassfassungID;
            }
            _unitOfWork.ChecklistRepository.Insert(checklist);

            var checkListHeaders = BuildNewChecklistHeaders(checklist, details, languages);
            foreach (var checklistHeader in checkListHeaders)
            {
                SetChecklistHeadersTranslations(checklistHeader, details, languages);
                _unitOfWork.ChecklistHeaderRepository.Insert(checklistHeader);

                var questions = BuildNewChecklistQuestions(checklist, details, languages, checklistHeader);
                foreach (var question in questions)
                {
                    SetChecklistQuestionsTranslations(question, details, languages);
                }

                _unitOfWork.ChecklistQuestionRepository.Insert(questions);

            }
            _unitOfWork.Save();
        }

        private void UpdateExistingChecklistIfNeeded(
            Checklist checklist, 
            SuvaChecklistDto dto,
            IReadOnlyDictionary<string, SuvaChecklistDetailsDto> details, 
            IReadOnlyDictionary<string, Sprache> languages)
        {
            // check if we need to update checklist
            
            if (!checklist.LastUpdatedOnChildItem.IsEmpty() &&
                checklist.LastUpdatedOnChildItem.Equals(dto.LastUpdatedOnChilditem))
            {
                return;
            }

            checklist.BearbeitetAm = DateTime.Now;
            checklist.LastUpdatedOnChildItem = dto.LastUpdatedOnChilditem;
            SetChecklistTranslations(checklist, details, languages);
            
           foreach (var question in checklist.Questions)
           {
               question.BearbeitetAm = DateTime.Now;
               SetChecklistQuestionsTranslations(question, details, languages); 
               _unitOfWork.ChecklistQuestionRepository.Update(question);
           }

           _unitOfWork.ChecklistRepository.Update(checklist);
           _unitOfWork.Save();
        }

        private static Checklist BuildNewChecklist(
            SuvaChecklistDto dto, 
            IReadOnlyDictionary<string, SuvaChecklistDetailsDto> details, 
            IReadOnlyDictionary<string, Sprache> languages, 
            Erlass erlass)
        {
            var checklist = new Checklist
            {
                SuvaChecklistID = dto.ItemId,
                ErlassID = erlass.ErlassID,
                SrNummer = dto.Id,
                ErstelltAm = DateTime.Now,
                BearbeitetAm = DateTime.Now,
                LastUpdatedOnChildItem = dto.LastUpdatedOnChilditem,
            };


           SetChecklistTranslations(checklist, details, languages);
           return checklist;
        }

        private static void SetChecklistTranslations(Checklist checklist, IReadOnlyDictionary<string, SuvaChecklistDetailsDto> details,
            IReadOnlyDictionary<string, Sprache> languages)
        {
            var translation = XMLHelper.CreateNewUebersetzung(new List<string>()
            {
                "Title",
                "Description"
            });

            var detailsDtoDe = details["de"];
            if (detailsDtoDe != null)
            {
                SetChecklistTranslation(checklist, detailsDtoDe, languages["de"].SpracheID, translation);
            }

            var detailsDtoFr = details["fr"];
            if (detailsDtoFr != null)
            {
                SetChecklistTranslation(checklist, detailsDtoFr, languages["fr"].SpracheID, XDocument.Parse(checklist.Translation));
            }

            var detailsDtoIt = details["it"];
            if (detailsDtoIt != null)
            {
                SetChecklistTranslation(checklist, detailsDtoIt, languages["it"].SpracheID, XDocument.Parse(checklist.Translation));
            }

            var detailsDtoEn = details["en"];
            if (detailsDtoEn != null)
            {
                SetChecklistTranslation(checklist, detailsDtoEn, languages["en"].SpracheID, XDocument.Parse(checklist.Translation));
            }
        }

        private static void SetChecklistTranslation(Checklist checklist, SuvaChecklistDetailsDto dto, int SpracheID,
            XDocument translation)
        {
            var multilingualValues = new Dictionary<string, string>
            {
                { "Title", dto.Title },
                { "Description", dto.Description }
            };
            checklist.Translation = XMLHelper.UpdateUebersetzung(translation, multilingualValues, SpracheID);
        }

        private static List<ChecklistQuestion> BuildNewChecklistQuestions(
            Checklist checklist,
            IReadOnlyDictionary<string, SuvaChecklistDetailsDto> details,
            IReadOnlyDictionary<string, Sprache> languages,
            ChecklistHeader checklistHeader)
        {
            var questions = new List<ChecklistQuestion>();

            var detailsDtoDe = details["de"];
            if (detailsDtoDe != null)
            {
                questions.AddRange(from @group in detailsDtoDe.Groups
                                   from q in @group.Checkpoints
                                   where q.Type != "Note"
                                   select new ChecklistQuestion
                                   {
                                       SuvaQuestionID = q.ItemId,
                                       ChecklistID = checklist.ChecklistID,
                                       Checklist = checklist,
                                       SharedImage = q.Attachments.FirstOrDefault(c => c.Type == "Image")?.SharedImage,
                                       ErstelltAm = DateTime.Now,
                                       BearbeitetAm = DateTime.Now,
                                       ChecklistType = q.Type,
                                       Numeration = q.Header,
                                       //ChecklistHeaderTemplateID = testGroup.DatasourceId,
                                       ChecklistHeaderID = checklistHeader.ChecklistHeaderID,
                                       ChecklistHeader = checklistHeader
                                   });
            }
            return questions;
        }

        private static List<ChecklistHeader> BuildNewChecklistHeaders(
            Checklist checklist,
            IReadOnlyDictionary<string, SuvaChecklistDetailsDto> details,
            IReadOnlyDictionary<string, Sprache> languages)
        {
            var headers = new List<ChecklistHeader>();
            var detailsDtoDe = details["de"];
            if (detailsDtoDe != null)
            {
                List<Group> test = (from p in detailsDtoDe.Groups select p).ToList();

                int counter = 0;
                foreach (Group testGroup in test)
                {
                    counter++;
                    headers.Add(new ChecklistHeader()
                    {
                        SuvaHeaderID = testGroup.ItemId,
                        ChecklistID = checklist.ChecklistID,
                        Checklist = checklist,
                        OrderID = counter,
                        ErstelltAm = DateTime.Now,
                        BearbeitetAm = DateTime.Now,
                    });
                }
            }
            return headers;
        }

        private static void SetChecklistQuestionsTranslations(ChecklistQuestion question,
            IReadOnlyDictionary<string, SuvaChecklistDetailsDto> details,
            IReadOnlyDictionary<string, Sprache> languages)
        {
            var translation = XMLHelper.CreateNewUebersetzung(new List<string>()
            {
                "Title"
            });

            var detailsDtoDe = details["de"];
            if (detailsDtoDe != null)
            {
                SetQuestionTranslation(question, detailsDtoDe, languages["de"].SpracheID, translation);
            }

            var detailsDtoFr = details["fr"];
            if (detailsDtoFr != null)
            {
                SetQuestionTranslation(question, detailsDtoFr, languages["fr"].SpracheID, XDocument.Parse(question.Translation));
            }

            var detailsDtoIt = details["it"];
            if (detailsDtoIt != null)
            {
                SetQuestionTranslation(question, detailsDtoIt, languages["it"].SpracheID, XDocument.Parse(question.Translation));
            }

            var detailsDtoEn = details["en"];
            if (detailsDtoEn != null)
            {
                SetQuestionTranslation(question, detailsDtoEn, languages["en"].SpracheID, XDocument.Parse(question.Translation));
            }

        }

        private static void SetQuestionTranslation(ChecklistQuestion question, SuvaChecklistDetailsDto dto, int SpracheID, XDocument translation)
        {
            foreach (var group in dto.Groups)
            {
                foreach (var q in group.Checkpoints)
                {
                    if (question.SuvaQuestionID != q.ItemId) continue;

                    var titleDecoded = HttpUtility.HtmlDecode(q.Title);
                    var multilingualValues = new Dictionary<string, string>
                    {
                        { "Title", titleDecoded ?? "" }
                    };

                    question.Translation = XMLHelper.UpdateUebersetzung(translation, multilingualValues, SpracheID);
                    var sharedImage = q.Attachments.FirstOrDefault(c => c.Type == "Image")?.SharedImage;
                    if (sharedImage != null)
                    {
                        question.SharedImage = sharedImage;
                    }
                }
            }
        }

        private Sprache GetLanguageByLocale(string locale)
        {
            return _unitOfWork.Context.Sprachen.FirstOrDefault(s => s.Lokalisierung == locale);
        }

        private static void SetChecklistHeadersTranslations(ChecklistHeader header,
            IReadOnlyDictionary<string, SuvaChecklistDetailsDto> details,
            IReadOnlyDictionary<string, Sprache> languages)
        {
            var translation = XMLHelper.CreateNewUebersetzung(new List<string>()
            {
                "Title"
            });

            var detailsDtoDe = details["de"];
            if (detailsDtoDe != null)
            {
                SetHeaderTranslation(header, detailsDtoDe, languages["de"].SpracheID, translation);
            }

            var detailsDtoFr = details["fr"];
            if (detailsDtoFr != null)
            {
                SetHeaderTranslation(header, detailsDtoFr, languages["fr"].SpracheID, XDocument.Parse(header.Translation));
            }

            var detailsDtoIt = details["it"];
            if (detailsDtoIt != null)
            {
                SetHeaderTranslation(header, detailsDtoIt, languages["it"].SpracheID, XDocument.Parse(header.Translation));
            }

            var detailsDtoEn = details["en"];
            if (detailsDtoEn != null)
            {
                SetHeaderTranslation(header, detailsDtoEn, languages["en"].SpracheID, XDocument.Parse(header.Translation));
            }

        }

        private static void SetHeaderTranslation(ChecklistHeader header, SuvaChecklistDetailsDto dto, int SpracheID, XDocument translation)
        {
            foreach (var group in dto.Groups)
            {
                if (header.SuvaHeaderID != group.ItemId) continue;

                var titleDecoded = HttpUtility.HtmlDecode(group.Title);
                var multilingualValues = new Dictionary<string, string>
                    {
                        { "Title", titleDecoded ?? "" }
                    };
                header.Translation = XMLHelper.UpdateUebersetzung(translation, multilingualValues, SpracheID);
            }
        }

    }
}