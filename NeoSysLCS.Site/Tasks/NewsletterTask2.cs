using System;
using System.Data.Entity;
using System.Diagnostics;
using System.Linq;
using log4net;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.Helper;
using NeoSysLCS.Site.Controllers;
using NeoSysLCS.Site.Services.Newsletter;

namespace NeoSysLCS.Site.Tasks
{
    /// <summary>
    /// Refactored newsletter task using service abstractions for improved testability
    /// </summary>
    public class NewsletterTask2 : BaseController
    {
        private static readonly ILog log = LogManager.GetLogger(typeof(NewsletterTask2));

        private readonly IUnitOfWork _unitOfWork;
        private readonly INewsletterEmailService _emailService;
        private readonly IPdfGenerationService _pdfGenerationService;
        private readonly IFileUploadService _fileUploadService;

        /// <summary>
        /// Default constructor for Hangfire compatibility
        /// </summary>
        public NewsletterTask2()
            : this(
                new UnitOfWork(),
                new NewsletterEmailService(),
                new NewsletterPdfGenerationService(new UnitOfWork()),
                new NewsletterFileUploadService())
        {
        }

        /// <summary>
        /// Constructor with dependency injection for testing
        /// </summary>
        /// <param name="unitOfWork">Unit of work for data access</param>
        /// <param name="emailService">Email service for sending newsletters</param>
        /// <param name="pdfGenerationService">PDF generation service</param>
        /// <param name="fileUploadService">File upload service</param>
        public NewsletterTask2(
            IUnitOfWork unitOfWork,
            INewsletterEmailService emailService,
            IPdfGenerationService pdfGenerationService,
            IFileUploadService fileUploadService)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _emailService = emailService ?? throw new ArgumentNullException(nameof(emailService));
            _pdfGenerationService = pdfGenerationService ?? throw new ArgumentNullException(nameof(pdfGenerationService));
            _fileUploadService = fileUploadService ?? throw new ArgumentNullException(nameof(fileUploadService));
        }

        /// <summary>
        /// Main method to send newsletters to eligible users
        /// </summary>
        public void SendNewsletters()
        {
            Debug.WriteLine("Newsletter job triggered!");
            log.Info("Newsletter job triggered!");

            var context = _unitOfWork.Context;
            var newsletterRole = context.ApplicationRoles.FirstOrDefault(r => r.Name == Role.Newsletter);
            var activeUsers = context.ApplicationUsers.Include("Kunde").Where(u => !u.LockoutEndDateUtc.HasValue);

            var sent = 0;
            var skipped = 0;
            var failed = 0;
            var processed = 0;

            foreach (var user in activeUsers)
            {
                try
                {
                    var lastSentNewsletterToUser = FindLastSentNewsletter(user);
                    if (ShouldSendNewsletter(user, newsletterRole, lastSentNewsletterToUser))
                    {
                        if (ProcessNewsletterForUser(user, lastSentNewsletterToUser))
                        {
                            sent++;
                            Debug.WriteLine($"SENT Newsletter to: {user.FullName}, TimeSpan {user.NewsletterPeriod}");
                            log.Info($"SENT Newsletter to: {user.FullName}, TimeSpan {user.NewsletterPeriod}");
                        }
                        else
                        {
                            failed++;
                            Debug.WriteLine($"FAILED to generate Newsletter for: {user.FullName}, TimeSpan {user.NewsletterPeriod}");
                            log.Warn($"FAILED to generate Newsletter for: {user.FullName}, TimeSpan {user.NewsletterPeriod}");
                        }
                    }
                    else
                    {
                        skipped++;
                        Debug.WriteLine($"SKIP SENDING Newsletter to: {user.FullName}, TimeSpan {user.NewsletterPeriod}");
                        log.Info($"SKIP SENDING Newsletter to: {user.FullName}, TimeSpan {user.NewsletterPeriod}");
                    }
                }
                catch (Exception e)
                {
                    failed++;
                    Debug.WriteLine($"FAILED Sending Newsletter to: {user.FullName}, TimeSpan {user.NewsletterPeriod}");
                    log.Error($"FAILED Sending Newsletter to: {user.FullName}, TimeSpan {user.NewsletterPeriod}", e);
                }

                processed++;
            }

            log.Info("FINISHED with newsletter sending!");
            log.Info($"Sent: {sent}");
            log.Info($"Skipped: {skipped}");
            log.Info($"Failed: {failed}");
            log.Info($"Total processed: {processed}");
        }

        /// <summary>
        /// Processes newsletter generation and sending for a single user
        /// </summary>
        /// <param name="user">The user to process</param>
        /// <param name="lastNewsletter">The last newsletter sent to the user</param>
        /// <returns>True if successful, false otherwise</returns>
        private bool ProcessNewsletterForUser(ApplicationUser user, ApplicationUserNewsletterHistory lastNewsletter)
        {
            try
            {
                // Get newsletter content data
                var newsletterData = GetNewsletterData(user, lastNewsletter);

                // Generate PDF
                using (var pdfStream = _pdfGenerationService.GenerateNewsletterPdf(
                    user,
                    newsletterData.Kommentare,
                    newsletterData.Gesetzaenderungen,
                    newsletterData.Consultations,
                    lastNewsletter))
                {
                    if (pdfStream == null)
                    {
                        log.Warn($"Null PdfStream newsletter for: {user.FullName}, TimeSpan {user.NewsletterPeriod}");
                        return false;
                    }

                    // Upload PDF
                    var fileUrl = _fileUploadService.UploadNewsletterPdf(user, pdfStream);

                    // Save newsletter history
                    SaveNewsletterHistory(user, fileUrl);

                    // Send email
                    _emailService.SendNewsletterEmail(user, pdfStream, user.SpracheID);

                    return true;
                }
            }
            catch (Exception ex)
            {
                log.Error($"Error processing newsletter for user {user.FullName}: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// Gets newsletter content data for a user
        /// </summary>
        private (
            IQueryable<Repositories.ViewModels.KommentarNewsletterViewModel> Kommentare,
            IQueryable<Repositories.ViewModels.GesetzaenderungViewModel> Gesetzaenderungen,
            IQueryable<Repositories.ViewModels.ConsultationViewModel> Consultations
        ) GetNewsletterData(ApplicationUser user, ApplicationUserNewsletterHistory lastNewsletter)
        {
            if (user.Kunde == null)
            {
                throw new InvalidOperationException($"User {user.FullName} has no associated Kunde");
            }

            int spracheID = user.SpracheID.Value;
            var sendingPeriod = user.NewsletterPeriod.GetValueOrDefault(6);
            var lastNewsletterSentTime = lastNewsletter?.DateSent ?? DateTime.Today.AddMonths(-sendingPeriod);

            var kommentare = _unitOfWork.DashboardRepository
                .GetAllKommentarNewsletterViewModelsByKunde(user.Kunde.KundeID, spracheID, lastNewsletterSentTime);

            var gesetzaenderungen = _unitOfWork.DashboardRepository
                .GetAllGesetzaenderungViewModelsByKunde(user.Kunde.KundeID, spracheID, lastNewsletterSentTime);

            var consultations = _unitOfWork.ConsultationRepository
                .GetConsultationViewModelsNewsletter(user.Kunde.KundeID, spracheID, lastNewsletterSentTime);

            return (kommentare, gesetzaenderungen, consultations);
        }

        /// <summary>
        /// Determines if a newsletter should be sent to a user
        /// </summary>
        private bool ShouldSendNewsletter(ApplicationUser user, ApplicationRole role, ApplicationUserNewsletterHistory lastNewsletter)
        {
            if (!UserHasRole(user, role))
            {
                return false;
            }

            var sendingPeriod = user.NewsletterPeriod.GetValueOrDefault(6);
            return lastNewsletter == null || lastNewsletter.DateSent.AddMonths(sendingPeriod).Date <= DateTime.Now.Date;
        }

        /// <summary>
        /// Checks if a user has a specific role
        /// </summary>
        private bool UserHasRole(ApplicationUser user, ApplicationRole role)
        {
            var foundRole = user.Roles.FirstOrDefault(ur => ur.RoleId == role.Id);
            return foundRole != null;
        }

        /// <summary>
        /// Saves newsletter history record
        /// </summary>
        private void SaveNewsletterHistory(ApplicationUser user, string fileUrl)
        {
            var history = new ApplicationUserNewsletterHistory()
            {
                ID = Guid.NewGuid().ToString(),
                DateSent = DateTime.Now,
                UserID = user.Id,
                User = user,
                NewsletterFileLink = fileUrl
            };
            _unitOfWork.Context.ApplicationUserNewsletterHistories.Add(history);
            _unitOfWork.Context.SaveChanges();
        }

        /// <summary>
        /// Finds the last sent newsletter for a user
        /// </summary>
        private ApplicationUserNewsletterHistory FindLastSentNewsletter(ApplicationUser user)
        {
            return _unitOfWork.Context.ApplicationUserNewsletterHistories
                .OrderByDescending(h => h.DateSent)
                .FirstOrDefault(h => h.UserID == user.Id);
        }
    }
}
