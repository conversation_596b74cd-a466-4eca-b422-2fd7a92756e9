using System;
using System.Collections.Generic;
using System.Configuration;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Security;
using System.Threading.Tasks;
using System.Web;
using DevExpress.Web;
using DocumentFormat.OpenXml.Packaging;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;
using NeoSysLCS.Site.Cortec;
using NeoSysLCS.Site.Helpers;
using NeoSysLCS.Site.Utilities;
using NeoSysLCS.Site.Utilities.Import.Erlass;

namespace NeoSysLCS.Site.Tasks
{

	public class ErlassImportTask
	{
		private readonly IUnitOfWork _unitOfWork;

		public ErlassImportTask()
		{
			_unitOfWork = new UnitOfWork();
		}

		public void ImportDocuments()
		{
			Debug.WriteLine("ErlassImport sync triggered!");

			List<StandortViewModel> viewModels = _unitOfWork.StandortRepository.GetAllStandortViewModelsForErlassImport();

			foreach (StandortViewModel viewModel in viewModels)
			{

                try
                {
                    Debug.WriteLine("Start import of document: " + viewModel.ErlassImportDoc + ", StandortID: " + viewModel.StandortID);
					string env = ConfigurationManager.AppSettings["Environment"];
					if (env == "SxTest" || env == "Prod") // get file from local file system for SX test VM and production
					{
						string erlassImportDoc = viewModel.ErlassImportDoc;
						string documentName = erlassImportDoc.Replace("http://198.168.111.79/Upload/ErlassImport/", "").Replace("https://lexplus.ch/Upload/ErlassImport/", "");
						string filePath = "C:\\inetpub\\neosys\\Upload\\ErlassImport\\" + documentName;

						var s = new StreamReader(filePath);
						Importer importer = new Importer();
						importer.ImportFromExcelFile(s.BaseStream, viewModel.StandortID);
					}
					else // load file from azure blob storag
					{
						// Disable SSL certificate validation
						ServicePointManager.ServerCertificateValidationCallback = 
							(sender, certificate, chain, sslPolicyErrors) => true;

						WebClient client = new WebClient();
						byte[] buffer = client.DownloadData(viewModel.ErlassImportDoc);
						var memStream = new MemoryStream(buffer);

						Importer importer = new Importer();
						importer.ImportFromExcelFile(memStream, viewModel.StandortID);
					}
					Debug.WriteLine("Finished import of document: " + viewModel.ErlassImportDoc + ", StandortID: " + viewModel.StandortID);
				}

					catch (Exception e)
				{
					Debug.WriteLine("Failed to import erlasse for Standort: " + viewModel.Name + ", ID: " + viewModel.StandortID);
					ExceptionUtility.LogException(e, "Failed to import erlasse for Standort: " + viewModel.Name + ", ID: " + viewModel.StandortID);
				}
        }
		}
	}
}
