using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Diagnostics;
using System.Linq;
using log4net;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Site.Areas.Admin.Controllers;

namespace NeoSysLCS.Site.Tasks
{
    public class EvaluationTask
    {

        private static readonly ILog log = LogManager.GetLogger(typeof(EvaluationTask));
        private readonly IUnitOfWork _unitOfWork;

        public EvaluationTask()
        {
            _unitOfWork = new UnitOfWork();
        }

        public void Evaluate()
        {
            Debug.WriteLine("Evaluation triggered!");

            EvaluationsController evaluationsController = new EvaluationsController();
            List<ApplicationUser> users = evaluationsController.GetProjektleiterUsers(false);

            // get/inserts days of delays (from "Datum Aktualisierung")
            var daysOfDelayForCustomersInStatusUpdate = _unitOfWork.EvaluationRepository.QueryEvaluationForCustomersInStatusUpdateDelayedDays(users);

            // get/inserts amount of delayed aktualisierung
            var amountOfDelaysForCustomersInStatusUpdate = _unitOfWork.EvaluationRepository.QueryEvaluationForCustomersInStatusUpdateAmount(users);

            // get/inserts offers volume per new customer
            var offersVolumePerNewCustomers = _unitOfWork.EvaluationRepository.QueryEvaluationForVolumePerNewCustomer(users);

            // get/inserts offers volume per existing customer
            var offersVolumePerExistingCustomers = _unitOfWork.EvaluationRepository.QueryEvaluationForVolumePerExistingCustomer();

            var evaluations = daysOfDelayForCustomersInStatusUpdate
                .Concat(amountOfDelaysForCustomersInStatusUpdate)
                .Concat(offersVolumePerNewCustomers)
                .Concat(offersVolumePerExistingCustomers);

            try
            {
                _unitOfWork.EvaluationRepository.UpsertEvaluations(evaluations);
            }
            catch (Exception e)
            {
                log.Error("FAILED upserting Evaluations", e);
            }
        }

    }
}