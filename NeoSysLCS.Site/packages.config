<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Antlr" version="3.5.0.2" targetFramework="net45" />
  <package id="bootstrap" version="3.3.7" targetFramework="net451" />
  <package id="DocumentFormat.OpenXml" version="2.7.2" targetFramework="net47" />
  <package id="EntityFramework" version="6.1.3" targetFramework="net451" />
  <package id="FluentSecurity" version="2.1.0" targetFramework="net45" />
  <package id="Hangfire" version="1.7.28" targetFramework="net47" />
  <package id="Hangfire.Core" version="1.7.28" targetFramework="net47" />
  <package id="Hangfire.SqlServer" version="1.7.28" targetFramework="net47" />
  <package id="HtmlAgilityPack" version="1.5.1" targetFramework="net47" />
  <package id="jQuery" version="3.1.1" targetFramework="net451" />
  <package id="jquery.validate.unobtrusive.bootstrap" version="1.2.3" targetFramework="net45" />
  <package id="jQuery.Validation" version="1.16.0" targetFramework="net451" />
  <package id="log4net" version="2.0.8" targetFramework="net462" />
  <package id="Microsoft.AspNet.Identity.Core" version="2.2.1" targetFramework="net451" />
  <package id="Microsoft.AspNet.Identity.Core.de" version="2.2.1" targetFramework="net451" />
  <package id="Microsoft.AspNet.Identity.Core.fr" version="2.2.1" targetFramework="net451" />
  <package id="Microsoft.AspNet.Identity.Core.it" version="2.2.1" targetFramework="net451" />
  <package id="Microsoft.AspNet.Identity.EntityFramework" version="2.2.1" targetFramework="net451" />
  <package id="Microsoft.AspNet.Identity.Owin" version="2.2.1" targetFramework="net47" />
  <package id="Microsoft.AspNet.Mvc" version="5.2.3" targetFramework="net451" />
  <package id="Microsoft.AspNet.Razor" version="3.2.3" targetFramework="net451" />
  <package id="Microsoft.AspNet.Web.Optimization" version="1.1.3" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebApi" version="5.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebApi.Client" version="5.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebApi.WebHost" version="5.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebPages" version="3.2.3" targetFramework="net451" />
  <package id="Microsoft.Azure.KeyVault.Core" version="2.0.4" targetFramework="net451" />
  <package id="Microsoft.Azure.Storage.Blob" version="11.1.7" targetFramework="net47" />
  <package id="Microsoft.Azure.Storage.Common" version="11.1.7" targetFramework="net47" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="1.1.1" targetFramework="net47" />
  <package id="Microsoft.Data.Edm" version="5.8.2" targetFramework="net462" />
  <package id="Microsoft.Data.OData" version="5.8.2" targetFramework="net462" />
  <package id="Microsoft.Data.Services.Client" version="5.8.2" targetFramework="net462" />
  <package id="Microsoft.jQuery.Unobtrusive.Ajax" version="3.2.3" targetFramework="net451" />
  <package id="Microsoft.jQuery.Unobtrusive.Validation" version="3.2.3" targetFramework="net451" />
  <package id="Microsoft.NETCore.Platforms" version="2.0.0" targetFramework="net47" />
  <package id="Microsoft.Office.Interop.Excel" version="15.0.4795.1000" targetFramework="net47" />
  <package id="Microsoft.Owin" version="3.1.0" targetFramework="net462" />
  <package id="Microsoft.Owin.Host.SystemWeb" version="3.1.0" targetFramework="net462" />
  <package id="Microsoft.Owin.Security" version="3.1.0" targetFramework="net462" />
  <package id="Microsoft.Owin.Security.Cookies" version="3.1.0" targetFramework="net462" />
  <package id="Microsoft.Owin.Security.OAuth" version="3.1.0" targetFramework="net462" />
  <package id="Microsoft.Web.Infrastructure" version="*******" targetFramework="net45" />
  <package id="Microsoft.Win32.Primitives" version="4.3.0" targetFramework="net47" />
  <package id="Modernizr" version="2.8.3" targetFramework="net45" />
  <package id="NETStandard.Library" version="2.0.0" targetFramework="net47" />
  <package id="Newtonsoft.Json" version="10.0.3" targetFramework="net47" />
  <package id="Owin" version="1.0" targetFramework="net45" />
  <package id="PDFsharp-MigraDoc-gdi" version="1.50.5147" targetFramework="net47" />
  <package id="Postal.Mvc5" version="1.2.0" targetFramework="net45" />
  <package id="RazorEngine" version="3.10.0" targetFramework="net47" />
  <package id="Respond" version="1.4.2" targetFramework="net45" />
  <package id="System.AppContext" version="4.3.0" targetFramework="net47" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net47" />
  <package id="System.Collections" version="4.3.0" targetFramework="net47" />
  <package id="System.Collections.Concurrent" version="4.3.0" targetFramework="net47" />
  <package id="System.ComponentModel.EventBasedAsync" version="4.3.0" targetFramework="net462" />
  <package id="System.Console" version="4.3.0" targetFramework="net47" />
  <package id="System.Diagnostics.Debug" version="4.3.0" targetFramework="net47" />
  <package id="System.Diagnostics.DiagnosticSource" version="4.6.0" targetFramework="net47" />
  <package id="System.Diagnostics.Tools" version="4.3.0" targetFramework="net47" />
  <package id="System.Diagnostics.Tracing" version="4.3.0" targetFramework="net47" />
  <package id="System.Dynamic.Runtime" version="4.3.0" targetFramework="net462" />
  <package id="System.Globalization" version="4.3.0" targetFramework="net47" />
  <package id="System.Globalization.Calendars" version="4.3.0" targetFramework="net47" />
  <package id="System.IO" version="4.3.0" targetFramework="net47" />
  <package id="System.IO.Compression" version="4.3.0" targetFramework="net47" />
  <package id="System.IO.Compression.ZipFile" version="4.3.0" targetFramework="net47" />
  <package id="System.IO.FileSystem" version="4.3.0" targetFramework="net47" />
  <package id="System.IO.FileSystem.Primitives" version="4.3.0" targetFramework="net47" />
  <package id="System.IO.Hashing" version="6.0.0" targetFramework="net47" />
  <package id="System.IO.Packaging" version="4.4.0" targetFramework="net47" />
  <package id="System.Linq" version="4.3.0" targetFramework="net47" />
  <package id="System.Linq.Expressions" version="4.3.0" targetFramework="net47" />
  <package id="System.Linq.Queryable" version="4.3.0" targetFramework="net462" />
  <package id="System.Memory" version="4.5.4" targetFramework="net47" />
  <package id="System.Memory.Data" version="1.0.2" targetFramework="net47" />
  <package id="System.Net.Http" version="4.3.4" targetFramework="net47" />
  <package id="System.Net.Primitives" version="4.3.0" targetFramework="net47" />
  <package id="System.Net.Requests" version="4.3.0" targetFramework="net462" />
  <package id="System.Net.Sockets" version="4.3.0" targetFramework="net47" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net47" />
  <package id="System.ObjectModel" version="4.3.0" targetFramework="net47" />
  <package id="System.Reflection" version="4.3.0" targetFramework="net47" />
  <package id="System.Reflection.Extensions" version="4.3.0" targetFramework="net47" />
  <package id="System.Reflection.Primitives" version="4.3.0" targetFramework="net47" />
  <package id="System.Resources.ResourceManager" version="4.3.0" targetFramework="net47" />
  <package id="System.Runtime" version="4.3.0" targetFramework="net47" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.7.1" targetFramework="net47" />
  <package id="System.Runtime.Extensions" version="4.3.0" targetFramework="net47" />
  <package id="System.Runtime.Handles" version="4.3.0" targetFramework="net47" />
  <package id="System.Runtime.InteropServices" version="4.3.0" targetFramework="net47" />
  <package id="System.Runtime.InteropServices.RuntimeInformation" version="4.3.0" targetFramework="net47" />
  <package id="System.Runtime.Numerics" version="4.3.0" targetFramework="net47" />
  <package id="System.Security.Cryptography.Algorithms" version="4.3.0" targetFramework="net47" />
  <package id="System.Security.Cryptography.Encoding" version="4.3.0" targetFramework="net47" />
  <package id="System.Security.Cryptography.Primitives" version="4.3.0" targetFramework="net47" />
  <package id="System.Security.Cryptography.X509Certificates" version="4.3.0" targetFramework="net47" />
  <package id="System.Spatial" version="5.8.2" targetFramework="net462" />
  <package id="System.Text.Encoding" version="4.3.0" targetFramework="net47" />
  <package id="System.Text.Encoding.Extensions" version="4.3.0" targetFramework="net47" />
  <package id="System.Text.Encodings.Web" version="4.7.2" targetFramework="net47" />
  <package id="System.Text.Json" version="4.7.2" targetFramework="net47" />
  <package id="System.Text.RegularExpressions" version="4.3.0" targetFramework="net47" />
  <package id="System.Threading" version="4.3.0" targetFramework="net47" />
  <package id="System.Threading.Tasks" version="4.3.0" targetFramework="net47" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net47" />
  <package id="System.Threading.Timer" version="4.3.0" targetFramework="net47" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net47" />
  <package id="System.Xml.ReaderWriter" version="4.3.0" targetFramework="net47" />
  <package id="System.Xml.XDocument" version="4.3.0" targetFramework="net47" />
  <package id="WebGrease" version="1.6.0" targetFramework="net45" />
</packages>