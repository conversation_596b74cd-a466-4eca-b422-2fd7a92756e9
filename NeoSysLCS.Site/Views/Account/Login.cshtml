@model NeoSysLCS.Repositories.ViewModels.LoginViewModel
@using NeoSysLCS.Resources.Properties

@{
    ViewBag.Title = "Login";
    ViewContext.Writer.Write("<h1 class='page-header'>" + ViewBag.Title + "</h1>");



<div class="container">
    <div class="row">

        <div class="col-md-4 col-md-offset-4">
            <div class="login-panel panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">@Resources.Login_Bitte_Einloggen</h3>
                </div>
                <div class="panel-body">
                    @using (Html.BeginForm("Login", "Account", new { ReturnUrl = ViewBag.ReturnUrl }, FormMethod.Post, new { role = "form" }))
                    {
                        @*@Html.AntiForgeryToken()*@
                        @Html.ValidationSummary(true, "", new { @class = "text-danger" })

                        <div class="form-group" style="margin-bottom: 10px;">
                            <div class="input-group">
                                <span class="input-group-addon"><i class="fa fa-envelope-o fa-fw fa-lg"></i></span>
                                @Html.TextBoxFor(m => m.Email, new { @class = "form-control", @autofocus = true, @placeholder = "E-Mail" })

                                @Html.ValidationMessageFor(m => m.Email, "", new { @class = "text-danger" })
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="input-group">
                                <span class="input-group-addon"><i class="fa fa-key fa-fw fa-lg"></i></span>
                                @Html.PasswordFor(m => m.Password, new { @class = "form-control", @placeholder = "Passwort" })
                                @Html.ValidationMessageFor(m => m.Password, "", new { @class = "text-danger" })
                            </div>
                        </div>
                        <div class="checkbox">
                            @Html.CheckBoxFor(m => m.RememberMe)
                            @Html.LabelFor(m => m.RememberMe)
                        </div>
                        <!-- Change this to a button or input when using this as a form -->
                        <div class="form-group">
                            <div class="col-sm-offset-2 col-sm-10">
                                <input type="submit" value="Login" class="btn btn-lg btn-success btn-block" />
                            </div>
                        </div>
                    }

                        <div class="center-block" style="padding: 0px; border: 0px; margin-left: 130px; margin-bottom: 3px;">
                            @*<input type="button" value="@Resources.Password_Reset_Forgot" class="btn btn-link" style="padding: 0px; border: 0px; margin: 0px; margin-bottom: 3px;"
                                        onclick="javascript: ShowDetailPopup('/Account/PasswordReset', 'PwdReset');"/>
                            <a href="@Url.Action("PasswordReset", "Account", new { area = "" })">@Resources.Password_Reset_Forgot</a>*@
                           @Html.ActionLink(Resources.Login_Forgot_Password, "PasswordReset", "Home", new { area = "" }, new { @class = "" })
                        </div>

                    @*<button type="button" class="btn btn-default" style="border:none; background-color: transparent; outline:none; padding-left: 5px; padding-right: 0px; padding-bottom:20px; padding-top: 5px"
                            onclick="javascript: ShowDetailPopup('/Portal/FAQPortal/ShowFAQPopup?view=Benutzerverwaltung', 'test');">
                        <img src="~/Content/images/Question_Mark.png" style="width:20px;height:20px" />
                    </button>*@
                </div>
            </div>
        </div>
    </div>
</div>
    }

@Html.DevExpress().PopupControl(settings =>
{

    settings.Name = "PwdReset";
    settings.ShowPageScrollbarWhenModal = true;
    settings.Width = 400;
    settings.Height = 510;
    settings.ResizingMode = ResizingMode.Live;
    settings.AutoUpdatePosition = true;
    settings.AllowDragging = true;
    settings.CloseAction = CloseAction.CloseButton;
    settings.PopupAnimationType = AnimationType.None;
    settings.Modal = true;
    settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
    settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
}).GetHtml()




<script type="text/javascript">
    function ShowDetailPopup(url, win) {
        modal = eval(win);
        if (window.height < 600) {
            modal.SetHeight(window.height - 50);
        } else {
            modal.SetHeight(600);
        }

        if (window.width < 800) {
            modal.SetWidth(window.width - 50);
        } else {
            modal.SetWidth(800);
        }

        modal.SetContentUrl(url);
        modal.Show();
    }
</script>