@using NeoSysLCS.Resources.Properties

@model  int


@{
    Html.DevExpress().HyperLink(hl =>
    {
        hl.Name = "QuenticKundendokumentExport_" + Model;
        hl.NavigateUrl = Url.Action("QuenticExportToXls", "KundendokumentImportExport", new
        {
            Area = "",
            standortId = ViewData["StandortID"],
            kundendokumentId = Model
        });
        hl.Properties.Text = Resources.XSL_Export_Export;
        hl.ToolTip = Resources.XSL_Export_Export + " Quentic Excel";
        hl.Properties.Target = "_blank";
        hl.Properties.ImageUrl = "~/Content/images/file-download2.png";
    }).Render();

}