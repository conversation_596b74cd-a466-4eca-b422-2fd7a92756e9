@using NeoSysLCS.Resources.Properties

@model  int


@{
    Html.DevExpress().HyperLink(hl =>
    {
        hl.Name = "ObjektForderungenExport" + Model;
        hl.NavigateUrl = Url.Action("ExportTo", "ObjektForderungen", new
        {
            Area = "",
            objektId = Model
        });
        hl.Properties.Text = Resources.XSL_Export_Export;
        hl.Properties.Target = "_blank";
        hl.Properties.ImageUrl = "~/Content/images/file-download2.png";
    }).Render();

}