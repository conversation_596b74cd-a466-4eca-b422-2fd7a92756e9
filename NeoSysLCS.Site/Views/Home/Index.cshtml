@using System.Web.UI.WebControls
@using Microsoft.AspNet.Identity
@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers

@{
    ViewBag.Title = Resources.View_Title_Neosys_Portal_Dashboard;
    ViewContext.Writer.Write("<h1 class='page-header'>" + ViewBag.Title + "</h1>");
}

<h3>Letzte Änderungen</h3>

<!-- Nav tabs -->
<ul class="nav nav-tabs" role="tablist">
    <li class="active"><a href="#kunden" role="tab" data-toggle="tab"><PERSON>nden</a></li>
    <li><a href="#objekte" role="tab" data-toggle="tab">Objekte</a></li>
    <li><a href="#erlaesse" role="tab" data-toggle="tab">Erlasse</a></li>
    <li><a href="#standorte" role="tab" data-toggle="tab">Standorte</a></li>
</ul>

<!-- Tab panes -->
<div class="tab-content">
    <div class="tab-pane fade in active" id="kunden">

        @{
            var grid = Html.DevExpress().GridView(settings =>
            {

                SessionHelper sh = new SessionHelper();

                settings.Name = "HomedView";
                settings.KeyFieldName = "KundeID";

                //GridViewHelper.ApplyDefaultSettings(settings);



                settings.CommandColumn.Visible = false;
                settings.CommandColumn.ShowNewButtonInHeader = false;
                settings.CommandColumn.ShowDeleteButton = false;
                settings.CommandColumn.ShowEditButton = false;
                settings.Width = Unit.Percentage(50);

                //Columns
                settings.Columns.Add("Name");


                MVCxGridViewColumn gueltigVon = settings.Columns.Add("BearbeitetAm", MVCxGridViewColumnType.DateEdit);
                gueltigVon.Caption = "Bearbeitet";
                gueltigVon.SortDescending();




                settings.Columns.Add(column =>
                {
                    column.FieldName = "BearbeitetVonID";
                    column.Caption = "Bearbeitet von";


                    column.ColumnType = MVCxGridViewColumnType.ComboBox;
                    column.Settings.FilterMode = ColumnFilterMode.DisplayText;
                    var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;


                    column.SetDataItemTemplateContent(container => Html.DevExpress().TextBox(tb =>
                    {
                        tb.Name = "The Name";
                        var keyValue = DataBinder.Eval(container.DataItem, "BearbeitetVonID");
                        ApplicationUserManager usrMngr = sh.GetUserManager();
                        ApplicationUser usr = usrMngr.FindById(keyValue.ToString());

                        tb.ReadOnly = true;

                        tb.Text = usr.FullName;



                    }));


                });


                GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);

                //Detail
                //settings.SettingsDetail.ShowDetailRow = true;
                //settings.SetDetailRowTemplateContent(c =>
                //{
                //    Html.DevExpress().PageControl(pageControlSettings =>
                //    {
                //        pageControlSettings.Name = "PflichtPageControl_" + DataBinder.Eval(c.DataItem, "PflichtID");
                //        pageControlSettings.Width = Unit.Percentage(100);
                //        // TODO Übersetzung
                //        pageControlSettings.TabPages.Add("Pflichtübersetzungen").SetContent(() =>
                //        {
                //            Html.RenderAction("PflichtUebersetzungenGridView", new { id = DataBinder.Eval(c.DataItem, "PflichtID") });
                //        });
                //        // TODO Übersetzung
                //        pageControlSettings.TabPages.Add("Objekte").SetContent(() =>
                //        {
                //            Html.RenderAction("PflichtenObjektPartial", new { id = DataBinder.Eval(c.DataItem, "PflichtID") });

                //        });
                //    }).Render();
                //});


            });

            if (ViewData["EditError"] != null)
            {
                grid.SetEditErrorText((string)ViewData["EditError"]);
            }
        }
        @grid.Bind(Model).GetHtml()

    </div>
    <div class="tab-pane fade" id="objekte">...</div>
    <div class="tab-pane fade" id="erlaesse">...</div>
    <div class="tab-pane fade" id="standorte">...</div>
</div>
