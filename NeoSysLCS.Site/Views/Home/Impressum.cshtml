@using NeoSysLCS.Resources.Properties
@{
    ViewBag.Title = Resources.View_Impressum_Title;
}

<address>
    <strong>Neosys AG</strong><br />
    Privatstrasse 10<br />
    CH-4563 Gerlafingen<br />
    <abbr title="@Resources.View_Impressum_Telefon">Tel:</abbr> +41(0)32 674 45 11<br />
    <EMAIL><br />
    <a href="http://www.neosys.ch" target="_blank">http://www.neosys.ch</a>
</address>

<p></p>
<strong>@Resources.View_Impressum_EntwickeltDurch:</strong>
<br />
<br />
<address>
    <strong>SECURIX AG</strong><br />
    Aarburgerstrasse 7<br />
    CH-4600 Olten<br />
    <abbr title="@Resources.View_Impressum_Telefon">Tel:</abbr>+41(0)62 297 12 12<br />
    <EMAIL><br />
    <a href="https://www.securix.swiss" target="_blank">https://www.securix.swiss</a>
</address>

<p>
    <a href="https://www.swissmadesoftware.org/" target="_blank"><img src="/Content/images/sms-logo-small-footer.png" alt="swiss made software" title="swiss made software" /></a>
    <div class="row col-lg-3"><small>The label "swiss made software" stands for Swiss values and innovation, openness and flexibility in software development.</small></div>
</p>