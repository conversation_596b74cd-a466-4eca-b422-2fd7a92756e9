@using NeoSysLCS.Resources.Properties
@model NeoSysLCS.Repositories.ViewModels.IndexViewModel
@{
    ViewBag.Title = Resources.Menu_Benutzerprofil;
    ViewContext.Writer.Write("<h1 class='page-header'>" + ViewBag.Title + "</h1>");
}

@if (ViewBag.StatusMessage != null && ViewBag.StatusMessage != "")
{

    if (@ViewBag.StatusMessage == @Resources.Passwort_wurde_geaendert)
    {
        <div class='alert alert-success'>
            <p>
                @ViewBag.StatusMessage
            </p>
        </div>
    }
    else
    {
        <div class='alert alert-info'>
            <p>
                @ViewBag.StatusMessage
            </p>
        </div>
    }
}

<div class="row">
    <div class="col-md-8">
        <p>
            @if (Model.HasPassword)
            {
                @Html.ActionLink(Resources.ChangePasswordView_Title, "ChangePassword")
            }
        </p>
    </div>
</div>
