@using NeoSysLCS.Site.Helpers
@using NeoSysLCS.Repositories
@using NeoSysLCS.Resources.Properties
@model IQueryable<NeoSysLCS.Repositories.ViewModels.PrivacyPolicyViewModel>

@{
    var grid = Html.DevExpress().GridView(settings =>
    {
        IUnitOfWork unitOfWork = new UnitOfWork();

        settings.Name = "privacyPolicyGrid";
        GridViewHelper.ApplyUebersetzungsSettings(settings);

        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;
        settings.Settings.ShowGroupPanel = false;
        settings.Settings.ShowGroupedColumns = false;
        settings.Settings.ShowFilterRow = false;
        settings.SettingsBehavior.AllowSort = true;

        settings.ControlStyle.CssClass = "grid";


        settings.CommandColumn.Visible = true;
        settings.CommandColumn.ShowSelectCheckbox = true;
        settings.CommandColumn.Width = 100;
        settings.SettingsBehavior.AllowSelectByRowClick = false;
        settings.Settings.UseFixedTableLayout = true;

        SessionHelper sessionHelper = new SessionHelper();


        settings.KeyFieldName = "PrivacyPolicyID";
        settings.CallbackRouteValues = new { Controller = "PrivacyPolicyConfirmation", Action = "PrivacyPolicyConfirmationGridView" };

       
        settings.CustomJSProperties = (sender, e) => {  
            e.Properties["cpVisibleRowCount"] = (sender as MVCxGridView).VisibleRowCount;  
        };
        settings.ClientSideEvents.Init = "function(s, e) { totalCount = s.cpVisibleRowCount }"; 
        settings.ClientSideEvents.SelectionChanged = @"function(s,e){
                SelectionChanged(s,e); return;
            }";


        settings.Columns.Add(column =>
        {
            column.Caption = Resources.Colunm_Name;
            column.ReadOnly = true;
            column.EditFormSettings.Visible = DefaultBoolean.False;
            column.ColumnType = MVCxGridViewColumnType.TextBox;
            column.Settings.AllowHeaderFilter = DefaultBoolean.False;

            column.SetDataItemTemplateContent(container =>
            {

                Html.DevExpress().HyperLink(hyperlink =>
                {
                    var keyValue = container.KeyValue;
                    hyperlink.Name = "PrivacyPolicy" + keyValue;

                    var name = (string)DataBinder.Eval(container.DataItem, "Name");
                    hyperlink.Properties.Text = name;

                    var url = (string)DataBinder.Eval(container.DataItem, "Quelle");
                    hyperlink.NavigateUrl = url;
                    hyperlink.Properties.Target = "_blank";

                }).Render();
            });
        });
    });


}
@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = "PrivacyPolicyID";
}).GetHtml()