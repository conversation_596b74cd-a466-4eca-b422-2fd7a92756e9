@using System.Web.UI.WebControls
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers


<script type="text/javascript">
    //<![CDATA[

    function openPopupShowDataOnly(cell, value, title) {
        //set size
        setPopupWidth();

        //set title of popup
        readOnlyPopup.SetHeaderText(title);

        //set content of popup
        $('#popupContent').html(value);

        //show popup
        readOnlyPopup.Show();
    }

    function setPopupWidth() {
        //set height
        if (window.innerHeight < 520) {
            readOnlyPopup.SetHeight(window.innerHeight - 50);
            $('#popupContent').height(readOnlyPopup.GetHeight() - 130);
        } else {
            readOnlyPopup.SetHeight(520);
            $('#popupContent').height(390);
        }

        //set width
        if (window.innerWidth < 650) {
            readOnlyPopup.SetWidth(window.innerWidth - 50);
        } else {
            readOnlyPopup.SetWidth(650);
        }


    }

    // ]]>
</script>

@Html.DevExpress().PopupControl(
                settings =>
                {
                    settings.Name = "readOnlyPopup";
                    settings.PopupElementID = "roPopup";

                    settings.Width = 330;
                    settings.Height = 250;
                    settings.MinHeight = 150;
                    settings.MinWidth = 150;
                    settings.MaxHeight = 800;
                    settings.MaxWidth = 800;
                    settings.Width = 310;

                    settings.ShowCloseButton = true;
                    settings.PreRender = (sender, e) =>
                    {
                        var popup = (ASPxPopupControl)sender;
                        popup.ShowShadow = true;
                    };
                    settings.ShowFooter = false;
                    settings.ShowHeader = true;
                    settings.AllowResize = true;
                    settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
                    settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
                    settings.Opacity = 100;
                    settings.PopupAnimationType = AnimationType.Slide;
                    settings.CloseAnimationType = AnimationType.None;
                    settings.AllowDragging = true;
                    settings.DragElement = DragElement.Header;
                    settings.ResizingMode = ResizingMode.Live;
                    settings.ShowSizeGrip = ShowSizeGrip.Auto;
                    settings.CloseAction = CloseAction.OuterMouseClick;
                    settings.CloseOnEscape = false;
                    settings.PopupAction = PopupAction.LeftMouseClick;
                    settings.AppearAfter = 300;
                    settings.DisappearAfter = 500;
                    settings.ScrollBars = ScrollBars.None;


                    settings.SetContent(() =>
                    {
                        ViewContext.Writer.Write("<div id='popupContent' style=''></div>");
                    });
                }).GetHtml()

