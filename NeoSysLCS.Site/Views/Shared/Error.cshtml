@using System.Web.UI.WebControls
@using NeoSysLCS.Resources.Properties
@model System.Web.Mvc.HandleErrorInfo

@{
    ViewBag.Title = Resources.General_Error;
    ViewContext.Writer.Write("<h1 class='page-header'>" + ViewBag.Title + "</h1>");
}

<p>@(Model != null && Model.Exception != null && Model.Exception.Message != null ? Model.Exception.Message  : "An unknown error occurred.")</p>
<pre>@(Model != null && Model.Exception != null ? Model.Exception.StackTrace : "No stack trace available.")</pre>

@Html.ActionLink(Resources.Error_Navigation_zurueck, "Index", "Home", new { area = "" }, new { @class = "" })
