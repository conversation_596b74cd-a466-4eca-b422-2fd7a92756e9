@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers
@using NeoSysLCS.Site.Utilities

<!DOCTYPE html>
<html>
<head>
    
        @*<script type="text/javascript">
window.NREUM||(NREUM={}),__nr_require=function(t,e,n){function r(n){if(!e[n]){var o=e[n]={exports:{}};t[n][0].call(o.exports,function(e){var o=t[n][1][e];return r(o||e)},o,o.exports)}return e[n].exports}if("function"==typeof __nr_require)return __nr_require;for(var o=0;o<n.length;o++)r(n[o]);return r}({1:[function(t,e,n){function r(t){try{c.console&&console.log(t)}catch(e){}}var o,i=t("ee"),a=t(19),c={};try{o=localStorage.getItem("__nr_flags").split(","),console&&"function"==typeof console.log&&(c.console=!0,o.indexOf("dev")!==-1&&(c.dev=!0),o.indexOf("nr_dev")!==-1&&(c.nrDev=!0))}catch(s){}c.nrDev&&i.on("internal-error",function(t){r(t.stack)}),c.dev&&i.on("fn-err",function(t,e,n){r(n.stack)}),c.dev&&(r("NR AGENT IN DEVELOPMENT MODE"),r("flags: "+a(c,function(t,e){return t}).join(", ")))},{}],2:[function(t,e,n){function r(t,e,n,r,o){try{h?h-=1:i("err",[o||new UncaughtException(t,e,n)])}catch(c){try{i("ierr",[c,(new Date).getTime(),!0])}catch(s){}}return"function"==typeof f&&f.apply(this,a(arguments))}function UncaughtException(t,e,n){this.message=t||"Uncaught error with no additional information",this.sourceURL=e,this.line=n}function o(t){i("err",[t,(new Date).getTime()])}var i=t("handle"),a=t(20),c=t("ee"),s=t("loader"),f=window.onerror,u=!1,h=0;s.features.err=!0,t(1),window.onerror=r;try{throw new Error}catch(d){"stack"in d&&(t(12),t(11),"addEventListener"in window&&t(6),s.xhrWrappable&&t(13),u=!0)}c.on("fn-start",function(t,e,n){u&&(h+=1)}),c.on("fn-err",function(t,e,n){u&&(this.thrown=!0,o(n))}),c.on("fn-end",function(){u&&!this.thrown&&h>0&&(h-=1)}),c.on("internal-error",function(t){i("ierr",[t,(new Date).getTime(),!0])})},{}],3:[function(t,e,n){t("loader").features.ins=!0},{}],4:[function(t,e,n){function r(t,e,n){t[0]=a(t[0],"fn-",null,n)}function o(t,e,n){this.method=n,this.timerDuration="number"==typeof t[1]?t[1]:0,t[0]=a(t[0],"fn-",this,n)}var i=t("ee").get("timer"),a=t(21)(i),c="setTimeout",s="setInterval",f="clearTimeout",u="-start",h="-";e.exports=i,a.inPlace(window,[c,"setImmediate"],c+h),a.inPlace(window,[s],s+h),a.inPlace(window,[f,"clearImmediate"],f+h),i.on(s+u,r),i.on(c+u,o)},{}],5:[function(t,e,n){function r(t,e,n){var r=t[e];"function"==typeof r&&(t[e]=function(){var t=r.apply(this,arguments);return o.emit(n+"start",arguments,t),t.then(function(e){return o.emit(n+"end",[null,e],t),e},function(e){throw o.emit(n+"end",[e],t),e})})}var o=t("ee").get("fetch"),i=t(19);e.exports=o;var a=window,c="fetch-",s=c+"body-",f=["arrayBuffer","blob","json","text","formData"],u=a.Request,h=a.Response,d=a.fetch,p="prototype";u&&h&&d&&(i(f,function(t,e){r(u[p],e,s),r(h[p],e,s)}),r(a,"fetch",c),o.on(c+"end",function(t,e){var n=this;e?e.clone().arrayBuffer().then(function(t){n.rxSize=t.byteLength,o.emit(c+"done",[null,e],n)}):o.emit(c+"done",[t],n)}))},{}],6:[function(t,e,n){function r(t){for(var e=t;e&&!e.hasOwnProperty(u);)e=Object.getPrototypeOf(e);e&&o(e)}function o(t){c.inPlace(t,[u,h],"-",i)}function i(t,e){return t[1]}var a=t("ee").get("events"),c=t(21)(a,!0),s=t("gos"),f=XMLHttpRequest,u="addEventListener",h="removeEventListener";e.exports=a,"getPrototypeOf"in Object?(r(document),r(window),r(f.prototype)):f.prototype.hasOwnProperty(u)&&(o(window),o(f.prototype)),a.on(u+"-start",function(t,e){var n=t[1],r=s(n,"nr@wrapped",function(){function t(){if("function"==typeof n.handleEvent)return n.handleEvent.apply(n,arguments)}var e={object:t,"function":n}[typeof n];return e?c(e,"fn-",null,e.name||"anonymous"):n});this.wrapped=t[1]=r}),a.on(h+"-start",function(t){t[1]=this.wrapped||t[1]})},{}],7:[function(t,e,n){function r(t,e,n){t[0]=a(t[0],"fn-",null,n)}function o(t,e,n){this.method=n,this.timerDuration="number"==typeof t[1]?t[1]:0,t[0]=a(t[0],"fn-",this,n)}var i=t("ee").get("timer"),a=t(21)(i),c="setTimeout",s="setInterval",f="clearTimeout",u="-start",h="-";e.exports=i,a.inPlace(window,[c,"setImmediate"],c+h),a.inPlace(window,[s],s+h),a.inPlace(window,[f,"clearImmediate"],f+h),i.on(s+u,r),i.on(c+u,o)},{}],8:[function(t,e,n){var r=t("ee").get("history"),o=t(21)(r),i="equestAnimationFrame";e.exports=r,o.inPlace(window.history,["pushState","replaceState"],"-")},{}],9:[function(t,e,n){var r=t("ee").get("mutation"),o=t(21)(r),i=NREUM.o.MO;e.exports=r,i&&(window.MutationObserver=function(t){return this instanceof i?new i(o(t,"fn-")):i.apply(this,arguments)},MutationObserver.prototype=i.prototype)},{}],10:[function(t,e,n){function r(t,e,n,r,o){try{h?h-=1:i("err",[o||new UncaughtException(t,e,n)])}catch(c){try{i("ierr",[c,(new Date).getTime(),!0])}catch(s){}}return"function"==typeof f&&f.apply(this,a(arguments))}function UncaughtException(t,e,n){this.message=t||"Uncaught error with no additional information",this.sourceURL=e,this.line=n}function o(t){i("err",[t,(new Date).getTime()])}var i=t("handle"),a=t(20),c=t("ee"),s=t("loader"),f=window.onerror,u=!1,h=0;s.features.err=!0,t(1),window.onerror=r;try{throw new Error}catch(d){"stack"in d&&(t(12),t(11),"addEventListener"in window&&t(6),s.xhrWrappable&&t(13),u=!0)}c.on("fn-start",function(t,e,n){u&&(h+=1)}),c.on("fn-err",function(t,e,n){u&&(this.thrown=!0,o(n))}),c.on("fn-end",function(){u&&!this.thrown&&h>0&&(h-=1)}),c.on("internal-error",function(t){i("ierr",[t,(new Date).getTime(),!0])})},{}],11:[function(t,e,n){var r=t("ee").get("raf"),o=t(21)(r),i="equestAnimationFrame";e.exports=r,o.inPlace(window,["r"+i,"mozR"+i,"webkitR"+i,"msR"+i],"raf-"),r.on("raf-start",function(t){t[0]=o(t[0],"fn-")})},{}],12:[function(t,e,n){function r(t,e,n){t[0]=a(t[0],"fn-",null,n)}function o(t,e,n){this.method=n,this.timerDuration="number"==typeof t[1]?t[1]:0,t[0]=a(t[0],"fn-",this,n)}var i=t("ee").get("timer"),a=t(21)(i),c="setTimeout",s="setInterval",f="clearTimeout",u="-start",h="-";e.exports=i,a.inPlace(window,[c,"setImmediate"],c+h),a.inPlace(window,[s],s+h),a.inPlace(window,[f,"clearImmediate"],f+h),i.on(s+u,r),i.on(c+u,o)},{}],13:[function(t,e,n){function r(t,e){h.inPlace(e,["onreadystatechange"],"fn-",c)}function o(){var t=this,e=u.context(t);t.readyState>3&&!e.resolved&&(e.resolved=!0,u.emit("xhr-resolved",[],t)),h.inPlace(t,v,"fn-",c)}function i(t){w.push(t),l&&(g=-g,b.data=g)}function a(){for(var t=0;t<w.length;t++)r([],w[t]);w.length&&(w=[])}function c(t,e){return e}function s(t,e){for(var n in t)e[n]=t[n];return e}t(6);var f=t("ee"),u=f.get("xhr"),h=t(21)(u),d=NREUM.o,p=d.XHR,l=d.MO,m="readystatechange",v=["onload","onerror","onabort","onloadstart","onloadend","onprogress","ontimeout"],w=[];e.exports=u;var y=window.XMLHttpRequest=function(t){var e=new p(t);try{u.emit("new-xhr",[e],e),e.addEventListener(m,o,!1)}catch(n){try{u.emit("internal-error",[n])}catch(r){}}return e};if(s(p,y),y.prototype=p.prototype,h.inPlace(y.prototype,["open","send"],"-xhr-",c),u.on("send-xhr-start",function(t,e){r(t,e),i(e)}),u.on("open-xhr-start",r),l){var g=1,b=document.createTextNode(g);new l(a).observe(b,{characterData:!0})}else f.on("fn-end",function(t){t[0]&&t[0].type===m||a()})},{}],14:[function(t,e,n){function r(t){if("string"==typeof t&&t.length)return t.length;if("object"==typeof t){if("undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer&&t.byteLength)return t.byteLength;if("undefined"!=typeof Blob&&t instanceof Blob&&t.size)return t.size;if(!("undefined"!=typeof FormData&&t instanceof FormData))try{return JSON.stringify(t).length}catch(e){return}}}},{}],15:[function(t,e,n){e.exports=function(t){var e=document.createElement("a"),n=window.location,r={};e.href=t,r.port=e.port;var o=e.href.split("://");!r.port&&o[1]&&(r.port=o[1].split("/")[0].split("@@").pop().split(":")[1]),r.port&&"0"!==r.port||(r.port="https"===o[0]?"443":"80"),r.hostname=e.hostname||n.hostname,r.pathname=e.pathname,r.protocol=o[0],"/"!==r.pathname.charAt(0)&&(r.pathname="/"+r.pathname);var i=!e.protocol||":"===e.protocol||e.protocol===n.protocol,a=e.hostname===document.domain&&e.port===n.port;return r.sameOrigin=i&&(!e.hostname||a),r}},{}],16:[function(t,e,n){function r(){}function o(t,e,n,r){return function(){return i(t,[(new Date).getTime()].concat(c(arguments)),e?null:this,n),e?void 0:this}}var i=t("handle"),a=t(19),c=t(20),s=t("ee").get("tracer"),f=NREUM;"undefined"==typeof window.newrelic&&(newrelic=f);var u=["setPageViewName","setCustomAttribute","setErrorHandler","finished","addToTrace","inlineHit","addRelease"],h="api-",d=h+"ixn-";a(u,function(t,e){f[e]=o(h+e,!0,"api")}),f.addPageAction=o(h+"addPageAction",!0),f.setCurrentRouteName=o(h+"routeName",!0),e.exports=newrelic,f.interaction=function(){return(new r).get()};var p=r.prototype={createTracer:function(t,e){var n={},r=this,o="function"==typeof e;return i(d+"tracer",[Date.now(),t,n],r),function(){if(s.emit((o?"":"no-")+"fn-start",[Date.now(),r,o],n),o)try{return e.apply(this,arguments)}finally{s.emit("fn-end",[Date.now()],n)}}}};a("setName,setAttribute,save,ignore,onEnd,getContext,end,get".split(","),function(t,e){p[e]=o(d+e)}),newrelic.noticeError=function(t){"string"==typeof t&&(t=new Error(t)),i("err",[t,(new Date).getTime()])}},{}],17:[function(t,e,n){e.exports=function(t){if("string"==typeof t&&t.length)return t.length;if("object"==typeof t){if("undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer&&t.byteLength)return t.byteLength;if("undefined"!=typeof Blob&&t instanceof Blob&&t.size)return t.size;if(!("undefined"!=typeof FormData&&t instanceof FormData))try{return JSON.stringify(t).length}catch(e){return}}}},{}],18:[function(t,e,n){var r=0,o=navigator.userAgent.match(/Firefox[\/\s](\d+\.\d+)/);o&&(r=+o[1]),e.exports=r},{}],19:[function(t,e,n){function r(t,e){var n=[],r="",i=0;for(r in t)o.call(t,r)&&(n[i]=e(r,t[r]),i+=1);return n}var o=Object.prototype.hasOwnProperty;e.exports=r},{}],20:[function(t,e,n){function r(t,e,n){e||(e=0),"undefined"==typeof n&&(n=t?t.length:0);for(var r=-1,o=n-e||0,i=Array(o<0?0:o);++r<o;)i[r]=t[e+r];return i}e.exports=r},{}],21:[function(t,e,n){function r(t){return!(t&&t instanceof Function&&t.apply&&!t[a])}var o=t("ee"),i=t(20),a="nr@original",c=Object.prototype.hasOwnProperty,s=!1;e.exports=function(t,e){function n(t,e,n,o){function nrWrapper(){var r,a,c,s;try{a=this,r=i(arguments),c="function"==typeof n?n(r,a):n||{}}catch(f){d([f,"",[r,a,o],c])}u(e+"start",[r,a,o],c);try{return s=t.apply(a,r)}catch(h){throw u(e+"err",[r,a,h],c),h}finally{u(e+"end",[r,a,s],c)}}return r(t)?t:(e||(e=""),nrWrapper[a]=t,h(t,nrWrapper),nrWrapper)}function f(t,e,o,i){o||(o="");var a,c,s,f="-"===o.charAt(0);for(s=0;s<e.length;s++)c=e[s],a=t[c],r(a)||(t[c]=n(a,f?c+o:o,i,c))}function u(n,r,o){if(!s||e){var i=s;s=!0;try{t.emit(n,r,o)}catch(a){d([a,n,r,o])}s=i}}function h(t,e){if(Object.defineProperty&&Object.keys)try{var n=Object.keys(t);return n.forEach(function(n){Object.defineProperty(e,n,{get:function(){return t[n]},set:function(e){return t[n]=e,e}})}),e}catch(r){d([r])}for(var o in t)c.call(t,o)&&(e[o]=t[o]);return e}function d(e){try{t.emit("internal-error",e)}catch(n){}}return t||(t=o),n.inPlace=f,n.flag=a,n}},{}],ee:[function(t,e,n){function r(){}function o(t){function e(t){return t&&t instanceof r?t:t?s(t,c,i):i()}function n(n,r,o){if(!d.aborted){t&&t(n,r,o);for(var i=e(o),a=l(n),c=a.length,s=0;s<c;s++)a[s].apply(i,r);var f=u[y[n]];return f&&f.push([g,n,r,i]),i}}function p(t,e){w[t]=l(t).concat(e)}function l(t){return w[t]||[]}function m(t){return h[t]=h[t]||o(n)}function v(t,e){f(t,function(t,n){e=e||"feature",y[n]=e,e in u||(u[e]=[])})}var w={},y={},g={on:p,emit:n,get:m,listeners:l,context:e,buffer:v,abort:a,aborted:!1};return g}function i(){return new r}function a(){(u.api||u.feature)&&(d.aborted=!0,u=d.backlog={})}var c="nr@context",s=t("gos"),f=t(19),u={},h={},d=e.exports=o();d.backlog=u},{}],gos:[function(t,e,n){function r(t,e){return e}function o(t,e,n){return t}var i=t(21),a=t("ee").get("promise"),c=i(a),s=t(19),f=NREUM.o.PR;e.exports=a,f&&(window.Promise=r,["all","race"].forEach(function(t){var e=f[t];f[t]=function(n){function r(t){return function(){a.emit("propagate",[null,!o],i),o=o||!t}}var o=!1;s(n,function(e,n){Promise.resolve(n).then(r("all"===t),r(!1))});var i=e.apply(f,arguments),c=f.resolve(i);return c}}),["resolve","reject"].forEach(function(t){var e=f[t];f[t]=function(t){var n=e.apply(f,arguments);return t!==n&&a.emit("propagate",[t,!0],n),n}}),f.prototype["catch"]=function(t){return this.then(null,t)},f.prototype=Object.create(f.prototype,{constructor:{value:r}}),s(Object.getOwnPropertyNames(f),function(t,e){try{r[e]=f[e]}catch(n){}}),a.on("executor-start",function(t){t[0]=c(t[0],"resolve-",this),t[1]=c(t[1],"resolve-",this)}),a.on("executor-err",function(t,e,n){t[1](n)}),c.inPlace(f.prototype,["then"],"then-",o),a.on("then-start",function(t,e){this.promise=e,t[0]=c(t[0],"cb-",this),t[1]=c(t[1],"cb-",this)}),a.on("then-end",function(t,e,n){this.nextPromise=n;var r=this.promise;a.emit("propagate",[r,!0],n)}),a.on("cb-end",function(t,e,n){a.emit("propagate",[n,!0],this.nextPromise)}),a.on("propagate",function(t,e,n){this.getCtx&&!e||(this.getCtx=function(){if(t instanceof Promise)var e=a.context(t);return e&&e.getCtx?e.getCtx():this})}),r.toString=function(){return""+f})},{}],handle:[function(t,e,n){function r(t,e,n,r){o.buffer([t],r),o.emit(t,e,n)}var o=t("ee").get("handle");e.exports=r,r.ee=o},{}],id:[function(t,e,n){function r(t){var e=typeof t;return!t||"object"!==e&&"function"!==e?-1:t===window?0:a(t,i,function(){return o++})}var o=1,i="nr@id",a=t("gos");e.exports=r},{}],loader:[function(t,e,n){function r(){if(!b++){var t=g.info=NREUM.info,e=h.getElementsByTagName("script")[0];if(setTimeout(f.abort,3e4),!(t&&t.licenseKey&&t.applicationID&&e))return f.abort();s(w,function(e,n){t[e]||(t[e]=n)}),c("mark",["onload",a()],null,"api");var n=h.createElement("script");n.src="https://"+t.agent,e.parentNode.insertBefore(n,e)}}function o(){"complete"===h.readyState&&i()}function i(){c("mark",["domContent",a()],null,"api")}function a(){return(new Date).getTime()}var c=t("handle"),s=t(19),f=t("ee"),u=window,h=u.document,d="addEventListener",p="attachEvent",l=u.XMLHttpRequest,m=l&&l.prototype;NREUM.o={ST:setTimeout,CT:clearTimeout,XHR:l,REQ:u.Request,EV:u.Event,PR:u.Promise,MO:u.MutationObserver},t(16);var v=""+location,w={beacon:"bam.nr-data.net",errorBeacon:"bam.nr-data.net",agent:"js-agent.newrelic.com/nr-spa-1016.min.js"},y=l&&m&&m[d]&&!/CriOS/.test(navigator.userAgent),g=e.exports={offset:a(),origin:v,features:{},xhrWrappable:y};h[d]?(h[d]("DOMContentLoaded",i,!1),u[d]("load",r,!1)):(h[p]("onreadystatechange",o),u[p]("onload",r)),c("mark",["firstbyte",a()],null,"api");var b=0},{}]},{},["loader",2,14,5,3,4]];
;NREUM.info={beacon:"bam.nr-data.net",errorBeacon:"bam.nr-data.net",licenseKey:"5183fce148",applicationID:"5350766",sa:1}
        </script>*@
    
    <meta charset="utf-8"/>
    <meta name="viewport" content="initial-scale=1.0, maximum-scale=1.0, user-scalable=no, width=device-width">
    <meta content="yes" name="apple-mobile-web-app-capable">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <link rel="apple-touch-icon" href="/LEXPLUS-CMYK.png">
    <meta name="msapplication-config" content="none" />
    <meta content="IE=Edge,chrome=1" http-equiv="X-UA-Compatible">
    <title>@ViewBag.Title - Neosys Lexplus</title>
    @Styles.Render("~/Content/css")
    @Scripts.Render("~/bundles/modernizr")


    @Html.DevExpress().GetStyleSheets(
        new StyleSheet {ExtensionSuite = ExtensionSuite.NavigationAndLayout},
        new StyleSheet {ExtensionSuite = ExtensionSuite.GridView},
        new StyleSheet {ExtensionSuite = ExtensionSuite.TreeList},
        new StyleSheet {ExtensionSuite = ExtensionSuite.HtmlEditor}
        )


    @Scripts.Render("~/bundles/jquery")
    @Scripts.Render("~/bundles/bootstrap")


    @RenderSection("scripts", required: false)
    @RenderSection("AdditionalResources", required: false)
    <!-- The DevExpress ASP.NET MVC Extensions' scripts -->
    @Html.DevExpress().GetScripts(
        new Script {ExtensionSuite = ExtensionSuite.NavigationAndLayout},
        new Script {ExtensionSuite = ExtensionSuite.GridView}
        )

    @*<script type="text/javascript" src="https://wibservicedesk.atlassian.net/s/d41d8cd98f00b204e9800998ecf8427e-T/chha76/b/c/c8a734256c6dd2d1e4344e119e50264f/_/download/batch/com.atlassian.jira.collector.plugin.jira-issue-collector-plugin:issuecollector/com.atlassian.jira.collector.plugin.jira-issue-collector-plugin:issuecollector.js?locale=de-DE&collectorId=c709aa6d"></script>

    <script type="text/javascript">
	window.ATL_JQ_PAGE_PROPS = {
		"triggerFunction": function(showCollectorDialog) {
			jQuery("#myNewBug").click(function(e) {
				e.preventDefault();
				showCollectorDialog();
			});
		}
	};
	setHeartbeat();
    </script>*@


    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=UA-109193532-1"></script>
    <script>
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());

          gtag('config', '344046725');
    </script>


    <meta name="robots" content="noindex,nofollow">
    <meta name="description" content="Lexplus by Neosys"/>
</head>
<body>




    <div id="wrapper">
        


        <!-- Navigation -->
        <nav class="navbar navbar-default navbar-static-top" role="navigation" style="margin-bottom: 0; margin-right: 20px">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="/"><img src="~/LEXPLUS-CMYK.png" width="186" /></a>
            </div>
            <!-- /.navbar-header -->
            <div style="margin-right: 15px;">
                @Html.Partial("_LoginPartial")
            </div>
           

            <div class="navbar-default sidebar" role="navigation">
                <!-- /.navbar-top-links -->
                @if (Request.IsAuthenticated && new SessionHelper().IsPrivacyPolicyAccepted())
                {

                    <div class="sidebar-nav navbar-collapse">
                        <p>&nbsp;</p>
                        <ul class="nav" id="side-menu">

                            @if (!User.IsInRole(Role.Accountable))
                            {
                                <li class="@Html.IsActive("Home", "Index")">
                                    <a href="/"><i class="fa fa-home fa-fw fa-lg"></i> @Resources.View_Breadcrumb_Home</a>
                                </li>
                            }
                            @if (User.IsInRole(Role.Admin) | User.IsInRole(Role.ProjectManager) | User.IsInRole(Role.ObjectGuru))
                            {
                                <li>
                                    <a href="#"><i class="fa fa-tasks fa-fw fa-lg"></i> Erlasse<span class="fa arrow"></span></a>

                                    <ul class="nav nav-second-level @Html.IsSubErlassActive()">
                                        <li class="@Html.IsActive("Erlasse", "Index")">
                                            @Html.ActionLink("Erlasse", "Index", "Erlasse", new { area = "Admin", displayArchived = false, standortErlasse = false }, new { @class = "" })
                                        </li>
                                        <li class="@Html.IsActive("Erlasse", "StandortErlasse")">
                                            @Html.ActionLink("Standort Erlasse", "StandortErlasse", "Erlasse", new { area = "Admin", displayArchived = false, standortErlasse = true }, new { @class = "" })
                                        </li>
                                        <li class="@Html.IsActive("Checklist", "Index")">
                                            @Html.ActionLink("SUVA-Checklisten", "Index", "Checklist", new { area = "Admin" }, new { @class = "" })
                                        </li>
                                    </ul>
                                    <!-- /.nav-second-level -->
                                </li>
                                <li class="@Html.IsActive("Kommentar", "Index")">
                                    <a href="@Url.Action("Index", "Kommentar", new {area = "Admin"})"><i class="fa fa-tasks fa-fw fa-lg"></i> Kommentare </a>
                                </li>
                                <li class="@Html.IsActive("Consultation", "Index")">
                                    <a href="@Url.Action("Index", "Consultation", new {area = "Admin"})"><i class="fa fa-tasks fa-fw fa-lg"></i> Vernehmlassung </a>
                                </li>
                                <li class="@Html.IsActive("Objekte", "Index")">
                                    <a href="@Url.Action("Index", "Objekte", new {area = "Admin"})"><i class="fa fa-cubes fa-fw fa-lg"></i> Objekte</a>
                                </li>
                                <li class="@Html.IsActive("Obligation", "Index")">
                                    <a href="@Url.Action("Index", "Obligation", new {area = "Admin"})"><i class="fa fa-tasks fa-fw fa-lg"></i>@Resources.Entitaet_Pflicht_Plural</a>
                                </li>
                                <li>
                                    <a href="#"><i class="fa fa-database fa-fw fa-lg"></i> Stammdaten<span class="fa arrow"></span></a>

                                    <ul class="nav nav-second-level @Html.IsSubActive()">
                                        <li class="@Html.IsActive("Erlasstypen", "Index")">
                                            @Html.ActionLink("Erlasstypen", "Index", "Erlasstypen", new { area = "Admin" }, new { @class = "" })
                                        </li>
                                        <li class="@Html.IsActive("Herausgeber", "Index")">
                                            @Html.ActionLink("Herausgeber", "Index", "Herausgeber", new { area = "Admin" }, new { @class = "" })
                                        </li>
                                        <li class="@Html.IsActive("Herausgeber", "StandortHerausgeber")">
                                            @Html.ActionLink("Standort Herausgeber", "StandortHerausgeber", "Herausgeber", new { area = "Admin" }, new { @class = "" })
                                        </li>
                                        <li class="@Html.IsActive("Rechtsbereiche", "Index")">
                                            @Html.ActionLink("Rechtsbereiche", "Index", "Rechtsbereiche", new { area = "Admin" }, new { @class = "" })
                                        </li>
                                        <li class="@Html.IsActive("Objektkategorien", "Index")">
                                            @Html.ActionLink("Objektkategorien", "Index", "Objektkategorien", new { area = "Admin" }, new { @class = "" })
                                        </li>
                                    </ul>
                                    <!-- /.nav-second-level -->
                                </li>
                            }



                            @if (User.IsInRole(Role.Admin))
                            {
                                <li class="@Html.IsSubAdminActive()">
                                    <a href="#"><i class="fa fa-cog fa-fw fa-lg"></i> Administration<span class="fa arrow"></span></a>
                                    <ul class="nav nav-second-level @Html.IsSubAdminActive()">
                                        <li class="@Html.IsActive("RolesAdmin", "Index")">
                                            @Html.ActionLink("Rollenverwaltung", "Index", "RolesAdmin", new { area = "Admin" }, new { @class = "" })
                                        </li>
                                        <li class="@Html.IsActive("UsersAdmin", "Index")">
                                            @Html.ActionLink(Resources.Titel_Benutzerverwaltung, "Index", "UsersAdmin", new { area = "Admin" }, new { @class = "" })
                                        </li>
                                        @{
                                            bool isNonProdEnvironment = EnvironmentHelper.IsNonProductionEnvironment();
                                        }
                                        @if (isNonProdEnvironment)
                                        {
                                        <li class="@Html.IsActive("Newsletter", "Index")">
                                            @Html.ActionLink("Newsletter", "Index", "Newsletter", new { area = "Admin" }, new { @class = "" })
                                        </li>
                                        }
                                        <li class="@Html.IsActive("CustomerNews", "Index")">
                                            @Html.ActionLink("News", "Index", "CustomerNews", new { area = "Admin" }, new { @class = "" })
                                        </li>
                                        <li class="@Html.IsActive("FAQ", "Index")">
                                            @Html.ActionLink("FAQ", "Index", "FAQ", new { area = "Admin" }, new { @class = "" })
                                        </li>
                                        <li class="@Html.IsActive("PrivacyPolicy", "Index")">
                                            @Html.ActionLink(Resources.Entitaet_PrivacyPolicy_Title, "Index", "PrivacyPolicy", new { area = "Admin" }, new { @class = "" })
                                        </li>
                                        <li class="@Html.IsActive("EmailTemplate", "Index")">
                                             @Html.ActionLink(Resources.Email_template, "Index", "EmailTemplate", new { area = "Admin" }, new { @class = "" })
                                        </li>
                                    </ul>
                                </li>

                            }
                            @if (User.IsInRole(Role.Admin) | User.IsInRole(Role.ProjectManager) | User.IsInRole(Role.ObjectGuru))
                            {

                                <li class="@Html.IsActive("Kunden", "Index")">
                                    <a href="@Url.Action("Index", "Kunden", new {area = "Admin"})"><i class="fa fa-group fa-fw fa-lg"></i> Kunden</a>
                                </li>

                            }

                            @if (User.IsInRole(Role.Admin) | User.IsInRole(Role.ProjectManager) | User.IsInRole(Role.ObjectGuru))
                            {
                                <li class="@Html.IsSubAuswertungActive()">
                                    <a href="#"><i class="fa fa-bar-chart fa-fw fa-lg"></i> Auswertungen<span class="fa arrow"></span></a>
                                    <ul class="nav nav-second-level @Html.IsSubAuswertungActive()">
                                        <li class="@Html.IsActive("Auswertungen", "Index")">
                                            <a href="@Url.Action("AktuelleForderungen", "Auswertungen", new {area = "Admin"})"><i class="fa fa-file-text fa-fw fa-lg"></i> Forderungen</a>
                                        </li>
                                        <li class="@Html.IsActive("Auswertungen", "Index")">
                                            <a href="@Url.Action("VerwendeteObjekte", "Auswertungen", new {area = "Admin"})"><i class="fa fa-cube fa-fw fa-lg"></i> Objekte</a>
                                        </li>

                                    </ul>
                                </li>

                            }

                            @if (User.IsInRole(Role.SuperUser) || User.IsInRole(Role.ReadOnly) || User.IsInRole(Role.EndUser) || User.IsInRole(Role.Accountable))
                            {
                                <li class="@Html.IsActive("Standorte", "Index")">
                                    <a href="@Url.Action("Index", "Standorte", new {area = "Portal"})"><i class="fa fa-map-marker fa-fw fa-lg"></i> @NeoSysLCS.Site.Helpers.CultureHelper.GetStandortMenuName(0, 0, false)</a>
                                </li>
                            }
                            
                            @if (User.IsInRole(Role.SuperUser) || User.IsInRole(Role.ReadOnly) || User.IsInRole(Role.EndUser) || User.IsInRole(Role.Accountable) || User.IsInRole(Role.Newsletter))
                            {
                                <li class="@Html.IsActive("Massnahme", "Index")">
                                    <a href="@Url.Action("Index", "Massnahme", new {area = "Portal"})"><i class="fa fa-group fa-fw fa-lg"></i> @Resources.Entitaet_Massnahme_Plural</a>
                                </li>
                            }

                            @if (User.IsInRole(Role.SuperUser))
                            {
                                <li class="@Html.IsSystemAdminActive()">
                                    <a href="#"><i class="fa fa-cog fa-fw fa-lg"></i> @Resources.View_Administration_Title <span class="fa arrow"></span></a>
                                    <ul class="nav nav-second-level @Html.IsSystemAdminActive()">
                                        <li class="@Html.IsActive("StandortAdministration", "Index")">
                                            @Html.ActionLink(Resources.Verwaltung + " " + Resources.Titel_Kundendokument_Singular, "Index", "StandortAdministration", new { area = "Portal" }, new { @class = "" })
                                        </li>
                                        <li class="@Html.IsActive("UsersAdmin", "Index")">
                                            @Html.ActionLink(Resources.Titel_Benutzerverwaltung, "Index", "UsersAdmin", new { area = "Admin" }, new { @class = "" })
                                        </li>
                                        @*<li class="@Html.IsActive("Kundeninformation", "Index")">
                        @Html.ActionLink("Kundeninformation", "Index", "Kundeninformation", new { area = "Admin" }, new { @class = "" })
                    </li>*@
                                    </ul>
                                </li>

                            }

                            @if (User.IsInRole(Role.SuperUser) || User.IsInRole(Role.Auditor) || User.IsInRole(Role.ReadOnly) || User.IsInRole(Role.Accountable) || User.IsInRole(Role.Newsletter))
                            {
                                <li class="@Html.IsSubDownloadbereichActive()">
                                    <a href="#"><i class="fa fa-download fa-fw fa-lg"></i>@Resources.View_Downloadbereich_Title<span class="fa arrow"></span></a>
                                    <ul class="nav nav-second-level @Html.IsSubDownloadbereichActive()">
                                        @if (User.IsInRole(Role.SuperUser) || User.IsInRole(Role.Auditor) || User.IsInRole(Role.ReadOnly) || User.IsInRole(Role.Accountable))
                                        {
                                            <li class="@Html.IsActive("Downloadbereich", "Index")">
                                                <a href="@Url.Action("AllDocuments", "Downloadbereich", new { area = "Portal" })"><i class="fa fa-file fa-fw fa-lg"></i>@Resources.View_All_Documents_Title</a>
                                            </li>
                                        }
                                        @if (User.IsInRole(Role.Newsletter))
                                        {
                                            <li class="@Html.IsActive("Downloadbereich", "Index")">
                                                <a href="@Url.Action("NewsletterHistory", "Downloadbereich", new {area = "Portal"})"><i class="fa fa-newspaper-o fa-fw fa-lg"></i>@Resources.View_Newsletter_Title</a>
                                            </li>
                                        }
                                        @if (User.IsInRole(Role.SuperUser) || User.IsInRole(Role.Auditor) || User.IsInRole(Role.ReadOnly) || User.IsInRole(Role.Accountable))
                                        {
                                            <li class="@Html.IsActive("Downloadbereich", "Index")">
                                                <a href="@Url.Action("StandorteMitBericht", "Downloadbereich", new { area = "Portal" })"><i class="fa fa-table fa-fw fa-lg"></i>@Resources.View_Bericht_Title</a>
                                            </li>
                                        }
                                        </ul>
                                </li>
                            }

                            @if (User.IsInRole(Role.SuperUser) || User.IsInRole(Role.Auditor))
                            {
                                <li class="@Html.IsPortalAuswertungActive()">
                                    <a href="#"><i class="fa fa-bar-chart fa-fw fa-lg"></i> @Resources.Menu_Auswertungen <span class="fa arrow"></span></a>
                                    <ul class="nav nav-second-level @Html.IsPortalAuswertungActive()">
                                        <li class="@Html.IsActive("Auswertung", "Index")">
                                            <a href="@Url.Action("Index", "Auswertung", new {area = "Portal"})"> @Resources.Menu_Diagramme </a>
                                        </li>
                                        <li class="@Html.IsActive("Konzern", "Index")">
                                            <a href="@Url.Action("Index", "Konzern", new {area = "Portal"})"> @Resources.View_Konzern_Title </a>
                                        </li>
                                        <li class="@Html.IsActive("LegalCompliance", "Index")">
                                            <a href="@Url.Action("Index", "LegalCompliance", new {area = "Portal"})"> Legal Compliance </a>
                                        </li>
                                    </ul>
                                </li>
                            }
                            @if (User.IsInRole(Role.Admin) || User.IsInRole(Role.ProjectManager))
                            {

                                <li class="@Html.IsActive("Evaluations", "Index")">
                                    <a href="@Url.Action("Index", "Evaluations", new {area = "Admin"})"><i class="fa fa-group fa-fw fa-lg"></i> Evaluations</a>
                                </li>
                            }
                        </ul>
                        <!-- /#side-menu -->
                    </div>
                    <!-- /.sidebar-collapse -->
                    <!-- end is authenticated -->
                }
                else
                {
                    <div class="row" style="margin-left: -1px;">
                        <div class="col-lg-12">
                            <h5>… die online Gesetzesdatenbank von</h5>
                            <p>&nbsp;</p>
                            <p>&nbsp;</p>
                            <img src="~/logo-neosys.gif"/>
                            <br/><br/>
                            <address>
                                <strong>Neosys AG</strong><br>
                                Privatstrasse 10<br>
                                CH-4563 Gerlafingen<br>
                                <abbr title="Telefon">Tel:</abbr> +41(0)32 674 45 11<br />
                                <a href="mailto:<EMAIL>"><EMAIL></a><br />
                                <a href="http://www.neosys.ch" target="_blank">http://www.neosys.ch</a>
                            </address>
                        </div>
                    </div>
                }
            </div>
                <!-- /.navbar-static-side -->
        </nav>



        <div id="page-wrapper">
            <div class="row">
                <div class="col-lg-12">
                    <i id="menu-toggle" class="fa fa-angle-double-left fa-lg slide-handle"></i>
                    <div class="breadcrumb-noesys">@RenderSection("breadcrumb", false)</div>
                    @*<h1 class="page-header">@ViewBag.Title</h1>*@

                    @RenderBody()
                </div>
                <!-- /.col-lg-12 -->
            </div>

            <!-- /.row -->
        </div>
        <footer style="margin-top:10px">
           &copy; @DateTime.Now.Year - <a href="http://www.neosys.ch" target="_blank" alt="neosys">Neosys</a> - @Html.ActionLink(Resources.View_Impressum_Title, "Impressum", "Home", new { area = "" }, new { @class = "" }) - <a href="@Resources.View_AGB_Link" class="small" target="_blank">AGB</a><small style="color: #ccc"> - @Resources.View_Footer_Version: @typeof(NeoSysLCS.Site.MvcApplication).Assembly.GetName().Version</small>
        </footer>
        <!-- /#page-wrapper -->

    </div>
    <!-- /#wrapper -->




</body>
</html>
