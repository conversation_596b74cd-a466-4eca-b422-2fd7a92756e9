using System.Linq;
using System.Web.Mvc;

namespace NeoSysLCS.Site.Utilities
{
    /// <summary>
    /// Helper for the navigation
    /// </summary>
    public static class NavUtilities
    {
        public static string IsActive(this HtmlHelper html,
                                      string control,
                                      string action)
        {
            var routeData = html.ViewContext.RouteData;

            var routeAction = (string)routeData.Values["action"];
            var routeControl = (string)routeData.Values["controller"];

            // both must match
            var returnActive = control == routeControl &&
                               action == routeAction;

            return returnActive ? "active" : "";
        }

        public static string IsSubActive(this HtmlHelper html)
        {
            var routeData = html.ViewContext.RouteData;
            string[] stammdaten = { "Herausgeber", "StandortHerausgeber", "Erlasstypen", "Rechtsbereiche", "Objektkategorien" };

            //var routeAction = (string)routeData.Values["action"];
            var routeControl = (string)routeData.Values["controller"];

            // both must match
            var returnActive = stammdaten.Contains(routeControl);

            return returnActive ? "in" : "";
        }

        public static string IsSubErlassActive(this HtmlHelper html)
        {
            var routeData = html.ViewContext.RouteData;
            string[] stammdaten = { "Erlasse", "Checklist" };

            //var routeAction = (string)routeData.Values["action"];
            var routeControl = (string)routeData.Values["controller"];

            // both must match
            var returnActive = stammdaten.Contains(routeControl);

            return returnActive ? "in" : "";
        }

        public static string IsSubAdminActive(this HtmlHelper html)
        {
            var routeData = html.ViewContext.RouteData;
            var administration = new[] { "UsersAdmin", "RolesAdmin", "Newsletter", "Kundeninformation", "FAQ" };

            //var routeAction = (string)routeData.Values["action"];
            var routeControl = (string)routeData.Values["controller"];

            // both must match
            var returnActive = administration.Contains(routeControl);

            return returnActive ? "in" : "";
        }

        public static string IsSystemAdminActive(this HtmlHelper html)
        {
            var routeData = html.ViewContext.RouteData;
            var administration = new[] { "UsersAdmin", "StandortAdministration" };

            //var routeAction = (string)routeData.Values["action"];
            var routeControl = (string)routeData.Values["controller"];

            // both must match
            var returnActive = administration.Contains(routeControl);

            return returnActive ? "in" : "";
        }

        public static string IsPortalAuswertungActive(this HtmlHelper html)
        {
            var routeData = html.ViewContext.RouteData;
            var administration = new[] { "Konzern", "Auswertung", "LegalCompliance" };


            //var routeAction = (string)routeData.Values["action"];
            var routeControl = (string)routeData.Values["controller"];

            // both must match
            var returnActive = administration.Contains(routeControl);

            return returnActive ? "in" : "";
        }

        public static string IsSubAuswertungActive(this HtmlHelper html)
        {
            var routeData = html.ViewContext.RouteData;
            var administration = new[] { "Auswertungen", "RolesAdmin", "Kundeninformation" };

            //var routeAction = (string)routeData.Values["action"];
            var routeControl = (string)routeData.Values["controller"];

            // both must match
            var returnActive = administration.Contains(routeControl);

            return returnActive ? "in" : "";
        }

        public static string IsSubKundeActive(this HtmlHelper html)
        {
            var routeData = html.ViewContext.RouteData;
            string[] kunde = { "KundenAdmin" };

            var routeControl = (string)routeData.Values["controller"];

            // both must match
            var returnActive = kunde.Contains(routeControl);

            return returnActive ? "in" : "";
        }

        public static string IsSubPortalActive(this HtmlHelper html)
        {
            var routeData = html.ViewContext.RouteData;
            string[] portal = { "Standorte", "Kundendokument" };

            var routeControl = (string)routeData.Values["controller"];

            // both must match
            var returnActive = portal.Contains(routeControl);

            return returnActive ? "in" : "";
        }

        public static string IsSubDownloadbereichActive(this HtmlHelper html)
        {
            var routeData = html.ViewContext.RouteData;
            var downloadbereich = new[] { "Downloadbereich" };

            //var routeAction = (string)routeData.Values["action"];
            var routeControl = (string)routeData.Values["controller"];

            // both must match
            var returnActive = downloadbereich.Contains(routeControl);

            return returnActive ? "in" : "";
        }

    }
}