using System;
using System.Linq;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;

namespace NeoSysLCS.Site.Utilities.Import
{
    public class KundendokumentIndividuelleForderungImporter : BaseImporter
    {

        public KundendokumentIndividuelleForderungImporter(SpreadsheetDocument spreadsheetDocument,
            string sheetname, int kundendokumentId, UnitOfWork unitOfWork)
            : base(spreadsheetDocument, sheetname, kundendokumentId, unitOfWork)
        {
           
        }

        /// <summary>
        /// NOTE: if you add or remove columns in the export, please add or remove them also here
        /// </summary>
        enum Columns
        {
            Id = 0,
            Standortobjekt = 1,
            Kundenbezug = 2,
            Beschreibung = 3,
            GueltigVon = 4,
            GueltigBis = 5,
            Erfuellung = 6,
            <PERSON>rfuellungszeitpunkt = 7,
            ErfuelltDurch = 8,
            Verantwortlich = 9,
            Ablageort = 10,
            Kommentar = 11,
        }

        protected override void CheckColumns(Row row)
        {
            var rowCells = row.Elements<Cell>();
            if (rowCells.Count() < 12)
            {
                throw new Exception(Resources.Properties.Resources.Fehler_Import_NumberOfColumns);
            }
        }

        protected override void ImportRow(Row row)
        {
            int entityId;
            IndividuelleForderung entity = null;
            var rowCells = row.Elements<Cell>();
            var id = GetCellValue(rowCells.ElementAt((int)Columns.Id));

            //check for empty rows
            if (!string.IsNullOrEmpty(id))
            {
                if (int.TryParse(id, out entityId))
                {
                    entity = _unitOfWork.IndividuelleForderungRepository.GetByID(entityId);
                }

                if (entity == null || entity.StandortID != _kundendokument.StandortID)
                {
                    throw new Exception(
                        Resources.Properties.Resources.Fehler_Import_IndividuelleForderungNotInStandort.Replace(
                            "##ID##", id));
                }

                if (entity == null || entity.KundeID != _kundendokument.KundeID)
                {
                    throw new Exception(
                        Resources.Properties.Resources.Fehler_Import_IndividuelleForderungNotFromKunde.Replace(
                            "##ID##", id));
                }

                try
                {
                    //Kundenbezug
                    entity.Kundenbezug = GetCellValue(rowCells.ElementAt((int) Columns.Kundenbezug));
                    //Erfüllung
                    entity.Erfuellung =
                        ConvertToKundendokumentErfuellung(GetCellValue(rowCells.ElementAt((int) Columns.Erfuellung)));

                    //Erfüllungszeitpunkt
                    entity.Erfuellungszeitpunkt =
                        ConvertToDateTime(GetCellValue(rowCells.ElementAt((int) Columns.Erfuellungszeitpunkt)));

                    //other fields
                    entity.ErfuelltDurch = GetCellValue(rowCells.ElementAt((int) Columns.ErfuelltDurch));
                    entity.Verantwortlich = GetCellValue(rowCells.ElementAt((int) Columns.Verantwortlich));
                    entity.Ablageort = GetCellValue(rowCells.ElementAt((int) Columns.Ablageort));
                    entity.Kommentar = GetCellValue(rowCells.ElementAt((int) Columns.Kommentar));
                    //update and save
                    _unitOfWork.IndividuelleForderungRepository.Update(entity);
                }
                catch (Exception e)
                {
                    throw new Exception(
                        Resources.Properties.Resources.Fehler_Import_IndividuelleForderungGeneralError.Replace(
                            "##ID##", id).Replace("##error##", e.Message));
                }
            }
        }
    }

}