using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text.RegularExpressions;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;
using NeoSysLCS.Site.Helpers;

namespace NeoSysLCS.Site.Utilities.Import.Erlass
{
    /// <summary>
    /// Base class for xslx importer
    /// </summary>
    public abstract class BaseImporter
    {
        protected SharedStringTablePart _stringTable;
        protected UnitOfWork _unitOfWork;
        protected IEnumerable<Row> _rows;
        private static int COLUMN_COUNT = 54;
        protected int _rowCount = -1;

        protected BaseImporter(SpreadsheetDocument spreadsheetDocument, string sheetname, UnitOfWork unitOfWork)
        {
            var workbookPart = spreadsheetDocument.WorkbookPart;
            var worksheetPart = GetWorksheetPart(workbookPart, sheetname);
            var sheetData = worksheetPart.Worksheet.Elements<SheetData>().First();
            _stringTable = workbookPart.GetPartsOfType<SharedStringTablePart>().FirstOrDefault();
            _unitOfWork = unitOfWork;
            _rows = sheetData.Elements<Row>();
        }

        /// <summary>
        /// Imports a row.
        /// </summary>
        /// <param name="row">The row.</param>
        protected abstract void ImportRow(IEnumerable<Cell> cells);
        protected abstract void DeleteRow(IEnumerable<Cell> cells);
        /// <summary>
        /// Checks the columns.
        /// </summary>
        /// <param name="row">The row.</param>
        protected void CheckColumns(IEnumerable<Cell> rowCells, string rowSeqNumber)
        {
            if ((rowCells.Count() < COLUMN_COUNT))
            {
                throw new Exception(Resources.Properties.Resources.Fehler_Import_NumberOfColumns + "(" + rowSeqNumber + "). Number of cells is " + rowCells.Count() + " instead of " + COLUMN_COUNT);
            }
        }

        protected void CountRows()
        {
            // all rows should have set ErlassID based on which it will be determined number of set rows
            if (_rows == null)
            {
                return;
            }
            _rowCount = 0;

            int idx = 0;
            foreach (Row r in _rows)
            {
                var test = GetEnumerator(r);
                var rowCells = ConvertToIEnumerable(test);
                var customErlassID = GetCellValue(rowCells.ElementAt((int)XlsxColumns.ErlassId));
                //skip header row
                if (idx == 0)
                {
                    idx++;
                    continue;
                } else if (string.IsNullOrEmpty(customErlassID))
                {
                    break;
                }
                _rowCount++;
                idx++;

            }
        }

        /// <summary>
        /// NOTE: if you add or remove columns in the export, please add or remove them also here
        /// </summary>
        // NOTE: column blank is required since the page header in Excel merges columns
        protected enum XlsxColumns
        {
            ErlassId = 0, // A
            ObjektId = 1, // B
            RechtsbereicheIds = 2, // C
            HerausgeberDe = 3, // D
            HerausgeberFr = 4, // E
            HerausgeberIt = 5, // F
            HerausgeberEn = 6, // G
            ErlassNr = 7, // H
            AbkuerzungDe = 8, // I
            AbkuerzungFr = 9, // J
            AbkuerzungIt = 10, // K
            AbkuerzungEn = 11, // L
            TitelDe = 12, // M
            TitelFr = 13, // N
            TitelIt = 14, // O
            TitelEn = 15, // P
            QuelleDe = 16, // Q
            QuelleFr = 17, // R
            QuelleIt = 18, // S
            QuelleEn = 19, // T
            Erlassfassung = 20, // U
            ErlassfassungInkrafttretung = 21, // V
            QuelleErlassfassungDe = 22, // W
            QuelleErlassfassungFr = 23, // X
            QuelleErlassfassungIt = 24, // Y
            QuelleErlassfassungEn = 25, // Z
            KommentarBetroffenDe = 26, // AA
            KommentarNichtbetroffenDe = 27, // AB
            KommentarBetroffenFr = 28, // AC
            KommentarNichtbetroffenFr = 29, // AD
            KommentarBetroffenIt = 30, // AE
            KommentarNichtbetroffenIt = 31, // AF
            KommentarBetroffenEn = 32, // AG
            KommentarNichtbetroffenEn = 33, // AH
            LinkKommentarBetroffenDe = 34, // AI
            LinkKommentarNichtbetroffenDe = 35, // AJ
            LinkKommentarBetroffenFr = 36, // AK
            LinkKommentarNichtbetroffenFr = 37, // AL
            LinkKommentarBetroffenIt = 38, // AM
            LinkKommentarNichtbetroffenIt = 39, // AN
            LinkKommentarBetroffenEn = 40, // AO
            LinkKommentarNichtbetroffenEn = 41, // AP
            Art = 42, // AQ
            LinkArtDe = 43, // AR
            LinkArtFr = 44, // AS
            LinkArtIt = 45, // AT
            LinkArtEn = 46, // AU
            ForderungDe = 47, // AV
            ForderungFr = 48, // AW
            ForderungIt = 49, // AX
            ForderungEn = 50, // AY
            ForderungInkraftretung = 51, // AZ
            Bewilligung = 52, // BA
            Nachweis = 53, // BB
            DeleteFlag=54 //BC
        }


        /// <summary>
        /// Imports a excel sheet.
        /// </summary>
        public void Import()
        {
            if (_rowCount == -1)
            {
                CountRows();
            }
            int idx = 0;
            
            try
            {
                ExceptionUtility.LogInfo($"Starting import process. Total rows: {_rowCount}");
                
                foreach (Row r in _rows)
                {
                    try
                    {
                        var test = GetEnumerator(r);
                        var rowCells = ConvertToIEnumerable(test);
                        //skip header row
                        if (idx == 0)
                        {
                            CheckColumns(rowCells, "Header row");
                        }
                        else
                        {
                            var humanReadableRowNumber = idx + 1;
                            ExceptionUtility.LogInfo($"Processing row #{humanReadableRowNumber}");
                            CheckColumns(rowCells, "row #" + humanReadableRowNumber);

                            // Check if the last column (DeleteFlag) contains "x"
                            var deleteFlagColumnIndex = (int)XlsxColumns.DeleteFlag;
                            var deleteFlagValue = GetCellValue(rowCells.ElementAt(deleteFlagColumnIndex));
                            
                            if (!string.IsNullOrEmpty(deleteFlagValue) && deleteFlagValue.Trim().ToLower() == "x")
                            {
                                //delete forderung
                                ExceptionUtility.LogInfo($"Deleting row #{humanReadableRowNumber} (DeleteFlag is 'x')");
                                DeleteRow(rowCells);
                                // Skip this row if DeleteFlag is "x"
                                idx++;
                                continue;
                            }
                            
                            try
                            {
                                ImportRow(rowCells);
                                ExceptionUtility.LogInfo($"Successfully imported row #{humanReadableRowNumber}");
                            }
                            catch (Exception ex)
                            {
                                ExceptionUtility.LogException(ex, $"Error importing row #{humanReadableRowNumber}");
                                throw new Exception($"Error importing row #{humanReadableRowNumber}: {ex.Message}", ex);
                            }
                        }

                        if (idx == _rowCount)
                        {
                            break;
                        }
                        idx++;
                    }
                    catch (Exception ex)
                    {
                        ExceptionUtility.LogException(ex, $"Error processing row #{idx + 1}");
                        throw;
                    }
                }
                
                ExceptionUtility.LogInfo("Import process completed successfully");
            }
            catch (Exception ex)
            {
                ExceptionUtility.LogException(ex, "Error in Import process");
                throw;
            }
        }

        protected string GetCellValue(Cell cell)
        {
            if (cell.CellValue != null)
            {
                if (cell.DataType != null && cell.DataType == CellValues.SharedString)
                {
                    if (_stringTable != null)
                    {
                        //All strings in an Excel worksheet are stored in a array like structure called the SharedSTringTable.
                        var value = _stringTable.SharedStringTable.ElementAt(int.Parse(cell.CellValue.InnerText)).InnerText;
                        if (value.Contains("/") && value.Length == 1)
                        {
                            value = "";
                        }

                        return value;
                    }
                }
                else
                {
                    var value = cell.CellValue.Text;
                    if (value.Contains("/") && value.Length == 1)
                    {
                        value = "";
                    }

                    return value;
                }
            }
            return null;
        }

        protected WorksheetPart GetWorksheetPart(WorkbookPart workbookPart, string sheetName)
        {
            try
            {
                var sheet = workbookPart.Workbook.Descendants<Sheet>().FirstOrDefault(s => sheetName.Equals(s.Name));
                if (sheet == null)
                {
                    ExceptionUtility.LogError($"Worksheet '{sheetName}' not found in the Excel file.");
                    throw new InvalidOperationException($"Worksheet '{sheetName}' not found in the Excel file.");
                }
                
                string relId = sheet.Id;
                return (WorksheetPart)workbookPart.GetPartById(relId);
            }
            catch (InvalidOperationException)
            {
                // Re-throw specific exceptions we've created
                throw;
            }
            catch (Exception ex)
            {
                ExceptionUtility.LogException(ex, $"Error getting worksheet part '{sheetName}'");
                throw new InvalidOperationException($"Error accessing worksheet '{sheetName}'. {ex.Message}", ex);
            }
        }

        protected DateTime? ConvertToDateTime(string value)
        {
            // Check if the value is not null or empty
            if (!string.IsNullOrEmpty(value))
            {
                double oaDate;
                // First, try to parse the value as an OA date
                if (double.TryParse(value, out oaDate))
                {
                    return DateTime.FromOADate(oaDate);
                }
                else
                {
                    // If not an OA date, try to parse as a standard date string
                    DateTime parsedDate;
                    if (DateTime.TryParseExact(value, new string[] { "MM/dd/yyyy", "yyyy" }, CultureInfo.InvariantCulture, DateTimeStyles.None, out parsedDate))
                    {
                        return parsedDate;
                    }
                }
            }
            // If none of the formats match, or the value is empty, return null
            return null;
        }


        protected int ExtractStandortId(string erlassId)
        {
            if (string.IsNullOrEmpty(erlassId))
            {
                var errorMessage = "Could not parse standortId from empty ID";
                ExceptionUtility.LogError(errorMessage);
                throw new ArgumentException(errorMessage, nameof(erlassId));
            }

            int standortId;
            try
            {
                var items = Regex.Split(erlassId, @"[-]");
                if (items.Length < 2)
                {
                    throw new FormatException($"Invalid ErlassId format: {erlassId}. Expected format contains a hyphen (-).");
                }
                standortId = Convert.ToInt32(items[1]);
            }
            catch (Exception ex)
            {
                var errorMessage = $"Could not parse standortId from ID: {erlassId}";
                ExceptionUtility.LogException(ex, errorMessage);
                throw new Exception(errorMessage, ex);
            }
            return standortId;
        }
        protected Herausgeber TryToGetHerausgeber(IEnumerable<Cell> rowCells, int standortID)
        {
            var herausgeberNameDe = GetCellValue(rowCells.ElementAt((int)XlsxColumns.HerausgeberDe));
            var herausgeberNameFr = GetCellValue(rowCells.ElementAt((int)XlsxColumns.HerausgeberFr));
            var herausgeberNameIt = GetCellValue(rowCells.ElementAt((int)XlsxColumns.HerausgeberIt));
            var herausgeberNameEn = GetCellValue(rowCells.ElementAt((int)XlsxColumns.HerausgeberEn));
            Herausgeber herausgeber = null;
            // TODO I'm not sure if Erlasse,Standorte are needed as includeProperties - eventually these should be removed
            if (!string.IsNullOrEmpty(herausgeberNameDe))
            {
                herausgeber = this._unitOfWork.HerausgeberRepository.Get(hg => hg.NameDE == herausgeberNameDe && hg.StandortID.HasValue && hg.StandortID.Value == standortID, null, "Erlasse,Standorte").ToArray().FirstOrDefault();
            }
            else if (!string.IsNullOrEmpty(herausgeberNameFr))
            {
                herausgeber = this._unitOfWork.HerausgeberRepository.Get(hg => hg.NameFR == herausgeberNameFr && hg.StandortID.HasValue && hg.StandortID.Value == standortID, null, "Erlasse,Standorte").ToArray().FirstOrDefault();
            }
            else if (!string.IsNullOrEmpty(herausgeberNameIt))
            {
                herausgeber = this._unitOfWork.HerausgeberRepository.Get(hg => hg.NameIT == herausgeberNameIt && hg.StandortID.HasValue && hg.StandortID.Value == standortID, null, "Erlasse,Standorte").ToArray().FirstOrDefault();
            }
            else if (!string.IsNullOrEmpty(herausgeberNameEn))
            {
                herausgeber = this._unitOfWork.HerausgeberRepository.Get(hg => hg.NameEN == herausgeberNameEn && hg.StandortID.HasValue && hg.StandortID.Value == standortID, null, "Erlasse,Standorte").ToArray().FirstOrDefault();
            }

            if (herausgeber == null || herausgeber.HerausgeberID == 0)
            {
                return null;
            }

            return herausgeber;
        }
        protected Herausgeber GetHerausgeber(IEnumerable<Cell> rowCells, int standortID)
        {
            var herausgeber = TryToGetHerausgeber(rowCells, standortID);

            if (herausgeber == null || herausgeber.HerausgeberID == 0)
            {
                throw new Exception("Internal error - Herausgeber could't be found"); // TODO think about more meaningful message
            }

            return herausgeber;
        }

        protected ErlassViewModel GetErlassViewModel(IEnumerable<Cell> rowCells)
        {
            var customErlassId = GetCellValue(rowCells.ElementAt((int)XlsxColumns.ErlassId));
            var erlass = this._unitOfWork.ErlassRepository.GetErlassViewModelByCustomErlassId(customErlassId);
            if (erlass == null)
            {
                throw new Exception("Could not find an Erlass with customId " + customErlassId);
            }

            return erlass;
        }

        protected ErlassViewModel TryToGetErlassViewModel(IEnumerable<Cell> rowCells)
        {
            var customErlassId = GetCellValue(rowCells.ElementAt((int)XlsxColumns.ErlassId));
            return this._unitOfWork.ErlassRepository.GetErlassViewModelByCustomErlassId(customErlassId);
        }

        protected ErlassfassungViewModel TryToGetErlassfassungWithDecisionDate(DateTime beschluss, int erlassId)
        {
            var erlassfassung = _unitOfWork
                .ErlassfassungRepository
                .Get(ef => ef.ErlassID == erlassId && DateTime.Equals(ef.Beschluss, beschluss))
                .FirstOrDefault();
            if (erlassfassung == null)
            {
                return null;
            }

            return _unitOfWork.ErlassfassungRepository.GetViewModelByID(erlassfassung.ErlassfassungID);
        }

        protected ErlassfassungViewModel GetErlassfassungWithDecisionDate(DateTime beschluss, int erlassId)
        {

            var erlassfassungViewModel = TryToGetErlassfassungWithDecisionDate(beschluss, erlassId);
            if (erlassfassungViewModel == null)
            {
                throw new Exception("Internal error - Could not find Erlassfassung with Beschluss");
            }

            return erlassfassungViewModel;
        }

        protected DateTime GetAndVerifyBeschluss(IEnumerable<Cell> rowCells)
        {
            var beschluss = ConvertToDateTime(GetCellValue(rowCells.ElementAt((int)XlsxColumns.Erlassfassung)));
            if (beschluss == null)
            {
                throw new Exception("Beschluss Date expected!"); // TODO define more meaningful error
            }

            return (DateTime)beschluss;
        }

        /// <summary>
        /// Given a cell name, parses the specified cell to get the column name.
        /// </summary>
        /// <param name="cellReference">Address of the cell (ie. B2)</param>
        /// <returns>Column Name (ie. B)</returns>
        public static string GetColumnName(string cellReference)
        {
            // Match the column name portion of the cell name.
            Regex regex = new Regex("[A-Za-z]+");
            Match match = regex.Match(cellReference);

            return match.Value;
        }

        ///<summary>returns an empty cell when a blank cell is encountered
        ///</summary>
        public IEnumerator<Cell> GetEnumerator(Row row)
        {
            int currentCount = 0;

            // row is a class level variable representing the current
            // DocumentFormat.OpenXml.Spreadsheet.Row
            foreach (DocumentFormat.OpenXml.Spreadsheet.Cell cell in
                     row.Descendants<DocumentFormat.OpenXml.Spreadsheet.Cell>())
            {
                string columnName = GetColumnName(cell.CellReference);

                int currentColumnIndex = ConvertColumnNameToNumber(columnName);

                for (; currentCount < currentColumnIndex; currentCount++)
                {
                    yield return new DocumentFormat.OpenXml.Spreadsheet.Cell();
                }

                yield return cell;
                currentCount++;
            }
            for (; currentCount < COLUMN_COUNT; currentCount++)
            {
                yield return new Cell();
            }
        }

        /// <summary>
        /// Given just the column name (no row index),
        /// it will return the zero based column index.
        /// </summary>
        /// <param name="columnName">Column Name (ie. A or AB)</param>
        /// <returns>Zero based index if the conversion was successful</returns>
        /// <exception cref="ArgumentException">thrown if the given string
        /// contains characters other than uppercase letters</exception>
        public static int ConvertColumnNameToNumber(string columnName)
        {
            Regex alpha = new Regex("^[A-Z]+$");
            if (!alpha.IsMatch(columnName)) throw new ArgumentException();

            char[] colLetters = columnName.ToCharArray();
            Array.Reverse(colLetters);

            int convertedValue = 0;
            for (int i = 0; i < colLetters.Length; i++)
            {
                char letter = colLetters[i];
                int current = i == 0 ? letter - 65 : letter - 64; // ASCII 'A' = 65
                convertedValue += current * (int)Math.Pow(26, i);
            }

            return convertedValue;
        }

        public static IEnumerable<Cell> ConvertToIEnumerable(IEnumerator<Cell> enumerator)
        {
            var ie = new List<Cell>();
            while (enumerator.MoveNext())
            {
                // now empEnumerator.Current is the Employee instance without casting
                Cell c = enumerator.Current;
                ie.Add(c);
            }

            return ie;
        }
    }
}
