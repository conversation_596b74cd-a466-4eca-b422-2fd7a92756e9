using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Web;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.AspNet.Identity;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;

namespace NeoSysLCS.Site.Utilities.Import.Erlass
{
    public class ErlassImporter : BaseImporter
    {
        public ErlassImporter(SpreadsheetDocument spreadsheetDocument,
            string sheetname, UnitOfWork unitOfWork)
            : base(spreadsheetDocument, sheetname, unitOfWork)
        {
           
        }

        protected override void DeleteRow(IEnumerable<Cell> cells)
        {
            //Erlass is not getting deleted
        }

        protected override void ImportRow(IEnumerable<Cell> rowCells)
        {
            var erlass = TryToGetErlassViewModel(rowCells);
            if (erlass == null)
            {
                CreateErlass(rowCells);
                this._unitOfWork.Save();
            }
            else
            {
                // Update SrNummer if it has changed in the Excel file
                UpdateErlassNrIfChanged(rowCells);
            }
            UpdateErlassWithTranslations(rowCells);
            this._unitOfWork.Save();
        }

        private void CreateErlass(IEnumerable<Cell> rowCells)
        {
            var customErlassID = GetCellValue(rowCells.ElementAt((int)XlsxColumns.ErlassId));
            var standortID = ExtractStandortId(customErlassID);

            var erlassVM = new ErlassViewModel();
            erlassVM.HerausgeberID = GetHerausgeber(rowCells, standortID).HerausgeberID;
            erlassVM.CustomErlassId = customErlassID;
            erlassVM.SrNummer = GetCellValue(rowCells.ElementAt((int)XlsxColumns.ErlassNr));
            erlassVM.ErlasstypID = 3;
            this._unitOfWork.ErlassRepository.Insert(erlassVM);
        }

        private void UpdateErlassWithTranslations(IEnumerable<Cell> rowCells)
        {
            var newErlass = GetErlassViewModel(rowCells);

            var erlassVMDe = new ErlassViewModel();
            erlassVMDe.SpracheID = 1;
            erlassVMDe.Titel = GetCellValue(rowCells.ElementAt((int)XlsxColumns.TitelDe));
            erlassVMDe.Abkuerzung = GetCellValue(rowCells.ElementAt((int)XlsxColumns.AbkuerzungDe));
            erlassVMDe.Quelle = GetCellValue(rowCells.ElementAt((int)XlsxColumns.QuelleDe));
            this._unitOfWork.ErlassRepository.InsertUebersetzungErlass(erlassVMDe, newErlass.ErlassID);

            var erlassVMFr = new ErlassViewModel();
            erlassVMFr.SpracheID = 2;
            erlassVMFr.Titel = GetCellValue(rowCells.ElementAt((int)XlsxColumns.TitelFr));
            erlassVMFr.Abkuerzung = GetCellValue(rowCells.ElementAt((int)XlsxColumns.AbkuerzungFr));
            erlassVMFr.Quelle = GetCellValue(rowCells.ElementAt((int)XlsxColumns.QuelleFr));
            this._unitOfWork.ErlassRepository.InsertUebersetzungErlass(erlassVMFr, newErlass.ErlassID);

            var erlassVMIt = new ErlassViewModel();
            erlassVMIt.SpracheID = 3;
            erlassVMIt.Titel = GetCellValue(rowCells.ElementAt((int)XlsxColumns.TitelIt));
            erlassVMIt.Abkuerzung = GetCellValue(rowCells.ElementAt((int)XlsxColumns.AbkuerzungIt));
            erlassVMIt.Quelle = GetCellValue(rowCells.ElementAt((int)XlsxColumns.QuelleIt));
            this._unitOfWork.ErlassRepository.InsertUebersetzungErlass(erlassVMIt, newErlass.ErlassID);

            var erlassVMEn = new ErlassViewModel();
            erlassVMEn.SpracheID = 4;
            erlassVMEn.Titel = GetCellValue(rowCells.ElementAt((int)XlsxColumns.TitelEn));
            erlassVMEn.Abkuerzung = GetCellValue(rowCells.ElementAt((int)XlsxColumns.AbkuerzungEn));
            erlassVMEn.Quelle = GetCellValue(rowCells.ElementAt((int)XlsxColumns.QuelleEn));
            this._unitOfWork.ErlassRepository.InsertUebersetzungErlass(erlassVMEn, newErlass.ErlassID);

        }

        private void UpdateErlassNrIfChanged(IEnumerable<Cell> rowCells)
        {
            var existingErlass = TryToGetErlassViewModel(rowCells);
            if (existingErlass == null)
            {
                return; // No existing erlass to update
            }

            var newSrNummer = GetCellValue(rowCells.ElementAt((int)XlsxColumns.ErlassNr));
            
            // Compare the existing SrNummer with the new one from Excel
            if (!string.Equals(existingErlass.SrNummer, newSrNummer, StringComparison.OrdinalIgnoreCase))
            {
                // Update the SrNummer in the existing erlass
                existingErlass.SrNummer = newSrNummer;
                this._unitOfWork.ErlassRepository.UpdateSrNummerOnly(existingErlass);
            }
        } 

    }

}
