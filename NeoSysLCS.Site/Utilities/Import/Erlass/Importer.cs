using System;
using System.IO;
using DevExpress.Web;
using DevExpress.Web.ASPxHtmlEditor;
using DocumentFormat.OpenXml.Packaging;
using NeoSysLCS.Repositories;
using System.Globalization;
using NeoSysLCS.DomainModel.Models;
using System.Diagnostics;
using DocumentFormat.OpenXml;

namespace NeoSysLCS.Site.Utilities.Import.Erlass
{
    public class Importer
    {
        public static UploadControlValidationSettings ValidationSettings =  new UploadControlValidationSettings {
            AllowedFileExtensions = new[] {".xlsx"},
            NotAllowedFileExtensionErrorText = "Falsches Format (*.xlsx)"
        };



        /// <summary>
        /// Imports the Erlass excel to the specified Standort
        /// </summary>
        /// <param name="stream">The stream.</param>
        public void ImportFromExcelFile(Stream stream, int standortId)
        {
            // Create a memory stream copy that can be reset reliably
            using (var memoryStream = new MemoryStream())
            {
                try
                {
                    // Copy the original stream to memory stream
                    stream.CopyTo(memoryStream);
                    memoryStream.Position = 0;
                    
                    // Skip validation and try to process the file directly
                    var unitOfWork = new UnitOfWork();
                    string sheetNameForderungen = "Tabelle1";
                    bool importSuccessful = false;

                    try
                    {
                        memoryStream.Position = 0;
                        using (var spreadsheetDocument = SpreadsheetDocument.Open(memoryStream, false, new OpenSettings { MarkupCompatibilityProcessSettings = new MarkupCompatibilityProcessSettings(MarkupCompatibilityProcessMode.ProcessAllParts, FileFormatVersions.Office2007) }))
                        {
                            BaseImporter herausgeberImporter = new HerausgeberImporter(spreadsheetDocument, sheetNameForderungen, unitOfWork);
                            herausgeberImporter.Import();
                            ExceptionUtility.LogInfo("Herausgeber successfully imported");
                            importSuccessful = true;
                        }
                    }
                    catch (Exception e)
                    {
                        ExceptionUtility.LogException(e, "Importer - Herausgeber Import");
                        if (!importSuccessful)
                            throw;
                    }

                    try
                    {
                        memoryStream.Position = 0;
                        using (var spreadsheetDocument = SpreadsheetDocument.Open(memoryStream, false, new OpenSettings { MarkupCompatibilityProcessSettings = new MarkupCompatibilityProcessSettings(MarkupCompatibilityProcessMode.ProcessAllParts, FileFormatVersions.Office2007) }))
                        {
                            BaseImporter erlassImporter = new ErlassImporter(spreadsheetDocument, sheetNameForderungen, unitOfWork);
                            erlassImporter.Import();
                            ExceptionUtility.LogInfo("Erlasse successfully imported");
                        }
                    }
                    catch (Exception e)
                    {
                        ExceptionUtility.LogException(e, "Importer - Erlass Import");
                        // Continue with other imports even if this one fails
                    }

                    try
                    {
                        memoryStream.Position = 0;
                        using (var spreadsheetDocument = SpreadsheetDocument.Open(memoryStream, false, new OpenSettings { MarkupCompatibilityProcessSettings = new MarkupCompatibilityProcessSettings(MarkupCompatibilityProcessMode.ProcessAllParts, FileFormatVersions.Office2007) }))
                        {
                            BaseImporter artikelImporter = new ArtikelImporter(spreadsheetDocument, sheetNameForderungen, unitOfWork);
                            artikelImporter.Import();
                            ExceptionUtility.LogInfo("Artikel successfully imported");
                        }
                    }
                    catch (Exception e)
                    {
                        ExceptionUtility.LogException(e, "Importer - Artikel Import");
                        // Continue with other imports even if this one fails
                    }

                    try
                    {
                        memoryStream.Position = 0;
                        using (var spreadsheetDocument = SpreadsheetDocument.Open(memoryStream, false, new OpenSettings { MarkupCompatibilityProcessSettings = new MarkupCompatibilityProcessSettings(MarkupCompatibilityProcessMode.ProcessAllParts, FileFormatVersions.Office2007) }))
                        {
                            BaseImporter erlassfassungImporter = new ErlassFassungImporter(spreadsheetDocument, sheetNameForderungen, unitOfWork);
                            erlassfassungImporter.Import();
                            ExceptionUtility.LogInfo("Erlassfassung successfully imported");
                        }
                    }
                    catch (Exception e)
                    {
                        ExceptionUtility.LogException(e, "Importer - Erlassfassung Import");
                        // Continue with other imports even if this one fails
                    }

                    try
                    {
                        memoryStream.Position = 0;
                        using (var spreadsheetDocument = SpreadsheetDocument.Open(memoryStream, false, new OpenSettings { MarkupCompatibilityProcessSettings = new MarkupCompatibilityProcessSettings(MarkupCompatibilityProcessMode.ProcessAllParts, FileFormatVersions.Office2007) }))
                        {
                            BaseImporter forderungsversionImporter = new ForderungsversionImporter(spreadsheetDocument, sheetNameForderungen, unitOfWork);
                            forderungsversionImporter.Import();
                            ExceptionUtility.LogInfo("Forderungsversion successfully imported");
                        }
                    }
                    catch (Exception e)
                    {
                        ExceptionUtility.LogException(e, "Importer - Forderungsversion Import");
                        // Continue with other imports even if this one fails
                    }

                    try
                    {
                        memoryStream.Position = 0;
                        using (var spreadsheetDocument = SpreadsheetDocument.Open(memoryStream, false, new OpenSettings { MarkupCompatibilityProcessSettings = new MarkupCompatibilityProcessSettings(MarkupCompatibilityProcessMode.ProcessAllParts, FileFormatVersions.Office2007) }))
                        {
                            BaseImporter objekteAssigner = new MiscellaneousAssigner(spreadsheetDocument, sheetNameForderungen, unitOfWork);
                            objekteAssigner.Import();
                            ExceptionUtility.LogInfo("Objekte successfully imported");
                        }
                    }
                    catch (Exception e)
                    {
                        ExceptionUtility.LogException(e, "Importer - Objekte Import");
                        // Continue with other imports even if this one fails
                    }

                    // Only save if at least one import was successful
                    if (importSuccessful)
                    {
                        try
                        {
                            unitOfWork.StandortRepository.UpdateErlassImportDoc(standortId);
                            unitOfWork.Save();
                            ExceptionUtility.LogInfo("Import completed and changes saved to database");
                        }
                        catch (Exception ex)
                        {
                            ExceptionUtility.LogException(ex, "Error saving changes to database");
                            throw;
                        }
                    }
                    else
                    {
                        ExceptionUtility.LogError("No imports were successful. Changes not saved to database.");
                    }
                }
                catch (Exception ex)
                {
                    ExceptionUtility.LogException(ex, "Importer - General Error");
                    throw;
                }
            }
        }
     }
}