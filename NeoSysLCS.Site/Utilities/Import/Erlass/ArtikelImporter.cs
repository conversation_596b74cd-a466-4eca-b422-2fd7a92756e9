using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Web;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.AspNet.Identity;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;

namespace NeoSysLCS.Site.Utilities.Import.Erlass
{
    public class ArtikelImporter : BaseImporter
    {
        public ArtikelImporter(SpreadsheetDocument spreadsheetDocument,
            string sheetname, UnitOfWork unitOfWork)
            : base(spreadsheetDocument, sheetname, unitOfWork)
        {
           
        }

        protected override void ImportRow(IEnumerable<Cell> rowCells)
        {
            var artikelNr = GetCellValue(rowCells.ElementAt((int)XlsxColumns.Art));
            
            // Skip rows with empty artikelNr
            if (string.IsNullOrWhiteSpace(artikelNr))
            {
                ExceptionUtility.LogInfo("Skipping row with empty Artikel number");
                return;
            }
            
            // Use TryToGetErlassViewModel instead of GetErlassViewModel to handle missing Erlass gracefully
            var erlass = this.TryToGetErlassViewModel(rowCells);
            if (erlass == null)
            {
                var customErlassId = GetCellValue(rowCells.ElementAt((int)XlsxColumns.ErlassId));
                ExceptionUtility.LogInfo($"Skipping Artikel import: No Erlass found with customId {customErlassId}");
                return;
            }
            
            var artikel = FindArtikel(erlass.ErlassID, artikelNr);
            if (artikel == null)
            {
                CreateArtikel(artikelNr, erlass.ErlassID);
                this._unitOfWork.Save();
            }
            UpdateArtikelWithTranslations(erlass.ErlassID, artikelNr, rowCells);
            this._unitOfWork.Save();
        }

        private Artikel FindArtikel(int erlassId, string artikelNr)
        {
            if (string.IsNullOrWhiteSpace(artikelNr))
            {
                return null;
            }
            
            var artikelList = this._unitOfWork.ArtikelRepository.GetByErlass(erlassId);
            if (artikelList == null || !artikelList.Any())
            {
                return null;
            }
            
            string lowerArtikelNr = artikelNr.ToLower();
            return artikelList.FirstOrDefault(art => 
                art != null && 
                art.Nummer != null && 
                art.Nummer.ToLower().Equals(lowerArtikelNr));
        }

        private void CreateArtikel(string artikelNr, int erlassId)
        {
            var artikelVM = new ArtikelViewModel();
            artikelVM.ErlassID = erlassId;
            artikelVM.Nummer = artikelNr;
            this._unitOfWork.ArtikelRepository.Insert(artikelVM);
        }

        private void UpdateArtikelWithTranslations(int erlassId, string artikelNr, IEnumerable<Cell> rowCells)
        {
            var newArtikel = FindArtikel(erlassId, artikelNr);
            if (newArtikel == null)
            {
                throw new Exception("Internal Error"); // TODO define more meaningful error
            }

            var artikelVMDe = new ArtikelViewModel();
            artikelVMDe.ArtikelID = newArtikel.ArtikelID;
            artikelVMDe.SpracheID = 1;
            var quelleDe = GetCellValue(rowCells.ElementAt((int)XlsxColumns.LinkArtDe));
            if (string.IsNullOrEmpty(quelleDe))
            {
                quelleDe = GetCellValue(rowCells.ElementAt((int)XlsxColumns.QuelleDe));
            }
            artikelVMDe.Quelle = quelleDe;
            this._unitOfWork.ArtikelRepository.UpdateArtikelUebersetzung(artikelVMDe);

            var artikelVMFr = new ArtikelViewModel();
            artikelVMFr.ArtikelID = newArtikel.ArtikelID;
            artikelVMFr.SpracheID = 2;
            var quelleFr = GetCellValue(rowCells.ElementAt((int)XlsxColumns.LinkArtFr));
            if (string.IsNullOrEmpty(quelleFr))
            {
                quelleFr = GetCellValue(rowCells.ElementAt((int)XlsxColumns.QuelleFr));
            }
            artikelVMFr.Quelle = quelleFr;
            this._unitOfWork.ArtikelRepository.UpdateArtikelUebersetzung(artikelVMFr);

            var artikelVMIt = new ArtikelViewModel();
            artikelVMIt.ArtikelID = newArtikel.ArtikelID;
            artikelVMIt.SpracheID = 3;
            var quelleIt = GetCellValue(rowCells.ElementAt((int)XlsxColumns.LinkArtIt));
            if (string.IsNullOrEmpty(quelleIt))
            {
                quelleIt = GetCellValue(rowCells.ElementAt((int)XlsxColumns.QuelleIt));
            }
            artikelVMIt.Quelle = quelleIt;
            this._unitOfWork.ArtikelRepository.UpdateArtikelUebersetzung(artikelVMIt);

            var artikelVMEn = new ArtikelViewModel();
            artikelVMEn.ArtikelID = newArtikel.ArtikelID;
            artikelVMEn.SpracheID = 4;
            var quelleEn = GetCellValue(rowCells.ElementAt((int)XlsxColumns.LinkArtEn));
            if (string.IsNullOrEmpty(quelleEn))
            {
                quelleEn = GetCellValue(rowCells.ElementAt((int)XlsxColumns.QuelleEn));
            }
            artikelVMEn.Quelle = quelleEn;
            this._unitOfWork.ArtikelRepository.UpdateArtikelUebersetzung(artikelVMEn);

        }

        protected override void DeleteRow(IEnumerable<Cell> cells)
        {
           //Artikel is not getting deleted
        }
    }
   

}
