using System;
using System.Linq;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;

namespace NeoSysLCS.Site.Utilities.Import
{
    public class KundendokumentPflichtImporter : BaseImporter
    {
        public KundendokumentPflichtImporter(SpreadsheetDocument spreadsheetDocument,
            string sheetname, int kundendokumentId, UnitOfWork unitOfWork)
            : base(spreadsheetDocument, sheetname, kundendokumentId, unitOfWork)
        {
           
        }

        /// <summary>
        /// NOTE: if you add or remove columns in the export, please add or remove them also here
        /// </summary>
        enum Columns
        {
            Id = 0,
            StandortObjekt = 1,
            Beschreibung = 2,
            Status = 3,
            Kundenbezug = 4,
            Erfuellung = 5,
            Erfuellungszeitpunkt = 6,
            ErfuelltDurch = 7,
            Verantwortlich = 8,
            Ablageort = 9,
            Kommentar = 10,
        }

        protected override void CheckColumns(Row row)
        {
            var rowCells = row.Elements<Cell>();
            if (rowCells.Count() < 11)
            {
                throw new Exception(Resources.Properties.Resources.Fehler_Import_NumberOfColumns);
            }
        }

        protected override void ImportRow(Row row)
        {
            int entityId;
            KundendokumentPflicht entity = null;
            var rowCells = row.Elements<Cell>();
            var id = GetCellValue(rowCells.ElementAt((int)Columns.Id));

            //check for empty rows
            if (!string.IsNullOrEmpty(id))
            {
                if (int.TryParse(id, out entityId))
                {
                    entity = _unitOfWork.KundendokumentPflichtRepository.GetByID(entityId, "Kundendokument");
                }

                if (entity == null || entity.KundendokumentID != _kundendokument.KundendokumentID)
                {
                    throw new Exception(
                        Resources.Properties.Resources.Fehler_Import_PflichtNotInKundendokument.Replace("##ID##", id));
                }

                if (entity == null || entity.KundeID != _kundendokument.KundeID)
                {
                    throw new Exception(Resources.Properties.Resources.Fehler_Import_PlichtNotFromKunde.Replace(
                        "##ID##", id));
                }

                try
                {
                    //Kundenbezug
                    entity.Kundenbezug = GetCellValue(rowCells.ElementAt((int) Columns.Kundenbezug));
                    //Erfüllung
                    entity.Erfuellung =
                        ConvertToKundendokumentErfuellung(GetCellValue(rowCells.ElementAt((int) Columns.Erfuellung)));

                    //Erfüllungszeitpunkt
                    entity.Erfuellungszeitpunkt =
                        ConvertToDateTime(GetCellValue(rowCells.ElementAt((int) Columns.Erfuellungszeitpunkt)));

                    //other fields
                    entity.ErfuelltDurch = GetCellValue(rowCells.ElementAt((int) Columns.ErfuelltDurch));
                    entity.Verantwortlich = GetCellValue(rowCells.ElementAt((int) Columns.Verantwortlich));
                    entity.Ablageort = GetCellValue(rowCells.ElementAt((int) Columns.Ablageort));
                    entity.Kommentar = GetCellValue(rowCells.ElementAt((int) Columns.Kommentar));

                    //update and save
                    _unitOfWork.KundendokumentPflichtRepository.Update(entity);
                }
                catch (Exception e)
                {
                    throw new Exception(
                        Resources.Properties.Resources.Fehler_Import_PflichtGeneralError.Replace("##ID##", id)
                            .Replace("##error##", e.Message));
                }
            }
        }
    }
}