<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="EntityFramework" version="6.1.3" targetFramework="net47" />
  <package id="Microsoft.AspNet.Identity.Core" version="2.2.1" targetFramework="net451" />
  <package id="Microsoft.AspNet.Identity.EntityFramework" version="2.2.1" targetFramework="net451" />
  <package id="Microsoft.AspNet.Identity.Owin" version="2.2.1" targetFramework="net451" />
  <package id="Microsoft.AspNet.Mvc" version="5.2.3" targetFramework="net451" />
  <package id="Microsoft.AspNet.Razor" version="3.2.3" targetFramework="net451" />
  <package id="Microsoft.AspNet.WebPages" version="3.2.3" targetFramework="net451" />
  <package id="Microsoft.Owin" version="3.1.0" targetFramework="net462" />
  <package id="Microsoft.Owin.Security" version="3.1.0" targetFramework="net462" />
  <package id="Microsoft.Owin.Security.Cookies" version="3.1.0" targetFramework="net462" />
  <package id="Microsoft.Owin.Security.OAuth" version="3.1.0" targetFramework="net462" />
  <package id="Microsoft.Web.Infrastructure" version="1.0.0.0" targetFramework="net45" />
  <package id="Newtonsoft.Json" version="10.0.3" targetFramework="net47" />
  <package id="Owin" version="1.0" targetFramework="net45" />
</packages>