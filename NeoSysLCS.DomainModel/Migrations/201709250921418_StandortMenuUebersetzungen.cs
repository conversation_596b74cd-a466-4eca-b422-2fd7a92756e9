namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class StandortMenuUebersetzungen : DbMigration
    {
        public override void Up()
        {
            CreateTable(
                "dbo.StandortMenuUebersetzung",
                c => new
                    {
                        StandortMenuUebersetzungID = c.Int(nullable: false, identity: true),
                        KundeID = c.Int(nullable: false),
                        SpracheID = c.Int(nullable: false),
                        Titel = c.String(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.StandortMenuUebersetzungID)
                .ForeignKey("dbo.AspNetUsers", t => t.BearbeitetVonID)
                .ForeignKey("dbo.AspNetUsers", t => t.ErstelltVonID)
                .ForeignKey("dbo.Kunde", t => t.KundeID)
                .ForeignKey("dbo.Sprache", t => t.SpracheID)
                .Index(t => t.KundeID)
                .Index(t => t.SpracheID)
                .Index(t => t.ErstelltVonID)
                .Index(t => t.BearbeitetVonID);
            
            DropColumn("dbo.Kunde", "StandortMenuName");
        }
        
        public override void Down()
        {
            AddColumn("dbo.Kunde", "StandortMenuName", c => c.String());
            DropForeignKey("dbo.StandortMenuUebersetzung", "SpracheID", "dbo.Sprache");
            DropForeignKey("dbo.StandortMenuUebersetzung", "KundeID", "dbo.Kunde");
            DropForeignKey("dbo.StandortMenuUebersetzung", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.StandortMenuUebersetzung", "BearbeitetVonID", "dbo.AspNetUsers");
            DropIndex("dbo.StandortMenuUebersetzung", new[] { "BearbeitetVonID" });
            DropIndex("dbo.StandortMenuUebersetzung", new[] { "ErstelltVonID" });
            DropIndex("dbo.StandortMenuUebersetzung", new[] { "SpracheID" });
            DropIndex("dbo.StandortMenuUebersetzung", new[] { "KundeID" });
            DropTable("dbo.StandortMenuUebersetzung");
        }
    }
}
