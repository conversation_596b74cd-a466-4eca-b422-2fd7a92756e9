namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class ErweiterungErlassFeatures17 : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.Erlass", "LUeberpruefung", c => c.DateTime());
            AddColumn("dbo.Erlass", "ProjektleiterID", c => c.String(maxLength: 128));
            CreateIndex("dbo.Erlass", "ProjektleiterID");
            AddForeignKey("dbo.Erlass", "ProjektleiterID", "dbo.AspNetUsers", "Id");
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.Erlass", "ProjektleiterID", "dbo.AspNetUsers");
            DropIndex("dbo.Erlass", new[] { "ProjektleiterID" });
            DropColumn("dbo.Erlass", "ProjektleiterID");
            DropColumn("dbo.Erlass", "LUeberpruefung");
        }
    }
}
