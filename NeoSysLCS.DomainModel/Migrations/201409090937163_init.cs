namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class init : DbMigration
    {
        public override void Up()
        {
            CreateTable(
                "dbo.AspNetRoles",
                c => new
                    {
                        Id = c.String(nullable: false, maxLength: 128),
                        Name = c.String(nullable: false, maxLength: 256),
                        Displayname = c.String(),
                        Discriminator = c.String(nullable: false, maxLength: 128),
                    })
                .PrimaryKey(t => t.Id)
                .Index(t => t.Name, unique: true, name: "RoleNameIndex");
            
            CreateTable(
                "dbo.AspNetUserRoles",
                c => new
                    {
                        UserId = c.String(nullable: false, maxLength: 128),
                        RoleId = c.String(nullable: false, maxLength: 128),
                    })
                .PrimaryKey(t => new { t.UserId, t.RoleId })
                .ForeignKey("dbo.AspNetRoles", t => t.RoleId, cascadeDelete: true)
                .ForeignKey("dbo.AspNetUsers", t => t.UserId, cascadeDelete: true)
                .Index(t => t.UserId)
                .Index(t => t.RoleId);
            
            CreateTable(
                "dbo.AspNetUsers",
                c => new
                    {
                        Id = c.String(nullable: false, maxLength: 128),
                        Email = c.String(maxLength: 256),
                        EmailConfirmed = c.Boolean(nullable: false),
                        PasswordHash = c.String(),
                        SecurityStamp = c.String(),
                        PhoneNumber = c.String(),
                        PhoneNumberConfirmed = c.Boolean(nullable: false),
                        TwoFactorEnabled = c.Boolean(nullable: false),
                        LockoutEndDateUtc = c.DateTime(),
                        LockoutEnabled = c.Boolean(nullable: false),
                        AccessFailedCount = c.Int(nullable: false),
                        UserName = c.String(nullable: false, maxLength: 256),
                        Vorname = c.String(),
                        Nachname = c.String(),
                        SpracheID = c.Int(),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                        Discriminator = c.String(nullable: false, maxLength: 128),
                        Kunde_KundeID = c.Int(),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.AspNetUsers", t => t.BearbeitetVonID)
                .ForeignKey("dbo.AspNetUsers", t => t.ErstelltVonID)
                .ForeignKey("dbo.Kunde", t => t.Kunde_KundeID)
                .ForeignKey("dbo.Sprache", t => t.SpracheID)
                .Index(t => t.UserName, unique: true, name: "UserNameIndex")
                .Index(t => t.SpracheID)
                .Index(t => t.ErstelltVonID)
                .Index(t => t.BearbeitetVonID)
                .Index(t => t.Kunde_KundeID);
            
            CreateTable(
                "dbo.AspNetUserClaims",
                c => new
                    {
                        Id = c.Int(nullable: false, identity: true),
                        UserId = c.String(nullable: false, maxLength: 128),
                        ClaimType = c.String(),
                        ClaimValue = c.String(),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.AspNetUsers", t => t.UserId, cascadeDelete: true)
                .Index(t => t.UserId);
            
            CreateTable(
                "dbo.Kunde",
                c => new
                    {
                        KundeID = c.Int(nullable: false, identity: true),
                        Name = c.String(),
                        ProjektleiterID = c.String(maxLength: 128),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.KundeID)
                .ForeignKey("dbo.AspNetUsers", t => t.BearbeitetVonID)
                .ForeignKey("dbo.AspNetUsers", t => t.ErstelltVonID)
                .ForeignKey("dbo.AspNetUsers", t => t.ProjektleiterID)
                .Index(t => t.ProjektleiterID)
                .Index(t => t.ErstelltVonID)
                .Index(t => t.BearbeitetVonID);
            
            CreateTable(
                "dbo.Kontakt",
                c => new
                    {
                        KontaktID = c.Int(nullable: false, identity: true),
                        KundeID = c.Int(nullable: false),
                        Nachname = c.String(),
                        Vorname = c.String(),
                        EMail = c.String(),
                        Funktion = c.String(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.KontaktID)
                .ForeignKey("dbo.AspNetUsers", t => t.BearbeitetVonID)
                .ForeignKey("dbo.AspNetUsers", t => t.ErstelltVonID)
                .ForeignKey("dbo.Kunde", t => t.KundeID, cascadeDelete: true)
                .Index(t => t.KundeID)
                .Index(t => t.ErstelltVonID)
                .Index(t => t.BearbeitetVonID);
            
            CreateTable(
                "dbo.Rechtsbereich",
                c => new
                    {
                        RechtsbereichID = c.Int(nullable: false, identity: true),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.RechtsbereichID)
                .ForeignKey("dbo.AspNetUsers", t => t.BearbeitetVonID)
                .ForeignKey("dbo.AspNetUsers", t => t.ErstelltVonID)
                .Index(t => t.ErstelltVonID)
                .Index(t => t.BearbeitetVonID);
            
            CreateTable(
                "dbo.Forderungsversion",
                c => new
                    {
                        ForderungsversionID = c.Int(nullable: false, identity: true),
                        VorversionID = c.Int(),
                        NachfolgeversionID = c.Int(),
                        Inkrafttretung = c.DateTime(nullable: false),
                        Aufhebung = c.DateTime(),
                        Bewilligungspflicht = c.Boolean(nullable: false),
                        Nachweispflicht = c.Boolean(nullable: false),
                        InternerKommentar = c.String(),
                        ForderungID = c.Int(nullable: false),
                        ErlassfassungID = c.Int(nullable: false),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.ForderungsversionID)
                .ForeignKey("dbo.AspNetUsers", t => t.BearbeitetVonID)
                .ForeignKey("dbo.Forderung", t => t.ForderungID, cascadeDelete: true)
                .ForeignKey("dbo.Erlassfassung", t => t.ErlassfassungID, cascadeDelete: true)
                .ForeignKey("dbo.AspNetUsers", t => t.ErstelltVonID)
                .ForeignKey("dbo.Forderungsversion", t => t.NachfolgeversionID)
                .ForeignKey("dbo.Forderungsversion", t => t.VorversionID)
                .Index(t => t.VorversionID)
                .Index(t => t.NachfolgeversionID)
                .Index(t => t.ForderungID)
                .Index(t => t.ErlassfassungID)
                .Index(t => t.ErstelltVonID)
                .Index(t => t.BearbeitetVonID);
            
            CreateTable(
                "dbo.Erlassfassung",
                c => new
                    {
                        ErlassfassungID = c.Int(nullable: false, identity: true),
                        ErlassID = c.Int(nullable: false),
                        Beschluss = c.DateTime(nullable: false),
                        Inkrafttretung = c.DateTime(nullable: false),
                        Freigabe = c.Boolean(),
                        FreigabeVon = c.Int(),
                        FreigabeAm = c.DateTime(),
                        QsFreigabe = c.Boolean(),
                        QsFreigabeVon = c.Int(),
                        QsFreigabeAm = c.DateTime(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.ErlassfassungID)
                .ForeignKey("dbo.AspNetUsers", t => t.BearbeitetVonID)
                .ForeignKey("dbo.Erlass", t => t.ErlassID, cascadeDelete: true)
                .ForeignKey("dbo.AspNetUsers", t => t.ErstelltVonID)
                .Index(t => t.ErlassID)
                .Index(t => t.ErstelltVonID)
                .Index(t => t.BearbeitetVonID);
            
            CreateTable(
                "dbo.Erlass",
                c => new
                    {
                        ErlassID = c.Int(nullable: false, identity: true),
                        SrNummer = c.String(),
                        ErlasstypID = c.Int(),
                        HerausgeberId = c.Int(nullable: false),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.ErlassID)
                .ForeignKey("dbo.Herausgeber", t => t.HerausgeberId, cascadeDelete: true)
                .ForeignKey("dbo.AspNetUsers", t => t.BearbeitetVonID)
                .ForeignKey("dbo.Erlasstyp", t => t.ErlasstypID)
                .ForeignKey("dbo.AspNetUsers", t => t.ErstelltVonID)
                .Index(t => t.ErlasstypID)
                .Index(t => t.HerausgeberId)
                .Index(t => t.ErstelltVonID)
                .Index(t => t.BearbeitetVonID);
            
            CreateTable(
                "dbo.Artikel",
                c => new
                    {
                        ArtikelID = c.Int(nullable: false, identity: true),
                        Nummer = c.String(),
                        ErlassID = c.Int(nullable: false),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.ArtikelID)
                .ForeignKey("dbo.AspNetUsers", t => t.BearbeitetVonID)
                .ForeignKey("dbo.Erlass", t => t.ErlassID, cascadeDelete: true)
                .ForeignKey("dbo.AspNetUsers", t => t.ErstelltVonID)
                .Index(t => t.ErlassID)
                .Index(t => t.ErstelltVonID)
                .Index(t => t.BearbeitetVonID);
            
            CreateTable(
                "dbo.ArtikelUebersetzung",
                c => new
                    {
                        ArtikelUebersetzungID = c.Int(nullable: false, identity: true),
                        ArtikelID = c.Int(nullable: false),
                        SpracheID = c.Int(nullable: false),
                        Quelle = c.String(),
                    })
                .PrimaryKey(t => t.ArtikelUebersetzungID)
                .ForeignKey("dbo.Artikel", t => t.ArtikelID, cascadeDelete: true)
                .ForeignKey("dbo.Sprache", t => t.SpracheID, cascadeDelete: true)
                .Index(t => t.ArtikelID)
                .Index(t => t.SpracheID);
            
            CreateTable(
                "dbo.Sprache",
                c => new
                    {
                        SpracheID = c.Int(nullable: false, identity: true),
                        Name = c.String(),
                        Lokalisierung = c.String(),
                        Reihenfolge = c.Short(nullable: false),
                    })
                .PrimaryKey(t => t.SpracheID);
            
            CreateTable(
                "dbo.Standort",
                c => new
                    {
                        StandortID = c.Int(nullable: false, identity: true),
                        KundeID = c.Int(nullable: false),
                        Name = c.String(),
                        InterneNotiz = c.String(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.StandortID)
                .ForeignKey("dbo.AspNetUsers", t => t.BearbeitetVonID)
                .ForeignKey("dbo.AspNetUsers", t => t.ErstelltVonID)
                .ForeignKey("dbo.Kunde", t => t.KundeID, cascadeDelete: true)
                .Index(t => t.KundeID)
                .Index(t => t.ErstelltVonID)
                .Index(t => t.BearbeitetVonID);
            
            CreateTable(
                "dbo.Herausgeber",
                c => new
                    {
                        HerausgeberID = c.Int(nullable: false, identity: true),
                        Name = c.String(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.HerausgeberID)
                .ForeignKey("dbo.AspNetUsers", t => t.BearbeitetVonID)
                .ForeignKey("dbo.AspNetUsers", t => t.ErstelltVonID)
                .Index(t => t.ErstelltVonID)
                .Index(t => t.BearbeitetVonID);
            
            CreateTable(
                "dbo.IndividuelleForderung",
                c => new
                    {
                        IndividuelleForderungID = c.Int(nullable: false, identity: true),
                        Beschreibung = c.String(),
                        Kommentar = c.String(),
                        GueltigVon = c.DateTime(nullable: false),
                        GueltigBis = c.DateTime(),
                        StandortID = c.Int(),
                        ThemenbereichID = c.Int(),
                        StandortObjektID = c.Int(),
                        KundenbezugID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.IndividuelleForderungID)
                .ForeignKey("dbo.AspNetUsers", t => t.BearbeitetVonID)
                .ForeignKey("dbo.AspNetUsers", t => t.ErstelltVonID)
                .ForeignKey("dbo.Kundenbezug", t => t.KundenbezugID)
                .ForeignKey("dbo.StandortObjekt", t => t.StandortObjektID)
                .ForeignKey("dbo.Standort", t => t.StandortID)
                .ForeignKey("dbo.Themenbereich", t => t.ThemenbereichID)
                .Index(t => t.StandortID)
                .Index(t => t.ThemenbereichID)
                .Index(t => t.StandortObjektID)
                .Index(t => t.KundenbezugID)
                .Index(t => t.ErstelltVonID)
                .Index(t => t.BearbeitetVonID);
            
            CreateTable(
                "dbo.Kundenbezug",
                c => new
                    {
                        KundenbezugID = c.Int(nullable: false, identity: true),
                        Name = c.String(),
                        StandortID = c.Int(),
                    })
                .PrimaryKey(t => t.KundenbezugID)
                .ForeignKey("dbo.Standort", t => t.StandortID)
                .Index(t => t.StandortID);
            
            CreateTable(
                "dbo.KundendokumentForderungsversion",
                c => new
                    {
                        KundendokumentForderungsversionID = c.Int(nullable: false, identity: true),
                        Kommentar = c.String(),
                        Erfuellung = c.Boolean(nullable: false),
                        Erfuellungszeitpunkt = c.DateTime(),
                        ErfuelltDurch = c.String(),
                        Verantwortlich = c.String(),
                        NichtRelevant = c.Boolean(nullable: false),
                        Ablageort = c.String(),
                        QSFreigabe = c.Boolean(nullable: false),
                        Status = c.Int(),
                        KundendokumentID = c.Int(),
                        ForderungsversionID = c.Int(),
                        StandortObjektID = c.Int(),
                        KundenbezugID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.KundendokumentForderungsversionID)
                .ForeignKey("dbo.AspNetUsers", t => t.BearbeitetVonID)
                .ForeignKey("dbo.AspNetUsers", t => t.ErstelltVonID)
                .ForeignKey("dbo.Forderungsversion", t => t.ForderungsversionID)
                .ForeignKey("dbo.Kundenbezug", t => t.KundenbezugID)
                .ForeignKey("dbo.Kundendokument", t => t.KundendokumentID)
                .ForeignKey("dbo.StandortObjekt", t => t.StandortObjektID)
                .Index(t => t.KundendokumentID)
                .Index(t => t.ForderungsversionID)
                .Index(t => t.StandortObjektID)
                .Index(t => t.KundenbezugID)
                .Index(t => t.ErstelltVonID)
                .Index(t => t.BearbeitetVonID);
            
            CreateTable(
                "dbo.Kundendokument",
                c => new
                    {
                        KundendokumentID = c.Int(nullable: false, identity: true),
                        PublizierenAm = c.DateTime(nullable: false),
                        QSFreigabe = c.Boolean(nullable: false),
                        QSKommentar = c.String(),
                        Freigeben = c.Boolean(nullable: false),
                        StandortID = c.Int(nullable: false),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.KundendokumentID)
                .ForeignKey("dbo.AspNetUsers", t => t.BearbeitetVonID)
                .ForeignKey("dbo.AspNetUsers", t => t.ErstelltVonID)
                .ForeignKey("dbo.Standort", t => t.StandortID, cascadeDelete: true)
                .Index(t => t.StandortID)
                .Index(t => t.ErstelltVonID)
                .Index(t => t.BearbeitetVonID);
            
            CreateTable(
                "dbo.KundendokumentIndividuelleForderung",
                c => new
                    {
                        KundendokumentIndividuelleForderungID = c.Int(nullable: false, identity: true),
                        Erfuellung = c.Boolean(nullable: false),
                        Erfuellungszeitpunkt = c.DateTime(),
                        ErfuelltDurch = c.String(),
                        Verantwortlich = c.String(),
                        Ablageort = c.String(),
                        QsFreigabe = c.Boolean(nullable: false),
                        Status = c.Int(),
                        IndividuelleForderungID = c.Int(nullable: false),
                        KundendokumentID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.KundendokumentIndividuelleForderungID)
                .ForeignKey("dbo.IndividuelleForderung", t => t.IndividuelleForderungID, cascadeDelete: true)
                .ForeignKey("dbo.Kundendokument", t => t.KundendokumentID, cascadeDelete: true)
                .Index(t => t.IndividuelleForderungID)
                .Index(t => t.KundendokumentID);
            
            CreateTable(
                "dbo.KundendokumentPflicht",
                c => new
                    {
                        KundendokumentPflichtID = c.Int(nullable: false, identity: true),
                        Kommentar = c.String(),
                        Erfuellung = c.Boolean(nullable: false),
                        Erfuellungszeitpunkt = c.DateTime(),
                        ErfuelltDurch = c.String(),
                        Verantwortlich = c.String(),
                        NichtRelevant = c.Boolean(nullable: false),
                        Ablageort = c.String(),
                        QSFreigabe = c.Boolean(nullable: false),
                        Status = c.Int(),
                        KundendokumentID = c.Int(),
                        PflichtID = c.Int(),
                        StandortObjektID = c.Int(),
                        KundenbezugID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.KundendokumentPflichtID)
                .ForeignKey("dbo.AspNetUsers", t => t.BearbeitetVonID)
                .ForeignKey("dbo.AspNetUsers", t => t.ErstelltVonID)
                .ForeignKey("dbo.Kundenbezug", t => t.KundenbezugID)
                .ForeignKey("dbo.Kundendokument", t => t.KundendokumentID)
                .ForeignKey("dbo.Pflicht", t => t.PflichtID)
                .ForeignKey("dbo.StandortObjekt", t => t.StandortObjektID)
                .Index(t => t.KundendokumentID)
                .Index(t => t.PflichtID)
                .Index(t => t.StandortObjektID)
                .Index(t => t.KundenbezugID)
                .Index(t => t.ErstelltVonID)
                .Index(t => t.BearbeitetVonID);
            
            CreateTable(
                "dbo.Pflicht",
                c => new
                    {
                        PflichtID = c.Int(nullable: false, identity: true),
                        GueltigVon = c.DateTime(nullable: false),
                        GueltigBis = c.DateTime(),
                        ErlassfassungID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.PflichtID)
                .ForeignKey("dbo.AspNetUsers", t => t.BearbeitetVonID)
                .ForeignKey("dbo.Erlassfassung", t => t.ErlassfassungID)
                .ForeignKey("dbo.AspNetUsers", t => t.ErstelltVonID)
                .Index(t => t.ErlassfassungID)
                .Index(t => t.ErstelltVonID)
                .Index(t => t.BearbeitetVonID);
            
            CreateTable(
                "dbo.Objekt",
                c => new
                    {
                        ObjektID = c.Int(nullable: false, identity: true),
                        ObjektkategorieID = c.Int(nullable: false),
                        Freigabe = c.Boolean(),
                        FreigabeVon = c.String(maxLength: 128),
                        FreigabeAm = c.DateTime(),
                        QsFreigabe = c.Boolean(),
                        QsFreigabeVon = c.String(maxLength: 128),
                        QsFreigabeAm = c.DateTime(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.ObjektID)
                .ForeignKey("dbo.AspNetUsers", t => t.BearbeitetVonID)
                .ForeignKey("dbo.AspNetUsers", t => t.ErstelltVonID)
                .ForeignKey("dbo.Objektkategorie", t => t.ObjektkategorieID, cascadeDelete: true)
                .Index(t => t.ObjektkategorieID)
                .Index(t => t.ErstelltVonID)
                .Index(t => t.BearbeitetVonID);
            
            CreateTable(
                "dbo.Objektkategorie",
                c => new
                    {
                        ObjektkategorieID = c.Int(nullable: false, identity: true),
                        ParentObjektkategorieID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.ObjektkategorieID)
                .ForeignKey("dbo.AspNetUsers", t => t.BearbeitetVonID)
                .ForeignKey("dbo.AspNetUsers", t => t.ErstelltVonID)
                .ForeignKey("dbo.Objektkategorie", t => t.ParentObjektkategorieID)
                .Index(t => t.ParentObjektkategorieID)
                .Index(t => t.ErstelltVonID)
                .Index(t => t.BearbeitetVonID);
            
            CreateTable(
                "dbo.ObjektkategorieUebersetzung",
                c => new
                    {
                        ObjektkategorieUebersetzungID = c.Int(nullable: false, identity: true),
                        ObjektkategorieID = c.Int(nullable: false),
                        SpracheID = c.Int(nullable: false),
                        Name = c.String(),
                    })
                .PrimaryKey(t => t.ObjektkategorieUebersetzungID)
                .ForeignKey("dbo.Objektkategorie", t => t.ObjektkategorieID, cascadeDelete: true)
                .ForeignKey("dbo.Sprache", t => t.SpracheID, cascadeDelete: true)
                .Index(t => t.ObjektkategorieID)
                .Index(t => t.SpracheID);
            
            CreateTable(
                "dbo.ObjektUebersetzung",
                c => new
                    {
                        ObjektUebersetzungID = c.Int(nullable: false, identity: true),
                        Name = c.String(),
                        Beschreibung = c.String(),
                        ObjektID = c.Int(nullable: false),
                        SpracheID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.ObjektUebersetzungID)
                .ForeignKey("dbo.Objekt", t => t.ObjektID, cascadeDelete: true)
                .ForeignKey("dbo.Sprache", t => t.SpracheID, cascadeDelete: true)
                .Index(t => t.ObjektID)
                .Index(t => t.SpracheID);
            
            CreateTable(
                "dbo.PflichtUebersetzung",
                c => new
                    {
                        PflichtUebersetzungID = c.Int(nullable: false, identity: true),
                        PflichtID = c.Int(nullable: false),
                        SpracheID = c.Int(nullable: false),
                        Beschreibung = c.String(),
                    })
                .PrimaryKey(t => t.PflichtUebersetzungID)
                .ForeignKey("dbo.Pflicht", t => t.PflichtID, cascadeDelete: true)
                .ForeignKey("dbo.Sprache", t => t.SpracheID, cascadeDelete: true)
                .Index(t => t.PflichtID)
                .Index(t => t.SpracheID);
            
            CreateTable(
                "dbo.StandortObjekt",
                c => new
                    {
                        StandortObjektID = c.Int(nullable: false, identity: true),
                        Name = c.String(),
                        Beschreibung = c.String(),
                        StandortID = c.Int(),
                        ObjektID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.StandortObjektID)
                .ForeignKey("dbo.AspNetUsers", t => t.BearbeitetVonID)
                .ForeignKey("dbo.AspNetUsers", t => t.ErstelltVonID)
                .ForeignKey("dbo.Objekt", t => t.ObjektID)
                .ForeignKey("dbo.Standort", t => t.StandortID)
                .Index(t => t.StandortID)
                .Index(t => t.ObjektID)
                .Index(t => t.ErstelltVonID)
                .Index(t => t.BearbeitetVonID);
            
            CreateTable(
                "dbo.Themenbereich",
                c => new
                    {
                        ThemenbereichID = c.Int(nullable: false, identity: true),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.ThemenbereichID)
                .ForeignKey("dbo.AspNetUsers", t => t.BearbeitetVonID)
                .ForeignKey("dbo.AspNetUsers", t => t.ErstelltVonID)
                .Index(t => t.ErstelltVonID)
                .Index(t => t.BearbeitetVonID);
            
            CreateTable(
                "dbo.ThemenbereichUebersetzung",
                c => new
                    {
                        ThemenbereichUebersetzungID = c.Int(nullable: false, identity: true),
                        Name = c.String(),
                        ThemenbereichID = c.Int(nullable: false),
                        SpracheID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.ThemenbereichUebersetzungID)
                .ForeignKey("dbo.Sprache", t => t.SpracheID, cascadeDelete: true)
                .ForeignKey("dbo.Themenbereich", t => t.ThemenbereichID, cascadeDelete: true)
                .Index(t => t.ThemenbereichID)
                .Index(t => t.SpracheID);
            
            CreateTable(
                "dbo.Forderung",
                c => new
                    {
                        ForderungID = c.Int(nullable: false, identity: true),
                        ArtikelID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.ForderungID)
                .ForeignKey("dbo.Artikel", t => t.ArtikelID)
                .ForeignKey("dbo.AspNetUsers", t => t.BearbeitetVonID)
                .ForeignKey("dbo.AspNetUsers", t => t.ErstelltVonID)
                .Index(t => t.ArtikelID)
                .Index(t => t.ErstelltVonID)
                .Index(t => t.BearbeitetVonID);
            
            CreateTable(
                "dbo.Erlasstyp",
                c => new
                    {
                        ErlasstypID = c.Int(nullable: false, identity: true),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.ErlasstypID)
                .ForeignKey("dbo.AspNetUsers", t => t.BearbeitetVonID)
                .ForeignKey("dbo.AspNetUsers", t => t.ErstelltVonID)
                .Index(t => t.ErstelltVonID)
                .Index(t => t.BearbeitetVonID);
            
            CreateTable(
                "dbo.ErlasstypUebersetzung",
                c => new
                    {
                        ErlasstypUebersetzungID = c.Int(nullable: false, identity: true),
                        ErlasstypID = c.Int(nullable: false),
                        SpracheID = c.Int(nullable: false),
                        Titel = c.String(),
                    })
                .PrimaryKey(t => t.ErlasstypUebersetzungID)
                .ForeignKey("dbo.Erlasstyp", t => t.ErlasstypID, cascadeDelete: true)
                .ForeignKey("dbo.Sprache", t => t.SpracheID, cascadeDelete: true)
                .Index(t => t.ErlasstypID)
                .Index(t => t.SpracheID);
            
            CreateTable(
                "dbo.ErlassUebersetzung",
                c => new
                    {
                        ErlassUebersetzungID = c.Int(nullable: false, identity: true),
                        ErlassID = c.Int(nullable: false),
                        SpracheID = c.Int(nullable: false),
                        Titel = c.String(),
                        Abkuerzung = c.String(),
                        Quelle = c.String(),
                    })
                .PrimaryKey(t => t.ErlassUebersetzungID)
                .ForeignKey("dbo.Erlass", t => t.ErlassID, cascadeDelete: true)
                .ForeignKey("dbo.Sprache", t => t.SpracheID, cascadeDelete: true)
                .Index(t => t.ErlassID)
                .Index(t => t.SpracheID);
            
            CreateTable(
                "dbo.ErlassfassungUebersetzung",
                c => new
                    {
                        ErlassfassungUebersetzungID = c.Int(nullable: false, identity: true),
                        ErlassfassungID = c.Int(nullable: false),
                        SpracheID = c.Int(nullable: false),
                        Quelle = c.String(),
                        BetroffenKommentar = c.String(),
                        NichtBetroffenKommentar = c.String(),
                    })
                .PrimaryKey(t => t.ErlassfassungUebersetzungID)
                .ForeignKey("dbo.Erlassfassung", t => t.ErlassfassungID, cascadeDelete: true)
                .ForeignKey("dbo.Sprache", t => t.SpracheID, cascadeDelete: true)
                .Index(t => t.ErlassfassungID)
                .Index(t => t.SpracheID);
            
            CreateTable(
                "dbo.ForderungsversionUebersetzung",
                c => new
                    {
                        ForderungsversionUebersetzungID = c.Int(nullable: false, identity: true),
                        Beschreibung = c.String(),
                        ForderungsversionID = c.Int(nullable: false),
                        SpracheID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.ForderungsversionUebersetzungID)
                .ForeignKey("dbo.Forderungsversion", t => t.ForderungsversionID, cascadeDelete: true)
                .ForeignKey("dbo.Sprache", t => t.SpracheID, cascadeDelete: true)
                .Index(t => t.ForderungsversionID)
                .Index(t => t.SpracheID);
            
            CreateTable(
                "dbo.RechtsbereichUebersetzung",
                c => new
                    {
                        RechtsbereichUebersetzungID = c.Int(nullable: false, identity: true),
                        Name = c.String(),
                        RechtsbereichID = c.Int(nullable: false),
                        SpracheID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => t.RechtsbereichUebersetzungID)
                .ForeignKey("dbo.Rechtsbereich", t => t.RechtsbereichID, cascadeDelete: true)
                .ForeignKey("dbo.Sprache", t => t.SpracheID, cascadeDelete: true)
                .Index(t => t.RechtsbereichID)
                .Index(t => t.SpracheID);
            
            CreateTable(
                "dbo.AspNetUserLogins",
                c => new
                    {
                        LoginProvider = c.String(nullable: false, maxLength: 128),
                        ProviderKey = c.String(nullable: false, maxLength: 128),
                        UserId = c.String(nullable: false, maxLength: 128),
                    })
                .PrimaryKey(t => new { t.LoginProvider, t.ProviderKey, t.UserId })
                .ForeignKey("dbo.AspNetUsers", t => t.UserId, cascadeDelete: true)
                .Index(t => t.UserId);
            
            CreateTable(
                "dbo.HerausgeberStandort",
                c => new
                    {
                        Herausgeber_HerausgeberID = c.Int(nullable: false),
                        Standort_StandortID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.Herausgeber_HerausgeberID, t.Standort_StandortID })
                .ForeignKey("dbo.Herausgeber", t => t.Herausgeber_HerausgeberID)
                .ForeignKey("dbo.Standort", t => t.Standort_StandortID)
                .Index(t => t.Herausgeber_HerausgeberID)
                .Index(t => t.Standort_StandortID);
            
            CreateTable(
                "dbo.ObjektForderungsversion",
                c => new
                    {
                        Objekt_ObjektID = c.Int(nullable: false),
                        Forderungsversion_ForderungsversionID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.Objekt_ObjektID, t.Forderungsversion_ForderungsversionID })
                .ForeignKey("dbo.Objekt", t => t.Objekt_ObjektID)
                .ForeignKey("dbo.Forderungsversion", t => t.Forderungsversion_ForderungsversionID)
                .Index(t => t.Objekt_ObjektID)
                .Index(t => t.Forderungsversion_ForderungsversionID);
            
            CreateTable(
                "dbo.ObjektPflicht",
                c => new
                    {
                        Objekt_ObjektID = c.Int(nullable: false),
                        Pflicht_PflichtID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.Objekt_ObjektID, t.Pflicht_PflichtID })
                .ForeignKey("dbo.Objekt", t => t.Objekt_ObjektID)
                .ForeignKey("dbo.Pflicht", t => t.Pflicht_PflichtID)
                .Index(t => t.Objekt_ObjektID)
                .Index(t => t.Pflicht_PflichtID);
            
            CreateTable(
                "dbo.ThemenbereichForderungsversion",
                c => new
                    {
                        Themenbereich_ThemenbereichID = c.Int(nullable: false),
                        Forderungsversion_ForderungsversionID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.Themenbereich_ThemenbereichID, t.Forderungsversion_ForderungsversionID })
                .ForeignKey("dbo.Themenbereich", t => t.Themenbereich_ThemenbereichID)
                .ForeignKey("dbo.Forderungsversion", t => t.Forderungsversion_ForderungsversionID)
                .Index(t => t.Themenbereich_ThemenbereichID)
                .Index(t => t.Forderungsversion_ForderungsversionID);
            
            CreateTable(
                "dbo.StandortKontakt",
                c => new
                    {
                        Standort_StandortID = c.Int(nullable: false),
                        Kontakt_KontaktID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.Standort_StandortID, t.Kontakt_KontaktID })
                .ForeignKey("dbo.Standort", t => t.Standort_StandortID)
                .ForeignKey("dbo.Kontakt", t => t.Kontakt_KontaktID)
                .Index(t => t.Standort_StandortID)
                .Index(t => t.Kontakt_KontaktID);
            
            CreateTable(
                "dbo.StandortRechtsbereich",
                c => new
                    {
                        Standort_StandortID = c.Int(nullable: false),
                        Rechtsbereich_RechtsbereichID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.Standort_StandortID, t.Rechtsbereich_RechtsbereichID })
                .ForeignKey("dbo.Standort", t => t.Standort_StandortID)
                .ForeignKey("dbo.Rechtsbereich", t => t.Rechtsbereich_RechtsbereichID)
                .Index(t => t.Standort_StandortID)
                .Index(t => t.Rechtsbereich_RechtsbereichID);
            
            CreateTable(
                "dbo.StandortSprache",
                c => new
                    {
                        Standort_StandortID = c.Int(nullable: false),
                        Sprache_SpracheID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.Standort_StandortID, t.Sprache_SpracheID })
                .ForeignKey("dbo.Standort", t => t.Standort_StandortID)
                .ForeignKey("dbo.Sprache", t => t.Sprache_SpracheID)
                .Index(t => t.Standort_StandortID)
                .Index(t => t.Sprache_SpracheID);
            
            CreateTable(
                "dbo.ForderungsversionRechtsbereich",
                c => new
                    {
                        Forderungsversion_ForderungsversionID = c.Int(nullable: false),
                        Rechtsbereich_RechtsbereichID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.Forderungsversion_ForderungsversionID, t.Rechtsbereich_RechtsbereichID })
                .ForeignKey("dbo.Forderungsversion", t => t.Forderungsversion_ForderungsversionID)
                .ForeignKey("dbo.Rechtsbereich", t => t.Rechtsbereich_RechtsbereichID)
                .Index(t => t.Forderungsversion_ForderungsversionID)
                .Index(t => t.Rechtsbereich_RechtsbereichID);
            
            CreateTable(
                "dbo.RechtsbereichKontakt",
                c => new
                    {
                        Rechtsbereich_RechtsbereichID = c.Int(nullable: false),
                        Kontakt_KontaktID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.Rechtsbereich_RechtsbereichID, t.Kontakt_KontaktID })
                .ForeignKey("dbo.Rechtsbereich", t => t.Rechtsbereich_RechtsbereichID)
                .ForeignKey("dbo.Kontakt", t => t.Kontakt_KontaktID)
                .Index(t => t.Rechtsbereich_RechtsbereichID)
                .Index(t => t.Kontakt_KontaktID);

            CreateTable(
                "dbo.Suvalink",
                c => new
                {
                    SuvalinkID = c.Int(nullable: false, identity: true),
                    ErlassID = c.Int(),
                    SpracheID = c.Int(nullable: false),
                    InternerLink = c.String(),
                })
                .PrimaryKey(t => t.SuvalinkID)
                .ForeignKey("dbo.Erlass", t => t.ErlassID)
                .ForeignKey("dbo.Sprache", t => t.SpracheID);
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.Suvalink", "ErlassID", "dbo.Erlass");
            DropForeignKey("dbo.Suvalink", "SpracheID", "dbo.Sprache");
            DropForeignKey("dbo.AspNetUserRoles", "UserId", "dbo.AspNetUsers");
            DropForeignKey("dbo.AspNetUserLogins", "UserId", "dbo.AspNetUsers");
            DropForeignKey("dbo.AspNetUserClaims", "UserId", "dbo.AspNetUsers");
            DropForeignKey("dbo.AspNetUserRoles", "RoleId", "dbo.AspNetRoles");
            DropForeignKey("dbo.AspNetUsers", "SpracheID", "dbo.Sprache");
            DropForeignKey("dbo.AspNetUsers", "Kunde_KundeID", "dbo.Kunde");
            DropForeignKey("dbo.Kunde", "ProjektleiterID", "dbo.AspNetUsers");
            DropForeignKey("dbo.RechtsbereichUebersetzung", "SpracheID", "dbo.Sprache");
            DropForeignKey("dbo.RechtsbereichUebersetzung", "RechtsbereichID", "dbo.Rechtsbereich");
            DropForeignKey("dbo.RechtsbereichKontakt", "Kontakt_KontaktID", "dbo.Kontakt");
            DropForeignKey("dbo.RechtsbereichKontakt", "Rechtsbereich_RechtsbereichID", "dbo.Rechtsbereich");
            DropForeignKey("dbo.Forderungsversion", "VorversionID", "dbo.Forderungsversion");
            DropForeignKey("dbo.ForderungsversionRechtsbereich", "Rechtsbereich_RechtsbereichID", "dbo.Rechtsbereich");
            DropForeignKey("dbo.ForderungsversionRechtsbereich", "Forderungsversion_ForderungsversionID", "dbo.Forderungsversion");
            DropForeignKey("dbo.Forderungsversion", "NachfolgeversionID", "dbo.Forderungsversion");
            DropForeignKey("dbo.ForderungsversionUebersetzung", "SpracheID", "dbo.Sprache");
            DropForeignKey("dbo.ForderungsversionUebersetzung", "ForderungsversionID", "dbo.Forderungsversion");
            DropForeignKey("dbo.Forderungsversion", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.Forderungsversion", "ErlassfassungID", "dbo.Erlassfassung");
            DropForeignKey("dbo.Erlassfassung", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.ErlassfassungUebersetzung", "SpracheID", "dbo.Sprache");
            DropForeignKey("dbo.ErlassfassungUebersetzung", "ErlassfassungID", "dbo.Erlassfassung");
            DropForeignKey("dbo.Erlass", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.Erlassfassung", "ErlassID", "dbo.Erlass");
            DropForeignKey("dbo.ErlassUebersetzung", "SpracheID", "dbo.Sprache");
            DropForeignKey("dbo.ErlassUebersetzung", "ErlassID", "dbo.Erlass");
            DropForeignKey("dbo.Erlass", "ErlasstypID", "dbo.Erlasstyp");
            DropForeignKey("dbo.Erlasstyp", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.ErlasstypUebersetzung", "SpracheID", "dbo.Sprache");
            DropForeignKey("dbo.ErlasstypUebersetzung", "ErlasstypID", "dbo.Erlasstyp");
            DropForeignKey("dbo.Erlasstyp", "BearbeitetVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.Erlass", "BearbeitetVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.Forderungsversion", "ForderungID", "dbo.Forderung");
            DropForeignKey("dbo.Forderung", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.Forderung", "BearbeitetVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.Forderung", "ArtikelID", "dbo.Artikel");
            DropForeignKey("dbo.Artikel", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.Artikel", "ErlassID", "dbo.Erlass");
            DropForeignKey("dbo.Artikel", "BearbeitetVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.ArtikelUebersetzung", "SpracheID", "dbo.Sprache");
            DropForeignKey("dbo.StandortSprache", "Sprache_SpracheID", "dbo.Sprache");
            DropForeignKey("dbo.StandortSprache", "Standort_StandortID", "dbo.Standort");
            DropForeignKey("dbo.StandortRechtsbereich", "Rechtsbereich_RechtsbereichID", "dbo.Rechtsbereich");
            DropForeignKey("dbo.StandortRechtsbereich", "Standort_StandortID", "dbo.Standort");
            DropForeignKey("dbo.Standort", "KundeID", "dbo.Kunde");
            DropForeignKey("dbo.StandortKontakt", "Kontakt_KontaktID", "dbo.Kontakt");
            DropForeignKey("dbo.StandortKontakt", "Standort_StandortID", "dbo.Standort");
            DropForeignKey("dbo.IndividuelleForderung", "ThemenbereichID", "dbo.Themenbereich");
            DropForeignKey("dbo.ThemenbereichUebersetzung", "ThemenbereichID", "dbo.Themenbereich");
            DropForeignKey("dbo.ThemenbereichUebersetzung", "SpracheID", "dbo.Sprache");
            DropForeignKey("dbo.ThemenbereichForderungsversion", "Forderungsversion_ForderungsversionID", "dbo.Forderungsversion");
            DropForeignKey("dbo.ThemenbereichForderungsversion", "Themenbereich_ThemenbereichID", "dbo.Themenbereich");
            DropForeignKey("dbo.Themenbereich", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.Themenbereich", "BearbeitetVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.IndividuelleForderung", "StandortID", "dbo.Standort");
            DropForeignKey("dbo.Kundenbezug", "StandortID", "dbo.Standort");
            DropForeignKey("dbo.Kundendokument", "StandortID", "dbo.Standort");
            DropForeignKey("dbo.StandortObjekt", "StandortID", "dbo.Standort");
            DropForeignKey("dbo.StandortObjekt", "ObjektID", "dbo.Objekt");
            DropForeignKey("dbo.KundendokumentPflicht", "StandortObjektID", "dbo.StandortObjekt");
            DropForeignKey("dbo.KundendokumentForderungsversion", "StandortObjektID", "dbo.StandortObjekt");
            DropForeignKey("dbo.IndividuelleForderung", "StandortObjektID", "dbo.StandortObjekt");
            DropForeignKey("dbo.StandortObjekt", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.StandortObjekt", "BearbeitetVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.KundendokumentPflicht", "PflichtID", "dbo.Pflicht");
            DropForeignKey("dbo.PflichtUebersetzung", "SpracheID", "dbo.Sprache");
            DropForeignKey("dbo.PflichtUebersetzung", "PflichtID", "dbo.Pflicht");
            DropForeignKey("dbo.ObjektPflicht", "Pflicht_PflichtID", "dbo.Pflicht");
            DropForeignKey("dbo.ObjektPflicht", "Objekt_ObjektID", "dbo.Objekt");
            DropForeignKey("dbo.ObjektUebersetzung", "SpracheID", "dbo.Sprache");
            DropForeignKey("dbo.ObjektUebersetzung", "ObjektID", "dbo.Objekt");
            DropForeignKey("dbo.Objektkategorie", "ParentObjektkategorieID", "dbo.Objektkategorie");
            DropForeignKey("dbo.ObjektkategorieUebersetzung", "SpracheID", "dbo.Sprache");
            DropForeignKey("dbo.ObjektkategorieUebersetzung", "ObjektkategorieID", "dbo.Objektkategorie");
            DropForeignKey("dbo.Objekt", "ObjektkategorieID", "dbo.Objektkategorie");
            DropForeignKey("dbo.Objektkategorie", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.Objektkategorie", "BearbeitetVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.ObjektForderungsversion", "Forderungsversion_ForderungsversionID", "dbo.Forderungsversion");
            DropForeignKey("dbo.ObjektForderungsversion", "Objekt_ObjektID", "dbo.Objekt");
            DropForeignKey("dbo.Objekt", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.Objekt", "BearbeitetVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.Pflicht", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.Pflicht", "ErlassfassungID", "dbo.Erlassfassung");
            DropForeignKey("dbo.Pflicht", "BearbeitetVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.KundendokumentPflicht", "KundendokumentID", "dbo.Kundendokument");
            DropForeignKey("dbo.KundendokumentPflicht", "KundenbezugID", "dbo.Kundenbezug");
            DropForeignKey("dbo.KundendokumentPflicht", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.KundendokumentPflicht", "BearbeitetVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.KundendokumentIndividuelleForderung", "KundendokumentID", "dbo.Kundendokument");
            DropForeignKey("dbo.KundendokumentIndividuelleForderung", "IndividuelleForderungID", "dbo.IndividuelleForderung");
            DropForeignKey("dbo.KundendokumentForderungsversion", "KundendokumentID", "dbo.Kundendokument");
            DropForeignKey("dbo.Kundendokument", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.Kundendokument", "BearbeitetVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.KundendokumentForderungsversion", "KundenbezugID", "dbo.Kundenbezug");
            DropForeignKey("dbo.KundendokumentForderungsversion", "ForderungsversionID", "dbo.Forderungsversion");
            DropForeignKey("dbo.KundendokumentForderungsversion", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.KundendokumentForderungsversion", "BearbeitetVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.IndividuelleForderung", "KundenbezugID", "dbo.Kundenbezug");
            DropForeignKey("dbo.IndividuelleForderung", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.IndividuelleForderung", "BearbeitetVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.HerausgeberStandort", "Standort_StandortID", "dbo.Standort");
            DropForeignKey("dbo.HerausgeberStandort", "Herausgeber_HerausgeberID", "dbo.Herausgeber");
            DropForeignKey("dbo.Herausgeber", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.Erlass", "HerausgeberId", "dbo.Herausgeber");
            DropForeignKey("dbo.Herausgeber", "BearbeitetVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.Standort", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.Standort", "BearbeitetVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.ArtikelUebersetzung", "ArtikelID", "dbo.Artikel");
            DropForeignKey("dbo.Erlassfassung", "BearbeitetVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.Forderungsversion", "BearbeitetVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.Rechtsbereich", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.Rechtsbereich", "BearbeitetVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.Kontakt", "KundeID", "dbo.Kunde");
            DropForeignKey("dbo.Kontakt", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.Kontakt", "BearbeitetVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.Kunde", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.Kunde", "BearbeitetVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.AspNetUsers", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.AspNetUsers", "BearbeitetVonID", "dbo.AspNetUsers");
            DropIndex("dbo.RechtsbereichKontakt", new[] { "Kontakt_KontaktID" });
            DropIndex("dbo.RechtsbereichKontakt", new[] { "Rechtsbereich_RechtsbereichID" });
            DropIndex("dbo.ForderungsversionRechtsbereich", new[] { "Rechtsbereich_RechtsbereichID" });
            DropIndex("dbo.ForderungsversionRechtsbereich", new[] { "Forderungsversion_ForderungsversionID" });
            DropIndex("dbo.StandortSprache", new[] { "Sprache_SpracheID" });
            DropIndex("dbo.StandortSprache", new[] { "Standort_StandortID" });
            DropIndex("dbo.StandortRechtsbereich", new[] { "Rechtsbereich_RechtsbereichID" });
            DropIndex("dbo.StandortRechtsbereich", new[] { "Standort_StandortID" });
            DropIndex("dbo.StandortKontakt", new[] { "Kontakt_KontaktID" });
            DropIndex("dbo.StandortKontakt", new[] { "Standort_StandortID" });
            DropIndex("dbo.ThemenbereichForderungsversion", new[] { "Forderungsversion_ForderungsversionID" });
            DropIndex("dbo.ThemenbereichForderungsversion", new[] { "Themenbereich_ThemenbereichID" });
            DropIndex("dbo.ObjektPflicht", new[] { "Pflicht_PflichtID" });
            DropIndex("dbo.ObjektPflicht", new[] { "Objekt_ObjektID" });
            DropIndex("dbo.ObjektForderungsversion", new[] { "Forderungsversion_ForderungsversionID" });
            DropIndex("dbo.ObjektForderungsversion", new[] { "Objekt_ObjektID" });
            DropIndex("dbo.HerausgeberStandort", new[] { "Standort_StandortID" });
            DropIndex("dbo.HerausgeberStandort", new[] { "Herausgeber_HerausgeberID" });
            DropIndex("dbo.AspNetUserLogins", new[] { "UserId" });
            DropIndex("dbo.RechtsbereichUebersetzung", new[] { "SpracheID" });
            DropIndex("dbo.RechtsbereichUebersetzung", new[] { "RechtsbereichID" });
            DropIndex("dbo.ForderungsversionUebersetzung", new[] { "SpracheID" });
            DropIndex("dbo.ForderungsversionUebersetzung", new[] { "ForderungsversionID" });
            DropIndex("dbo.ErlassfassungUebersetzung", new[] { "SpracheID" });
            DropIndex("dbo.ErlassfassungUebersetzung", new[] { "ErlassfassungID" });
            DropIndex("dbo.ErlassUebersetzung", new[] { "SpracheID" });
            DropIndex("dbo.ErlassUebersetzung", new[] { "ErlassID" });
            DropIndex("dbo.ErlasstypUebersetzung", new[] { "SpracheID" });
            DropIndex("dbo.ErlasstypUebersetzung", new[] { "ErlasstypID" });
            DropIndex("dbo.Erlasstyp", new[] { "BearbeitetVonID" });
            DropIndex("dbo.Erlasstyp", new[] { "ErstelltVonID" });
            DropIndex("dbo.Forderung", new[] { "BearbeitetVonID" });
            DropIndex("dbo.Forderung", new[] { "ErstelltVonID" });
            DropIndex("dbo.Forderung", new[] { "ArtikelID" });
            DropIndex("dbo.ThemenbereichUebersetzung", new[] { "SpracheID" });
            DropIndex("dbo.ThemenbereichUebersetzung", new[] { "ThemenbereichID" });
            DropIndex("dbo.Themenbereich", new[] { "BearbeitetVonID" });
            DropIndex("dbo.Themenbereich", new[] { "ErstelltVonID" });
            DropIndex("dbo.StandortObjekt", new[] { "BearbeitetVonID" });
            DropIndex("dbo.StandortObjekt", new[] { "ErstelltVonID" });
            DropIndex("dbo.StandortObjekt", new[] { "ObjektID" });
            DropIndex("dbo.StandortObjekt", new[] { "StandortID" });
            DropIndex("dbo.PflichtUebersetzung", new[] { "SpracheID" });
            DropIndex("dbo.PflichtUebersetzung", new[] { "PflichtID" });
            DropIndex("dbo.ObjektUebersetzung", new[] { "SpracheID" });
            DropIndex("dbo.ObjektUebersetzung", new[] { "ObjektID" });
            DropIndex("dbo.ObjektkategorieUebersetzung", new[] { "SpracheID" });
            DropIndex("dbo.ObjektkategorieUebersetzung", new[] { "ObjektkategorieID" });
            DropIndex("dbo.Objektkategorie", new[] { "BearbeitetVonID" });
            DropIndex("dbo.Objektkategorie", new[] { "ErstelltVonID" });
            DropIndex("dbo.Objektkategorie", new[] { "ParentObjektkategorieID" });
            DropIndex("dbo.Objekt", new[] { "BearbeitetVonID" });
            DropIndex("dbo.Objekt", new[] { "ErstelltVonID" });
            DropIndex("dbo.Objekt", new[] { "ObjektkategorieID" });
            DropIndex("dbo.Pflicht", new[] { "BearbeitetVonID" });
            DropIndex("dbo.Pflicht", new[] { "ErstelltVonID" });
            DropIndex("dbo.Pflicht", new[] { "ErlassfassungID" });
            DropIndex("dbo.KundendokumentPflicht", new[] { "BearbeitetVonID" });
            DropIndex("dbo.KundendokumentPflicht", new[] { "ErstelltVonID" });
            DropIndex("dbo.KundendokumentPflicht", new[] { "KundenbezugID" });
            DropIndex("dbo.KundendokumentPflicht", new[] { "StandortObjektID" });
            DropIndex("dbo.KundendokumentPflicht", new[] { "PflichtID" });
            DropIndex("dbo.KundendokumentPflicht", new[] { "KundendokumentID" });
            DropIndex("dbo.KundendokumentIndividuelleForderung", new[] { "KundendokumentID" });
            DropIndex("dbo.KundendokumentIndividuelleForderung", new[] { "IndividuelleForderungID" });
            DropIndex("dbo.Kundendokument", new[] { "BearbeitetVonID" });
            DropIndex("dbo.Kundendokument", new[] { "ErstelltVonID" });
            DropIndex("dbo.Kundendokument", new[] { "StandortID" });
            DropIndex("dbo.KundendokumentForderungsversion", new[] { "BearbeitetVonID" });
            DropIndex("dbo.KundendokumentForderungsversion", new[] { "ErstelltVonID" });
            DropIndex("dbo.KundendokumentForderungsversion", new[] { "KundenbezugID" });
            DropIndex("dbo.KundendokumentForderungsversion", new[] { "StandortObjektID" });
            DropIndex("dbo.KundendokumentForderungsversion", new[] { "ForderungsversionID" });
            DropIndex("dbo.KundendokumentForderungsversion", new[] { "KundendokumentID" });
            DropIndex("dbo.Kundenbezug", new[] { "StandortID" });
            DropIndex("dbo.IndividuelleForderung", new[] { "BearbeitetVonID" });
            DropIndex("dbo.IndividuelleForderung", new[] { "ErstelltVonID" });
            DropIndex("dbo.IndividuelleForderung", new[] { "KundenbezugID" });
            DropIndex("dbo.IndividuelleForderung", new[] { "StandortObjektID" });
            DropIndex("dbo.IndividuelleForderung", new[] { "ThemenbereichID" });
            DropIndex("dbo.IndividuelleForderung", new[] { "StandortID" });
            DropIndex("dbo.Herausgeber", new[] { "BearbeitetVonID" });
            DropIndex("dbo.Herausgeber", new[] { "ErstelltVonID" });
            DropIndex("dbo.Standort", new[] { "BearbeitetVonID" });
            DropIndex("dbo.Standort", new[] { "ErstelltVonID" });
            DropIndex("dbo.Standort", new[] { "KundeID" });
            DropIndex("dbo.ArtikelUebersetzung", new[] { "SpracheID" });
            DropIndex("dbo.ArtikelUebersetzung", new[] { "ArtikelID" });
            DropIndex("dbo.Artikel", new[] { "BearbeitetVonID" });
            DropIndex("dbo.Artikel", new[] { "ErstelltVonID" });
            DropIndex("dbo.Artikel", new[] { "ErlassID" });
            DropIndex("dbo.Erlass", new[] { "BearbeitetVonID" });
            DropIndex("dbo.Erlass", new[] { "ErstelltVonID" });
            DropIndex("dbo.Erlass", new[] { "HerausgeberId" });
            DropIndex("dbo.Erlass", new[] { "ErlasstypID" });
            DropIndex("dbo.Erlassfassung", new[] { "BearbeitetVonID" });
            DropIndex("dbo.Erlassfassung", new[] { "ErstelltVonID" });
            DropIndex("dbo.Erlassfassung", new[] { "ErlassID" });
            DropIndex("dbo.Forderungsversion", new[] { "BearbeitetVonID" });
            DropIndex("dbo.Forderungsversion", new[] { "ErstelltVonID" });
            DropIndex("dbo.Forderungsversion", new[] { "ErlassfassungID" });
            DropIndex("dbo.Forderungsversion", new[] { "ForderungID" });
            DropIndex("dbo.Forderungsversion", new[] { "NachfolgeversionID" });
            DropIndex("dbo.Forderungsversion", new[] { "VorversionID" });
            DropIndex("dbo.Rechtsbereich", new[] { "BearbeitetVonID" });
            DropIndex("dbo.Rechtsbereich", new[] { "ErstelltVonID" });
            DropIndex("dbo.Kontakt", new[] { "BearbeitetVonID" });
            DropIndex("dbo.Kontakt", new[] { "ErstelltVonID" });
            DropIndex("dbo.Kontakt", new[] { "KundeID" });
            DropIndex("dbo.Kunde", new[] { "BearbeitetVonID" });
            DropIndex("dbo.Kunde", new[] { "ErstelltVonID" });
            DropIndex("dbo.Kunde", new[] { "ProjektleiterID" });
            DropIndex("dbo.AspNetUserClaims", new[] { "UserId" });
            DropIndex("dbo.AspNetUsers", new[] { "Kunde_KundeID" });
            DropIndex("dbo.AspNetUsers", new[] { "BearbeitetVonID" });
            DropIndex("dbo.AspNetUsers", new[] { "ErstelltVonID" });
            DropIndex("dbo.AspNetUsers", new[] { "SpracheID" });
            DropIndex("dbo.AspNetUsers", "UserNameIndex");
            DropIndex("dbo.AspNetUserRoles", new[] { "RoleId" });
            DropIndex("dbo.AspNetUserRoles", new[] { "UserId" });
            DropIndex("dbo.AspNetRoles", "RoleNameIndex");
            DropTable("dbo.RechtsbereichKontakt");
            DropTable("dbo.ForderungsversionRechtsbereich");
            DropTable("dbo.StandortSprache");
            DropTable("dbo.StandortRechtsbereich");
            DropTable("dbo.StandortKontakt");
            DropTable("dbo.ThemenbereichForderungsversion");
            DropTable("dbo.ObjektPflicht");
            DropTable("dbo.ObjektForderungsversion");
            DropTable("dbo.HerausgeberStandort");
            DropTable("dbo.AspNetUserLogins");
            DropTable("dbo.RechtsbereichUebersetzung");
            DropTable("dbo.ForderungsversionUebersetzung");
            DropTable("dbo.ErlassfassungUebersetzung");
            DropTable("dbo.ErlassUebersetzung");
            DropTable("dbo.ErlasstypUebersetzung");
            DropTable("dbo.Erlasstyp");
            DropTable("dbo.Forderung");
            DropTable("dbo.ThemenbereichUebersetzung");
            DropTable("dbo.Themenbereich");
            DropTable("dbo.StandortObjekt");
            DropTable("dbo.PflichtUebersetzung");
            DropTable("dbo.ObjektUebersetzung");
            DropTable("dbo.ObjektkategorieUebersetzung");
            DropTable("dbo.Objektkategorie");
            DropTable("dbo.Objekt");
            DropTable("dbo.Pflicht");
            DropTable("dbo.KundendokumentPflicht");
            DropTable("dbo.KundendokumentIndividuelleForderung");
            DropTable("dbo.Kundendokument");
            DropTable("dbo.KundendokumentForderungsversion");
            DropTable("dbo.Kundenbezug");
            DropTable("dbo.IndividuelleForderung");
            DropTable("dbo.Herausgeber");
            DropTable("dbo.Standort");
            DropTable("dbo.Sprache");
            DropTable("dbo.ArtikelUebersetzung");
            DropTable("dbo.Artikel");
            DropTable("dbo.Erlass");
            DropTable("dbo.Erlassfassung");
            DropTable("dbo.Forderungsversion");
            DropTable("dbo.Rechtsbereich");
            DropTable("dbo.Kontakt");
            DropTable("dbo.Kunde");
            DropTable("dbo.AspNetUserClaims");
            DropTable("dbo.AspNetUsers");
            DropTable("dbo.AspNetUserRoles");
            DropTable("dbo.AspNetRoles");
            DropTable("dbo.Suvalink");
        }
    }
}
