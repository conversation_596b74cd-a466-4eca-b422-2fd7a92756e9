namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class AddConsultationUebersetzung : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.Consultation", "Uebersetzung", c => c.String(storeType: "xml"));
            DropColumn("dbo.Consultation", "TitleDE");
            DropColumn("dbo.Consultation", "TitleFR");
            DropColumn("dbo.Consultation", "TitleIT");
            DropColumn("dbo.Consultation", "TitleEN");
            DropColumn("dbo.Consultation", "Quelle");
        }
        
        public override void Down()
        {
            AddColumn("dbo.Consultation", "Quelle", c => c.String());
            AddColumn("dbo.Consultation", "TitleEN", c => c.String());
            AddColumn("dbo.Consultation", "TitleIT", c => c.String());
            AddColumn("dbo.Consultation", "TitleFR", c => c.String());
            AddColumn("dbo.Consultation", "TitleDE", c => c.String());
            DropColumn("dbo.Consultation", "Uebersetzung");
        }
    }
}
