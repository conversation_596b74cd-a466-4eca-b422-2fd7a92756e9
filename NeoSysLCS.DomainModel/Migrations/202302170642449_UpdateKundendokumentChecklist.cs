namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class UpdateKundendokumentChecklist : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.KundendokumentChecklistQuestion", "ChecklistHeaderID", c => c.Int(nullable: false));
            AddColumn("dbo.KundendokumentChecklistQuestion", "ChecklistType", c => c.String());
            AddColumn("dbo.KundendokumentChecklistQuestion", "Numeration", c => c.String());
            CreateIndex("dbo.KundendokumentChecklistQuestion", "ChecklistHeaderID");
            AddForeignKey("dbo.KundendokumentChecklistQuestion", "ChecklistHeaderID", "dbo.ChecklistHeader", "ChecklistHeaderID");
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.KundendokumentChecklistQuestion", "ChecklistHeaderID", "dbo.ChecklistHeader");
            DropIndex("dbo.KundendokumentChecklistQuestion", new[] { "ChecklistHeaderID" });
            DropColumn("dbo.KundendokumentChecklistQuestion", "Numeration");
            DropColumn("dbo.KundendokumentChecklistQuestion", "ChecklistType");
            DropColumn("dbo.KundendokumentChecklistQuestion", "ChecklistHeaderID");
        }
    }
}
