namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class addCustomerNews : DbMigration
    {
        public override void Up()
        {
            CreateTable(
                "dbo.CustomerNews",
                c => new
                    {
                        CustomerNewsID = c.Int(nullable: false, identity: true),
                        FromDate = c.DateTime(nullable: false),
                        ToDate = c.DateTime(nullable: false),
                        Translation = c.String(storeType: "xml"),
                    })
                .PrimaryKey(t => t.CustomerNewsID);
        }
        
        public override void Down()
        {
            DropTable("dbo.CustomerNews");
        }
    }
}
