namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class addedNewKundeFields : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.Kunde", "Comment", c => c.String());
            AddColumn("dbo.Kunde", "ProjektleiterQSID", c => c.String(maxLength: 128));
            AddColumn("dbo.Kunde", "AnalysisStatus", c => c.Int(nullable: false));
            AddColumn("dbo.Kunde", "Status", c => c.Int(nullable: false));
            AddColumn("dbo.Kunde", "AnalysisDate", c => c.DateTime());
            AddColumn("dbo.Kunde", "DocumentReportDate", c => c.DateTime());
            AddColumn("dbo.Kunde", "EducationDate", c => c.DateTime());
            AddColumn("dbo.Kunde", "ContractCreatedDate", c => c.DateTime());
            AddColumn("dbo.Kunde", "ContractReturnedDate", c => c.DateTime());
            AddColumn("dbo.Kunde", "UpdateDate", c => c.DateTime());
            AddColumn("dbo.Kunde", "InquiryDate", c => c.DateTime());
            AddColumn("dbo.Kunde", "DocumentReceivedDate", c => c.DateTime());
            AddColumn("dbo.Kunde", "InvoiceDate", c => c.DateTime());
            AddColumn("dbo.Kunde", "Reccurence", c => c.String());
            CreateIndex("dbo.Kunde", "ProjektleiterQSID");
            AddForeignKey("dbo.Kunde", "ProjektleiterQSID", "dbo.AspNetUsers", "Id");
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.Kunde", "ProjektleiterQSID", "dbo.AspNetUsers");
            DropIndex("dbo.Kunde", new[] { "ProjektleiterQSID" });
            DropColumn("dbo.Kunde", "Reccurence");
            DropColumn("dbo.Kunde", "InvoiceDate");
            DropColumn("dbo.Kunde", "DocumentReceivedDate");
            DropColumn("dbo.Kunde", "InquiryDate");
            DropColumn("dbo.Kunde", "UpdateDate");
            DropColumn("dbo.Kunde", "ContractReturnedDate");
            DropColumn("dbo.Kunde", "ContractCreatedDate");
            DropColumn("dbo.Kunde", "EducationDate");
            DropColumn("dbo.Kunde", "DocumentReportDate");
            DropColumn("dbo.Kunde", "AnalysisDate");
            DropColumn("dbo.Kunde", "Status");
            DropColumn("dbo.Kunde", "AnalysisStatus");
            DropColumn("dbo.Kunde", "ProjektleiterQSID");
            DropColumn("dbo.Kunde", "Comment");
        }
    }
}
