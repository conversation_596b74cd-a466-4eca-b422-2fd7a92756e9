namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class addedDatesOnUebersetzungen : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.ArtikelUebersetzung", "ErstelltVonID", c => c.String(maxLength: 128));
            AddColumn("dbo.ArtikelUebersetzung", "BearbeitetVonID", c => c.String(maxLength: 128));
            AddColumn("dbo.ArtikelUebersetzung", "ErstelltAm", c => c.DateTime());
            AddColumn("dbo.ArtikelUebersetzung", "BearbeitetAm", c => c.DateTime());
            AddColumn("dbo.ObjektkategorieUebersetzung", "ErstelltVonID", c => c.String(maxLength: 128));
            AddColumn("dbo.ObjektkategorieUebersetzung", "BearbeitetVonID", c => c.String(maxLength: 128));
            AddColumn("dbo.ObjektkategorieUebersetzung", "ErstelltAm", c => c.DateTime());
            AddColumn("dbo.ObjektkategorieUebersetzung", "BearbeitetAm", c => c.DateTime());
            AddColumn("dbo.ObjektUebersetzung", "ErstelltVonID", c => c.String(maxLength: 128));
            AddColumn("dbo.ObjektUebersetzung", "BearbeitetVonID", c => c.String(maxLength: 128));
            AddColumn("dbo.ObjektUebersetzung", "ErstelltAm", c => c.DateTime());
            AddColumn("dbo.ObjektUebersetzung", "BearbeitetAm", c => c.DateTime());
            AddColumn("dbo.PflichtUebersetzung", "ErstelltVonID", c => c.String(maxLength: 128));
            AddColumn("dbo.PflichtUebersetzung", "BearbeitetVonID", c => c.String(maxLength: 128));
            AddColumn("dbo.PflichtUebersetzung", "ErstelltAm", c => c.DateTime());
            AddColumn("dbo.PflichtUebersetzung", "BearbeitetAm", c => c.DateTime());
            AddColumn("dbo.ThemenbereichUebersetzung", "ErstelltVonID", c => c.String(maxLength: 128));
            AddColumn("dbo.ThemenbereichUebersetzung", "BearbeitetVonID", c => c.String(maxLength: 128));
            AddColumn("dbo.ThemenbereichUebersetzung", "ErstelltAm", c => c.DateTime());
            AddColumn("dbo.ThemenbereichUebersetzung", "BearbeitetAm", c => c.DateTime());
            AddColumn("dbo.ErlasstypUebersetzung", "ErstelltVonID", c => c.String(maxLength: 128));
            AddColumn("dbo.ErlasstypUebersetzung", "BearbeitetVonID", c => c.String(maxLength: 128));
            AddColumn("dbo.ErlasstypUebersetzung", "ErstelltAm", c => c.DateTime());
            AddColumn("dbo.ErlasstypUebersetzung", "BearbeitetAm", c => c.DateTime());
            AddColumn("dbo.ErlassUebersetzung", "ErstelltVonID", c => c.String(maxLength: 128));
            AddColumn("dbo.ErlassUebersetzung", "BearbeitetVonID", c => c.String(maxLength: 128));
            AddColumn("dbo.ErlassUebersetzung", "ErstelltAm", c => c.DateTime());
            AddColumn("dbo.ErlassUebersetzung", "BearbeitetAm", c => c.DateTime());
            AddColumn("dbo.ErlassfassungUebersetzung", "ErstelltVonID", c => c.String(maxLength: 128));
            AddColumn("dbo.ErlassfassungUebersetzung", "BearbeitetVonID", c => c.String(maxLength: 128));
            AddColumn("dbo.ErlassfassungUebersetzung", "ErstelltAm", c => c.DateTime());
            AddColumn("dbo.ErlassfassungUebersetzung", "BearbeitetAm", c => c.DateTime());
            AddColumn("dbo.ForderungsversionUebersetzung", "ErstelltVonID", c => c.String(maxLength: 128));
            AddColumn("dbo.ForderungsversionUebersetzung", "BearbeitetVonID", c => c.String(maxLength: 128));
            AddColumn("dbo.ForderungsversionUebersetzung", "ErstelltAm", c => c.DateTime());
            AddColumn("dbo.ForderungsversionUebersetzung", "BearbeitetAm", c => c.DateTime());
            AddColumn("dbo.RechtsbereichUebersetzung", "ErstelltVonID", c => c.String(maxLength: 128));
            AddColumn("dbo.RechtsbereichUebersetzung", "BearbeitetVonID", c => c.String(maxLength: 128));
            AddColumn("dbo.RechtsbereichUebersetzung", "ErstelltAm", c => c.DateTime());
            AddColumn("dbo.RechtsbereichUebersetzung", "BearbeitetAm", c => c.DateTime());
            CreateIndex("dbo.ArtikelUebersetzung", "ErstelltVonID");
            CreateIndex("dbo.ArtikelUebersetzung", "BearbeitetVonID");
            CreateIndex("dbo.ObjektkategorieUebersetzung", "ErstelltVonID");
            CreateIndex("dbo.ObjektkategorieUebersetzung", "BearbeitetVonID");
            CreateIndex("dbo.ObjektUebersetzung", "ErstelltVonID");
            CreateIndex("dbo.ObjektUebersetzung", "BearbeitetVonID");
            CreateIndex("dbo.PflichtUebersetzung", "ErstelltVonID");
            CreateIndex("dbo.PflichtUebersetzung", "BearbeitetVonID");
            CreateIndex("dbo.ThemenbereichUebersetzung", "ErstelltVonID");
            CreateIndex("dbo.ThemenbereichUebersetzung", "BearbeitetVonID");
            CreateIndex("dbo.ErlasstypUebersetzung", "ErstelltVonID");
            CreateIndex("dbo.ErlasstypUebersetzung", "BearbeitetVonID");
            CreateIndex("dbo.ErlassUebersetzung", "ErstelltVonID");
            CreateIndex("dbo.ErlassUebersetzung", "BearbeitetVonID");
            CreateIndex("dbo.ErlassfassungUebersetzung", "ErstelltVonID");
            CreateIndex("dbo.ErlassfassungUebersetzung", "BearbeitetVonID");
            CreateIndex("dbo.ForderungsversionUebersetzung", "ErstelltVonID");
            CreateIndex("dbo.ForderungsversionUebersetzung", "BearbeitetVonID");
            CreateIndex("dbo.RechtsbereichUebersetzung", "ErstelltVonID");
            CreateIndex("dbo.RechtsbereichUebersetzung", "BearbeitetVonID");
            AddForeignKey("dbo.ArtikelUebersetzung", "BearbeitetVonID", "dbo.AspNetUsers", "Id");
            AddForeignKey("dbo.ArtikelUebersetzung", "ErstelltVonID", "dbo.AspNetUsers", "Id");
            AddForeignKey("dbo.ObjektkategorieUebersetzung", "BearbeitetVonID", "dbo.AspNetUsers", "Id");
            AddForeignKey("dbo.ObjektkategorieUebersetzung", "ErstelltVonID", "dbo.AspNetUsers", "Id");
            AddForeignKey("dbo.ObjektUebersetzung", "BearbeitetVonID", "dbo.AspNetUsers", "Id");
            AddForeignKey("dbo.ObjektUebersetzung", "ErstelltVonID", "dbo.AspNetUsers", "Id");
            AddForeignKey("dbo.PflichtUebersetzung", "BearbeitetVonID", "dbo.AspNetUsers", "Id");
            AddForeignKey("dbo.PflichtUebersetzung", "ErstelltVonID", "dbo.AspNetUsers", "Id");
            AddForeignKey("dbo.ThemenbereichUebersetzung", "BearbeitetVonID", "dbo.AspNetUsers", "Id");
            AddForeignKey("dbo.ThemenbereichUebersetzung", "ErstelltVonID", "dbo.AspNetUsers", "Id");
            AddForeignKey("dbo.ErlasstypUebersetzung", "BearbeitetVonID", "dbo.AspNetUsers", "Id");
            AddForeignKey("dbo.ErlasstypUebersetzung", "ErstelltVonID", "dbo.AspNetUsers", "Id");
            AddForeignKey("dbo.ErlassUebersetzung", "BearbeitetVonID", "dbo.AspNetUsers", "Id");
            AddForeignKey("dbo.ErlassUebersetzung", "ErstelltVonID", "dbo.AspNetUsers", "Id");
            AddForeignKey("dbo.ErlassfassungUebersetzung", "BearbeitetVonID", "dbo.AspNetUsers", "Id");
            AddForeignKey("dbo.ErlassfassungUebersetzung", "ErstelltVonID", "dbo.AspNetUsers", "Id");
            AddForeignKey("dbo.ForderungsversionUebersetzung", "BearbeitetVonID", "dbo.AspNetUsers", "Id");
            AddForeignKey("dbo.ForderungsversionUebersetzung", "ErstelltVonID", "dbo.AspNetUsers", "Id");
            AddForeignKey("dbo.RechtsbereichUebersetzung", "BearbeitetVonID", "dbo.AspNetUsers", "Id");
            AddForeignKey("dbo.RechtsbereichUebersetzung", "ErstelltVonID", "dbo.AspNetUsers", "Id");
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.RechtsbereichUebersetzung", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.RechtsbereichUebersetzung", "BearbeitetVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.ForderungsversionUebersetzung", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.ForderungsversionUebersetzung", "BearbeitetVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.ErlassfassungUebersetzung", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.ErlassfassungUebersetzung", "BearbeitetVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.ErlassUebersetzung", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.ErlassUebersetzung", "BearbeitetVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.ErlasstypUebersetzung", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.ErlasstypUebersetzung", "BearbeitetVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.ThemenbereichUebersetzung", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.ThemenbereichUebersetzung", "BearbeitetVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.PflichtUebersetzung", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.PflichtUebersetzung", "BearbeitetVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.ObjektUebersetzung", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.ObjektUebersetzung", "BearbeitetVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.ObjektkategorieUebersetzung", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.ObjektkategorieUebersetzung", "BearbeitetVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.ArtikelUebersetzung", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.ArtikelUebersetzung", "BearbeitetVonID", "dbo.AspNetUsers");
            DropIndex("dbo.RechtsbereichUebersetzung", new[] { "BearbeitetVonID" });
            DropIndex("dbo.RechtsbereichUebersetzung", new[] { "ErstelltVonID" });
            DropIndex("dbo.ForderungsversionUebersetzung", new[] { "BearbeitetVonID" });
            DropIndex("dbo.ForderungsversionUebersetzung", new[] { "ErstelltVonID" });
            DropIndex("dbo.ErlassfassungUebersetzung", new[] { "BearbeitetVonID" });
            DropIndex("dbo.ErlassfassungUebersetzung", new[] { "ErstelltVonID" });
            DropIndex("dbo.ErlassUebersetzung", new[] { "BearbeitetVonID" });
            DropIndex("dbo.ErlassUebersetzung", new[] { "ErstelltVonID" });
            DropIndex("dbo.ErlasstypUebersetzung", new[] { "BearbeitetVonID" });
            DropIndex("dbo.ErlasstypUebersetzung", new[] { "ErstelltVonID" });
            DropIndex("dbo.ThemenbereichUebersetzung", new[] { "BearbeitetVonID" });
            DropIndex("dbo.ThemenbereichUebersetzung", new[] { "ErstelltVonID" });
            DropIndex("dbo.PflichtUebersetzung", new[] { "BearbeitetVonID" });
            DropIndex("dbo.PflichtUebersetzung", new[] { "ErstelltVonID" });
            DropIndex("dbo.ObjektUebersetzung", new[] { "BearbeitetVonID" });
            DropIndex("dbo.ObjektUebersetzung", new[] { "ErstelltVonID" });
            DropIndex("dbo.ObjektkategorieUebersetzung", new[] { "BearbeitetVonID" });
            DropIndex("dbo.ObjektkategorieUebersetzung", new[] { "ErstelltVonID" });
            DropIndex("dbo.ArtikelUebersetzung", new[] { "BearbeitetVonID" });
            DropIndex("dbo.ArtikelUebersetzung", new[] { "ErstelltVonID" });
            DropColumn("dbo.RechtsbereichUebersetzung", "BearbeitetAm");
            DropColumn("dbo.RechtsbereichUebersetzung", "ErstelltAm");
            DropColumn("dbo.RechtsbereichUebersetzung", "BearbeitetVonID");
            DropColumn("dbo.RechtsbereichUebersetzung", "ErstelltVonID");
            DropColumn("dbo.ForderungsversionUebersetzung", "BearbeitetAm");
            DropColumn("dbo.ForderungsversionUebersetzung", "ErstelltAm");
            DropColumn("dbo.ForderungsversionUebersetzung", "BearbeitetVonID");
            DropColumn("dbo.ForderungsversionUebersetzung", "ErstelltVonID");
            DropColumn("dbo.ErlassfassungUebersetzung", "BearbeitetAm");
            DropColumn("dbo.ErlassfassungUebersetzung", "ErstelltAm");
            DropColumn("dbo.ErlassfassungUebersetzung", "BearbeitetVonID");
            DropColumn("dbo.ErlassfassungUebersetzung", "ErstelltVonID");
            DropColumn("dbo.ErlassUebersetzung", "BearbeitetAm");
            DropColumn("dbo.ErlassUebersetzung", "ErstelltAm");
            DropColumn("dbo.ErlassUebersetzung", "BearbeitetVonID");
            DropColumn("dbo.ErlassUebersetzung", "ErstelltVonID");
            DropColumn("dbo.ErlasstypUebersetzung", "BearbeitetAm");
            DropColumn("dbo.ErlasstypUebersetzung", "ErstelltAm");
            DropColumn("dbo.ErlasstypUebersetzung", "BearbeitetVonID");
            DropColumn("dbo.ErlasstypUebersetzung", "ErstelltVonID");
            DropColumn("dbo.ThemenbereichUebersetzung", "BearbeitetAm");
            DropColumn("dbo.ThemenbereichUebersetzung", "ErstelltAm");
            DropColumn("dbo.ThemenbereichUebersetzung", "BearbeitetVonID");
            DropColumn("dbo.ThemenbereichUebersetzung", "ErstelltVonID");
            DropColumn("dbo.PflichtUebersetzung", "BearbeitetAm");
            DropColumn("dbo.PflichtUebersetzung", "ErstelltAm");
            DropColumn("dbo.PflichtUebersetzung", "BearbeitetVonID");
            DropColumn("dbo.PflichtUebersetzung", "ErstelltVonID");
            DropColumn("dbo.ObjektUebersetzung", "BearbeitetAm");
            DropColumn("dbo.ObjektUebersetzung", "ErstelltAm");
            DropColumn("dbo.ObjektUebersetzung", "BearbeitetVonID");
            DropColumn("dbo.ObjektUebersetzung", "ErstelltVonID");
            DropColumn("dbo.ObjektkategorieUebersetzung", "BearbeitetAm");
            DropColumn("dbo.ObjektkategorieUebersetzung", "ErstelltAm");
            DropColumn("dbo.ObjektkategorieUebersetzung", "BearbeitetVonID");
            DropColumn("dbo.ObjektkategorieUebersetzung", "ErstelltVonID");
            DropColumn("dbo.ArtikelUebersetzung", "BearbeitetAm");
            DropColumn("dbo.ArtikelUebersetzung", "ErstelltAm");
            DropColumn("dbo.ArtikelUebersetzung", "BearbeitetVonID");
            DropColumn("dbo.ArtikelUebersetzung", "ErstelltVonID");
        }
    }
}
