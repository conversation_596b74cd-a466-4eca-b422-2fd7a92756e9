namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class addVorgaengerIDToKundendokument : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.Kundendokument", "VorgaengerKundendokumentID", c => c.Int());
            DropColumn("dbo.Kundendokument", "IsInitialeVersion");
        }
        
        public override void Down()
        {
            AddColumn("dbo.Kundendokument", "IsInitialeVersion", c => c<PERSON>(nullable: false));
            DropColumn("dbo.Kundendokument", "VorgaengerKundendokumentID");
        }
    }
}
