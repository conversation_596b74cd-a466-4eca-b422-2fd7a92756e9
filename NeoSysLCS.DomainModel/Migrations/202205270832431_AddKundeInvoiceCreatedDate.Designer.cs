// <auto-generated />
namespace NeoSysLCS.DomainModel.Migrations
{
    using System.CodeDom.Compiler;
    using System.Data.Entity.Migrations;
    using System.Data.Entity.Migrations.Infrastructure;
    using System.Resources;
    
    [GeneratedCode("EntityFramework.Migrations", "6.1.3-40302")]
    public sealed partial class AddKundeInvoiceCreatedDate : IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(AddKundeInvoiceCreatedDate));
        
        string IMigrationMetadata.Id
        {
            get { return "202205270832431_AddKundeInvoiceCreatedDate"; }
        }
        
        string IMigrationMetadata.Source
        {
            get { return null; }
        }
        
        string IMigrationMetadata.Target
        {
            get { return Resources.GetString("Target"); }
        }
    }
}
