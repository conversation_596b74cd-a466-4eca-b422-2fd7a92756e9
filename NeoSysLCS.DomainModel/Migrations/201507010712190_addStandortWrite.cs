namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class addStandortWrite : DbMigration
    {
        public override void Up()
        {
            /*AddColumn("dbo.AspNetUsers", "Standort_StandortID", c => c.Int());
            AddColumn("dbo.Standort", "ApplicationUser_Id", c => c.String(maxLength: 128));
            CreateIndex("dbo.AspNetUsers", "Standort_StandortID");
            CreateIndex("dbo.Standort", "ApplicationUser_Id");
            AddForeignKey("dbo.AspNetUsers", "Standort_StandortID", "dbo.Standort", "StandortID");
            AddForeignKey("dbo.Standort", "ApplicationUser_Id", "dbo.AspNetUsers", "Id");*/
        }
        
        public override void Down()
        {
            /*DropForeignKey("dbo.Standort", "ApplicationUser_Id", "dbo.AspNetUsers");
            DropForeignKey("dbo.AspNetUsers", "Standort_StandortID", "dbo.Standort");
            DropIndex("dbo.Standort", new[] { "ApplicationUser_Id" });
            DropIndex("dbo.AspNetUsers", new[] { "Standort_StandortID" });
            DropColumn("dbo.Standort", "ApplicationUser_Id");
            DropColumn("dbo.AspNetUsers", "Standort_StandortID");*/
        }
    }
}
