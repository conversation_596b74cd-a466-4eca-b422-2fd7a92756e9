namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class AddConsultationTranslation : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.Consultation", "TitleDE", c => c.String());
            AddColumn("dbo.Consultation", "TitleFR", c => c.String());
            AddColumn("dbo.Consultation", "TitleIT", c => c.String());
            AddColumn("dbo.Consultation", "TitleEN", c => c.String());
            DropColumn("dbo.Consultation", "Title");
        }
        
        public override void Down()
        {
            AddColumn("dbo.Consultation", "Title", c => c.String());
            DropColumn("dbo.Consultation", "TitleEN");
            DropColumn("dbo.Consultation", "TitleIT");
            DropColumn("dbo.Consultation", "TitleFR");
            DropColumn("dbo.Consultation", "TitleDE");
        }
    }
}
