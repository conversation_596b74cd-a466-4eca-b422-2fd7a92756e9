namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class removeKundendokumentIndividuelleForderungen : DbMigration
    {
        public override void Up()
        {
            DropForeignKey("dbo.KundendokumentIndividuelleForderung", "BearbeitetVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.KundendokumentIndividuelleForderung", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.KundendokumentIndividuelleForderung", "IndividuelleForderungID", "dbo.IndividuelleForderung");
            DropForeignKey("dbo.KundendokumentIndividuelleForderung", "KundeID", "dbo.Kunde");
            DropForeignKey("dbo.KundendokumentIndividuelleForderung", "KundendokumentID", "dbo.Kundendokument");
            DropIndex("dbo.KundendokumentIndividuelleForderung", new[] { "IndividuelleForderungID" });
            DropIndex("dbo.KundendokumentIndividuelleForderung", new[] { "KundendokumentID" });
            DropIndex("dbo.KundendokumentIndividuelleForderung", new[] { "KundeID" });
            DropIndex("dbo.KundendokumentIndividuelleForderung", new[] { "ErstelltVonID" });
            DropIndex("dbo.KundendokumentIndividuelleForderung", new[] { "BearbeitetVonID" });
            RenameColumn(table: "dbo.IndividuelleForderung", name: "StandortObjektID", newName: "StandortObjekt_StandortObjektID");
            RenameIndex(table: "dbo.IndividuelleForderung", name: "IX_StandortObjektID", newName: "IX_StandortObjekt_StandortObjektID");
            AddColumn("dbo.IndividuelleForderung", "StandortObjekt", c => c.String());
            AddColumn("dbo.IndividuelleForderung", "Erfuellung", c => c.Boolean(nullable: false));
            AddColumn("dbo.IndividuelleForderung", "Erfuellungszeitpunkt", c => c.DateTime());
            AddColumn("dbo.IndividuelleForderung", "ErfuelltDurch", c => c.String());
            AddColumn("dbo.IndividuelleForderung", "Verantwortlich", c => c.String());
            AddColumn("dbo.IndividuelleForderung", "Ablageort", c => c.String());
            DropTable("dbo.KundendokumentIndividuelleForderung");
        }
        
        public override void Down()
        {
            CreateTable(
                "dbo.KundendokumentIndividuelleForderung",
                c => new
                    {
                        KundendokumentIndividuelleForderungID = c.Int(nullable: false, identity: true),
                        Erfuellung = c.Boolean(nullable: false),
                        Erfuellungszeitpunkt = c.DateTime(),
                        ErfuelltDurch = c.String(),
                        Verantwortlich = c.String(),
                        Ablageort = c.String(),
                        QsFreigabe = c.Boolean(nullable: false),
                        Status = c.Int(nullable: false),
                        IndividuelleForderungID = c.Int(nullable: false),
                        KundendokumentID = c.Int(nullable: false),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.KundendokumentIndividuelleForderungID);
            
            DropColumn("dbo.IndividuelleForderung", "Ablageort");
            DropColumn("dbo.IndividuelleForderung", "Verantwortlich");
            DropColumn("dbo.IndividuelleForderung", "ErfuelltDurch");
            DropColumn("dbo.IndividuelleForderung", "Erfuellungszeitpunkt");
            DropColumn("dbo.IndividuelleForderung", "Erfuellung");
            DropColumn("dbo.IndividuelleForderung", "StandortObjekt");
            RenameIndex(table: "dbo.IndividuelleForderung", name: "IX_StandortObjekt_StandortObjektID", newName: "IX_StandortObjektID");
            RenameColumn(table: "dbo.IndividuelleForderung", name: "StandortObjekt_StandortObjektID", newName: "StandortObjektID");
            CreateIndex("dbo.KundendokumentIndividuelleForderung", "BearbeitetVonID");
            CreateIndex("dbo.KundendokumentIndividuelleForderung", "ErstelltVonID");
            CreateIndex("dbo.KundendokumentIndividuelleForderung", "KundeID");
            CreateIndex("dbo.KundendokumentIndividuelleForderung", "KundendokumentID");
            CreateIndex("dbo.KundendokumentIndividuelleForderung", "IndividuelleForderungID");
            AddForeignKey("dbo.KundendokumentIndividuelleForderung", "KundendokumentID", "dbo.Kundendokument", "KundendokumentID", cascadeDelete: true);
            AddForeignKey("dbo.KundendokumentIndividuelleForderung", "KundeID", "dbo.Kunde", "KundeID");
            AddForeignKey("dbo.KundendokumentIndividuelleForderung", "IndividuelleForderungID", "dbo.IndividuelleForderung", "IndividuelleForderungID");
            AddForeignKey("dbo.KundendokumentIndividuelleForderung", "ErstelltVonID", "dbo.AspNetUsers", "Id");
            AddForeignKey("dbo.KundendokumentIndividuelleForderung", "BearbeitetVonID", "dbo.AspNetUsers", "Id");
        }
    }
}
