// <auto-generated />
namespace NeoSysLCS.DomainModel.Migrations
{
    using System.CodeDom.Compiler;
    using System.Data.Entity.Migrations;
    using System.Data.Entity.Migrations.Infrastructure;
    using System.Resources;
    
    [GeneratedCode("EntityFramework.Migrations", "6.1.3-40302")]
    public sealed partial class addCustomErlassIDOnErlass : IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(addCustomErlassIDOnErlass));
        
        string IMigrationMetadata.Id
        {
            get { return "202308270648039_addCustomErlassIDOnErlass"; }
        }
        
        string IMigrationMetadata.Source
        {
            get { return null; }
        }
        
        string IMigrationMetadata.Target
        {
            get { return Resources.GetString("Target"); }
        }
    }
}
