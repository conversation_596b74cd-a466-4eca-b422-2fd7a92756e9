namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class AddKundeSummary : DbMigration
    {
        public override void Up()
        {
            CreateTable(
                "dbo.KundeSummary",
                c => new
                    {
                        KundeSummaryId = c.Int(nullable: false, identity: true),
                        KundeId = c.Int(nullable: false),
                        RechtsbereicheDE = c.String(),
                        RechtsbereicheFR = c.String(),
                        RechtsbereicheIT = c.String(),
                        RechtsbereicheEN = c.String(),
                        HerausgeberDE = c.String(),
                        HerausgeberFR = c.String(),
                        HerausgeberIT = c.String(),
                        HerausgeberEN = c.String(),
                        StandortobjekteDE = c.String(),
                        StandortobjekteFR = c.String(),
                        StandortobjekteIT = c.String(),
                        StandortobjekteEN = c.String(),
                        Sprachen = c.String(),
                        CreatedOn = c.DateTime(),
                    })
                .PrimaryKey(t => t.KundeSummaryId);
            
        }
        
        public override void Down()
        {
            DropTable("dbo.KundeSummary");
        }
    }
}
