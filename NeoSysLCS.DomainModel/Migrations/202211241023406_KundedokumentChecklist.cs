namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class KundedokumentChecklist : DbMigration
    {
        public override void Up()
        {
            CreateTable(
                "dbo.KundendokumentChecklist",
                c => new
                    {
                        KundendokumentChecklistID = c.Int(nullable: false, identity: true),
                        Status = c.Int(nullable: false),
                        KundendokumentID = c.Int(nullable: false),
                        ChecklistID = c.Int(nullable: false),
                        Translation = c.String(storeType: "xml"),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.KundendokumentChecklistID)
                .ForeignKey("dbo.AspNetUsers", t => t.BearbeitetVonID)
                .ForeignKey("dbo.Checklist", t => t.ChecklistID)
                .ForeignKey("dbo.AspNetUsers", t => t.ErstelltVonID)
                .ForeignKey("dbo.Kunde", t => t.KundeID)
                .ForeignKey("dbo.Kundendokument", t => t.KundendokumentID, cascadeDelete: true)
                .Index(t => t.KundendokumentID)
                .Index(t => t.ChecklistID)
                .Index(t => t.KundeID)
                .Index(t => t.ErstelltVonID)
                .Index(t => t.BearbeitetVonID);
            
            CreateTable(
                "dbo.KundendokumentChecklistQuestion",
                c => new
                    {
                        KundenDokumentChecklistQuestionID = c.Int(nullable: false, identity: true),
                        KundendokumentChecklistID = c.Int(nullable: false),
                        ChecklistQuestionID = c.Int(nullable: false),
                        SharedImage = c.String(),
                        Translation = c.String(storeType: "xml"),
                        Status = c.Int(nullable: false),
                        Answer = c.Int(nullable: false),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.KundenDokumentChecklistQuestionID)
                .ForeignKey("dbo.AspNetUsers", t => t.BearbeitetVonID)
                .ForeignKey("dbo.ChecklistQuestion", t => t.ChecklistQuestionID)
                .ForeignKey("dbo.AspNetUsers", t => t.ErstelltVonID)
                .ForeignKey("dbo.Kunde", t => t.KundeID)
                .ForeignKey("dbo.KundendokumentChecklist", t => t.KundendokumentChecklistID, cascadeDelete: true)
                .Index(t => t.KundendokumentChecklistID)
                .Index(t => t.ChecklistQuestionID)
                .Index(t => t.KundeID)
                .Index(t => t.ErstelltVonID)
                .Index(t => t.BearbeitetVonID);
            
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.KundendokumentChecklistQuestion", "KundendokumentChecklistID", "dbo.KundendokumentChecklist");
            DropForeignKey("dbo.KundendokumentChecklistQuestion", "KundeID", "dbo.Kunde");
            DropForeignKey("dbo.KundendokumentChecklistQuestion", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.KundendokumentChecklistQuestion", "ChecklistQuestionID", "dbo.ChecklistQuestion");
            DropForeignKey("dbo.KundendokumentChecklistQuestion", "BearbeitetVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.KundendokumentChecklist", "KundendokumentID", "dbo.Kundendokument");
            DropForeignKey("dbo.KundendokumentChecklist", "KundeID", "dbo.Kunde");
            DropForeignKey("dbo.KundendokumentChecklist", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.KundendokumentChecklist", "ChecklistID", "dbo.Checklist");
            DropForeignKey("dbo.KundendokumentChecklist", "BearbeitetVonID", "dbo.AspNetUsers");
            DropIndex("dbo.KundendokumentChecklistQuestion", new[] { "BearbeitetVonID" });
            DropIndex("dbo.KundendokumentChecklistQuestion", new[] { "ErstelltVonID" });
            DropIndex("dbo.KundendokumentChecklistQuestion", new[] { "KundeID" });
            DropIndex("dbo.KundendokumentChecklistQuestion", new[] { "ChecklistQuestionID" });
            DropIndex("dbo.KundendokumentChecklistQuestion", new[] { "KundendokumentChecklistID" });
            DropIndex("dbo.KundendokumentChecklist", new[] { "BearbeitetVonID" });
            DropIndex("dbo.KundendokumentChecklist", new[] { "ErstelltVonID" });
            DropIndex("dbo.KundendokumentChecklist", new[] { "KundeID" });
            DropIndex("dbo.KundendokumentChecklist", new[] { "ChecklistID" });
            DropIndex("dbo.KundendokumentChecklist", new[] { "KundendokumentID" });
            DropTable("dbo.KundendokumentChecklistQuestion");
            DropTable("dbo.KundendokumentChecklist");
        }
    }
}
