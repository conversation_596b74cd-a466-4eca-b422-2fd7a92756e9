namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class newDataModelLegalCompliance : DbMigration
    {
        public override void Up()
        {
            CreateTable(
                "dbo.LegalCompliance",
                c => new
                    {
                        LegalComplianceID = c.Int(nullable: false, identity: true),
                        ErstellungsDatum = c.DateTime(nullable: false),
                        StandortID = c.Int(nullable: false),
                        KundendokumentID = c.Int(nullable: false),
                        Total = c.Int(nullable: false),
                        Erfuellt = c.Int(nullable: false),
                        NichtErfuellt = c.Int(nullable: false),
                        InAbklaerung = c.Int(nullable: false),
                        NichtBearbeitet = c.Int(nullable: false),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.LegalComplianceID)
                .ForeignKey("dbo.AspNetUsers", t => t.BearbeitetVonID)
                .ForeignKey("dbo.AspNetUsers", t => t.ErstelltVonID)
                .Index(t => t.ErstelltVonID)
                .Index(t => t.BearbeitetVonID);
            
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.LegalCompliance", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.LegalCompliance", "BearbeitetVonID", "dbo.AspNetUsers");
            DropIndex("dbo.LegalCompliance", new[] { "BearbeitetVonID" });
            DropIndex("dbo.LegalCompliance", new[] { "ErstelltVonID" });
            DropTable("dbo.LegalCompliance");
        }
    }
}
