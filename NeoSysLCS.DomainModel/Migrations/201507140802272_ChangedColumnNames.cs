namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class ChangedColumnNames : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.KundendokumentForderungsversion", "LetztePruefungAm", c => c.DateTime());
            AddColumn("dbo.KundendokumentForderungsversion", "NaechstePruefungAm", c => c.DateTime());
            AddColumn("dbo.KundendokumentForderungsversion", "Pruefmethode", c => c.String());
            DropColumn("dbo.KundendokumentForderungsversion", "Erfuellungszeitpunkt");
            DropColumn("dbo.KundendokumentForderungsversion", "ErfuelltDurch");
        }
        
        public override void Down()
        {
            AddColumn("dbo.KundendokumentForderungsversion", "ErfuelltDurch", c => c.String());
            AddColumn("dbo.KundendokumentForderungsversion", "Erfuellungszeitpunkt", c => c.DateTime());
            DropColumn("dbo.KundendokumentForderungsversion", "Pruefmethode");
            DropColumn("dbo.KundendokumentForderungsversion", "NaechstePruefungAm");
            DropColumn("dbo.KundendokumentForderungsversion", "LetztePruefungAm");
        }
    }
}
