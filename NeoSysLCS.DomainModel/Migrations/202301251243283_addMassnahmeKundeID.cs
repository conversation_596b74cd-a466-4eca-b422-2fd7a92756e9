namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class addMassnahmeKundeID : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.Massnahme", "KundeID", c => c.Int());
            CreateIndex("dbo.Massnahme", "KundeID");
            AddForeignKey("dbo.Massnahme", "KundeID", "dbo.Kunde", "KundeID");
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.Massnahme", "KundeID", "dbo.Kunde");
            DropIndex("dbo.Massnahme", new[] { "KundeID" });
            DropColumn("dbo.Massnahme", "KundeID");
        }
    }
}
