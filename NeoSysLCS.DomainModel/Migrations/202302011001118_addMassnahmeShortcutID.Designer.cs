// <auto-generated />
namespace NeoSysLCS.DomainModel.Migrations
{
    using System.CodeDom.Compiler;
    using System.Data.Entity.Migrations;
    using System.Data.Entity.Migrations.Infrastructure;
    using System.Resources;
    
    [GeneratedCode("EntityFramework.Migrations", "6.1.3-40302")]
    public sealed partial class addMassnahmeShortcutID : IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(addMassnahmeShortcutID));
        
        string IMigrationMetadata.Id
        {
            get { return "202302011001118_addMassnahmeShortcutID"; }
        }
        
        string IMigrationMetadata.Source
        {
            get { return Resources.GetString("Source"); }
        }
        
        string IMigrationMetadata.Target
        {
            get { return Resources.GetString("Target"); }
        }
    }
}
