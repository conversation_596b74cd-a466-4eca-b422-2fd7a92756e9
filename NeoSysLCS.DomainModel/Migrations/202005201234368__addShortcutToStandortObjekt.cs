namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class _addShortcutToStandortObjekt : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.StandortObjekt", "ShortcutID", c => c.Int());
            CreateIndex("dbo.StandortObjekt", "ShortcutID");
            AddForeignKey("dbo.StandortObjekt", "ShortcutID", "dbo.Shortcut", "ShortcutID");
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.StandortObjekt", "ShortcutID", "dbo.Shortcut");
            DropIndex("dbo.StandortObjekt", new[] { "ShortcutID" });
            DropColumn("dbo.StandortObjekt", "ShortcutID");
        }
    }
}
