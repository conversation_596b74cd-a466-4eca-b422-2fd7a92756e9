namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class addedDateOnSprachen : DbMigration
    {
        public override void Up()
        {
            DropForeignKey("dbo.AspNetUsers", "SpracheID", "dbo.Sprache");
            DropIndex("dbo.AspNetUsers", new[] { "SpracheID" });
            AddColumn("dbo.AspNetUsers", "Sprache_SpracheID", c => c.Int());
            AddColumn("dbo.Sprache", "ErstelltVonID", c => c.String(maxLength: 128));
            AddColumn("dbo.Sprache", "BearbeitetVonID", c => c.String(maxLength: 128));
            AddColumn("dbo.Sprache", "ErstelltAm", c => c.DateTime());
            AddColumn("dbo.Sprache", "BearbeitetAm", c => c.DateTime());
            CreateIndex("dbo.AspNetUsers", "Sprache_SpracheID");
            CreateIndex("dbo.Sprache", "ErstelltVonID");
            CreateIndex("dbo.Sprache", "BearbeitetVonID");
            AddForeignKey("dbo.Sprache", "BearbeitetVonID", "dbo.AspNetUsers", "Id");
            AddForeignKey("dbo.Sprache", "ErstelltVonID", "dbo.AspNetUsers", "Id");
            AddForeignKey("dbo.AspNetUsers", "Sprache_SpracheID", "dbo.Sprache", "SpracheID");
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.AspNetUsers", "Sprache_SpracheID", "dbo.Sprache");
            DropForeignKey("dbo.Sprache", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.Sprache", "BearbeitetVonID", "dbo.AspNetUsers");
            DropIndex("dbo.Sprache", new[] { "BearbeitetVonID" });
            DropIndex("dbo.Sprache", new[] { "ErstelltVonID" });
            DropIndex("dbo.AspNetUsers", new[] { "Sprache_SpracheID" });
            DropColumn("dbo.Sprache", "BearbeitetAm");
            DropColumn("dbo.Sprache", "ErstelltAm");
            DropColumn("dbo.Sprache", "BearbeitetVonID");
            DropColumn("dbo.Sprache", "ErstelltVonID");
            DropColumn("dbo.AspNetUsers", "Sprache_SpracheID");
            CreateIndex("dbo.AspNetUsers", "SpracheID");
            AddForeignKey("dbo.AspNetUsers", "SpracheID", "dbo.Sprache", "SpracheID");
        }
    }
}
