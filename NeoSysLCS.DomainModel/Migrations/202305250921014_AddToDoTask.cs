namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class AddToDoTask : DbMigration
    {
        public override void Up()
        {
            CreateTable(
                "dbo.ToDoTask",
                c => new
                    {
                        Id = c.String(nullable: false, maxLength: 128),
                        Type = c.String(),
                        Message = c.String(),
                        CreatedAt = c.DateTime(nullable: false),
                        SyncedAt = c.DateTime(nullable: false),
                        Customer_KundeID = c.Int(),
                    })
                .PrimaryKey(t => t.Id)
                .ForeignKey("dbo.Kunde", t => t.Customer_KundeID)
                .Index(t => t.Customer_KundeID);
            
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.ToDoTask", "Customer_KundeID", "dbo.Kunde");
            DropIndex("dbo.ToDoTask", new[] { "Customer_KundeID" });
            DropTable("dbo.ToDoTask");
        }
    }
}
