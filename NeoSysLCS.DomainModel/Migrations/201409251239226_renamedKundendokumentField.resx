<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Target" xml:space="preserve">
    <value>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</value>
  </data>
  <data name="DefaultSchema" xml:space="preserve">
    <value>dbo</value>
  </data>
</root>