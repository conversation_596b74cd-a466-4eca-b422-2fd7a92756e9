namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class UpdateChecklists : DbMigration
    {
        public override void Up()
        {
            CreateTable(
                "dbo.ChecklistHeader",
                c => new
                    {
                        ChecklistHeaderID = c.Int(nullable: false, identity: true),
                        SuvaHeaderID = c.String(),
                        ChecklistID = c.Int(nullable: false),
                        Translation = c.String(storeType: "xml"),
                        OrderID = c.Int(nullable: false),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.ChecklistHeaderID)
                .ForeignKey("dbo.AspNetUsers", t => t.BearbeitetVonID)
                .ForeignKey("dbo.Checklist", t => t.ChecklistID)
                .ForeignKey("dbo.AspNetUsers", t => t.ErstelltVonID)
                .Index(t => t.ChecklistID)
                .Index(t => t.ErstelltVonID)
                .Index(t => t.BearbeitetVonID);
            
            AddColumn("dbo.ChecklistQuestion", "ChecklistHeaderTemplateID", c => c.String());
            AddColumn("dbo.ChecklistQuestion", "ChecklistType", c => c.String());
            AddColumn("dbo.ChecklistQuestion", "Numeration", c => c.String());
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.ChecklistHeader", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.ChecklistHeader", "ChecklistID", "dbo.Checklist");
            DropForeignKey("dbo.ChecklistHeader", "BearbeitetVonID", "dbo.AspNetUsers");
            DropIndex("dbo.ChecklistHeader", new[] { "BearbeitetVonID" });
            DropIndex("dbo.ChecklistHeader", new[] { "ErstelltVonID" });
            DropIndex("dbo.ChecklistHeader", new[] { "ChecklistID" });
            DropColumn("dbo.ChecklistQuestion", "Numeration");
            DropColumn("dbo.ChecklistQuestion", "ChecklistType");
            DropColumn("dbo.ChecklistQuestion", "ChecklistHeaderTemplateID");
            DropTable("dbo.ChecklistHeader");
        }
    }
}
