// <auto-generated />
namespace NeoSysLCS.DomainModel.Migrations
{
    using System.CodeDom.Compiler;
    using System.Data.Entity.Migrations;
    using System.Data.Entity.Migrations.Infrastructure;
    using System.Resources;
    
    [GeneratedCode("EntityFramework.Migrations", "6.1.1-30610")]
    public sealed partial class AddBeschreibungToKunde : IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(AddBeschreibungToKunde));
        
        string IMigrationMetadata.Id
        {
            get { return "201409191230180_AddBeschreibungToKunde"; }
        }
        
        string IMigrationMetadata.Source
        {
            get { return null; }
        }
        
        string IMigrationMetadata.Target
        {
            get { return Resources.GetString("Target"); }
        }
    }
}
