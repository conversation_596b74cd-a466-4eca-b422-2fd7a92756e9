// <auto-generated />
namespace NeoSysLCS.DomainModel.Migrations
{
    using System.CodeDom.Compiler;
    using System.Data.Entity.Migrations;
    using System.Data.Entity.Migrations.Infrastructure;
    using System.Resources;
    
    [GeneratedCode("EntityFramework.Migrations", "6.1.3-40302")]
    public sealed partial class _addErlassRechtsbereich : IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(_addErlassRechtsbereich));
        
        string IMigrationMetadata.Id
        {
            get { return "202012021242225__addErlassRechtsbereich"; }
        }
        
        string IMigrationMetadata.Source
        {
            get { return null; }
        }
        
        string IMigrationMetadata.Target
        {
            get { return Resources.GetString("Target"); }
        }
    }
}
