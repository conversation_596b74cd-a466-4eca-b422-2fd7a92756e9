namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class ExtendChecklistWithBaseModel : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.Checklist", "ErstelltVonID", c => c.String(maxLength: 128));
            AddColumn("dbo.Checklist", "BearbeitetVonID", c => c.String(maxLength: 128));
            AddColumn("dbo.ChecklistQuestion", "ErstelltVonID", c => c.String(maxLength: 128));
            AddColumn("dbo.ChecklistQuestion", "BearbeitetVonID", c => c.String(maxLength: 128));
            CreateIndex("dbo.Checklist", "ErstelltVonID");
            CreateIndex("dbo.Checklist", "BearbeitetVonID");
            CreateIndex("dbo.ChecklistQuestion", "ErstelltVonID");
            CreateIndex("dbo.ChecklistQuestion", "BearbeitetVonID");
            AddForeignKey("dbo.Checklist", "BearbeitetVonID", "dbo.AspNetUsers", "Id");
            AddForeignKey("dbo.Checklist", "ErstelltVonID", "dbo.AspNetUsers", "Id");
            AddForeignKey("dbo.ChecklistQuestion", "BearbeitetVonID", "dbo.AspNetUsers", "Id");
            AddForeignKey("dbo.ChecklistQuestion", "ErstelltVonID", "dbo.AspNetUsers", "Id");
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.ChecklistQuestion", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.ChecklistQuestion", "BearbeitetVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.Checklist", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.Checklist", "BearbeitetVonID", "dbo.AspNetUsers");
            DropIndex("dbo.ChecklistQuestion", new[] { "BearbeitetVonID" });
            DropIndex("dbo.ChecklistQuestion", new[] { "ErstelltVonID" });
            DropIndex("dbo.Checklist", new[] { "BearbeitetVonID" });
            DropIndex("dbo.Checklist", new[] { "ErstelltVonID" });
            DropColumn("dbo.ChecklistQuestion", "BearbeitetVonID");
            DropColumn("dbo.ChecklistQuestion", "ErstelltVonID");
            DropColumn("dbo.Checklist", "BearbeitetVonID");
            DropColumn("dbo.Checklist", "ErstelltVonID");
        }
    }
}
