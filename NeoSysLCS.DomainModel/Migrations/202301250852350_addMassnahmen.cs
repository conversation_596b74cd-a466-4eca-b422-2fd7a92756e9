namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class addMassnahmen : DbMigration
    {
        public override void Up()
        {
            CreateTable(
                "dbo.Massnahme",
                c => new
                    {
                        MassnahmeID = c.Int(nullable: false, identity: true),
                        Betreff = c.String(),
                        Verantwortlich = c.String(),
                        Status = c.Int(nullable: false),
                        Termin = c.DateTime(),
                        Uebersetzung = c.String(storeType: "xml"),
                        StandortID = c.Int(nullable: false),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.MassnahmeID)
                .ForeignKey("dbo.AspNetUsers", t => t.BearbeitetVonID)
                .ForeignKey("dbo.AspNetUsers", t => t.ErstelltVonID)
                .ForeignKey("dbo.Standort", t => t.StandortID)
                .Index(t => t.StandortID)
                .Index(t => t.ErstelltVonID)
                .Index(t => t.BearbeitetVonID);
            
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.Massnahme", "StandortID", "dbo.Standort");
            DropForeignKey("dbo.Massnahme", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.Massnahme", "BearbeitetVonID", "dbo.AspNetUsers");
            DropIndex("dbo.Massnahme", new[] { "BearbeitetVonID" });
            DropIndex("dbo.Massnahme", new[] { "ErstelltVonID" });
            DropIndex("dbo.Massnahme", new[] { "StandortID" });
            DropTable("dbo.Massnahme");
        }
    }
}
