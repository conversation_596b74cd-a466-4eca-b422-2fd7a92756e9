namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class suvaChecklist : DbMigration
    {
        public override void Up()
        {
            CreateTable(
                "dbo.Checklist",
                c => new
                    {
                        ChecklistID = c.Int(nullable: false, identity: true),
                        SuvaChecklistID = c.String(),
                        SrNummer = c.String(),
                        ErlassID = c.Int(nullable: false),
                        Translation = c.String(storeType: "xml"),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.ChecklistID)
                .ForeignKey("dbo.Erlass", t => t.ErlassID)
                .Index(t => t.ErlassID);
            
            CreateTable(
                "dbo.ChecklistQuestion",
                c => new
                    {
                        ChecklistQuestionID = c.Int(nullable: false, identity: true),
                        SuvaQuestionID = c.String(),
                        ChecklistID = c.Int(nullable: false),
                        SharedImage = c.String(),
                        Translation = c.String(storeType: "xml"),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.ChecklistQuestionID)
                .ForeignKey("dbo.Checklist", t => t.ChecklistID)
                .Index(t => t.ChecklistID);
            
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.ChecklistQuestion", "ChecklistID", "dbo.Checklist");
            DropForeignKey("dbo.Checklist", "ErlassID", "dbo.Erlass");
            DropIndex("dbo.ChecklistQuestion", new[] { "ChecklistID" });
            DropIndex("dbo.Checklist", new[] { "ErlassID" });
            DropTable("dbo.ChecklistQuestion");
            DropTable("dbo.Checklist");
        }
    }
}
