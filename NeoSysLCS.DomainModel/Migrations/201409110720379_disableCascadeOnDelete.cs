namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class disableCascadeOnDelete : DbMigration
    {
        public override void Up()
        {
            DropForeignKey("dbo.AspNetUserRoles", "RoleId", "dbo.AspNetRoles");
            DropForeignKey("dbo.AspNetUserClaims", "UserId", "dbo.AspNetUsers");
            DropForeignKey("dbo.AspNetUserLogins", "UserId", "dbo.AspNetUsers");
            DropForeignKey("dbo.AspNetUserRoles", "UserId", "dbo.AspNetUsers");
            DropForeignKey("dbo.Kontakt", "KundeID", "dbo.Kunde");
            DropForeignKey("dbo.Standort", "KundeID", "dbo.Kunde");
            DropForeignKey("dbo.RechtsbereichUebersetzung", "RechtsbereichID", "dbo.Rechtsbereich");
            DropForeignKey("dbo.Forderungsversion", "ErlassfassungID", "dbo.Erlassfassung");
            DropForeignKey("dbo.Forderungsversion", "ForderungID", "dbo.Forderung");
            DropForeignKey("dbo.ForderungsversionUebersetzung", "ForderungsversionID", "dbo.Forderungsversion");
            DropForeignKey("dbo.Erlassfassung", "ErlassID", "dbo.Erlass");
            DropForeignKey("dbo.ErlassfassungUebersetzung", "ErlassfassungID", "dbo.Erlassfassung");
            DropForeignKey("dbo.Artikel", "ErlassID", "dbo.Erlass");
            DropForeignKey("dbo.ErlassUebersetzung", "ErlassID", "dbo.Erlass");
            DropForeignKey("dbo.Erlass", "HerausgeberId", "dbo.Herausgeber");
            DropForeignKey("dbo.ArtikelUebersetzung", "ArtikelID", "dbo.Artikel");
            DropForeignKey("dbo.ArtikelUebersetzung", "SpracheID", "dbo.Sprache");
            DropForeignKey("dbo.Kundendokument", "StandortID", "dbo.Standort");
            DropForeignKey("dbo.KundendokumentIndividuelleForderung", "IndividuelleForderungID", "dbo.IndividuelleForderung");
            DropForeignKey("dbo.KundendokumentIndividuelleForderung", "KundendokumentID", "dbo.Kundendokument");
            DropForeignKey("dbo.PflichtUebersetzung", "PflichtID", "dbo.Pflicht");
            DropForeignKey("dbo.Objekt", "ObjektkategorieID", "dbo.Objektkategorie");
            DropForeignKey("dbo.ObjektUebersetzung", "ObjektID", "dbo.Objekt");
            DropForeignKey("dbo.ObjektkategorieUebersetzung", "ObjektkategorieID", "dbo.Objektkategorie");
            DropForeignKey("dbo.ObjektkategorieUebersetzung", "SpracheID", "dbo.Sprache");
            DropForeignKey("dbo.ObjektUebersetzung", "SpracheID", "dbo.Sprache");
            DropForeignKey("dbo.PflichtUebersetzung", "SpracheID", "dbo.Sprache");
            DropForeignKey("dbo.ThemenbereichUebersetzung", "ThemenbereichID", "dbo.Themenbereich");
            DropForeignKey("dbo.ThemenbereichUebersetzung", "SpracheID", "dbo.Sprache");
            DropForeignKey("dbo.ErlasstypUebersetzung", "ErlasstypID", "dbo.Erlasstyp");
            DropForeignKey("dbo.ErlasstypUebersetzung", "SpracheID", "dbo.Sprache");
            DropForeignKey("dbo.ErlassUebersetzung", "SpracheID", "dbo.Sprache");
            DropForeignKey("dbo.ErlassfassungUebersetzung", "SpracheID", "dbo.Sprache");
            DropForeignKey("dbo.ForderungsversionUebersetzung", "SpracheID", "dbo.Sprache");
            DropForeignKey("dbo.RechtsbereichUebersetzung", "SpracheID", "dbo.Sprache");
            DropIndex("dbo.KundendokumentForderungsversion", new[] { "KundendokumentID" });
            DropIndex("dbo.KundendokumentForderungsversion", new[] { "ForderungsversionID" });
            DropIndex("dbo.KundendokumentForderungsversion", new[] { "StandortObjektID" });
            DropIndex("dbo.KundendokumentPflicht", new[] { "KundendokumentID" });
            DropIndex("dbo.KundendokumentPflicht", new[] { "PflichtID" });
            DropIndex("dbo.KundendokumentPflicht", new[] { "StandortObjektID" });
            AddColumn("dbo.KundendokumentForderungsversion", "Relevant", c => c.Boolean(nullable: false));
            AddColumn("dbo.KundendokumentPflicht", "Relevant", c => c.Boolean(nullable: false));
            AlterColumn("dbo.KundendokumentForderungsversion", "KundendokumentID", c => c.Int(nullable: false));
            AlterColumn("dbo.KundendokumentForderungsversion", "ForderungsversionID", c => c.Int(nullable: false));
            AlterColumn("dbo.KundendokumentForderungsversion", "StandortObjektID", c => c.Int(nullable: false));
            AlterColumn("dbo.KundendokumentPflicht", "KundendokumentID", c => c.Int(nullable: false));
            AlterColumn("dbo.KundendokumentPflicht", "PflichtID", c => c.Int(nullable: false));
            AlterColumn("dbo.KundendokumentPflicht", "StandortObjektID", c => c.Int(nullable: false));
            CreateIndex("dbo.KundendokumentForderungsversion", "KundendokumentID");
            CreateIndex("dbo.KundendokumentForderungsversion", "ForderungsversionID");
            CreateIndex("dbo.KundendokumentForderungsversion", "StandortObjektID");
            CreateIndex("dbo.KundendokumentPflicht", "KundendokumentID");
            CreateIndex("dbo.KundendokumentPflicht", "PflichtID");
            CreateIndex("dbo.KundendokumentPflicht", "StandortObjektID");
            AddForeignKey("dbo.AspNetUserRoles", "RoleId", "dbo.AspNetRoles", "Id");
            AddForeignKey("dbo.AspNetUserClaims", "UserId", "dbo.AspNetUsers", "Id");
            AddForeignKey("dbo.AspNetUserLogins", "UserId", "dbo.AspNetUsers", "Id");
            AddForeignKey("dbo.AspNetUserRoles", "UserId", "dbo.AspNetUsers", "Id");
            AddForeignKey("dbo.Kontakt", "KundeID", "dbo.Kunde", "KundeID");
            AddForeignKey("dbo.Standort", "KundeID", "dbo.Kunde", "KundeID");
            AddForeignKey("dbo.RechtsbereichUebersetzung", "RechtsbereichID", "dbo.Rechtsbereich", "RechtsbereichID");
            AddForeignKey("dbo.Forderungsversion", "ErlassfassungID", "dbo.Erlassfassung", "ErlassfassungID");
            AddForeignKey("dbo.Forderungsversion", "ForderungID", "dbo.Forderung", "ForderungID");
            AddForeignKey("dbo.ForderungsversionUebersetzung", "ForderungsversionID", "dbo.Forderungsversion", "ForderungsversionID");
            AddForeignKey("dbo.Erlassfassung", "ErlassID", "dbo.Erlass", "ErlassID");
            AddForeignKey("dbo.ErlassfassungUebersetzung", "ErlassfassungID", "dbo.Erlassfassung", "ErlassfassungID");
            AddForeignKey("dbo.Artikel", "ErlassID", "dbo.Erlass", "ErlassID");
            AddForeignKey("dbo.ErlassUebersetzung", "ErlassID", "dbo.Erlass", "ErlassID");
            AddForeignKey("dbo.Erlass", "HerausgeberId", "dbo.Herausgeber", "HerausgeberID");
            AddForeignKey("dbo.ArtikelUebersetzung", "ArtikelID", "dbo.Artikel", "ArtikelID");
            AddForeignKey("dbo.ArtikelUebersetzung", "SpracheID", "dbo.Sprache", "SpracheID");
            AddForeignKey("dbo.Kundendokument", "StandortID", "dbo.Standort", "StandortID");
            AddForeignKey("dbo.KundendokumentIndividuelleForderung", "IndividuelleForderungID", "dbo.IndividuelleForderung", "IndividuelleForderungID");
            AddForeignKey("dbo.KundendokumentIndividuelleForderung", "KundendokumentID", "dbo.Kundendokument", "KundendokumentID");
            AddForeignKey("dbo.PflichtUebersetzung", "PflichtID", "dbo.Pflicht", "PflichtID");
            AddForeignKey("dbo.Objekt", "ObjektkategorieID", "dbo.Objektkategorie", "ObjektkategorieID");
            AddForeignKey("dbo.ObjektUebersetzung", "ObjektID", "dbo.Objekt", "ObjektID");
            AddForeignKey("dbo.ObjektkategorieUebersetzung", "ObjektkategorieID", "dbo.Objektkategorie", "ObjektkategorieID");
            AddForeignKey("dbo.ObjektkategorieUebersetzung", "SpracheID", "dbo.Sprache", "SpracheID");
            AddForeignKey("dbo.ObjektUebersetzung", "SpracheID", "dbo.Sprache", "SpracheID");
            AddForeignKey("dbo.PflichtUebersetzung", "SpracheID", "dbo.Sprache", "SpracheID");
            AddForeignKey("dbo.ThemenbereichUebersetzung", "ThemenbereichID", "dbo.Themenbereich", "ThemenbereichID");
            AddForeignKey("dbo.ThemenbereichUebersetzung", "SpracheID", "dbo.Sprache", "SpracheID");
            AddForeignKey("dbo.ErlasstypUebersetzung", "ErlasstypID", "dbo.Erlasstyp", "ErlasstypID");
            AddForeignKey("dbo.ErlasstypUebersetzung", "SpracheID", "dbo.Sprache", "SpracheID");
            AddForeignKey("dbo.ErlassUebersetzung", "SpracheID", "dbo.Sprache", "SpracheID");
            AddForeignKey("dbo.ErlassfassungUebersetzung", "SpracheID", "dbo.Sprache", "SpracheID");
            AddForeignKey("dbo.ForderungsversionUebersetzung", "SpracheID", "dbo.Sprache", "SpracheID");
            AddForeignKey("dbo.RechtsbereichUebersetzung", "SpracheID", "dbo.Sprache", "SpracheID");
            DropColumn("dbo.KundendokumentForderungsversion", "NichtRelevant");
            DropColumn("dbo.KundendokumentPflicht", "NichtRelevant");
        }
        
        public override void Down()
        {
            AddColumn("dbo.KundendokumentPflicht", "NichtRelevant", c => c.Boolean(nullable: false));
            AddColumn("dbo.KundendokumentForderungsversion", "NichtRelevant", c => c.Boolean(nullable: false));
            DropForeignKey("dbo.RechtsbereichUebersetzung", "SpracheID", "dbo.Sprache");
            DropForeignKey("dbo.ForderungsversionUebersetzung", "SpracheID", "dbo.Sprache");
            DropForeignKey("dbo.ErlassfassungUebersetzung", "SpracheID", "dbo.Sprache");
            DropForeignKey("dbo.ErlassUebersetzung", "SpracheID", "dbo.Sprache");
            DropForeignKey("dbo.ErlasstypUebersetzung", "SpracheID", "dbo.Sprache");
            DropForeignKey("dbo.ErlasstypUebersetzung", "ErlasstypID", "dbo.Erlasstyp");
            DropForeignKey("dbo.ThemenbereichUebersetzung", "SpracheID", "dbo.Sprache");
            DropForeignKey("dbo.ThemenbereichUebersetzung", "ThemenbereichID", "dbo.Themenbereich");
            DropForeignKey("dbo.PflichtUebersetzung", "SpracheID", "dbo.Sprache");
            DropForeignKey("dbo.ObjektUebersetzung", "SpracheID", "dbo.Sprache");
            DropForeignKey("dbo.ObjektkategorieUebersetzung", "SpracheID", "dbo.Sprache");
            DropForeignKey("dbo.ObjektkategorieUebersetzung", "ObjektkategorieID", "dbo.Objektkategorie");
            DropForeignKey("dbo.ObjektUebersetzung", "ObjektID", "dbo.Objekt");
            DropForeignKey("dbo.Objekt", "ObjektkategorieID", "dbo.Objektkategorie");
            DropForeignKey("dbo.PflichtUebersetzung", "PflichtID", "dbo.Pflicht");
            DropForeignKey("dbo.KundendokumentIndividuelleForderung", "KundendokumentID", "dbo.Kundendokument");
            DropForeignKey("dbo.KundendokumentIndividuelleForderung", "IndividuelleForderungID", "dbo.IndividuelleForderung");
            DropForeignKey("dbo.Kundendokument", "StandortID", "dbo.Standort");
            DropForeignKey("dbo.ArtikelUebersetzung", "SpracheID", "dbo.Sprache");
            DropForeignKey("dbo.ArtikelUebersetzung", "ArtikelID", "dbo.Artikel");
            DropForeignKey("dbo.Erlass", "HerausgeberId", "dbo.Herausgeber");
            DropForeignKey("dbo.ErlassUebersetzung", "ErlassID", "dbo.Erlass");
            DropForeignKey("dbo.Artikel", "ErlassID", "dbo.Erlass");
            DropForeignKey("dbo.ErlassfassungUebersetzung", "ErlassfassungID", "dbo.Erlassfassung");
            DropForeignKey("dbo.Erlassfassung", "ErlassID", "dbo.Erlass");
            DropForeignKey("dbo.ForderungsversionUebersetzung", "ForderungsversionID", "dbo.Forderungsversion");
            DropForeignKey("dbo.Forderungsversion", "ForderungID", "dbo.Forderung");
            DropForeignKey("dbo.Forderungsversion", "ErlassfassungID", "dbo.Erlassfassung");
            DropForeignKey("dbo.RechtsbereichUebersetzung", "RechtsbereichID", "dbo.Rechtsbereich");
            DropForeignKey("dbo.Standort", "KundeID", "dbo.Kunde");
            DropForeignKey("dbo.Kontakt", "KundeID", "dbo.Kunde");
            DropForeignKey("dbo.AspNetUserRoles", "UserId", "dbo.AspNetUsers");
            DropForeignKey("dbo.AspNetUserLogins", "UserId", "dbo.AspNetUsers");
            DropForeignKey("dbo.AspNetUserClaims", "UserId", "dbo.AspNetUsers");
            DropForeignKey("dbo.AspNetUserRoles", "RoleId", "dbo.AspNetRoles");
            DropIndex("dbo.KundendokumentPflicht", new[] { "StandortObjektID" });
            DropIndex("dbo.KundendokumentPflicht", new[] { "PflichtID" });
            DropIndex("dbo.KundendokumentPflicht", new[] { "KundendokumentID" });
            DropIndex("dbo.KundendokumentForderungsversion", new[] { "StandortObjektID" });
            DropIndex("dbo.KundendokumentForderungsversion", new[] { "ForderungsversionID" });
            DropIndex("dbo.KundendokumentForderungsversion", new[] { "KundendokumentID" });
            AlterColumn("dbo.KundendokumentPflicht", "StandortObjektID", c => c.Int());
            AlterColumn("dbo.KundendokumentPflicht", "PflichtID", c => c.Int());
            AlterColumn("dbo.KundendokumentPflicht", "KundendokumentID", c => c.Int());
            AlterColumn("dbo.KundendokumentForderungsversion", "StandortObjektID", c => c.Int());
            AlterColumn("dbo.KundendokumentForderungsversion", "ForderungsversionID", c => c.Int());
            AlterColumn("dbo.KundendokumentForderungsversion", "KundendokumentID", c => c.Int());
            DropColumn("dbo.KundendokumentPflicht", "Relevant");
            DropColumn("dbo.KundendokumentForderungsversion", "Relevant");
            CreateIndex("dbo.KundendokumentPflicht", "StandortObjektID");
            CreateIndex("dbo.KundendokumentPflicht", "PflichtID");
            CreateIndex("dbo.KundendokumentPflicht", "KundendokumentID");
            CreateIndex("dbo.KundendokumentForderungsversion", "StandortObjektID");
            CreateIndex("dbo.KundendokumentForderungsversion", "ForderungsversionID");
            CreateIndex("dbo.KundendokumentForderungsversion", "KundendokumentID");
            AddForeignKey("dbo.RechtsbereichUebersetzung", "SpracheID", "dbo.Sprache", "SpracheID", cascadeDelete: true);
            AddForeignKey("dbo.ForderungsversionUebersetzung", "SpracheID", "dbo.Sprache", "SpracheID", cascadeDelete: true);
            AddForeignKey("dbo.ErlassfassungUebersetzung", "SpracheID", "dbo.Sprache", "SpracheID", cascadeDelete: true);
            AddForeignKey("dbo.ErlassUebersetzung", "SpracheID", "dbo.Sprache", "SpracheID", cascadeDelete: true);
            AddForeignKey("dbo.ErlasstypUebersetzung", "SpracheID", "dbo.Sprache", "SpracheID", cascadeDelete: true);
            AddForeignKey("dbo.ErlasstypUebersetzung", "ErlasstypID", "dbo.Erlasstyp", "ErlasstypID", cascadeDelete: true);
            AddForeignKey("dbo.ThemenbereichUebersetzung", "SpracheID", "dbo.Sprache", "SpracheID", cascadeDelete: true);
            AddForeignKey("dbo.ThemenbereichUebersetzung", "ThemenbereichID", "dbo.Themenbereich", "ThemenbereichID", cascadeDelete: true);
            AddForeignKey("dbo.PflichtUebersetzung", "SpracheID", "dbo.Sprache", "SpracheID", cascadeDelete: true);
            AddForeignKey("dbo.ObjektUebersetzung", "SpracheID", "dbo.Sprache", "SpracheID", cascadeDelete: true);
            AddForeignKey("dbo.ObjektkategorieUebersetzung", "SpracheID", "dbo.Sprache", "SpracheID", cascadeDelete: true);
            AddForeignKey("dbo.ObjektkategorieUebersetzung", "ObjektkategorieID", "dbo.Objektkategorie", "ObjektkategorieID", cascadeDelete: true);
            AddForeignKey("dbo.ObjektUebersetzung", "ObjektID", "dbo.Objekt", "ObjektID", cascadeDelete: true);
            AddForeignKey("dbo.Objekt", "ObjektkategorieID", "dbo.Objektkategorie", "ObjektkategorieID", cascadeDelete: true);
            AddForeignKey("dbo.PflichtUebersetzung", "PflichtID", "dbo.Pflicht", "PflichtID", cascadeDelete: true);
            AddForeignKey("dbo.KundendokumentIndividuelleForderung", "KundendokumentID", "dbo.Kundendokument", "KundendokumentID", cascadeDelete: true);
            AddForeignKey("dbo.KundendokumentIndividuelleForderung", "IndividuelleForderungID", "dbo.IndividuelleForderung", "IndividuelleForderungID", cascadeDelete: true);
            AddForeignKey("dbo.Kundendokument", "StandortID", "dbo.Standort", "StandortID", cascadeDelete: true);
            AddForeignKey("dbo.ArtikelUebersetzung", "SpracheID", "dbo.Sprache", "SpracheID", cascadeDelete: true);
            AddForeignKey("dbo.ArtikelUebersetzung", "ArtikelID", "dbo.Artikel", "ArtikelID", cascadeDelete: true);
            AddForeignKey("dbo.Erlass", "HerausgeberId", "dbo.Herausgeber", "HerausgeberID", cascadeDelete: true);
            AddForeignKey("dbo.ErlassUebersetzung", "ErlassID", "dbo.Erlass", "ErlassID", cascadeDelete: true);
            AddForeignKey("dbo.Artikel", "ErlassID", "dbo.Erlass", "ErlassID", cascadeDelete: true);
            AddForeignKey("dbo.ErlassfassungUebersetzung", "ErlassfassungID", "dbo.Erlassfassung", "ErlassfassungID", cascadeDelete: true);
            AddForeignKey("dbo.Erlassfassung", "ErlassID", "dbo.Erlass", "ErlassID", cascadeDelete: true);
            AddForeignKey("dbo.ForderungsversionUebersetzung", "ForderungsversionID", "dbo.Forderungsversion", "ForderungsversionID", cascadeDelete: true);
            AddForeignKey("dbo.Forderungsversion", "ForderungID", "dbo.Forderung", "ForderungID", cascadeDelete: true);
            AddForeignKey("dbo.Forderungsversion", "ErlassfassungID", "dbo.Erlassfassung", "ErlassfassungID", cascadeDelete: true);
            AddForeignKey("dbo.RechtsbereichUebersetzung", "RechtsbereichID", "dbo.Rechtsbereich", "RechtsbereichID", cascadeDelete: true);
            AddForeignKey("dbo.Standort", "KundeID", "dbo.Kunde", "KundeID", cascadeDelete: true);
            AddForeignKey("dbo.Kontakt", "KundeID", "dbo.Kunde", "KundeID", cascadeDelete: true);
            AddForeignKey("dbo.AspNetUserRoles", "UserId", "dbo.AspNetUsers", "Id", cascadeDelete: true);
            AddForeignKey("dbo.AspNetUserLogins", "UserId", "dbo.AspNetUsers", "Id", cascadeDelete: true);
            AddForeignKey("dbo.AspNetUserClaims", "UserId", "dbo.AspNetUsers", "Id", cascadeDelete: true);
            AddForeignKey("dbo.AspNetUserRoles", "RoleId", "dbo.AspNetRoles", "Id", cascadeDelete: true);
        }
    }
}
