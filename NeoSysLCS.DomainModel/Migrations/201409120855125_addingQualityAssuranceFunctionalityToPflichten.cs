namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class addingQualityAssuranceFunctionalityToPflichten : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.Pflicht", "Freigabe", c => c.<PERSON>());
            AddColumn("dbo.Pflicht", "FreigabeVon", c => c.Int());
            AddColumn("dbo.Pflicht", "FreigabeAm", c => c.DateTime());
            AddColumn("dbo.Pflicht", "QsFreigabe", c => c.<PERSON>());
            AddColumn("dbo.Pflicht", "QsFreigabeVon", c => c.Int());
            AddColumn("dbo.Pflicht", "QsFreigabeAm", c => c.DateTime());
        }
        
        public override void Down()
        {
            DropColumn("dbo.Pflicht", "QsFreigabeAm");
            DropColumn("dbo.Pflicht", "QsFreigabeVon");
            DropColumn("dbo.Pflicht", "QsFreigabe");
            DropColumn("dbo.Pflicht", "FreigabeAm");
            DropColumn("dbo.Pflicht", "FreigabeVon");
            DropColumn("dbo.Pflicht", "Freigabe");
        }
    }
}
