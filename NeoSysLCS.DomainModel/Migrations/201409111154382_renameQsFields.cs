namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class renameQsFields : DbMigration
    {
        public override void Up()
        {
            RenameColumn("dbo.KundendokumentPflicht", "QSFreigabe", "QsFreigabe");
            RenameColumn("dbo.KundendokumentForderungsversion", "QSFreigabe", "QsFreigabe");
            RenameColumn("dbo.Kundendokument", "QSFreigabe", "QsFreigabe");
            RenameColumn("dbo.Kundendokument", "QSKommentar", "QsKommentar");
        }
        
        public override void Down()
        {
            RenameColumn("dbo.KundendokumentPflicht", "QsFreigabe", "QSFreigabe");
            RenameColumn("dbo.KundendokumentForderungsversion", "QsFreigabe", "QSFreigabe");
            RenameColumn("dbo.Kundendokument", "QsFreigabe", "QSFreigabe");
            RenameColumn("dbo.Kundendokument", "QsKommentar", "QSKommentar");
        }
    }
}
