namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class forderungsversionQsFreigabeFix : DbMigration
    {
        public override void Up()
        {
            AlterColumn("dbo.Forderungsversion", "FreigabeVon", c => c.String(maxLength: 128));
            AlterColumn("dbo.Forderungsversion", "QsFreigabeVon", c => c.String(maxLength: 128));
        }
        
        public override void Down()
        {
            AlterColumn("dbo.Forderungsversion", "QsFreigabeVon", c => c.Int());
            AlterColumn("dbo.Forderungsversion", "FreigabeVon", c => c.Int());
        }
    }
}
