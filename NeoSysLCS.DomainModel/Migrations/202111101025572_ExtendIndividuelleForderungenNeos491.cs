namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class ExtendIndividuelleForderungenNeos491 : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.IndividuelleForderung", "KundendokumentID", c => c.Int());
            CreateIndex("dbo.IndividuelleForderung", "KundendokumentID");
            AddForeignKey("dbo.IndividuelleForderung", "KundendokumentID", "dbo.Kundendokument", "KundendokumentID");
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.IndividuelleForderung", "KundendokumentID", "dbo.Kundendokument");
            DropIndex("dbo.IndividuelleForderung", new[] { "KundendokumentID" });
            DropColumn("dbo.IndividuelleForderung", "KundendokumentID");
        }
    }
}
