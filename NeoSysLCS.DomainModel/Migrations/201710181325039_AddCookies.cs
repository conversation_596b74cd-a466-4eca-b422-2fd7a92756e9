namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class AddCookies : DbMigration
    {
        public override void Up()
        {
            CreateTable(
                "dbo.GridViewCookie",
                c => new
                    {
                        GridViewCookieID = c.String(nullable: false, maxLength: 128),
                        ForderungenLayout = c.String(),
                        ErlassLayout = c.String(),
                        RemovedForderungenLayout = c.String(),
                        StandortObjektLayout = c.String(),
                    })
                .PrimaryKey(t => t.GridViewCookieID);
            
        }
        
        public override void Down()
        {
            DropTable("dbo.GridViewCookie");
        }
    }
}
