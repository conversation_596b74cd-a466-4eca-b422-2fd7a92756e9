namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class makeErlassfassungMandatoryForPflicht : DbMigration
    {
        public override void Up()
        {
            DropIndex("dbo.Pflicht", new[] { "ErlassfassungID" });
            AlterColumn("dbo.Pflicht", "ErlassfassungID", c => c.Int(nullable: false));
            CreateIndex("dbo.Pflicht", "ErlassfassungID");
        }
        
        public override void Down()
        {
            DropIndex("dbo.Pflicht", new[] { "ErlassfassungID" });
            AlterColumn("dbo.Pflicht", "ErlassfassungID", c => c.Int());
            CreateIndex("dbo.Pflicht", "ErlassfassungID");
        }
    }
}
