namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class AddEvaluations : DbMigration
    {
        public override void Up()
        {
            CreateTable(
                "dbo.Evaluation",
                c => new
                    {
                        EvaluationID = c.String(nullable: false, maxLength: 128),
                        ProjectManagerID = c.String(maxLength: 128),
                        Month = c.String(),
                        Year = c.String(),
                        EvaluationType = c.Int(nullable: false),
                        EvaluationValue = c.Int(nullable: false),
                        CreatedOn = c.DateTime(),
                    })
                .PrimaryKey(t => t.EvaluationID)
                .ForeignKey("dbo.AspNetUsers", t => t.ProjectManagerID)
                .Index(t => t.ProjectManagerID);
            
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.Evaluation", "ProjectManagerID", "dbo.AspNetUsers");
            DropIndex("dbo.Evaluation", new[] { "ProjectManagerID" });
            DropTable("dbo.Evaluation");
        }
    }
}
