namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Collections.Generic;
    using System.Data.Entity.Infrastructure.Annotations;
    using System.Data.Entity.Migrations;
    
    public partial class addErlassAufhebungFieldAndRemoveDynamicFilterAnnotations : DbMigration
    {
        public override void Up()
        {
            AlterTableAnnotations(
                "dbo.Kontakt",
                c => new
                    {
                        KontaktID = c.Int(nullable: false, identity: true),
                        Nachname = c.String(),
                        Vorname = c.String(),
                        EMail = c.String(),
                        Funktion = c.String(),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_KontaktFilter",
                        new AnnotationValues(oldValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition", newValue: null)
                    },
                });
            
            AlterTableAnnotations(
                "dbo.Standort",
                c => new
                    {
                        StandortID = c.Int(nullable: false, identity: true),
                        Name = c.String(),
                        InterneNotiz = c.String(),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_StandortFilter",
                        new AnnotationValues(oldValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition", newValue: null)
                    },
                });
            
            AlterTableAnnotations(
                "dbo.IndividuelleForderung",
                c => new
                    {
                        IndividuelleForderungID = c.Int(nullable: false, identity: true),
                        Beschreibung = c.String(),
                        Kommentar = c.String(),
                        GueltigVon = c.DateTime(nullable: false),
                        GueltigBis = c.DateTime(),
                        StandortID = c.Int(),
                        ThemenbereichID = c.Int(),
                        StandortObjektID = c.Int(),
                        KundenbezugID = c.Int(),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_IndividuelleForderungen",
                        new AnnotationValues(oldValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition", newValue: null)
                    },
                });
            
            AlterTableAnnotations(
                "dbo.Kundenbezug",
                c => new
                    {
                        KundenbezugID = c.Int(nullable: false, identity: true),
                        Name = c.String(),
                        StandortID = c.Int(),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_KundenbezugFilter",
                        new AnnotationValues(oldValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition", newValue: null)
                    },
                });
            
            AlterTableAnnotations(
                "dbo.KundendokumentForderungsversion",
                c => new
                    {
                        KundendokumentForderungsversionID = c.Int(nullable: false, identity: true),
                        Kommentar = c.String(),
                        Erfuellung = c.Boolean(nullable: false),
                        Erfuellungszeitpunkt = c.DateTime(),
                        ErfuelltDurch = c.String(),
                        Verantwortlich = c.String(),
                        Relevant = c.Boolean(nullable: false),
                        Ablageort = c.String(),
                        QsFreigabe = c.Boolean(nullable: false),
                        Spalte1 = c.String(),
                        Spalte2 = c.String(),
                        Spalte3 = c.String(),
                        Spalte4 = c.String(),
                        Spalte5 = c.String(),
                        Spalte6 = c.String(),
                        Spalte7 = c.String(),
                        Spalte8 = c.String(),
                        Spalte9 = c.String(),
                        Spalte10 = c.String(),
                        Status = c.Int(nullable: false),
                        KundendokumentID = c.Int(nullable: false),
                        ForderungsversionID = c.Int(nullable: false),
                        StandortObjektID = c.Int(nullable: false),
                        KundenbezugID = c.Int(),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_KundendokumentForderungsversionFilter",
                        new AnnotationValues(oldValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition", newValue: null)
                    },
                });
            
            AlterTableAnnotations(
                "dbo.Kundendokument",
                c => new
                    {
                        KundendokumentID = c.Int(nullable: false, identity: true),
                        PublizierenAm = c.DateTime(),
                        QsKommentar = c.String(),
                        Status = c.Int(nullable: false),
                        IsInitialeVersion = c.Boolean(nullable: false),
                        StandortID = c.Int(nullable: false),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_KundendokumentFilter",
                        new AnnotationValues(oldValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition", newValue: null)
                    },
                });
            
            AlterTableAnnotations(
                "dbo.KundendokumentErlassfassung",
                c => new
                    {
                        KundendokumentErlassfassungID = c.Int(nullable: false, identity: true),
                        Betroffen = c.Boolean(nullable: false),
                        QsFreigabe = c.Boolean(nullable: false),
                        Status = c.Int(nullable: false),
                        KundendokumentID = c.Int(nullable: false),
                        ErlassfassungID = c.Int(nullable: false),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_KundendokumentErlassfassungen",
                        new AnnotationValues(oldValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition", newValue: null)
                    },
                });
            
            AlterTableAnnotations(
                "dbo.KundendokumentIndividuelleForderung",
                c => new
                    {
                        KundendokumentIndividuelleForderungID = c.Int(nullable: false, identity: true),
                        Erfuellung = c.Boolean(nullable: false),
                        Erfuellungszeitpunkt = c.DateTime(),
                        ErfuelltDurch = c.String(),
                        Verantwortlich = c.String(),
                        Ablageort = c.String(),
                        QsFreigabe = c.Boolean(nullable: false),
                        Status = c.Int(nullable: false),
                        IndividuelleForderungID = c.Int(nullable: false),
                        KundendokumentID = c.Int(nullable: false),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_KundendokumentIndividuelleForderung",
                        new AnnotationValues(oldValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition", newValue: null)
                    },
                });
            
            AlterTableAnnotations(
                "dbo.KundendokumentPflicht",
                c => new
                    {
                        KundendokumentPflichtID = c.Int(nullable: false, identity: true),
                        Kommentar = c.String(),
                        Erfuellung = c.Boolean(nullable: false),
                        Erfuellungszeitpunkt = c.DateTime(),
                        ErfuelltDurch = c.String(),
                        Verantwortlich = c.String(),
                        Relevant = c.Boolean(nullable: false),
                        Ablageort = c.String(),
                        QsFreigabe = c.Boolean(nullable: false),
                        Status = c.Int(nullable: false),
                        KundendokumentID = c.Int(nullable: false),
                        PflichtID = c.Int(nullable: false),
                        StandortObjektID = c.Int(nullable: false),
                        KundenbezugID = c.Int(),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_KundendokumentPflicht",
                        new AnnotationValues(oldValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition", newValue: null)
                    },
                });
            
            AlterTableAnnotations(
                "dbo.StandortObjekt",
                c => new
                    {
                        StandortObjektID = c.Int(nullable: false, identity: true),
                        Name = c.String(),
                        Beschreibung = c.String(),
                        StandortID = c.Int(),
                        ObjektID = c.Int(),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_StandortObjektFilter",
                        new AnnotationValues(oldValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition", newValue: null)
                    },
                });
            
            AddColumn("dbo.Erlass", "Aufhebung", c => c.DateTime());
        }
        
        public override void Down()
        {
            DropColumn("dbo.Erlass", "Aufhebung");
            AlterTableAnnotations(
                "dbo.StandortObjekt",
                c => new
                    {
                        StandortObjektID = c.Int(nullable: false, identity: true),
                        Name = c.String(),
                        Beschreibung = c.String(),
                        StandortID = c.Int(),
                        ObjektID = c.Int(),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_StandortObjektFilter",
                        new AnnotationValues(oldValue: null, newValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition")
                    },
                });
            
            AlterTableAnnotations(
                "dbo.KundendokumentPflicht",
                c => new
                    {
                        KundendokumentPflichtID = c.Int(nullable: false, identity: true),
                        Kommentar = c.String(),
                        Erfuellung = c.Boolean(nullable: false),
                        Erfuellungszeitpunkt = c.DateTime(),
                        ErfuelltDurch = c.String(),
                        Verantwortlich = c.String(),
                        Relevant = c.Boolean(nullable: false),
                        Ablageort = c.String(),
                        QsFreigabe = c.Boolean(nullable: false),
                        Status = c.Int(nullable: false),
                        KundendokumentID = c.Int(nullable: false),
                        PflichtID = c.Int(nullable: false),
                        StandortObjektID = c.Int(nullable: false),
                        KundenbezugID = c.Int(),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_KundendokumentPflicht",
                        new AnnotationValues(oldValue: null, newValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition")
                    },
                });
            
            AlterTableAnnotations(
                "dbo.KundendokumentIndividuelleForderung",
                c => new
                    {
                        KundendokumentIndividuelleForderungID = c.Int(nullable: false, identity: true),
                        Erfuellung = c.Boolean(nullable: false),
                        Erfuellungszeitpunkt = c.DateTime(),
                        ErfuelltDurch = c.String(),
                        Verantwortlich = c.String(),
                        Ablageort = c.String(),
                        QsFreigabe = c.Boolean(nullable: false),
                        Status = c.Int(nullable: false),
                        IndividuelleForderungID = c.Int(nullable: false),
                        KundendokumentID = c.Int(nullable: false),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_KundendokumentIndividuelleForderung",
                        new AnnotationValues(oldValue: null, newValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition")
                    },
                });
            
            AlterTableAnnotations(
                "dbo.KundendokumentErlassfassung",
                c => new
                    {
                        KundendokumentErlassfassungID = c.Int(nullable: false, identity: true),
                        Betroffen = c.Boolean(nullable: false),
                        QsFreigabe = c.Boolean(nullable: false),
                        Status = c.Int(nullable: false),
                        KundendokumentID = c.Int(nullable: false),
                        ErlassfassungID = c.Int(nullable: false),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_KundendokumentErlassfassungen",
                        new AnnotationValues(oldValue: null, newValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition")
                    },
                });
            
            AlterTableAnnotations(
                "dbo.Kundendokument",
                c => new
                    {
                        KundendokumentID = c.Int(nullable: false, identity: true),
                        PublizierenAm = c.DateTime(),
                        QsKommentar = c.String(),
                        Status = c.Int(nullable: false),
                        IsInitialeVersion = c.Boolean(nullable: false),
                        StandortID = c.Int(nullable: false),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_KundendokumentFilter",
                        new AnnotationValues(oldValue: null, newValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition")
                    },
                });
            
            AlterTableAnnotations(
                "dbo.KundendokumentForderungsversion",
                c => new
                    {
                        KundendokumentForderungsversionID = c.Int(nullable: false, identity: true),
                        Kommentar = c.String(),
                        Erfuellung = c.Boolean(nullable: false),
                        Erfuellungszeitpunkt = c.DateTime(),
                        ErfuelltDurch = c.String(),
                        Verantwortlich = c.String(),
                        Relevant = c.Boolean(nullable: false),
                        Ablageort = c.String(),
                        QsFreigabe = c.Boolean(nullable: false),
                        Spalte1 = c.String(),
                        Spalte2 = c.String(),
                        Spalte3 = c.String(),
                        Spalte4 = c.String(),
                        Spalte5 = c.String(),
                        Spalte6 = c.String(),
                        Spalte7 = c.String(),
                        Spalte8 = c.String(),
                        Spalte9 = c.String(),
                        Spalte10 = c.String(),
                        Status = c.Int(nullable: false),
                        KundendokumentID = c.Int(nullable: false),
                        ForderungsversionID = c.Int(nullable: false),
                        StandortObjektID = c.Int(nullable: false),
                        KundenbezugID = c.Int(),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_KundendokumentForderungsversionFilter",
                        new AnnotationValues(oldValue: null, newValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition")
                    },
                });
            
            AlterTableAnnotations(
                "dbo.Kundenbezug",
                c => new
                    {
                        KundenbezugID = c.Int(nullable: false, identity: true),
                        Name = c.String(),
                        StandortID = c.Int(),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_KundenbezugFilter",
                        new AnnotationValues(oldValue: null, newValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition")
                    },
                });
            
            AlterTableAnnotations(
                "dbo.IndividuelleForderung",
                c => new
                    {
                        IndividuelleForderungID = c.Int(nullable: false, identity: true),
                        Beschreibung = c.String(),
                        Kommentar = c.String(),
                        GueltigVon = c.DateTime(nullable: false),
                        GueltigBis = c.DateTime(),
                        StandortID = c.Int(),
                        ThemenbereichID = c.Int(),
                        StandortObjektID = c.Int(),
                        KundenbezugID = c.Int(),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_IndividuelleForderungen",
                        new AnnotationValues(oldValue: null, newValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition")
                    },
                });
            
            AlterTableAnnotations(
                "dbo.Standort",
                c => new
                    {
                        StandortID = c.Int(nullable: false, identity: true),
                        Name = c.String(),
                        InterneNotiz = c.String(),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_StandortFilter",
                        new AnnotationValues(oldValue: null, newValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition")
                    },
                });
            
            AlterTableAnnotations(
                "dbo.Kontakt",
                c => new
                    {
                        KontaktID = c.Int(nullable: false, identity: true),
                        Nachname = c.String(),
                        Vorname = c.String(),
                        EMail = c.String(),
                        Funktion = c.String(),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_KontaktFilter",
                        new AnnotationValues(oldValue: null, newValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition")
                    },
                });
            
        }
    }
}
