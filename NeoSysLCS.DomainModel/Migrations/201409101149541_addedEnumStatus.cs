namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class addedEnumStatus : DbMigration
    {
        public override void Up()
        {
            AlterColumn("dbo.KundendokumentForderungsversion", "Status", c => c.Int(nullable: false));
            AlterColumn("dbo.KundendokumentIndividuelleForderung", "Status", c => c.Int(nullable: false));
            AlterColumn("dbo.KundendokumentPflicht", "Status", c => c.Int(nullable: false));
        }
        
        public override void Down()
        {
            AlterColumn("dbo.KundendokumentPflicht", "Status", c => c.Int());
            AlterColumn("dbo.KundendokumentIndividuelleForderung", "Status", c => c.Int());
            AlterColumn("dbo.KundendokumentForderungsversion", "Status", c => c.Int());
        }
    }
}
