// <auto-generated />
namespace NeoSysLCS.DomainModel.Migrations
{
    using System.CodeDom.Compiler;
    using System.Data.Entity.Migrations;
    using System.Data.Entity.Migrations.Infrastructure;
    using System.Resources;
    
    [GeneratedCode("EntityFramework.Migrations", "6.1.1-30610")]
    public sealed partial class addVorgaengerIDToKundendokument : IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(addVorgaengerIDToKundendokument));
        
        string IMigrationMetadata.Id
        {
            get { return "201410081433487_addVorgaengerIDToKundendokument"; }
        }
        
        string IMigrationMetadata.Source
        {
            get { return null; }
        }
        
        string IMigrationMetadata.Target
        {
            get { return Resources.GetString("Target"); }
        }
    }
}
