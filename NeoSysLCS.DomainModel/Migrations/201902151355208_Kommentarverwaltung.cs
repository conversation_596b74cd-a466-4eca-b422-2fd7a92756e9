namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class Kommentarverwaltung : DbMigration
    {
        public override void Up()
        {
            CreateTable(
                "dbo.Kommentar",
                c => new
                    {
                        KommentarID = c.Int(nullable: false, identity: true),
                        Eintrag = c.DateTime(),
                        Beschluss = c.DateTime(),
                        Inkrafttretung = c.DateTime(),
                        Nummer = c.String(),
                        ProjektleiterID = c.String(maxLength: 128),
                        Status = c.Int(nullable: false),
                        Newsletter = c<PERSON>(nullable: false),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.KommentarID)
                .ForeignKey("dbo.AspNetUsers", t => t.<PERSON>beitetVonID)
                .ForeignKey("dbo.AspNetUsers", t => t.ErstelltVonID)
                .ForeignKey("dbo.AspNetUsers", t => t.ProjektleiterID)
                .Index(t => t.ProjektleiterID)
                .Index(t => t.ErstelltVonID)
                .Index(t => t.BearbeitetVonID);
            
            CreateTable(
                "dbo.KommentarUebersetzung",
                c => new
                    {
                        KommentarUebersetzungID = c.Int(nullable: false, identity: true),
                        KommentarID = c.Int(nullable: false),
                        SpracheID = c.Int(nullable: false),
                        Quelle = c.String(),
                        BetroffenKommentar = c.String(),
                        LinkBetroffenKommentar = c.String(),
                        NichtBetroffenKommentar = c.String(),
                        LinkNichtBetroffenKommentar = c.String(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.KommentarUebersetzungID)
                .ForeignKey("dbo.AspNetUsers", t => t.BearbeitetVonID)
                .ForeignKey("dbo.AspNetUsers", t => t.ErstelltVonID)
                .ForeignKey("dbo.Kommentar", t => t.KommentarID, cascadeDelete: true)
                .ForeignKey("dbo.Sprache", t => t.SpracheID)
                .Index(t => t.KommentarID)
                .Index(t => t.SpracheID)
                .Index(t => t.ErstelltVonID)
                .Index(t => t.BearbeitetVonID);
            
            CreateTable(
                "dbo.KommentarErlass",
                c => new
                    {
                        Kommentar_KommentarID = c.Int(nullable: false),
                        Erlass_ErlassID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.Kommentar_KommentarID, t.Erlass_ErlassID })
                .ForeignKey("dbo.Kommentar", t => t.Kommentar_KommentarID)
                .ForeignKey("dbo.Erlass", t => t.Erlass_ErlassID)
                .Index(t => t.Kommentar_KommentarID)
                .Index(t => t.Erlass_ErlassID);
            
            CreateTable(
                "dbo.KommentarRechtsbereich",
                c => new
                    {
                        Kommentar_KommentarID = c.Int(nullable: false),
                        Rechtsbereich_RechtsbereichID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.Kommentar_KommentarID, t.Rechtsbereich_RechtsbereichID })
                .ForeignKey("dbo.Kommentar", t => t.Kommentar_KommentarID)
                .ForeignKey("dbo.Rechtsbereich", t => t.Rechtsbereich_RechtsbereichID)
                .Index(t => t.Kommentar_KommentarID)
                .Index(t => t.Rechtsbereich_RechtsbereichID);
            
            CreateTable(
                "dbo.KommentarStandortObjekt",
                c => new
                    {
                        Kommentar_KommentarID = c.Int(nullable: false),
                        StandortObjekt_StandortObjektID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.Kommentar_KommentarID, t.StandortObjekt_StandortObjektID })
                .ForeignKey("dbo.Kommentar", t => t.Kommentar_KommentarID)
                .ForeignKey("dbo.StandortObjekt", t => t.StandortObjekt_StandortObjektID)
                .Index(t => t.Kommentar_KommentarID)
                .Index(t => t.StandortObjekt_StandortObjektID);
            
            AddColumn("dbo.Erlassfassung", "KommentarID", c => c.Int());
            CreateIndex("dbo.Erlassfassung", "KommentarID");
            AddForeignKey("dbo.Erlassfassung", "KommentarID", "dbo.Kommentar", "KommentarID");
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.Erlassfassung", "KommentarID", "dbo.Kommentar");
            DropForeignKey("dbo.KommentarStandortObjekt", "StandortObjekt_StandortObjektID", "dbo.StandortObjekt");
            DropForeignKey("dbo.KommentarStandortObjekt", "Kommentar_KommentarID", "dbo.Kommentar");
            DropForeignKey("dbo.KommentarRechtsbereich", "Rechtsbereich_RechtsbereichID", "dbo.Rechtsbereich");
            DropForeignKey("dbo.KommentarRechtsbereich", "Kommentar_KommentarID", "dbo.Kommentar");
            DropForeignKey("dbo.Kommentar", "ProjektleiterID", "dbo.AspNetUsers");
            DropForeignKey("dbo.KommentarUebersetzung", "SpracheID", "dbo.Sprache");
            DropForeignKey("dbo.KommentarUebersetzung", "KommentarID", "dbo.Kommentar");
            DropForeignKey("dbo.KommentarUebersetzung", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.KommentarUebersetzung", "BearbeitetVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.Kommentar", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.KommentarErlass", "Erlass_ErlassID", "dbo.Erlass");
            DropForeignKey("dbo.KommentarErlass", "Kommentar_KommentarID", "dbo.Kommentar");
            DropForeignKey("dbo.Kommentar", "BearbeitetVonID", "dbo.AspNetUsers");
            DropIndex("dbo.KommentarStandortObjekt", new[] { "StandortObjekt_StandortObjektID" });
            DropIndex("dbo.KommentarStandortObjekt", new[] { "Kommentar_KommentarID" });
            DropIndex("dbo.KommentarRechtsbereich", new[] { "Rechtsbereich_RechtsbereichID" });
            DropIndex("dbo.KommentarRechtsbereich", new[] { "Kommentar_KommentarID" });
            DropIndex("dbo.KommentarErlass", new[] { "Erlass_ErlassID" });
            DropIndex("dbo.KommentarErlass", new[] { "Kommentar_KommentarID" });
            DropIndex("dbo.KommentarUebersetzung", new[] { "BearbeitetVonID" });
            DropIndex("dbo.KommentarUebersetzung", new[] { "ErstelltVonID" });
            DropIndex("dbo.KommentarUebersetzung", new[] { "SpracheID" });
            DropIndex("dbo.KommentarUebersetzung", new[] { "KommentarID" });
            DropIndex("dbo.Kommentar", new[] { "BearbeitetVonID" });
            DropIndex("dbo.Kommentar", new[] { "ErstelltVonID" });
            DropIndex("dbo.Kommentar", new[] { "ProjektleiterID" });
            DropIndex("dbo.Erlassfassung", new[] { "KommentarID" });
            DropColumn("dbo.Erlassfassung", "KommentarID");
            DropTable("dbo.KommentarStandortObjekt");
            DropTable("dbo.KommentarRechtsbereich");
            DropTable("dbo.KommentarErlass");
            DropTable("dbo.KommentarUebersetzung");
            DropTable("dbo.Kommentar");
        }
    }
}
