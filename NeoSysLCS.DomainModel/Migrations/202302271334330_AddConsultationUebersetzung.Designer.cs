// <auto-generated />
namespace NeoSysLCS.DomainModel.Migrations
{
    using System.CodeDom.Compiler;
    using System.Data.Entity.Migrations;
    using System.Data.Entity.Migrations.Infrastructure;
    using System.Resources;
    
    [GeneratedCode("EntityFramework.Migrations", "6.1.3-40302")]
    public sealed partial class AddConsultationUebersetzung : IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(AddConsultationUebersetzung));
        
        string IMigrationMetadata.Id
        {
            get { return "202302271334330_AddConsultationUebersetzung"; }
        }
        
        string IMigrationMetadata.Source
        {
            get { return null; }
        }
        
        string IMigrationMetadata.Target
        {
            get { return Resources.GetString("Target"); }
        }
    }
}
