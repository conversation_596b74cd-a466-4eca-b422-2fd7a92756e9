namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class AddShortcuts : DbMigration
    {
        public override void Up()
        {
            CreateTable(
                "dbo.Shortcut",
                c => new
                    {
                        ShortcutID = c.Int(nullable: false, identity: true),
                        StandortID = c.Int(nullable: false),
                        Name = c.String(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.ShortcutID)
                .ForeignKey("dbo.AspNetUsers", t => t.BearbeitetVonID)
                .ForeignKey("dbo.AspNetUsers", t => t.ErstelltVonID)
                .Index(t => t.ErstelltVonID)
                .Index(t => t.BearbeitetVonID);
            
            CreateTable(
                "dbo.ApplicationUserShortcut",
                c => new
                    {
                        ApplicationUser_Id = c.String(nullable: false, maxLength: 128),
                        Shortcut_ShortcutID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.ApplicationUser_Id, t.Shortcut_ShortcutID })
                .ForeignKey("dbo.AspNetUsers", t => t.ApplicationUser_Id)
                .ForeignKey("dbo.Shortcut", t => t.Shortcut_ShortcutID)
                .Index(t => t.ApplicationUser_Id)
                .Index(t => t.Shortcut_ShortcutID);
            
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.ApplicationUserShortcut", "Shortcut_ShortcutID", "dbo.Shortcut");
            DropForeignKey("dbo.ApplicationUserShortcut", "ApplicationUser_Id", "dbo.AspNetUsers");
            DropForeignKey("dbo.Shortcut", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.Shortcut", "BearbeitetVonID", "dbo.AspNetUsers");
            DropIndex("dbo.ApplicationUserShortcut", new[] { "Shortcut_ShortcutID" });
            DropIndex("dbo.ApplicationUserShortcut", new[] { "ApplicationUser_Id" });
            DropIndex("dbo.Shortcut", new[] { "BearbeitetVonID" });
            DropIndex("dbo.Shortcut", new[] { "ErstelltVonID" });
            DropTable("dbo.ApplicationUserShortcut");
            DropTable("dbo.Shortcut");
        }
    }
}
