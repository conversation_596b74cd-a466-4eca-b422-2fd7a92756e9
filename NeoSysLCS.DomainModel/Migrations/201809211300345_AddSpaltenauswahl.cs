namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class AddSpaltenauswahl : DbMigration
    {
        public override void Up()
        {
            CreateTable(
                "dbo.KundendokumentSpaltenauswahl",
                c => new
                    {
                        SpaltenID = c.Int(nullable: false, identity: true),
                        ThemenbereicheUnbound = c<PERSON>(nullable: false),
                        RechtsbereicheUnbound = c<PERSON>(nullable: false),
                        StandortObjektTitel = c<PERSON>(nullable: false),
                        ErlassID = c<PERSON>(nullable: false),
                        ArtikelNummer = c.<PERSON>(nullable: false),
                        Beschreibung = c.<PERSON>(nullable: false),
                        Bewilligungspflicht = c.<PERSON>(nullable: false),
                        Nachweispflicht = c.<PERSON>(nullable: false),
                        Status = c.<PERSON>(nullable: false),
                        Erfuellung = c.<PERSON>(nullable: false),
                        LetzterPruefZeitpunkt = c<PERSON>(nullable: false),
                        NaechstePruefungAm = c<PERSON>(nullable: false),
                        Pruefmethode = c<PERSON>(nullable: false),
                        ShortcutID = c<PERSON>(nullable: false),
                        Ablageort = c.Boolean(nullable: false),
                        Kommentar = c.Boolean(nullable: false),
                        Spalte1 = c.Boolean(nullable: false),
                        Spalte2 = c.Boolean(nullable: false),
                        Spalte3 = c.Boolean(nullable: false),
                        Spalte4 = c.Boolean(nullable: false),
                        Spalte5 = c.Boolean(nullable: false),
                        Spalte6 = c.Boolean(nullable: false),
                        Spalte7 = c.Boolean(nullable: false),
                        Spalte8 = c.Boolean(nullable: false),
                        Spalte9 = c.Boolean(nullable: false),
                        Spalte10 = c.Boolean(nullable: false),
                        ErlassSrNummer = c.Boolean(nullable: false),
                        ErlassfassungInkraftretung = c.Boolean(nullable: false),
                        ErlassfassungBearbeitetAm = c.Boolean(nullable: false),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.SpaltenID)
                .ForeignKey("dbo.AspNetUsers", t => t.BearbeitetVonID)
                .ForeignKey("dbo.AspNetUsers", t => t.ErstelltVonID)
                .Index(t => t.ErstelltVonID)
                .Index(t => t.BearbeitetVonID);
            
            AddColumn("dbo.Standort", "KundendokumentSpaltenauswahl_SpaltenID", c => c.Int());
            CreateIndex("dbo.Standort", "KundendokumentSpaltenauswahl_SpaltenID");
            AddForeignKey("dbo.Standort", "KundendokumentSpaltenauswahl_SpaltenID", "dbo.KundendokumentSpaltenauswahl", "SpaltenID");
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.Standort", "KundendokumentSpaltenauswahl_SpaltenID", "dbo.KundendokumentSpaltenauswahl");
            DropForeignKey("dbo.KundendokumentSpaltenauswahl", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.KundendokumentSpaltenauswahl", "BearbeitetVonID", "dbo.AspNetUsers");
            DropIndex("dbo.KundendokumentSpaltenauswahl", new[] { "BearbeitetVonID" });
            DropIndex("dbo.KundendokumentSpaltenauswahl", new[] { "ErstelltVonID" });
            DropIndex("dbo.Standort", new[] { "KundendokumentSpaltenauswahl_SpaltenID" });
            DropColumn("dbo.Standort", "KundendokumentSpaltenauswahl_SpaltenID");
            DropTable("dbo.KundendokumentSpaltenauswahl");
        }
    }
}
