using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace NeoSysLCS.DomainModel.Models
{
    public class ChecklistHeader : BaseModel
    {
        public int ChecklistHeaderID { get; set; }

        public string SuvaHeaderID { get; set; }

        public int ChecklistID { get; set; }
        public Checklist Checklist { get; set; }

        [Column(TypeName = "xml")]
        public string Translation { get; set; }

        public int OrderID { get; set; }
    }
}