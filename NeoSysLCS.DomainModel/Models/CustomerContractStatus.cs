using System.ComponentModel.DataAnnotations;


namespace NeoSysLCS.DomainModel.Models
{
    public enum CustomerContractStatus
    {
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_KundeContractStatus_None")]
        None = 0,
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_KundeContractStatus_Open")]
        Open = 1,
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_KundeContractStatus_Ok")]
        Ok = 2,
    }
}