using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace NeoSysLCS.DomainModel.Models
{
    public class KundendokumentForderungsversion : KundenportalBaseModel, IStateful
    {

        public int KundendokumentForderungsversionID { get; set; }
        public string Kommentar { get; set; }
        public KundendokumentErfuellung? Erfuellung { get; set; }
        public DateTime? LetztePruefungAm { get; set; }
        public DateTime? NaechstePruefungAm { get; set; }
        public string Verantwortlich { get; set; }
        public string Pruefmethode { get; set; }
        public bool Relevant { get; set; }
        public string Ablageort { get; set; }
        public bool QsFreigabe { get; set; }
        public string Kundenbezug { get; set; }
        public string Spalte1 { get; set; }
        public string Spalte2 { get; set; }
        public string Spalte3 { get; set; }
        public string Spalte4 { get; set; }
        public string Spalte5 { get; set; }
        public string Spalte6 { get; set; }
        public string Spalte7 { get; set; }
        public string Spalte8 { get; set; }
        public string Spalte9 { get; set; }
        public string Spalte10 { get; set; }

        public KundendokumentItemStatus Status { get; set; }

        public int KundendokumentID { get; set; }
        public Kundendokument Kundendokument { get; set; }

        public int ForderungsversionID { get; set; }
        public Forderungsversion Forderungsversion { get; set; }

        public int StandortObjektID { get; set; }
        public StandortObjekt StandortObjekt { get; set; }

        public Shortcut Shortcut { get; set; }

        [Column("Shortcut_ShortcutID")]
        public int? ShortcutID { get; set; }
    }
}