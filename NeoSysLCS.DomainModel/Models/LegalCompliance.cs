using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace NeoSysLCS.DomainModel.Models
{
    public class LegalCompliance : BaseModel
    {
        [Key, DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int LegalComplianceID { get; set; }
        public int StandortID { get; set; }
        public int KundendokumentID { get; set; }
        public int Total { get; set; }
        public int Erfuellt { get; set; }
        public int NichtErfuellt { get; set; }
        public int InAbklaerung { get; set; }
        public int NichtBearbeitet { get; set; }
    }
}