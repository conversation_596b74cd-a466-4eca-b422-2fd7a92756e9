using System.ComponentModel.DataAnnotations;

namespace NeoSysLCS.DomainModel.Models
{
    public enum OfferStatus
    {
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_OfferStatus_New")]
        New = 0,
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_OfferStatus_QS")]
        Qs = 1,
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_OfferStatus_Sent")]
        Sent = 2,
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_OfferStatus_Accepted")]
        Accepted = 3,
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_OfferStatus_Rejected")]
        Rejected = 4,
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_OfferStatus_InProgress")]
        InProgress = 5,
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_OfferStatus_ContractCreated")] // NEOS-675, Renamed to "Vertrag versendet"
        ContractCreated = 6,
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_OfferStatus_ContractReturned")]
        ContractReturned = 7,
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_OfferStatus_Completed")]
        Completed = 8,
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_OfferStatus_Closed")]
        Closed = 9,
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_OfferStatus_QS")]
        QsAdditionaWork = 10,
    }
}