using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace NeoSysLCS.DomainModel.Models
{
    public class FAQ : BaseModel
    {
        public FAQ()
        {
            FAQKategorien = new List<FAQKategorie>();
        }

        public int FaqID { get; set; }
        public int Prio { get; set; }
        public FAQStatus Status { get; set; }
        public ICollection<FAQKategorie> FAQKategorien { get; set; }

        [Column(TypeName = "xml")]
        public string Uebersetzung { get; set; }
    }
}