using System.ComponentModel.DataAnnotations;
using System.Security.AccessControl;

namespace NeoSysLCS.DomainModel.Models
{
    public enum KundendokumentItemStatusForKonzern
    {
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_KundendokumentItemStatus_New")]
        New = 0,
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_KundendokumentItemStatus_NewVersion")]
        NewVersion = 1,
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_KundendokumentItemStatus_OldVersion")]
        OldVersion = 3,
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_KundendokumentItemStatus_Existing")]
        Existing = 4,
    }
}