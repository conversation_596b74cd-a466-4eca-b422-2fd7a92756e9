using System;

namespace NeoSysLCS.DomainModel.Models
{
    public class KundendokumentPflicht : KundenportalBaseModel
    {
       
        public int KundendokumentPflichtID { get; set; }
        public string Kommentar { get; set; }
        public KundendokumentErfuellung? Erfuellung { get; set; }
        public DateTime? Erfuellungszeitpunkt { get; set; }
        public string ErfuelltDurch { get; set; }
        public string Verantwortlich { get; set; }
        public bool Relevant { get; set; }
        public string Ablageort { get; set; }
        public bool QsFreigabe { get; set; }
        public KundendokumentItemStatus Status { get; set; }
        public string Kundenbezug { get; set; }
        public int KundendokumentID { get; set; }
        public Kundendokument Kundendokument { get; set; }

        public int PflichtID { get; set; }
        public Pflicht Pflicht { get; set; }

        public int StandortObjektID { get; set; }
        public StandortObjekt StandortObjekt { get; set; }

    }
}