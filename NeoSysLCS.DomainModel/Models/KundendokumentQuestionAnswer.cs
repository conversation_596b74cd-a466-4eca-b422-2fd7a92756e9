using System.ComponentModel.DataAnnotations;
using System.Security.AccessControl;

namespace NeoSysLCS.DomainModel.Models
{
    public enum KundendokumentQuestionAnswer
    {
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_KundendokumentItemStatus_Yes")]
        Yes = 0,
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_KundendokumentItemStatus_Partially")]
        Partially = 1,
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_KundendokumentItemStatus_No")]
        No = 2,
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_KundendokumentErfuellung_NotEdited")]
        NotEdited = 4,
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_KundendokumentErfuellung_NotDefined")]
        Checkpoint = 5,
    }
}