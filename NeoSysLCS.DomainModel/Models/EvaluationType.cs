using System.ComponentModel.DataAnnotations;

namespace NeoSysLCS.DomainModel.Models
{
    public enum EvaluationType
    {
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_EvaluationType_Updates")]
        Updates = 0,
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_EvaluationType_DaysOfDelay")]
        DaysOfDelay = 1,
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_EvaluationType_DelayedUpdates")]
        DelayedUpdates = 2,
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_EvaluationType_OfferedVolumeNewCustomer")]
        OfferedVolumeNew = 3,
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_EvaluationType_OfferedVolumeExistingCustomer")]
        OfferedVolumeExisting = 4
    }
}