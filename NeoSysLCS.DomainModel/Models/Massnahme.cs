using System;
using System.ComponentModel.DataAnnotations.Schema;
namespace NeoSysLCS.DomainModel.Models
{
    public class Massnahme : KundenportalBaseModel
    {
        public int MassnahmeID { get; set; }

        public string Betreff { get; set; }


        public string Verantwortlich { get; set; }

        public MassnahmeStatus Status { get; set; }

        public DateTime? Termin { get; set; }

        [Column(TypeName = "xml")]
        public string Uebersetzung { get; set; }

        public int? StandortID { get; set; }
        
        [ForeignKey("StandortID")]
        public Standort Standort { get; set; }

        public int? ShortcutID { get; set; }

        [ForeignKey("ShortcutID")]
        public Shortcut Shortcut { get; set; }

        public int KundeMassnahmeID { get; set; }

        public int? ArtikelID { get; set; }

        // Used for Feature 2023 Nr. 27 --> Show how many Massnahmen are already created per Entry
        // Used for different type on objects based on where the massnahme was created:
            // Dashboard/Erlassänderungen: KommentarID
            // Dashboard/Kommentare: KommentarID
            // Dashboard/Vernehmlassungen: ConsultationID
            // Kundendokument/Forderungen: ArtikelID
            // Kundendokument/Gesetzesliste: KundendokumentErlassfassungID
            // Kundendokument/SUVA: KundendokumentChecklistID
        public int OriginID { get; set; }

        public bool ReminderSent { get; set; }

        public bool EmailNotification { get; set; }
    }
}