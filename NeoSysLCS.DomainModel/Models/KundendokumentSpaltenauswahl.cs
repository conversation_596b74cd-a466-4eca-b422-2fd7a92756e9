
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace NeoSysLCS.DomainModel.Models
{
    public class KundendokumentSpaltenauswahl : BaseModel
    {
        [Key]
        public int SpaltenID { get; set; }
        public bool RechtsbereicheUnbound { get; set; }
        public bool StandortObjektTitel { get; set; }
        public bool ErlassID { get; set; }
        public bool ArtikelNummer { get; set; }
        public bool Beschreibung { get; set; }
        public bool Bewilligungspflicht { get; set; }
        public bool Nachweispflicht { get; set; }
        public bool Status { get; set; }
        public bool Erfuellung { get; set; }
        public bool Massnahme { get; set; }
        public bool LetzterPruefZeitpunkt { get; set; }
        public bool NaechstePruefungAm { get; set; }
        public bool Pruefmethode { get; set; }
        public bool ShortcutID { get; set; }
        public bool Ablageort { get; set; }
        public bool Kommentar { get; set; }
        public bool Spalte1 { get; set; }
        public bool Spalte2 { get; set; }
        public bool Spalte3 { get; set; }
        public bool Spalte4 { get; set; }
        public bool Spalte5 { get; set; }
        public bool Spalte6 { get; set; }
        public bool Spalte7 { get; set; }
        public bool Spalte8 { get; set; }
        public bool Spalte9 { get; set; }
        public bool Spalte10 { get; set; }
        public bool ErlassSrNummer { get; set; }
        public bool ErlassfassungInkraftretung { get; set; }
        public bool ErlassfassungBearbeitetAm { get; set; }
    }
}