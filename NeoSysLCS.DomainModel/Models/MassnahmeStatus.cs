using System.ComponentModel.DataAnnotations;

namespace NeoSysLCS.DomainModel.Models
{
    public enum MassnahmeStatus
    {
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_MassnahmeStatus_New")]
        New = 0,
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_MassnahmeStatus_InProgress")]
        InProgress = 1,
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_MassnahmeStatus_Finished")]
        Finished = 2
    }
}