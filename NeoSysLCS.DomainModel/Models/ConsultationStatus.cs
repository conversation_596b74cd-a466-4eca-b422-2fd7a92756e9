using System.ComponentModel.DataAnnotations;

namespace NeoSysLCS.DomainModel.Models
{
    public enum ConsultationStatus
    {
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_ConsultationStatus_Planned")]
        Planned = 0,
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_ConsultationStatus_Ongoing")]
        Ongoing = 1,
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_ConsultationStatus_Completed")]
        Completed = 2,
    }
}