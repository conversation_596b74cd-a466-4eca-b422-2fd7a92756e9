using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNet.Identity;
using Microsoft.AspNet.Identity.EntityFramework;

namespace NeoSysLCS.DomainModel.Models
{
    [Serializable]
    // You can add profile data for the user by adding more properties to your ApplicationUser class, please visit http://go.microsoft.com/fwlink/?LinkID=317594 to learn more.
    public class ApplicationUser : IdentityUser
    {


        public ClaimsIdentity GenerateUserIdentity(UserManager<ApplicationUser> manager)
        {
            // Note the authenticationType must match the one defined in CookieAuthenticationOptions.AuthenticationType
            ClaimsIdentity userIdentity = manager.CreateIdentity(this, DefaultAuthenticationTypes.ApplicationCookie);
            // Add custom user claims here
            var user = manager.FindById(userIdentity.GetUserId());
            userIdentity.AddClaim(new Claim("Fullname", user.Vorname + " " + user.Nachname));


            return userIdentity;
        }

        public async Task<ClaimsIdentity> GenerateUserIdentityAsync(UserManager<ApplicationUser> manager)
        {
            // Note the authenticationType must match the one defined in CookieAuthenticationOptions.AuthenticationType
            ClaimsIdentity userIdentity = await manager.CreateIdentityAsync(this, DefaultAuthenticationTypes.ApplicationCookie);
            // Add custom user claims here
            var user = manager.FindById(userIdentity.GetUserId());
            userIdentity.AddClaim(new Claim("Fullname", user.Vorname + " " + user.Nachname));


            return userIdentity;
        }

        public ApplicationUser()
        {
            Shortcuts = new List<Shortcut>();

            ApplicationUserPrivacyPolicies = new HashSet<ApplicationUserPrivacyPolicy>();
        }

        public string Vorname { get; set; }
        public string Nachname { get; set; }
        public int? SpracheID { get; set; }

        [ForeignKey("SpracheID")]
        public Sprache Sprache { get; set; }
        public int? KundeID { get; set; }
        
        [ForeignKey("KundeID")]
        public Kunde Kunde { get; set; }


        [ForeignKey("ErstelltVon")]
        public string ErstelltVonID { get; set; }
        public ApplicationUser ErstelltVon { get; set; }

        [ForeignKey("BearbeitetVon")]
        public string BearbeitetVonID { get; set; }
        public ApplicationUser BearbeitetVon { get; set; }

        public DateTime? ErstelltAm { get; set; }
        public DateTime? BearbeitetAm { get; set; }

        public bool? ForceClearCookies { get; set; }

        public DateTime? TermsAccepted { get; set; }

        public virtual string FullName
        {
            get { return this.Vorname + " " + this.Nachname; }
        }

        public int? NewsletterPeriod { get; set; }
        
        public int? CortecId { get; set; }

        public ICollection<Standort> WriteStandorte { get; set; }

        public virtual ICollection<Shortcut> Shortcuts { get; set; }

        public virtual ICollection<ApplicationUserPrivacyPolicy> ApplicationUserPrivacyPolicies { get; set; }
    }
}