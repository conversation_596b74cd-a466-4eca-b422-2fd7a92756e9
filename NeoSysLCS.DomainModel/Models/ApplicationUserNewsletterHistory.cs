using System;
using System.ComponentModel.DataAnnotations.Schema;


namespace NeoSysLCS.DomainModel.Models
{
    public class ApplicationUserNewsletterHistory
    {
        public string ID { get; set; }

        public string UserID { get; set; }
        
        [ForeignKey("UserID")]
        public ApplicationUser User { get; set; }

        public DateTime DateSent { get; set; }

        public string NewsletterFileLink { get; set; }

    }
}