using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq.Expressions;
using System.ComponentModel.DataAnnotations.Schema;

namespace NeoSysLCS.DomainModel.Models
{
    [Serializable]
    public class Kunde : BaseModel
    {

        public Kunde()
        {
            Standorte = new List<Standort>();
            Kontakte = new List<Kontakt>();
        }

        public int KundeID { get; set; }

        [Required(AllowEmptyStrings = false)]
        [Display(Name = "Kundename")]
        public string Name { get; set; }

        public string Beschreibung { get; set; }

        public DateTime? LetzteAktualisierung { get; set; }

        public string ProjektleiterID { get; set; }
        public ApplicationUser Projektleiter { get; set; }

        public string ProjektleiterQSID { get; set; }
        public ApplicationUser ProjektleiterQS { get; set; }

        public KundeAnalysisStatus AnalysisStatus { get; set; }

        public KundeStatus Status { get; set; }

        public DateTime? AnalysisDate { get; set; }

        public DateTime? DocumentReportDate { get; set; }

        public DateTime? EducationDate { get; set; }

        public DateTime? ContractCreatedDate { get; set; }

        public DateTime? ContractReturnedDate { get; set; }

        public DateTime? UpdateDate { get; set; }

        public string ProjectNumberUpdate { get; set; }

        public DateTime? InquiryDate { get; set; }

        public DateTime? DocumentReceivedDate { get; set; }

        public DateTime? InvoiceDate { get; set; }

        public DateTime? InvoiceCreatedDate { get; set; }

        public int? Reccurence { get; set; }

        public string Link { get; set; }

        public int? Auftragsvolumen { get; set; }

        public string BillingInfo { get; set; }

        public virtual ICollection<Kontakt> Kontakte { get; set; }
        public virtual ICollection<Standort> Standorte { get; set; }
        public CustomerContractStatus ContractStatus { get; set; }

        public bool YearlyVisit { get; set; }

        public DateTime? CortecTaskCreatedAt { get; set; }

        // expressions

        public static Expression<Func<Kunde, bool>> All = x => true;

        public static Expression<Func<Kunde, bool>> Inaktiv = x =>
            x.Status == KundeStatus.Inactive;

        public static Expression<Func<Kunde, bool>> Aktualisierung =
            x => x.Status > KundeStatus.Analysis && x.Status <= KundeStatus.Sent;

        public static Expression<Func<Kunde, bool>> Newsletter = x =>
            x.Status == KundeStatus.Newsletter;
    }
}