using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace NeoSysLCS.DomainModel.Models
{
    public class PrivacyPolicy : BaseModel
    {
        public int ID { get; set; }

        public DateTime ChangedAt { get; set; }

        [Column(TypeName = "xml")]
        public string Uebersetzung { get; set; }

        public virtual ICollection<ApplicationUserPrivacyPolicy> ApplicationUserPrivacyPolicies { get; set; }

        public PrivacyPolicy()
        {
            ApplicationUserPrivacyPolicies = new HashSet<ApplicationUserPrivacyPolicy>();
        }
    }
}