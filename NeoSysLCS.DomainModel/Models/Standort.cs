using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data.Entity;

namespace NeoSysLCS.DomainModel.Models
{
    [Serializable]
    public class Standort : KundenportalBaseModel
    {

        public Standort()
        {
            Rechtsbereiche = new List<Rechtsbereich>();
            Sprachen = new List<Sprache>();
            Herausgeber = new List<Herausgeber>();
            Kontakte = new List<Kontakt>();
            Kundendokumente = new List<Kundendokument>();
            IndividuelleForderungen = new List<IndividuelleForderung>();
            StandortObjekte = new List<StandortObjekt>();
            WriteUsers = new List<ApplicationUser>();
            //Spaltenauswahl = new List<KundendokumentSpaltenauswahl>();
        }

        public int StandortID { get; set; }
        public string Name { get; set; }
        public string InterneNotiz { get; set; }

        public virtual ICollection<Rechtsbereich> Rechtsbereiche { get; set; }
        public virtual ICollection<Sprache> S<PERSON><PERSON> { get; set; }
        public virtual ICollection<Herausgeber> Herausgeber { get; set; }
        public virtual ICollection<Kontakt> Kontakte { get; set; }
        public virtual ICollection<Kundendokument> Kundendokumente { get; set; }
        public virtual ICollection<IndividuelleForderung> IndividuelleForderungen { get; set; }
        public virtual ICollection<StandortObjekt> StandortObjekte { get; set; }
        public virtual ICollection<ApplicationUser> WriteUsers { get; set; }
        public KundendokumentSpaltenauswahl KundendokumentSpaltenauswahl { get; set; }
        public string Uebersetzung { get; set; }
        public DateTime? ReportDate { get; set; }
        public string ErlassImportDoc { get; set; }
        public DateTime? Archive { get; set; }
    }
}