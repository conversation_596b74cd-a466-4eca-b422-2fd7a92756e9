using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace NeoSysLCS.DomainModel.Models
{
    public partial class Pflicht : BaseModel
    {

        public Pflicht()
        {
            this.Objekte = new List<Objekt>();
        }

        public int PflichtID { get; set; }
        public DateTime GueltigVon { get; set; }
        public DateTime? GueltigBis { get; set; }
        public int ErlassfassungID { get; set; }
        public Erlassfassung Erlassfassung { get; set; }
        public ICollection<Objekt> Objekte { get; set; }
        public Boolean? Freigabe { get; set; }
        [StringLength(128)]
        public string FreigabeVon { get; set; }
        public DateTime? FreigabeAm { get; set; }
        public Boolean? QsFreigabe { get; set; }
        [StringLength(128)]
        public string QsFreigabeVon { get; set; }
        public DateTime? QsFreigabeAm { get; set; }

        public void SetFreigabe(bool freigabe, string userID = null)
        {
            if (freigabe)
            {
                Freigabe = true;
                if (!FreigabeAm.HasValue)
                {
                    FreigabeAm = DateTime.Now;
                }
                if (string.IsNullOrEmpty(FreigabeVon))
                {
                    FreigabeVon = userID;
                }
            }
            else
            {
                Freigabe = null;
                FreigabeAm = null;
                FreigabeVon = null;
            }
        }

        public void SetQsFreigabe(bool freigabe, string userID = null)
        {
            if (freigabe)
            {
                QsFreigabe = true;
                if (!QsFreigabeAm.HasValue)
                {
                    QsFreigabeAm = DateTime.Now;
                }
                if (string.IsNullOrEmpty(QsFreigabeVon))
                {
                    QsFreigabeVon = userID;
                }
            }
            else
            {
                QsFreigabe = null;
                QsFreigabeAm = null;
                QsFreigabeVon = null;
            }
        }
    }
}
