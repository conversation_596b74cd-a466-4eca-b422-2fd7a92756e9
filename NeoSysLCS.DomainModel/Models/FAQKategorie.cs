using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace NeoSysLCS.DomainModel.Models
{
    public class FAQKategorie
    {
        public FAQKategorie ()
        {
            FAQ = new List<FAQ>();
        }

        public int FaqKategorieID { get; set; }
        public string Name { get; set; }
        public string Systemname { get; set; }
        public ICollection<FAQ> FAQ { get; set; }

    }
}