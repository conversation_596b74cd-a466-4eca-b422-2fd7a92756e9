using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Web;

namespace NeoSysLCS.DomainModel.Models
{
    public class StandortBericht : BaseModel
    {
        public int ID { get; set; }

        public int StandortID { get; set; }
        public Standort Standort { get; set; }

        [Column(TypeName = "xml")]
        public string <PERSON><PERSON><PERSON>tzung { get; set; }
    }
}