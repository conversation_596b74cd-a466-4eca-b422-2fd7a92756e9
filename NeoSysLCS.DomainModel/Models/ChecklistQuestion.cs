using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace NeoSysLCS.DomainModel.Models
{
    public class ChecklistQuestion : BaseModel
    {
        public int ChecklistQuestionID { get; set; }

        public string SuvaQuestionID { get; set; }

        public int ChecklistID { get; set; }
        public Checklist Checklist { get; set; }

        public string SharedImage { get; set; }

        [Column(TypeName = "xml")]
        public string Translation { get; set; }

        public int ChecklistHeaderID { get; set; }
        public ChecklistHeader ChecklistHeader { get; set; }

        //public string ChecklistHeaderTemplateID { get; set; }

        public string ChecklistType { get; set; }

        public string Numeration { get; set; }
    }
}