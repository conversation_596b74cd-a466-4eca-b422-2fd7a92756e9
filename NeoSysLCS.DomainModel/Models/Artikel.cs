using System.Collections.Generic;

namespace NeoSysLCS.DomainModel.Models
{
    public partial class Artikel : BaseModel
    {
        public Artikel()
        {
            Forderungen = new List<Forderung>();
        }


        public int ArtikelID { get; set; }
        public string Nummer { get; set; }
        public int ErlassID { get; set; }
        public Erlass Erlass { get; set; }
        public ICollection<Forderung> Forderungen { get; set; }
        public string QuelleDE { get; set; }
        public string QuelleFR { get; set; }
        public string QuelleIT { get; set; }
        public string QuelleEN { get; set; }

    }

    
}
