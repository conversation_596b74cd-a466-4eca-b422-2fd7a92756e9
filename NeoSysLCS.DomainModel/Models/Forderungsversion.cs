using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NeoSysLCS.DomainModel.Models
{
    public class Forderungsversion : BaseModel
    {
        public Forderungsversion()
        {
            Objekte = new List<Objekt>();
            Rechtsbereiche = new List<Rechtsbereich>();
            Obligations = new List<Obligation>();
        }

        public int ForderungsversionID { get; set; }

        [ForeignKey("Vorversion")]
        public int? VorversionID { get; set; }
        public Forderungsversion Vorversion { get; set; }

        [ForeignKey("Nachfolgeversion")]
        public int? NachfolgeversionID { get; set; }
        public Forderungsversion Nachfolgeversion { get; set; }

        public int VersionsNummer { get; set; }
        public DateTime Inkrafttretung { get; set; }
        public DateTime? Aufhebung { get; set; }
        public bool Bewilligungspflicht { get; set; }
        public bool Nachweispflicht { get; set; }
        public string InternerKommentar { get; set; }
        public int ForderungID { get; set; }
        public Forderung Forderung { get; set; }
        public int ErlassfassungID { get; set; }
        public Erlassfassung Erlassfassung { get; set; }
        public ICollection<Objekt> Objekte { get; set; }
        public ICollection<Rechtsbereich> Rechtsbereiche { get; set; }
        public ICollection<Obligation> Obligations { get; set; }

        public Boolean? Freigabe { get; set; }
        [StringLength(128)]
        public string FreigabeVon { get; set; }
        public DateTime? FreigabeAm { get; set; }
        public Boolean? QsFreigabe { get; set; }
        [StringLength(128)]
        public string QsFreigabeVon { get; set; }
        public DateTime? QsFreigabeAm { get; set; }
        public string BeschreibungDE { get; set; }
        public string BeschreibungFR { get; set; }
        public string BeschreibungIT { get; set; }
        public string BeschreibungEN { get; set; }


        public void SetFreigabe(bool freigabe, string userID = null)
        {
            if (freigabe)
            {
                Freigabe = true;
                if (!FreigabeAm.HasValue)
                {
                    FreigabeAm = DateTime.Now;
                }
                if (string.IsNullOrEmpty(FreigabeVon))
                {
                    FreigabeVon = userID;
                }
            }
            else
            {
                Freigabe = null;
                FreigabeAm = null;
                FreigabeVon = null;
            }
        }

        public void SetQsFreigabe(bool freigabe, string userID = null)
        {
            if (freigabe)
            {
                QsFreigabe = true;
                if (!QsFreigabeAm.HasValue)
                {
                    QsFreigabeAm = DateTime.Now;
                }
                if (string.IsNullOrEmpty(QsFreigabeVon))
                {
                    QsFreigabeVon = userID;
                }
            }
            else
            {
                QsFreigabe = null;
                QsFreigabeAm = null;
                QsFreigabeVon = null;
            }
        }
    }
}
