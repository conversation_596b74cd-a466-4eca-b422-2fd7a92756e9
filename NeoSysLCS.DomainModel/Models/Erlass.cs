using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Xml;
using System.Xml.Serialization;

namespace NeoSysLCS.DomainModel.Models
{
    public class Erlass : BaseModel
    {
        public Erlass()
        {
            Artikel = new List<Artikel>();
            Erlassversionen = new List<Erlassfassung>();
            Kommentare = new List<Kommentar>();
        }

        public int ErlassID { get; set; }
        public string CustomErlassID { get; set; }
        public string SrNummer { get; set; }
        public DateTime? Aufhebung { get; set; }
        public int? ErlasstypID { get; set; }
        public Erlasstyp Erlasstyp { get; set; }
        public int HerausgeberId { get; set; }
        public Herausgeber Herausgeber { get; set; }
        public ICollection<Artikel> Artikel { get; set; }
        public ICollection<Erlassfassung> Erlassversionen { get; set; }
        public ICollection<Kommentar> Kommentare { get; set; }
        public ICollection<Rechtsbereich> Rechtsbereiche { get; set; }
        public ICollection<Consultation> Consultations { get; set; }
        public DateTime? LUeberpruefung { get; set; }
        public string ProjektleiterID { get; set; }
        public ApplicationUser Projektleiter { get; set; }

        [Column(TypeName = "xml")]
        public string Uebersetzung { get; set; }
    }
}
