using System;

namespace NeoSysLCS.DomainModel.Models
{
    public class IndividuelleForderung : KundenportalBaseModel
    {

        public int IndividuelleForderungID { get; set; }
        public string Beschreibung { get; set; }
        public string Kommentar { get; set; }
        public DateTime GueltigVon { get; set; }
        public DateTime? GueltigBis { get; set; }
        public string Kundenbezug { get; set; }
        public string StandortObjekt { get; set; }

        public KundendokumentErfuellung? Erfuellung { get; set; }
        public DateTime? Erfuellungszeitpunkt { get; set; }
        public string ErfuelltDurch { get; set; }
        public string Verantwortlich { get; set; }
        public string Ablageort { get; set; }

        public int? StandortID { get; set; }
        public Standort Standort { get; set; }

        public int? KundendokumentID { get; set; }
        public Kundendokument Kundendokument { get; set; }
    }
}