using System;
using System.Data.Entity;
using System.Data.Entity.Core.Common;
using System.Data.Entity.ModelConfiguration.Conventions;
using System.Data.Entity.SqlServer;
using System.Web;
using Microsoft.AspNet.Identity.EntityFramework;
using Microsoft.AspNet.Identity;
using System.Linq;
using NeoSysLCS.DomainModel.Models.NeoSysLCS.DomainModel.Models;
using System.IO;
using System.Configuration;

namespace NeoSysLCS.DomainModel.Models
{
    public partial class NeoSysLCS_Dev : IdentityDbContext
    {
        static NeoSysLCS_Dev()
        {
            Database.SetInitializer<NeoSysLCS_Dev>(null);
        }

       public NeoSysLCS_Dev()
            : base(GetConnectionString())
        {
        }

        public static string GetConnectionString()
        {
            // Check environment variable first
            var envConnection = Environment.GetEnvironmentVariable("NEOSYSLCS_DEV_CONNECTION");
            if (!string.IsNullOrEmpty(envConnection))
            {
                return envConnection;
            }
            
            // Check for local config file
            string localConfigPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "local.config");
            if (File.Exists(localConfigPath))
            {
                try
                {
                    var fileMap = new ExeConfigurationFileMap { ExeConfigFilename = localConfigPath };
                    var config = ConfigurationManager.OpenMappedExeConfiguration(fileMap, ConfigurationUserLevel.None);
                    
                    var connStr = config.ConnectionStrings.ConnectionStrings["LocalDevelopment"]?.ConnectionString;
                    if (!string.IsNullOrEmpty(connStr))
                    {
                        return connStr;
                    }
                }
                catch { /* Fallback to default if any error occurs */ }
            }
            
            // Fall back to Web.config connection string
            return "NeoSysLCS_Dev";
        }

        public new class Configuration : DbConfiguration
        {
            public Configuration()
            {

                SetExecutionStrategy("System.Data.SqlClient",() => new SqlAzureExecutionStrategy(1, TimeSpan.FromSeconds(30))); 
            }
        }

        public virtual IDbSet<Artikel> Artikel { get; set; }
        public virtual IDbSet<Erlass> Erlasse { get; set; }
        public virtual IDbSet<Erlasstyp> Erlasstypen { get; set; }
        public virtual IDbSet<Erlassfassung> Erlassfassungen { get; set; }
        public virtual IDbSet<Forderung> Forderungen { get; set; }
        public virtual IDbSet<Herausgeber> Herausgebers { get; set; }
        public virtual IDbSet<Kunde> Kunden { get; set; }
        public virtual IDbSet<Objekt> Objekte { get; set; }
        public virtual IDbSet<Objektkategorie> Objektkategorien { get; set; }
        public virtual IDbSet<Pflicht> Pflichten { get; set; }
        public virtual IDbSet<Rechtsbereich> Rechtsbereiche { get; set; }
        public virtual IDbSet<Sprache> Sprachen { get; set; }
        public virtual IDbSet<Standort> Standorte { get; set; }
        public virtual IDbSet<ApplicationUser> ApplicationUsers { get; set; }
        public virtual IDbSet<ApplicationRole> ApplicationRoles { get; set; }
        public virtual IDbSet<ApplicationUserNewsletterHistory> ApplicationUserNewsletterHistories { get; set; }
        public virtual IDbSet<Forderungsversion> Forderungsversionen { get; set; }
        public virtual IDbSet<StandortObjekt> StandortObjekte { get; set; }
        public virtual IDbSet<KundendokumentForderungsversion> KundendokumentForderungsversionen { get; set; }
        public virtual IDbSet<KundendokumentErlassfassung> KundendokumentErlassfassungen { get; set; }
        public virtual IDbSet<AllgemeineKundeninformation> AllgemeineKundeninformationen { get; set; }
        public virtual IDbSet<KundendokumentSpaltenlabel> KundendokumentSpaltenlabel { get; set; }
        public virtual IDbSet<KundendokumentPflicht> KundendokumentPflichten { get; set; }
        public virtual IDbSet<IndividuelleForderung> IndividuelleForderungen { get; set; }
        public virtual IDbSet<Suvalink> Suvalinks { get; set; }
        public virtual IDbSet<GridViewCookie> GridViewCookie { get; set; }
        public virtual IDbSet<ApplicationUserStandortRole> ApplicationUserStandortRole { get; set; }
        public virtual IDbSet<Shortcut> Shortcut { get; set; }
        public virtual IDbSet<FAQ> FAQ { get; set; }
        public virtual IDbSet<FAQKategorie> FAQKategorie { get; set; }
        public virtual IDbSet<Kommentar> Kommentar { get; set; }
        public virtual IDbSet<LegalCompliance> LegalCompliance { get; set; }
        public virtual IDbSet<StandortMenuUebersetzung> StandortMenuUebersetzung { get; set; }
        public virtual IDbSet<Offer> Offer { get; set; }
        public virtual IDbSet<Evaluation> Evaluation { get; set; }
        public virtual IDbSet<KundeSummary> KundeSummary { get; set; }
        public virtual IDbSet<CustomerNews>  CustomerNews { get; set; }
        public virtual IDbSet<Consultation> Consultation { get; set; }

        public virtual IDbSet<Checklist> Checklist { get; set; }
        public virtual IDbSet<ChecklistQuestion> ChecklistQuestion { get; set; }
        public virtual IDbSet<KundendokumentChecklist> KundendokumentChecklist { get; set; }
        public virtual IDbSet<KundendokumentChecklistQuestion> KundendokumentChecklistQuestion { get; set; }
        public virtual IDbSet<Obligation> Obligations { get; set; }
        public virtual IDbSet<Massnahme> Massnahme { get; set; }
        public virtual IDbSet<Kundendokument> Kundendokument { get; set; }
        public virtual IDbSet<ChecklistHeader> ChecklistHeader { get; set; }
        public virtual IDbSet<KundeCortec> KundeCortec { get; set; }
        public virtual IDbSet<KundeCortecCorrespondenceAddress> KundeCortecCorrespondenceAddress { get; set; }
        public virtual IDbSet<ToDoTask> ToDoTasks { get; set; }
        public virtual IDbSet<PrivacyPolicy> PrivacyPolicy { get; set; }
        public virtual IDbSet<ApplicationUserPrivacyPolicy> ApplicationUserPrivacyPolicy { get; set; }
        public virtual IDbSet<CortecTask> CortecTasks { get; set; }
        public virtual IDbSet<StandortBericht> StandortBerichte { get; set; }
        public virtual IDbSet<EmailTemplate> EmailTemplates { get; set; }

        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {

            base.OnModelCreating(modelBuilder);
            modelBuilder.Conventions.Remove<PluralizingTableNameConvention>();
            modelBuilder.Conventions.Remove<ManyToManyCascadeDeleteConvention>();
            modelBuilder.Conventions.Remove<OneToManyCascadeDeleteConvention>();

            modelBuilder.Entity<Forderungsversion>()
              .HasOptional(a => a.Vorversion)
              .WithMany()
              .HasForeignKey(a => a.VorversionID);

            modelBuilder.Entity<Forderungsversion>()
              .HasOptional(a => a.Nachfolgeversion)
              .WithMany()
              .HasForeignKey(a => a.NachfolgeversionID);

            modelBuilder.Entity<ApplicationUser>()
                .HasOptional(a => a.ErstelltVon)
                .WithMany();
                /*.HasForeignKey(a => a.ErstelltVonID);*/

            modelBuilder.Entity<ApplicationUser>()
                .HasOptional(a => a.BearbeitetVon)
                .WithMany();
            //.HasForeignKey(a => a.BearbeitetVonID);*/

            modelBuilder.Entity<ApplicationUser>()
                 .HasMany<Standort>(s => s.WriteStandorte)
                 .WithMany(c => c.WriteUsers);
 
            modelBuilder.Entity<ApplicationUser>()
                 .HasMany<Shortcut>(s => s.Shortcuts)
                 .WithMany(c => c.Users);

            modelBuilder.Entity<ApplicationUserNewsletterHistory>()
                .HasRequired(a => a.User)
                .WithMany()
                .HasForeignKey(a => a.UserID);

            modelBuilder.Entity<FAQ>()
                 .HasMany<FAQKategorie>(s => s.FAQKategorien)
                 .WithMany(c => c.FAQ);

            modelBuilder.Entity<Kommentar>()
                 .HasMany<Erlass>(s => s.Erlasse)
                 .WithMany(c => c.Kommentare);

            modelBuilder.Entity<Kommentar>()
                 .HasMany<Objekt>(s => s.Objekte)
                 .WithMany(c => c.Kommentare);

            modelBuilder.Entity<Kommentar>()
                 .HasMany<Rechtsbereich>(s => s.Rechtsbereiche)
                 .WithMany(c => c.Kommentare);

            modelBuilder.Entity<Erlassfassung>()
                  .HasOptional(a => a.Kommentar)
                  .WithMany()
                  .HasForeignKey(a => a.KommentarID);

            modelBuilder.Entity<Consultation>()
                .HasMany<Erlass>(s => s.Erlasse)
                .WithMany(c => c.Consultations);

            modelBuilder.Entity<Obligation>()
                 .HasMany<Forderungsversion>(s => s.Forderungsversions)
                 .WithMany(c => c.Obligations);

            modelBuilder.Entity<KundeCortec>()
                 .HasMany<KundeCortecCorrespondenceAddress>(s => s.AdrKorrespondenz)
                 .WithRequired(k => k.KundeCortec)
                 .HasForeignKey(k => k.KundeCortecID);

            modelBuilder.Entity<ApplicationUserPrivacyPolicy>()
                .HasKey(t => new { t.UserID, t.PrivacyPolicyID });  // Defining composite primary key

            // Relationship with ApplicationUser
            modelBuilder.Entity<ApplicationUserPrivacyPolicy>()
                .HasRequired(t => t.User)  // ApplicationUserPrivacyPolicy requires an ApplicationUser
                .WithMany(u => u.ApplicationUserPrivacyPolicies)  // ApplicationUser includes many ApplicationUserPrivacyPolicies
                .HasForeignKey(t => t.UserID)  // The foreign key pointing to ApplicationUser
                .WillCascadeOnDelete(true);

            // Relationship with PrivacyPolicy
            modelBuilder.Entity<ApplicationUserPrivacyPolicy>()
                .HasRequired(t => t.PrivacyPolicy)  // ApplicationUserPrivacyPolicy requires a PrivacyPolicy
                .WithMany(p => p.ApplicationUserPrivacyPolicies)  // PrivacyPolicy includes many ApplicationUserPrivacyPolicies
                .HasForeignKey(t => t.PrivacyPolicyID)  // The foreign key pointing to PrivacyPolicy
                .WillCascadeOnDelete(true);

            //cascade delete on all translation tables

            // cascade delete on kundendokument
            modelBuilder.Entity<KundendokumentErlassfassung>().HasRequired(e => e.Kundendokument).WithMany(e => e.KundendokumentErlassfassungen).WillCascadeOnDelete(true);
            modelBuilder.Entity<KundendokumentForderungsversion>().HasRequired(e => e.Kundendokument).WithMany(e => e.KundendokumentForderungen).WillCascadeOnDelete(true);
            modelBuilder.Entity<KundendokumentPflicht>().HasRequired(e => e.Kundendokument).WithMany(e => e.KundendokumentPflichten).WillCascadeOnDelete(true);
            modelBuilder.Entity<KundendokumentChecklist>().HasRequired(e => e.Kundendokument).WithMany(e => e.KundendokumentChecklists).WillCascadeOnDelete(true);
            modelBuilder.Entity<KundendokumentChecklistQuestion>().HasRequired(e => e.KundendokumentChecklist).WithMany(e => e.Questions).WillCascadeOnDelete(true);
            //modelBuilder.Entity<IndividuelleForderung>().HasRequired(e => e.Kundendokument).WithMany(e => e.IndividuelleForderungen).WillCascadeOnDelete(true);
            modelBuilder.Entity<KundeCortecCorrespondenceAddress>().HasRequired(k => k.KundeCortec).WithMany(k => k.AdrKorrespondenz).WillCascadeOnDelete(true);
        }

        public static NeoSysLCS_Dev Create()
        {
            return new NeoSysLCS_Dev();
        }

    }


}