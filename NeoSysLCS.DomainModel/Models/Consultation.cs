using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace NeoSysLCS.DomainModel.Models
{
    public class Consultation : BaseModel
    {
        public Consultation()
        {
            Erlasse = new List<Erlass>();
        }

        public int ConsultationId { get; set; }
        public ConsultationStatus Status { get; set; }
        public DateTime EntryDate { get; set; }
        public DateTime? OpenedDate { get; set; }
        public DateTime? Deadline { get; set; }
        public DateTime? CompletedDate { get; set; }
        public ICollection<Erlass> Erlasse { get; set; }

        [Column(TypeName = "xml")]
        public string U<PERSON><PERSON>tzung { get; set; }

    }
}